import 'package:get/get.dart';
import 'package:new_agnes/analytics/AnalyticsService.dart';
import 'package:singular_flutter_sdk/singular.dart';
import 'package:singular_flutter_sdk/singular_config.dart';

class SingularService extends GetxService implements IAnalyticsService {
  @override
  void onInit() {
    super.onInit();
    // TODO: http接口获取 appKey 及 id
    initSingular(SingularCommon.API_KEY, SingularCommon.API_SECRET);
  }

  void initSingular(String API_KEY, String API_SECRET) {
    // 初始化 Singular
    SingularConfig config = new SingularConfig(API_KEY, API_SECRET);
    // Set a custom user ID for tracking
    config.customUserId = "agnes_user_test@android";
    // Allow data sharing (set to true for compliance if needed)
    // config.limitDataSharing = false;
    // config.facebookAppId = "123456789012345"; // Optional: For Facebook integration
    config.logLevel = 5; // Enable logging for debugging
    config.enableLogging = false; // Enable SDK logs

    Singular.start(config);
  }

  /// 后台暂时未定义统计的事件，或者运营所需新的埋点，测试通过需要添加到枚举中
  @override
  void trackEvent(String eventName, Map<String, dynamic>? eventParams) {
    eventParams != null
        ? Singular.eventWithArgs(eventName, eventParams)
        : Singular.event(eventName);
  }

  /// 后台已经自定义好的事件
  @override
  void event(AnalyticEvent event, [Map<String, dynamic>? additionalParams]) {
    final params = event.getFullParams(additionalParams);
    params != null
        ? Singular.eventWithArgs(event.name, params)
        : Singular.event(event.name);
  }
}

/// Singular 测试配置
class SingularCommon {
  static const String API_KEY="nj_kiwiar_37e77a8a";
  static const String  API_SECRET="ea240a932d0b04871fe06a33da968053";
}
