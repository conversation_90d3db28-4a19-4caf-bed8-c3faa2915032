import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:new_agnes/analytics/AnalyticsService.dart';
import 'package:new_agnes/api/StorageService.dart';

class FirebaseService extends GetxService implements IAnalyticsService {
  late FirebaseAnalytics _analytics;
  bool _isInitialized = false;

  @override
  void onInit() {
    super.onInit();
    debugPrint('FirebaseService onInit called');
    initFirebase();
  }

  Future<void> initFirebase() async {
    try {
      debugPrint('Initializing Firebase Analytics...');
      _analytics = FirebaseAnalytics.instance;

      // 启用分析功能（调试时可选）
      // await _analytics.setAnalyticsCollectionEnabled(true);

      // 设置用户ID
      // await _analytics.setUserId(id: "agnes_user_test@android");

      // 设置用户属性（可选）
      // await _analytics.setUserProperty(name: "user_type", value: "test_user");

      _isInitialized = true;
      debugPrint('Firebase Analytics initialized successfully');
    } catch (e, stackTrace) {
      _isInitialized = false;
      debugPrint('FirebaseService initialization error: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  /// 记录自定义事件
  @override
  void trackEvent(String eventName, Map<String, dynamic>? eventParams) {
    if (!_isInitialized) {
      debugPrint('Firebase Analytics not initialized, skipping event: $eventName');
      return;
    }

    try {
      debugPrint('Tracking custom event: $eventName with params: $eventParams');

      // 确保参数符合 Firebase 要求
      final sanitizedParams = _sanitizeParameters(eventParams);

      _analytics.logEvent(
        name: eventName,
        parameters: sanitizedParams,
      );

      debugPrint('Custom event tracked successfully: $eventName');
    } catch (e, stackTrace) {
      debugPrint('Error tracking custom event $eventName: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  /// 记录预定义事件
  @override
  void event(AnalyticEvent event, [Map<String, dynamic>? additionalParams]) {
    if (!_isInitialized) {
      debugPrint('Firebase Analytics not initialized, skipping event: ${event.name}');
      return;
    }

    try {
      final params = event.getFullParams(additionalParams);
      debugPrint('Tracking predefined event: ${event.name} with params: $params');

      // 确保参数符合 Firebase 要求
      final sanitizedParams = _sanitizeParameters(params);

      switch (event) {
        case AnalyticEvent.googleAccountRegistration:
          _analytics.logSignUp(signUpMethod: 'google');
          break;
        case AnalyticEvent.googleAccountLogin:
          _analytics.logLogin(loginMethod: 'google');
          break;
        case AnalyticEvent.phoneRegistration:
          _analytics.logSignUp(signUpMethod: 'phone');
          break;
        case AnalyticEvent.phoneLogin:
          _analytics.logLogin(loginMethod: 'phone');
          break;
        case AnalyticEvent.mailAccountRegistration:
          _analytics.logSignUp(signUpMethod: 'email');
          break;
        case AnalyticEvent.mailAccountLogin:
          _analytics.logLogin(loginMethod: 'email');
          break;
        default:
          _analytics.logEvent(
            name: event.name,
            parameters: sanitizedParams,
          );
      }

      debugPrint('Predefined event tracked successfully: ${event.name}');
    } catch (e, stackTrace) {
      debugPrint('Error tracking predefined event ${event.name}: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  /// 清理参数以符合 Firebase 要求
  Map<String, Object>? _sanitizeParameters(Map<String, dynamic>? params) {
    if (params == null) return null;

    final sanitized = <String, Object>{};

    params.forEach((key, value) {
      // Firebase Analytics 要求参数值必须是特定类型
      if (value is String ||
          value is int ||
          value is double ||
          value is bool ||
          value is num) {
        sanitized[key] = value;
      } else if (value != null) {
        // 转换为字符串
        sanitized[key] = value.toString();
      }
    });

    return sanitized.isEmpty ? null : sanitized;
  }

  /// 获取 FirebaseAnalytics 实例（用于访问特定方法）
  FirebaseAnalytics get analytics => _analytics;

  /// 检查服务是否已初始化
  bool get isInitialized => _isInitialized;
}
