import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:new_agnes/analytics/singular/SingularService.dart';
import 'package:new_agnes/analytics/firebase/FirebaseService.dart';

/// 分析工具类型枚举
enum AnalyticsProvider {
  singular,
  firebase,
}

/// 抽象分析服务接口
abstract class IAnalyticsService {
  void trackEvent(String eventName, Map<String, dynamic>? eventParams);
  void event(AnalyticEvent event, [Map<String, dynamic>? additionalParams]);
}

/// 分析管理器 - 工厂模式实现
class AnalyticsManager extends GetxService {
  final Map<AnalyticsProvider, IAnalyticsService> _services = {};
  
  @override
  void onInit() {
    super.onInit();
    _initServices();
  }
  
  void _initServices() {
    // 尝试初始化所有可用的分析服务
    _tryRegisterService(AnalyticsProvider.singular, () => Get.put(tag: AnalyticsProvider.singular.name, SingularService()));
    _tryRegisterService(AnalyticsProvider.firebase, () => Get.put(tag: AnalyticsProvider.firebase.name, FirebaseService()));
  }
  
  void _tryRegisterService(AnalyticsProvider provider, IAnalyticsService Function() serviceFactory) {
    try {
      final service = serviceFactory();
      _services[provider] = service;
    } catch (e) {
      // 服务未注册，忽略
      debugPrint('Analytics service $provider not available: $e');
    }
  }
  
  /// 注册分析服务
  void registerService(AnalyticsProvider provider, IAnalyticsService service) {
    _services[provider] = service;
  }
  
  /// 移除分析服务
  void unregisterService(AnalyticsProvider provider) {
    _services.remove(provider);
  }
  
  /// 获取特定分析服务
  IAnalyticsService? getService(AnalyticsProvider provider) {
    return _services[provider];
  }
  
  /// 获取所有已注册的服务
  List<IAnalyticsService> getAllServices() {
    return _services.values.toList();
  }
  
  /// 获取特定的分析服务列表
  List<IAnalyticsService> getServices(List<AnalyticsProvider> providers) {
    return _services.entries
        .where((entry) => providers.contains(entry.key))
        .map((entry) => entry.value)
        .toList();
  }
  
  /// 向所有已注册的服务发送自定义事件
  void trackEvent(String eventName, Map<String, dynamic>? eventParams) {
    for (var service in _services.values) {
      try {
        service.trackEvent(eventName, eventParams);
      } catch (e) {
        // 忽略单个服务的错误，确保其他服务正常工作
        debugPrint('Error tracking event in service: $e');
      }
    }
  }
  
  /// 向所有已注册的服务发送预定义事件
  void event(AnalyticEvent event, [Map<String, dynamic>? additionalParams]) {
    for (var service in _services.values) {
      try {
        service.event(event, additionalParams);
      } catch (e) {
        // 忽略单个服务的错误，确保其他服务正常工作
        debugPrint('Error tracking event in service: $e');
      }
    }
  }
  
  /// 向指定的服务发送自定义事件
  void trackEventTo(
    List<AnalyticsProvider> providers,
    String eventName,
    Map<String, dynamic>? eventParams,
  ) {
    final services = getServices(providers);
    for (var service in services) {
      try {
        service.trackEvent(eventName, eventParams);
      } catch (e) {
        debugPrint('Error tracking event in service: $e');
      }
    }
  }
  
  /// 向指定的服务发送预定义事件
  void eventTo(
    List<AnalyticsProvider> providers,
    AnalyticEvent event,
    [Map<String, dynamic>? additionalParams]
  ) {
    final services = getServices(providers);
    for (var service in services) {
      try {
        service.event(event, additionalParams);
      } catch (e) {
        debugPrint('Error tracking event in service: $e');
      }
    }
  }
}

/// Singular 事件枚举，包含事件名称和参数定义(备用)
enum AnalyticEvent {
  //todo 填入运营配置的事件名称，以及参数（可能有）
  loginWithGoogle('login_from_google', null
    //   {
    // 'method': 'google'
    //   }
  ),

  loginWithApple('login_from_apple', null),
  loginWithOther('continue_via_other_means', null),

  loginPhoneTab('tap_the_phone', null),
  loginPhoneBack('phone_page_back', null),
  loginPhoneSend('phone_page_send', null),
  loginPhoneSignIn('phone_page_login', null),

  loginEmailTab('tap_the_mail', null),
  loginEmailBack('mail_page _back', null),
  loginEmailSignIn('mail_page_login', null),
  loginEmailForgetPassword('mail_page_forgot_password', null),

  register('register', null),
  registerSend('register_page_send', null),
  registerBack('register_page_back', null),
  registerAgreeToLogin('register_page_agreelogin', null),

  // 注册或登录成功事件
  googleAccountRegistration('google_account_registration', null),
  googleAccountLogin('google_account_login', null),
  appleAccountRegistration('apple_account_registration', null),
  appleAccountLogin('apple_account_login', null),
  phoneRegistration('phone_registration', null),
  phoneLogin('phone_login', null),
  mailAccountRegistration('mail_account_registration', null),
  mailAccountLogin('mail_account_login', null),

  // todo 可能还缺少忘记密码相关页面埋点
  ;

  /// 事件名称
  final String name;

  /// 事件参数（可选）
  final Map<String, dynamic>? defaultParams;

  /// 构造函数
  const AnalyticEvent(this.name, this.defaultParams);

  /// 获取事件完整参数（合并默认参数和传入参数，目前没有，后面备用）
  Map<String, dynamic>? getFullParams([Map<String, dynamic>? additionalParams]) {
    if (defaultParams == null && additionalParams == null) {
      return null;
    }

    if (defaultParams == null) {
      return additionalParams;
    }

    if (additionalParams == null) {
      return defaultParams;
    }

    // 合并参数，additionalParams 优先级更高
    Map<String, dynamic> fullParams = Map.from(defaultParams!);
    fullParams.addAll(additionalParams);
    return fullParams;
  }
}