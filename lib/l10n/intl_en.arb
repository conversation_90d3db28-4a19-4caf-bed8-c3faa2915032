{"aboutDiamond": "About Diamond", "accept": "Accept", "account": "Account", "accountDeletionSuccessful": "Account deletion successful", "accountNotCreatedYet": "Account not created yet", "accountSignoutSuccessful": "Account signout successful", "activeAccount": "Active Account", "add": "Add", "addComment": "Add Comment", "addPeopleToChat": "Add People To Chat", "addToChat": "Add to Chat", "addUserEmailAndEnterToInvite": "Add user email and \"enter\" to invite", "addedToGroupSuccessfully": "Added to group successfully", "agnesCanMakeMistakesCheckImportantInfo": "<PERSON> can make mistakes. Check important info.", "agnesForSummariesReportsOrSlides": "@Agnes for summaries, reports, or slides.", "agnesVibepods": "<PERSON>", "agreeAndLogIn": "Agree and Log In", "aiDesign": "AI Design", "aiSlides": "AI Slides", "album": "Album", "allMembers": "All Members", "answerCall": "Answer Call", "appUpdate": "App Update", "archived": "Archived", "areYouSureYouWantToDeleteYourAccount": "Are you sure you want to delete your account?", "areYouSureYouWantToSignOutYourAccount": "Are you sure you want to sign out your account?", "askAgnes": "<PERSON>", "askAgnesToEditSlides": "Ask <PERSON> to edit slides", "assignATaskToBegin": "Assign a task to begin", "atLeastSixCharactersLongWithLettersNumbersSymbols": "At least six characters long with letters, numbers & symbols", "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny": "At present, there is no consensus on the tasks to be done. Feel free to discuss further and ask questions if you have any!", "avatar": "Avatar", "back": "Back", "bookmarkRemoved": "Bookmark removed", "bookmarked": "Bookmarked", "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": "By signing in, you are agreeing to the\n{termsOfService} and {privacyPolicy}.", "callDeclined": "Call declined", "cancel": "Cancel", "canvas": "<PERSON><PERSON>", "chapter": "Chapter", "chat": "Cha<PERSON>", "chatSettings": "<PERSON><PERSON>", "chooseImage": "Choose Image", "close": "Close", "code": "Code", "collect": "Collect", "comingSoon": "Coming Soon", "comment": "Comment", "commenter": "<PERSON><PERSON><PERSON>", "commentsTasks": "Comments Tasks", "confirm": "Confirm", "confirmDelete": "Confirm Delete", "confirmPassword": "Confirm Password", "confirmPasswordCannotBeEmpty": "Confirm password cannot be empty", "confirmSignOut": "Confirm Sign Out", "connect": "Connect", "connectTheAiHeadsetAndChatWithAgnesAtAnyTime": "Connect the AI headset and chat with <PERSON> at any time", "connectToAgnesVibepodsAndChatAnytime": "Connect to <PERSON> and chat anytime.", "connectWithFriendsToStartAGroupChat": "Connect with Friends to Start a Group Chat", "connected": "Connected", "connectionFailed": "Connection failed", "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform": "Content copied—go ahead and paste it on your favorite platform!", "continueWithApple": "Continue with Apple", "continueWithGoogle": "Continue with Google", "continueWithOtherWays": "Continue with other ways", "continueWord": "Continue", "copied": "<PERSON>pied", "copy": "Copy", "cost": "cost", "cover": "Cover", "createANewTaskToGetStarted": "Create a new task to get started", "createAnAccountToSignUpForAgnes": "Create an account to sign up for <PERSON>", "createGroupChat": "Create Group Chat", "currentlyInACall": "Currently in a call...", "deepResearch": "Deep Research", "deepresearch": "DeepResearch", "delete": "Delete", "deleteAccount": "Delete Account", "deleteChat": "Delete Chat", "doYouWantToExecuteTheseTasks": "Do you want to execute these tasks?", "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite": " doesn’t seem to be an <PERSON> user. Enter their email to send an invite.", "done": "done", "dontHaveAnAccount": "Don’t have an account?", "downloading": "Downloading", "eachNewTaskConsumesOneDiamond": "1. Each new task consumes one diamond.", "eachUserReceivesDiamondsPerMonth": "3. Each user receives 30 diamonds per month.", "edit": "Edit", "editGroupName": "Edit group name", "editor": "Editor", "email": "Email", "emailCannotBeEmpty": "Email cannot be empty", "emailFormatIncorrect": "Email format incorrect", "emaildomaincom": "<EMAIL>", "enhanceProductivityAndCreativityThroughExtendedAccess": "Enhance productivity and creativity through extended access", "enterConfirmPassword": "Enter Confirm Password", "enterGroupName": "Enter group name", "enterPassword": "Enter Password", "enterVerificationCode": "Enter Verification Code", "export": "Export", "file": "File", "floatingWindowNotEnabled": "Floating Window Not Enabled", "forTheFullExperiencePleaseUseThePcVersion": "For the full experience, please use the PC version", "forgotPassword": "Forgot Password", "general": "General", "getAgnesAiPlus": "Get Agnes AI Plus", "getAgnesAiPro": "Get Agnes AI Pro", "giveMeATaskToWorkOn": "Give me a task to work on…", "goToShop": "Go To Shop", "gotItImWorkingOnTheTaskPleaseWaitAMoment": "Got it! I'm working on the task. Please wait a moment.", "groupCreatedSuccessfully": "Group created successfully", "groupFiles": "Group Files", "groupName": "Group name", "groupNameOptional": "Group Name (Optional)", "harmful": "Harmful", "helpUsImprove": "Help us improve", "iAssistWithSearchWritingAndAnswers": "I assist with search, writing, and answers", "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded": "If you answer the incoming call yourcurrent call will be ended.", "imAgnes": "I'm <PERSON>", "inAVoiceCall": "In a voice call", "inaccurate": "Inaccurate", "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater": "In-app purchases are currently unavailable, Please try again later", "interfaceLanguage": "Interface Language", "invite": "Invite", "inviteByUsernameEmailOrPhoneNumber": "Invite by username, email, or phone number", "inviteFriends": "Invite Friends", "inviteOthersToCollaborate": "Invite others to collaborate", "inviteToCollaborate": "Invite to collaborate", "isEditing": "is editing", "join": "Join", "knowMore": "Know More", "language": "English", "letAgnesHelpYouCreateABetterSharingFormat": "Let <PERSON> help you create a better sharing format", "letAgnesResolve": "Let <PERSON> resolve", "linkCopied": "Link copied", "listening": "Listening", "loading": "Loading", "makeMeAShareableImagePost": "Make me a shareable image post", "makeMeAnHtmlWebPage": "Make me an HTML web page", "manageMembers": "Manage Members", "manuallyEndTheTask": "Manually end the task.", "markAsResolved": "<PERSON> as Resolved", "messageCopied": "Message Copied.", "multipleChoices": "Multiple choices", "myFavorites": "My Favorites", "narrate": "Narrate", "networkConnectionFailedPleaseTryAgain": "Network connection failed. Please try again.", "networkConnectionLost": "Network connection lost", "networkUnavailable": "Network unavailable", "newPassword": "New Password", "newProject": "New Project", "newTask": "New Task", "newTitle": "New Title", "noMoreFiles": "No More Files", "noMoreMessage": "no more message...", "noPendingTasksFoundAllTasksHaveBeenCompleted": "No pending tasks found. All tasks have been completed!", "notAvailableForPurchase": "Not Available for Purchase", "notConnected": "Not connected", "notNow": "Not now", "notifications": "Notifications", "notifyUserViaEmail": "Notify user via email", "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt": "Okay,  I noticed you don’t have the required permissions. Please ask the owner or an editor to do it.", "okayIllSummarizeTheConsensusTasks": "Okay, I'll summarize the consensus tasks:", "onceDeletedGroupChatHistoryWillBeCleared": "Once deleted, group chat history will be cleared.", "operationSuccessful": "Operation Successful!", "orLetMeKnowIfYouHaveAnyQuestions": "Or let me know if you have any questions.", "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged": "Other group members will be notified after the group name is changed.", "outOfDate": "Out of date", "owner": "Owner", "pages": "Pages", "passwordCannotBeEmpty": "Password cannot be empty", "pending": "Pending", "permissionToExecuteTasksInTheBackgroundIsNotGranted": "Permission to execute tasks in the background is not granted", "phone": "Phone", "phoneNumber": "Phone Number", "phoneNumberCannotBeEmpty": "Phone number cannot be empty", "photo": "Photo", "pleaseSelectASlideToEdit": "Please select a slide to edit.", "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements": "Please update to the latest version for new features and improvements.", "pleaseWaitForAgnesToCompleteTheTask": "Please wait for <PERSON> to complete the task", "pleaseWaitUntilTheDownloadIsCompleted": "Please wait until the download is completed", "presenter": "Presenter", "preview": "Preview", "privacyPolicy": "Privacy Policy", "provideAdditionalFeedbackOnThisAnswer": "Provide additional feedback on this answer", "quote": "Quote", "quotedMessageHasExpired": "Quoted message has expired.", "reconnect": "Reconnect", "reject": "Reject", "remove": "Remove", "removedSuccessfully": "Removed successfully", "rename": "<PERSON><PERSON>", "reopenTheApp": "Reopen the app", "requestTimedOutPleaseTryAgain": "Request timed out. Please try again.", "research": "Research", "researchForZdSeconds": "Research for {value} seconds", "resetPassword": "Reset Password", "resetYourPassword": "Reset Your Password", "role": "Role", "save": "Save", "saved": "Saved", "saving": "Saving…", "search": "Search", "searchByNameEmailOrPhone": "Search by name, email or phone", "searchForACountry": "Search for a country", "seconds": "Seconds", "seemsToBeDisconnectedFromTheInternet": "Seems to be disconnected from the internet", "selectMembers": "Select Members", "send": "Send", "sendMessage": "Send Message", "serverConnectionFailed": "Server connection failed.", "serviceIsStillStarting": "Service is still starting.", "sessionExpiredPleaseLogInAgain": "Session expired. Please log in again.", "setNow": "Set Now", "settings": "Settings", "share": "Share", "signIn": "Sign In", "signOut": "Sign Out", "signUp": "Sign Up", "someoneMe": "Someone @me", "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow": "Sorry, we’re at full capacity right now. Please try again tomorrow.", "sorryYourTitleTaskFailedPleaseTryAgainLater": "Sorry, your {title} task failed. Please try again later.", "startSpeaking": "Start speaking", "submit": "Submit", "supportsUploadingUpToZdImages": "Supports uploading up to {value} images.", "switchWifimobileDataAndRetry": "Switch Wi-Fi/mobile data and retry", "system": "System", "systemBusyPleaseTryAgain": "System busy, please try again.", "takePhoto": "Take Photo", "taskStoppedManually": "Task stopped manually.", "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks": "Tasks deleted as requested. Current pending tasks: {remaining_count}. Would you like me to show all pending tasks?", "termsOfService": "Terms of Service", "theDiamondCountUpdatesOnThe1stOfEveryMonth": "2. The diamond count updates on the 1st of every month.", "theGroupIsCurrentlyInACall": "The group is currently in a call...", "theLanguageUsedInTheUserInterface": "The language used in the user interface", "thePhoneNumberIsInvalid": "The phone number is invalid", "thereAreCurrentlyTotalPendingTasksTask": "There are currently {total} pending tasks:", "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture": "This app requires camera access to let you take a photo for your profile picture.", "thisIsntHelpful": "This isn't helpful", "thisSlideIsBeingEdited": "This slide is being edited.", "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera": "To allow you to send images for recognition, interaction, or setting your avatar, please allow <PERSON> to access your camera.", "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary": "To allow you to send images for recognition, interaction, or setting your avatar, please allow <PERSON> to access your photo library.", "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary": "To save generated images, please allow <PERSON> to save images to your photo library.", "transcribing": "Transcribing...", "transcriptionEnabledLoadingData": "Transcription enabled. Loading data…", "tryOneOfThese": "Try one of these:", "twoPasswordsAreInconsistent": "Two passwords are inconsistent", "update": "Update", "updateFailedPleaseTryAgainLater": "Update failed. Please try again later.", "updatePassword": "Update Password", "updatePasswordSuccessfully": "Update password successfully", "upgradeYourPlanToContinueUpgrade": "Upgrade your plan to continue: Upgrade", "useCharactersWithLettersNumbersSymbols": "Use 6+ characters with letters, numbers & symbols", "userAccess": "User Access", "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute": "User {user_name} has insufficient quota, slides remaining: {slides_remaining_count}, slides required quota: {slides_plan_count}, research remaining: {research_remaining_count}, research required quota: {research_plan_count}. You can choose tasks with enough quota to execute.", "username": "Username", "usersWithAccess": "Users with access", "usingTool": "Using Tool", "verificationCode": "Verification Code", "verificationCodeCannotBeEmpty": "Verification code cannot be empty", "verificationCodeSentToXxxemailcom": "Verification code sent to {xxx}@email.com", "versionIsNowAvailable": "Version {version} is now available.", "view": "View", "viewAllComments": "View all comments", "viewer": "Viewer", "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain": "Voice call creation failed, possibly due to network instability. Please try again.", "voiceCallEnded": "Voice call ended", "waiting": "Waiting", "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary": "We need access to save downloaded images or videos to your photo library.", "weNeedAccessToYourMicrophoneForVoiceInteraction": "We need access to your microphone for voice interaction.", "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile": "We need access to your photo library so you can select a picture for your profile.", "weNeedToAccessYourPhotoLibraryToSelectImages": "We need to access your photo library to select images", "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction": "We need to recognize your speech to convert it to text for interaction.", "weNeedToSaveImagesToYourPhotoLibrary": "We need to save images to your photo library", "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction": "We need to use Bluetooth to connect to your headset for voice interaction.", "weNeedToUseYourCameraToTakePhotos": "We need to use your camera to take photos", "welcomeToAgnes": "Welcome to <PERSON>", "writeSomething": "Write Something", "xMembersInVoice": "{x} members in voice...", "xxxChangedTheGroupName": "{xxx} changed the group name", "xxxHasInvitedYouToCollaborateOnEditingXxx": "{xxx} has invited you to collaborate on editing \"{xxx}\".", "xxxHasInvitedYouToCollaborateOnReviewingXxx": "{xxx} has invited you to collaborate on reviewing \"{xxx}\".", "xxxHasStartedAVoiceCall": "{xxx} has started a voice call", "xxxInvitesYouToJoinTheXyzGroupCall": "{xxx} invites you to join the \"{xyz}\" group call.", "you": "You", "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed": "You can execute, modify, or remove, how would you like to proceed?", "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes": "You can manually edit the slides or edit through conversation with <PERSON>.", "youCanSelectContentAndShareYourFeedback": "You can select content and share your feedback.", "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows": "You can't minimize a video call as AGNES isn't authorized to use floating windows.", "youCreatedTheGroup": "You created the group.", "youHaveBeenRemovedFromTheGroupChat": "You have been removed from the group chat", "youJoinedTheGroup": "You joined the group.", "yourCurrentPlan": "Your current plan", "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou": "Your phone has not authorized <PERSON> to use the floating window permission. <PERSON> will not be able to continue performing tasks in the background for you.", "yourRequestForTheResearchTaskLimitHasBeenReached": "Your request for the research task limit has been reached.", "yourRequestForTheSlideTaskLimitHasBeenReached": "Your request for the slide task limit has been reached.", "yourRequestForThisTaskLimitHasBeenReached": "Your request for this task limit has been reached.", "yourTitleTaskHasBeenSuccessfullyCompleted": "Your {title} task has been successfully completed!", "youveReachedTheEnd": "You’ve reached the end"}