
import 'dart:convert';

class StreamResultEntity {
  final String event;
  final String data;
  final String? model;
  final String? conversationId;

  const StreamResultEntity(
      {required this.event,
      required this.data,
      this.model,
      this.conversationId});

  Map<String, dynamic> dataJson() {
    return robustJsonDecode(data);
  }

  Map<String, dynamic> robustJsonDecode(String jsonString, {bool returnNullOnError = false}) {
    try {
      return jsonDecode(jsonString);
    } catch (e) {
      return {};
    }
  }

  @override
  String toString() {
    return "StreamResultEntity(event: $event, data: $data, model: $model, conversationId: $conversationId)";
  }
}
