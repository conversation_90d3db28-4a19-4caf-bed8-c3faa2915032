import 'package:flutter/material.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';

class StreamResultAnalysis {
  static StreamResultAnalysis? _instance;

  static StreamResultAnalysis get instance =>
      _instance ??= StreamResultAnalysis._();

  StreamResultAnalysis._();

  StringBuffer _jsonBuffer = StringBuffer();

  void onInit() {
    debugPrint("StreamResultAnalysis - onInit");
  }

  void onAnalysisData(String original, {onResult,String? model}) {
    if (_isPing(original)) {
      return;
    }
    _jsonBuffer.write(original);
    String source = _jsonBuffer.toString();
    if (!source.startsWith("event:")) {
      // 格式不对，不处理
      _jsonBuffer.clear();
      return;
    }
    // 查找data:的位置
    int dataIndex = source.indexOf("data:");
    if (dataIndex == -1) {
      // 没有找到data字段
      _jsonBuffer.clear();
      return;
    }
    // 提取event部分（从event:后到data:前）
    String event = source.substring(6, dataIndex).trim();
    // 提取data部分（从data:后到结尾）
    String data = source.substring(dataIndex + 5).trim(); // 5是"data:"的长度
    //校验data 是否是正确的json数据
    if (!data.endsWith("}")) {
      return;
    }
    _jsonBuffer.clear();
    debugPrint("解析结果 - Event: '$event'");
    debugPrint("解析结果 - Data: '$data'");
    StreamResultEntity resultEntity =
        StreamResultEntity(event: event, data: data,model: model);
    onResult(resultEntity);
  }

  void onDone() {
    debugPrint("StreamResultAnalysis - onDone");
    FlutterOverlayWindow.shareData({"type":"message","content":"已完成"});
  }

  void onError() {
    debugPrint("StreamResultAnalysis - onError");
    FlutterOverlayWindow.shareData({"type":"message","content":"已失败"});
  }

  bool _isPing(String original) {
    DateTime dateTime = DateTime.now();
    if (original.contains("ping") && original.contains("${dateTime.year}-${dateTime.month.toString().padLeft(2, "0")}-${dateTime.day.toString().padLeft(2, "0")}")) {
      //: ping - 2025-08-06 01:36:05.866774+00:00
      return true;
    }
    return false;
  }
}
