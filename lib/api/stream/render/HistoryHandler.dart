import 'dart:convert';

import 'package:new_agnes/api/history/historyMessage.dart';
import 'package:new_agnes/api/stream/render/StreamEventHandler.dart';
import 'package:new_agnes/api/stream/render/entity.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';
import 'package:new_agnes/utils/chat_waste_filter_utils.dart';

import '../../../module/chat/model/history_deep_research_model.dart';
import '../../../module/chat/model/tool_call_model.dart';
import '../stream_message_type_enum.dart';

class HistoryHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "sse_history";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    var data = entity.dataJson();
    List<HistoryMessage> messages = data["messages"]
        .map((item) => HistoryMessage.fromJson(item))
        .toList()
        .reversed
        .toList()
        .cast<HistoryMessage>();
    int? lastLlmIndex;
    for (int i = 0; i < messages.length; i++) {
      HistoryMessage message = messages[i];
      if (message.isLlmMessage) {
        int? curIndex = _createLlmTask(message, context, entity.conversationId);
        if (curIndex != null) {
          lastLlmIndex = curIndex;
        }
      } else if (message.isUserMessage) {
        _createUserTask(message,context);
      } else if (message.isSys) {
        _createSysTask(message, context);
      }
    }
    if (lastLlmIndex != null) {
      context.workflow.renderList[lastLlmIndex].isComplete.value = true;
    }
  }

  void _createUserTask(HistoryMessage data,RenderContext context) {
    RenderTask? lastTask = context.workflow.renderList.isNotEmpty
        ? context.workflow.renderList.last
        : null;
    lastTask?.isComplete.value = true;
    // 创建 RenderTask
    RenderTask userTask = RenderTask();
    userTask.agentName = context.currentAgentName ?? "";
    if(data.message=='execute now'){
      userTask.type.value = StreamMessageTypeEnum.message_execute_now;
    }else{
      userTask.type.value = StreamMessageTypeEnum.message_text;
    }
    userTask.content.value = data.message;
    userTask.sender.value = data.sender;
    userTask.docs = data.docs;
    // 添加到工作流
    context.workflow.renderList.add(userTask);
  }

  void _createSysTask(HistoryMessage data, RenderContext context) {
    // 创建 RenderTask
    RenderTask userTask = RenderTask();
    userTask.agentName = context.currentAgentName ?? "";
    userTask.content.value = data.message;
    userTask.type.value = data.typeIgnoreFold;
    userTask.sender.value = data.sender;
    // 添加到工作流
    context.workflow.renderList.add(userTask);
  }

  int? _createLlmTask(
      HistoryMessage data, RenderContext context, String? conversationId) {
    if(data.typeIgnoreFold == StreamMessageTypeEnum.unknown){
      return null;
    }
    // 创建 RenderTask
    RenderTask llmTask = RenderTask();
    RenderTask reporterTask = RenderTask();
    llmTask.agentName = context.currentAgentName ?? "";
    llmTask.type.value = data.typeIgnoreFold;
    llmTask.fold.value = data.isFold;
    llmTask.sender.value = data.sender;
    llmTask.conversationId = conversationId ?? "";
    if (llmTask.type.value == StreamMessageTypeEnum.tag) {
      llmTask.content.value = jsonDecode(data.message)['message'];
    } else if (llmTask.type.value == StreamMessageTypeEnum.research) {
      llmTask.type.value = StreamMessageTypeEnum.message_thinking;
      HistoryDeepResearchModel historyDeepResearchModel =
          HistoryDeepResearchModel.fromJson(jsonDecode(data.message));
      historyDeepResearchModel.workflow!.steps!.forEach((setp) {
        RenderTask thinkTask = RenderTask();
        if (setp!.agentName == 'planner') {
          thinkTask.agentName = setp!.agentName!;
          setp!.tasks!.forEach((task) {
            if (task!.type == 'thinking') {
              thinkTask.content.value =
                  thinkTask.content.value + task!.payload!.text!;
            }
          });
        }
        if (setp!.agentName == 'researcher') {
          thinkTask.agentName = setp!.agentName!;
          for (int a = 0; a < setp!.tasks!.length; a++) {
            HistoryDeepResearchModelWorkflowStepsTasks task = setp!.tasks![a]!;
            if (task!.type == 'thinking') {
              thinkTask.content.value =
                  thinkTask.content.value + task!.payload!.text!;
            }
            if (task!.type == 'tool_call' &&
                task.payload!.toolName != 'crawl_tool') {
              ToolCallModel toolCallModel = ToolCallModel();
              toolCallModel.toolCallId = '';
              toolCallModel.toolName = task.payload!.toolName;
              ToolCallModelToolInput toolInput = new ToolCallModelToolInput();
              toolInput.query = task.payload!.input!.query;
              toolCallModel.toolInput = toolInput;
              toolCallModel.toolResult = task.payload!.output;
              thinkTask.content.value = thinkTask.content.value +
                  '\n```json\n\n${jsonEncode(toolCallModel.toJson())}\n```\n';
            }
          }
        }
        if (setp!.agentName == 'reporter') {
          reporterTask.agentName = 'reporter';
          setp!.tasks!.forEach((task) {
            reporterTask.content.value =
                reporterTask.content.value + task!.payload!.text!;
            reporterTask.type.value = StreamMessageTypeEnum.message;
            reporterTask.isComplete.value = true;
            reporterTask.handOff = "handoff_to_deep_research()";
          });
        }
        llmTask.thinkTasks.add(thinkTask);
      });
    } else if (data.type == "message_fold") {
      Map<String, dynamic> json = jsonDecode(data.message);
      String? title = json["title"]?.toString();
      llmTask.content.value = json["data"]?.toString() ?? "";
      if (llmTask.type() == StreamMessageTypeEnum.plan_schedule) {
        llmTask.agentName = "planner";
      }
      if (title != null) {
        RenderTask titleTask = RenderTask();
        titleTask.type.value = StreamMessageTypeEnum.tag;
        titleTask.content.value = title;
        context.workflow.renderList.add(titleTask);
      }
    }else if(llmTask.type == StreamMessageTypeEnum.visual){
      dynamic append = jsonDecode(data.appendix??"[]");
      if(append is List){
        llmTask.productImages =
            append.map((e) => ProductImageModel.fromJson(e)).toList();
      }
    } else {
      llmTask.content.value = data.message;
    }
    if (ChatWasteFilterUtils.isFilter(llmTask.content()) && llmTask.content().length <= 20) {
      return null;
    }
    if(data.appendix != null && data.appendix!.isNotEmpty){
      dynamic append = jsonDecode(data.appendix!);
      if(append is Map){
        Map appendMap = append;
        if(appendMap.containsKey("mode")){
          llmTask.appendixMode = appendMap["mode"]?.toString() ?? "";
        }
      }
    }
    context.workflow.renderList.add(llmTask);
    if (reporterTask.content != null && reporterTask.content.value.isNotEmpty) {
      context.workflow.renderList.add(reporterTask);
    }
    if (data!.appendix!=null&&data!.appendix!.isNotEmpty) {
      try {
        print('数据=${data.appendix!}');
        var appendixData=jsonDecode(data.appendix!);
        var searchUrlsList=appendixData['search_urls']['data'];
        RenderTask searchTask = RenderTask();
        searchTask.type.value = StreamMessageTypeEnum.search_urls;
        searchTask.isComplete.value = true;
        searchTask.content.value = jsonEncode(searchUrlsList);
        context.workflow.renderList.add(searchTask);
      } catch (e) {
        print('解析错误');
      }
    }
    int curIndex = context.workflow.renderList.length-1;
    return curIndex;
  }
}
