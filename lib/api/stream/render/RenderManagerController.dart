import 'package:get/get.dart';
import 'package:new_agnes/api/stream/render/HistoryHandler.dart';
import 'package:new_agnes/api/stream/render/StreamEventHandler.dart';
import 'package:new_agnes/api/stream/render/entity.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';

class RenderManagerController  {
  late String _conversationId;
  final List<StreamEventHandler> _handlers = [];

  void set conversationId(String id) {
    _conversationId = id;
    _context.workflow.conversationId = id;
  }

  RenderManagerController() {
    _initializeHandlers();
  }

  final RenderContext _context = RenderContext(
    workflow: Workflow(),
    messageBuffer: StringBuffer(),
    triageBuffer: StringBuffer(),
    jsonContentBuffer: StringBuffer(),
  );

  // 对外暴露的响应式状态
  Workflow get workflow => _context.workflow;
  Rx<Workflow> get workflowRx => _context.workflow.obs;

  bool get isTaskRunning => _context.isTaskRunning;

  void _initializeHandlers() {
    // 注册所有事件处理器
    _handlers.addAll([
      StartOfAgentHandler(),
      EndOfAgentHandler(),
      StartOfLlmHandler(),
      EndOfLlmHandler(),
      MessageFoldHandler(),
      ToolCallHandler(),
      ToolCallResultHandler(),
      ToolTagHandler(),
      SearchUrlsHandler(),
      MessageHandler(),
      HandoffHandler(),
      GoButtonHandler(),
      EndOfWorkflowHandler(),
      FinalSessionStateHandler(),
      HistoryHandler(),
      ErrorHandler(),
      UpgradeMembershipHandler(),
      OutputHtmlHandler(),
      OutputVisualHandler(),
      UsageCountHandler()
      // ... 其他处理器
    ]);
  }

  void dispatchRender(StreamResultEntity entity) {
    final handler = _handlers.firstWhere(
      (h) => h.canHandle(entity.event),
      orElse: () => DefaultHandler(), // 默认处理器
    );
    handler.addToLog(entity, _context);
    handler.handle(entity, _context);
    // todo 通知解析数据状态更新
  }
}
