// 事件处理器接口
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/stream/render/JsonChunkFinder.dart';
import 'package:new_agnes/api/stream/render/entity.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';
import 'package:new_agnes/utils/chat_waste_filter_utils.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';

import '../stream_message_type_enum.dart';
@Deprecated("2025-09-25:群聊下个版本会修改掉")
abstract class StreamEventHandler {
  bool canHandle(String eventType);

  void handle(StreamResultEntity entity, RenderContext context);

  void addToLog(StreamResultEntity entity, RenderContext context) {
    logInfo(entity.toString());
  }

  /**
   * 过滤未使用的模式数据
   */
  bool hasFilter(String? handOff,String? agentName) {
    if (handOff == null || isTrigae(agentName) || isCoordinator(agentName)) {
      return false;
    }
    if (handOff.isEmpty) {
      return false;
    }
    if (handOff == "handoff_to_deep_research()" ||
        handOff == "handoff_to_research()" ||
        handOff == "handoff_to_ai_slides()" ||
        handOff == "handoff_to_ai_design()" ||
        handOff == "handoff_to_chat()") {
      return false;
    }
    logError("未出现的模式-过滤-handOff = $handOff  agentName = $agentName");
    return true;
  }

  bool isDeepSearchHandOff(String? handOff) =>
      handOff == "handoff_to_deep_research()" ||
      handOff == "handoff_to_research()";

  bool isPlanner(String? agentName) => agentName == "planner";

  bool isResearcher(String? agentName) => agentName == "researcher";

  bool isTrigae(String? agentName) => agentName == "triage";

  bool isCoordinator(String? agentName) => agentName == "coordinator";

  bool isReporter(String? agentName) => agentName == "reporter";

  void addTaskAndRefresh(RxList<RenderTask> list, RenderTask? task) {
    if (task != null) {
      list.add(task);
      list.refresh();
    }
  }

  // 新增：通用数据验证方法
  Map<String, dynamic>? getValidData(StreamResultEntity entity) {
    var data = entity.dataJson();
    return data.isNotEmpty ? data : null;
  }

  // 新增：安全获取字符串值
  String? getStringValue(Map<String, dynamic> data, String key) {
    return data[key]?.toString();
  }

  // 新增：安全获取布尔值
  bool getBoolValue(Map<String, dynamic> data, String key,
      {bool defaultValue = false}) {
    return data[key] is bool ? data[key] : defaultValue;
  }
}

// 上下文对象，包含共享状态
class RenderContext {
  final Workflow workflow;
  RenderTask? currentTask;
  final StringBuffer messageBuffer;
  final StringBuffer triageBuffer;
  final StringBuffer jsonContentBuffer;
  bool insideJsonBlock;
  String? currentAgentName;
  String? currentAgentId;
  String? currentHandOff;
  bool isTaskRunning;
  final String waitJson;

  RenderContext({
    required this.workflow,
    this.currentTask,
    required this.messageBuffer,
    required this.triageBuffer,
    required this.jsonContentBuffer,
    this.insideJsonBlock = false,
    this.currentAgentName,
    this.currentAgentId,
    this.currentHandOff,
    this.isTaskRunning = false,
    this.waitJson = '',
  });

  // 新增：重置上下文状态
  void reset() {
    messageBuffer.clear();
    triageBuffer.clear();
    jsonContentBuffer.clear();
    insideJsonBlock = false;
    currentAgentName = null;
    currentAgentId = null;
  }
}

/**
 * 默认处理器
 */
class DefaultHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) {
    return false;
  }

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    // TODO: 无法解析的事件，开发阶段需要全部处理
    print("无法处理的事件：${entity.event}");
  }
}

class StartOfAgentHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "start_of_agent";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Start of agent handler");
    context.isTaskRunning = true;

    final data = getValidData(entity);
    if (data == null) return;

    context.currentAgentName = getStringValue(data, "agent_name");
    context.currentAgentId = getStringValue(data, "agent_id");
    context.messageBuffer.clear();
    context.jsonContentBuffer.clear();
    context.insideJsonBlock = false;

    if (context.currentAgentName == StreamMessageTypeEnum.reporter.name) {
      List<RenderTask> result = context.workflow.renderList;
      for (int i = result.length - 1; i >= 0; i--) {
        if (result[i].thinkTasks.isNotEmpty) {
          result[i]
              .thinkTasks
              .add(RenderTask()..type.value = StreamMessageTypeEnum.reporter);
          break;
        }
      }
    }
  }
}

class EndOfAgentHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "end_of_agent";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("End of agent handler");
    context.reset();
  }
}

class StartOfLlmHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "start_of_llm";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Start of llm handler");
  }
}

class EndOfLlmHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "end_of_llm";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("End of llm handler");
    if (isTrigae(context.currentAgentName)) {
      // 存在一种场景,triage的时候,ai分辨不了,会反问一些信息,这时候需要区分是否要显示
      // handoff_开头,并且存在()的,类似handoff_to_ai_slides(),说明是方法,ai意图明确,不需要显示,否则需要显示
      if (context.triageBuffer.toString().startsWith("handoff_") &&
          context.triageBuffer.toString().endsWith("()")) {
        context.currentHandOff = context.triageBuffer.toString();
        context.currentTask?.handOff = context.currentHandOff ?? "";
        context.triageBuffer.clear();
        return;
      } else {
        context.workflow.renderList.add(RenderTask()
          ..type.value = StreamMessageTypeEnum.message
          ..content.value = context.triageBuffer.toString());
      }
    }
  }
}

/**
 * final_session_state 处理器
 */
class FinalSessionStateHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "final_session_state";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Final session state Handler ${entity.model}");
    RenderTask? task =
        appendProductMessage(entity, context.workflow.productData);
    if (task != null) {
      context.currentTask?.pptCompletedText = true;
      context.workflow.renderList.add(task);
      context.currentTask = task;
    }
    context.isTaskRunning = false;
    if (context.workflow.renderList.isNotEmpty) {
      context.workflow.renderList.last.isComplete.value = true;
      if (context.workflow.renderList.last.type ==
              StreamMessageTypeEnum.message_go_button &&
          context.workflow.renderList.length > 2) {
        context.workflow.renderList[context.workflow.renderList.length - 2]
            .isComplete.value = true;
      }
      context.workflow.renderList.last.isComplete.value = true;
      context.workflow.renderList.last.isComplete.refresh();
    }
    context.currentTask = null;
    context.workflow.productData = null;
  }

  RenderTask? appendProductMessage(
      StreamResultEntity entity, ProductModel? task) {
    if (task == null) return null;
    ProductModel productModel = task;
    if (productModel.type == "ai_slides_pages") {
      Map<dynamic, dynamic> jsonData = jsonDecode(entity.data);
      String conversationId = jsonData["conversation_id"] ?? "";
      if (jsonData.containsKey("messages")) {
        dynamic messageData = jsonData["messages"];
        if (messageData! is List && messageData.isEmpty) {
          return null;
        }
        Map<dynamic, dynamic> role = messageData[0];
        String pptTitle = role.containsKey("content") ? role["content"] : "";
        RenderTask task = RenderTask()
          ..type.value = StreamMessageTypeEnum.ppt
          ..conversationId = conversationId
          ..content.value = pptTitle;
        return task;
      }
    } else if (productModel.type == "design_image_shots" ||
        productModel.type == "design_video_seconds") {
      String jsonData = jsonEncode(productModel.products ?? []);
      List result = jsonDecode(jsonData);
      List<ProductImageModel> images = [];
      try {
        for (var r in result) {
          images.add(ProductImageModel.fromJson(jsonDecode(r)));
        }
      } catch (e) {
        logError("${productModel.type}=======" + e.toString());
      }
      RenderTask task = RenderTask()
        ..type.value = StreamMessageTypeEnum.visual
        ..productImages = images
        ..content.value = "";
      return task;
    }
    return null;
  }
}

/**
 * handoff 处理器
 */
class HandoffHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "handoff";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Handoff Handler");

    final data = getValidData(entity);
    if (data == null) return;

    final handoffMessage = getStringValue(data, "message");
    if (handoffMessage != null) {
      context.currentHandOff = handoffMessage;
      context.currentTask?.handOff = handoffMessage;
      context.workflow.handOff = handoffMessage;
    }
  }
}

/**
 * go_button 处理器
 */
class GoButtonHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "go_button";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Go button Handler");

    final data = getValidData(entity);
    if (data == null) return;

    // 目前直接创建Go任务，如果需要根据display_go判断，可以取消注释下面的代码
    // final buttonData = data["message"];
    // if (buttonData != null && buttonData["display_go"] == true) {
    _createGoTask("Go", context);
    // }
  }

  void _createGoTask(String goContent, RenderContext context) {
    // 创建JSON RenderTask
    RenderTask jsonTask = RenderTask();
    jsonTask.agentName = context.currentAgentName ?? "";
    jsonTask.content.value = goContent;
    jsonTask.type.value = StreamMessageTypeEnum.message_go_button;
    jsonTask.handOff = context.currentHandOff ?? "";
    // 添加到工作流
    addTaskAndRefresh(context.workflow.renderList, jsonTask);
  }
}

/**
 * message 处理器
 */
class MessageHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "message";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Message Handler");
    if(hasFilter(context.currentHandOff,context.currentAgentName)){
      return;
    }
    try {
      final data = getValidData(entity);
      if (data == null) return;

      final delta = data["delta"];
      final messageId = getStringValue(data, "message_id");

      if (delta != null && delta["content"] != null) {
        final content = delta["content"];
        if (isTrigae(context.currentAgentName)) {
          context.triageBuffer.write(content);
        } else {
          context.messageBuffer.write(content);

          if (isCoordinator(context.currentAgentName)) {
            // coordinator 需要判断字符长度20
            if (context.messageBuffer.isEmpty ||
                context.messageBuffer.length <= 20) {
              return;
            }
          }

          _ensureCurrentTask(messageId, context);
          context.currentTask?.content.value = context.messageBuffer.toString();

          _addTaskToAppropriateList(context, content);
        }
      }
    } catch (e) {
      debugPrint("MessageHandler error: $e");
      debugPrint("_handleMessage entity: ${entity}");
    }
  }

  void _ensureCurrentTask(String? messageId, RenderContext context) {
    if (context.currentTask?.messageId != messageId) {
      final messageTask = RenderTask()
        ..messageId = messageId ?? ""
        ..agentName = context.currentAgentName ?? ""
        ..handOff = context.currentHandOff ?? "";
      addTaskThinking(messageTask, context);
      messageTask.type.value = getMessageType(messageTask, context);
      context.currentTask = messageTask;
    }
  }

  void _addTaskToAppropriateList(RenderContext context, String content) {
    final handOff = context.workflow.handOff;
    final agentName = context.currentAgentName;
    RxList<RenderTask>? targetList;
    if (isDeepSearchHandOff(handOff) &&
        (isPlanner(agentName) || isResearcher(agentName))) {
      final resultList = context.workflow.renderList;
      for (int i = context.workflow.renderList.length - 1; i >= 0; i--) {
        if (resultList[i].type == StreamMessageTypeEnum.message_thinking) {
          resultList[i].content.value += content;
          targetList = resultList[i].thinkTasks;
          ;
          break;
        }
      }
    } else {
      // 存在一种场景,triage的时候,ai分辨不了,会反问一些信息,这时候需要区分是否要显示
      // handoff_开头,并且存在()的,类似handoff_to_ai_slides(),说明是方法,ai意图明确,不需要显示,否则需要显示
      targetList = context.workflow.renderList;
    }

    if (targetList != null && context.currentTask != null) {
      if (!targetList.contains(context.currentTask)) {
        addTaskAndRefresh(targetList, context.currentTask!);
      } else {
        targetList.refresh();
      }
    }
  }

  void addTaskThinking(RenderTask messageTask, RenderContext context) {
    if (!messageTask.handoffToDeepResearch) return;
    //如果消息的agentName是planner或者research的话
    // 最后一条消息不是thinking，应该要把类型改为message_think
    List<RenderTask> taskList = context.workflow.renderList();
    RenderTask? lastTask;
    if (taskList.isNotEmpty) {
      lastTask = taskList.last;
    }
    bool lastNotThinking =
        (lastTask?.type() != StreamMessageTypeEnum.message_thinking);
    bool plannerOrResearch =
        isPlanner(messageTask.agentName) || isResearcher(messageTask.agentName);
    if (plannerOrResearch && lastNotThinking) {
      RenderTask renderTask = new RenderTask()
        ..type.value = StreamMessageTypeEnum.message_thinking;
      renderTask.isHistory = false;
      context.workflow.renderList.add(renderTask);
    }
  }

  StreamMessageTypeEnum getMessageType(
      RenderTask messageTask, RenderContext context) {
    if (messageTask.handoffToAiSlides) {
      if (messageTask.agentName == "planner") {
        return StreamMessageTypeEnum.plan_schedule;
      } else {
        return StreamMessageTypeEnum.slide_design;
      }
    } else if (messageTask.handoffToDeepResearch) {
      return StreamMessageTypeEnum.message;
    } else if (messageTask.handoffToAiDesign) {
      if (messageTask.agentName == "planner") {
        return StreamMessageTypeEnum.plan_schedule;
      }
      return StreamMessageTypeEnum.values.firstWhere(
          (e) => e.name == messageTask.agentName,
          orElse: () => StreamMessageTypeEnum.message);
    }
    return StreamMessageTypeEnum.message;
  }
}

class ToolTagHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "tool_tag";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    final data = getValidData(entity);
    if (data == null) return;

    final content = getStringValue(data, "message");
    final type = getStringValue(data, "type");

    if (type == "fold_end") {
      return;
    }

    if (content != null) {
      final tagTask = RenderTask()
        ..agentName = context.currentAgentName ?? ""
        ..content.value = content
        ..type.value = StreamMessageTypeEnum.tag;

      addTaskAndRefresh(context.workflow.renderList, tagTask);
    }
  }
}

class SearchUrlsHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "search_urls";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    final data = getValidData(entity);
    if (data == null) return;

    final content = data["data"];
    if (content != null) {
      String value = jsonEncode(content);
      final task = RenderTask()
        ..type.value = StreamMessageTypeEnum.search_urls
        ..content.value = value;

      addTaskAndRefresh(context.workflow.renderList, task);
    }
  }
}

class MessageFoldHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "message_fold";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    if(hasFilter(context.currentHandOff,context.currentAgentName)){
      return;
    }
    final data = getValidData(entity);
    if (data == null) return;

    final content = getStringValue(data, "data");
    final type = getStringValue(data, "type");
    final title = getStringValue(data, "title");

    if (title != null) {
      final usingTool = RenderTask()
        ..agentName = context.currentAgentName ?? ""
        ..content.value = title
        ..type.value = StreamMessageTypeEnum.tag
        ..handOff = context.workflow.handOff;

      context.workflow.renderList.add(usingTool);
    }

    if (content != null && type != null) {
      StreamMessageTypeEnum typeEnum = StreamMessageTypeEnum.values.firstWhere(
          (element) => element.name == type,
          orElse: () => StreamMessageTypeEnum.message);
      final contentTask = RenderTask()
        ..agentName = context.currentAgentName ?? ""
        ..content.value = content
        ..type.value = typeEnum
        ..handOff = context.workflow.handOff
        ..fold.value = true;

      context.workflow.renderList.add(contentTask);
    }

    context.workflow.renderList.refresh();
  }
}

class ToolCallHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "tool_call";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    final data = getValidData(entity);
    if (data == null) return;

    final toolCall = ToolCall.fromJson(data);
    if (toolCall.tool_name != "crawl_tool") {
      context.currentTask?.toolCalls ??= [];
      context.currentTask?.toolCalls?.add(toolCall);
    }
  }
}

class ToolCallResultHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "tool_call_result";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    final data = getValidData(entity);
    if (data == null) return;

    final toolResult = getStringValue(data, "tool_result");
    final toolCallId = getStringValue(data, "tool_call_id");

    if (toolCallId != null && context.currentTask?.toolCalls != null) {
      for (final toolCall in context.currentTask!.toolCalls!) {
        if (toolCall.tool_call_id == toolCallId) {
          toolCall.tool_result = toolResult ?? "";
          final json = jsonEncode(toolCall.toJson());
          context.currentTask?.content.value += "\n```json\n$json\n```";
          debugPrint("ToolCallResultHandler result = $json");
          break;
        }
      }
    }
  }
}

class EndOfWorkflowHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "end_of_workflow";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {}
}

class ErrorHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "error";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    if (isDeepSearchHandOff(context.currentHandOff)) {
      List<RenderTask> result = context.workflow.renderList;
      for (int i = result.length - 1; i >= 0; i--) {
        if (result[i].type == StreamMessageTypeEnum.message_thinking) {
          result[i]
              .thinkTasks
              .add(RenderTask()..type.value = StreamMessageTypeEnum.reporter);
          break;
        }
      }
    }
    String message = entity.dataJson()["message"] ?? "Error";
    context.workflow.renderList.add(RenderTask()
      ..agentName = context.currentAgentName ?? ""
      ..type.value = StreamMessageTypeEnum.error
      ..handOff = context.workflow.handOff
      ..content.value = message);
  }
}

class UpgradeMembershipHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "upgrade_membership";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    //最近的一个添加reporter
    if (isDeepSearchHandOff(context.currentHandOff)) {
      List<RenderTask> result = context.workflow.renderList;
      for (int i = result.length - 1; i >= 0; i--) {
        if (result[i].type == StreamMessageTypeEnum.message_thinking) {
          result[i]
              .thinkTasks
              .add(RenderTask()..type.value = StreamMessageTypeEnum.reporter);
          break;
        }
      }
    }
    String message = entity.dataJson()["text"] ?? "Error";
    RenderTask updateTask = RenderTask()
      ..agentName = context.currentAgentName ?? ""
      ..type.value = StreamMessageTypeEnum.error
      ..handOff = context.workflow.handOff
      ..content.value = message;
    context.currentTask = updateTask;
    context.workflow.renderList.add(updateTask);
  }
}

class OutputVisualHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "output_visual";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    context.workflow.productData ??= ProductModel("", "", []);
    context.workflow.productData?.addProduct(entity.data);
    //添加design 预加载
    String jsonData = jsonEncode(context.workflow.productData!.products??[]);
    List result = jsonDecode(jsonData);
    List<ProductImageModel> images = [];
    try {
      for (var r in result) {
        images.add(ProductImageModel.fromJson(jsonDecode(r)));
      }
    } catch (e) {

    }
    if (context.workflow.renderList.isNotEmpty) {
      RenderTask lastTask = context.workflow.renderList.last;
      if (lastTask.type() == StreamMessageTypeEnum.design_cache) {
        lastTask.productImages = images;
      } else {
        RenderTask task = RenderTask()
          ..type.value = StreamMessageTypeEnum.design_cache
          ..productImages = images
          ..content.value = "";
        context.workflow.renderList.add(task);
      }
      context.workflow.renderList.refresh();
    }
  }
}

class OutputHtmlHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "output_html";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    context.workflow.productData ??= ProductModel("", "", []);
    context.workflow.productData?.addProduct(entity.data);
  }
}

class UsageCountHandler extends StreamEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "usage_count";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    Map<String, dynamic> data = entity.dataJson();
    if (data.containsKey("kind")) {
      String kind = data["kind"].toString();
      context.workflow.productData ??= ProductModel("", "", []);
      context.workflow.productData?.type = kind;
    }
    int index = -1;
    for(int i = context.workflow.renderList.length - 1 ; i >= 0; i--){
      RenderTask itemTask = context.workflow.renderList[i];
      if(itemTask.type() == StreamMessageTypeEnum.design_cache){
        index = i;
        break;
      }
    }
    if(index != -1){
      context.workflow.renderList.removeAt(index);
    }
  }
}
