import 'package:flutter/material.dart';

class JsonChunkFinder {
  static const String START_MARKER = '```json';
  static const String END_MARKER = '```';
  final List<int> _buffer = [];
  bool _startWillFound = false;
  bool _startFound = false;
  bool _endFound = false;
  int _startMarkerIndex = -1;
  int _jsonStartIndex = -1;
  int _endMarkerIndex = -1;
  int _startMarkerPartialMatch = 0; // 记录开始标记部分匹配的长度

  /// 处理输入的数据块，返回完整的JSON块（如果找到）
  String? processChunk(List<int> chunk) {
    // 将新数据添加到缓冲区
    _buffer.addAll(chunk);
    // 如果还没找到开始标记，则查找开始标记
    if (!_startFound) {
      _findStartMarker();
    }

    // 如果已找到开始标记但还没找到结束标记，则查找结束标记
    if (_startFound && !_endFound) {
      _findEndMarker();
    }

    // 如果两个标记都找到了，提取JSON内容
    if (_startFound && _endFound) {
      return _extractJson();
    }

    return null;
  }

  /// 查找开始标记 "```json"
  void _findStartMarker() {
    if (_buffer.length < START_MARKER.length) {
      _checkStartMarkerPartialMatch();
      return;
    }

    // 在缓冲区中查找开始标记
    for (int i = 0; i <= _buffer.length - START_MARKER.length; i++) {
      bool match = true;
      for (int j = 0; j < START_MARKER.length; j++) {
        if (_buffer[i + j] != START_MARKER.codeUnitAt(j)) {
          match = false;
          break;
        }
      }

      if (match) {
        _startFound = true;
        _startMarkerIndex = i;
        _jsonStartIndex = i + START_MARKER.length;
        _startMarkerPartialMatch = 0; // 找到完整匹配，清零部分匹配
        break;
      }
    }
    // 没找到完整匹配，检查是否有部分匹配
    _checkStartMarkerPartialMatch();
  }

  /// 查找结束标记 "```"
  void _findEndMarker() {
    if (_jsonStartIndex < 0 ||
        _buffer.length < _jsonStartIndex + END_MARKER.length) {
      return;
    }
    // 从JSON开始位置查找结束标记
    for (int i = _jsonStartIndex;
        i <= _buffer.length - END_MARKER.length;
        i++) {
      bool match = true;
      for (int j = 0; j < END_MARKER.length; j++) {
        if (_buffer[i + j] != END_MARKER.codeUnitAt(j)) {
          match = false;
          break;
        }
      }

      if (match) {
        _endFound = true;
        _endMarkerIndex = i;
        break;
      }
    }
  }

/// 检查开始标记的部分匹配
void _checkStartMarkerPartialMatch() {
  _startMarkerPartialMatch = 0;

  // 最多检查START_MARKER.length - 1个字符（因为完整匹配会在_findStartMarker中处理）
  int maxCheckLength = START_MARKER.length - 1;
  // 缓冲区中实际可检查的长度
  int bufferCheckLength = _buffer.length < maxCheckLength ? _buffer.length : maxCheckLength;

  // 从最长的可能匹配开始检查
  for (int len = bufferCheckLength; len >= 1; len--) {
    bool match = true;
    // 检查缓冲区末尾len个字符是否匹配开始标记的前len个字符
    for (int i = 0; i < len; i++) {
      int bufferPos = _buffer.length - len + i;
      if (_buffer[bufferPos] != START_MARKER.codeUnitAt(i)) {
        match = false;
        break;
      }
    }

    // debugPrint('ChatLogic _startMarkerPartialMatch len:$len match:$match');
    if (match) {
      _startMarkerPartialMatch = len;
      return;
    }
  }
}

  /// 提取完整的JSON内容
  String? _extractJson() {
    if (_jsonStartIndex >= 0 && _endMarkerIndex > _jsonStartIndex) {
      // 提取JSON内容（不包括标记）
      List<int> jsonBytes = _buffer.sublist(_jsonStartIndex, _endMarkerIndex);
      String jsonContent = String.fromCharCodes(jsonBytes);
      // 清理状态，准备下一次查找
      _reset();

      return jsonContent.trim();
    }
    return null;
  }

  /// 重置状态
  void _reset() {
    _buffer.clear();
    _startFound = false;
    _endFound = false;
    _startMarkerIndex = -1;
    _jsonStartIndex = -1;
    _endMarkerIndex = -1;
  }

  /// 获取当前缓冲区大小
  int get bufferSize => _buffer.length;

  /// 检查是否正在查找开始标记
  bool get isLookingForStart => !_startFound;

  /// 检查是否正在查找结束标记
  bool get isLookingForEnd => _startFound && !_endFound;

  /// 获取开始标记部分匹配的长度
  int get startMarkerPartialMatch => _startMarkerPartialMatch;

  /// 检查是否存在开始标记的部分匹配
  bool get hasStartMarkerPartialMatch => _startMarkerPartialMatch > 0;
}
