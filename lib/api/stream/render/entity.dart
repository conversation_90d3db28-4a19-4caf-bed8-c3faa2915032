import 'package:get/get.dart';
import 'package:new_agnes/data/caseAndQuestion.dart';

import '../stream_message_type_enum.dart';

class RenderTask {
  var conversationId = "";
  var messageId = "";
  var handOff = "";
  var agentName = "";
  var type = StreamMessageTypeEnum.message.obs;// llm:{tag, go_button,reseacher},user:{text,image,file}
  Rx<String> sender = "llm".obs;// user
  // 增量监听
  Rx<String> content = "".obs;
  // 动态添加折叠
  var fold = false.obs;
  var isComplete = false.obs;
  //ppt最后一行结束语没有边框样式，服务器返回的数据没有任何标识，只能用一个字段本地判断
  bool pptCompletedText = false;

  List<ToolCall>? toolCalls;
  RxList<RenderTask> thinkTasks = <RenderTask>[].obs;
  bool isHistory = true;
  List<ProductImageModel>? productImages;
  String _appendixMode = "";
  List<dynamic>? docs;

  set appendixMode(String value) {
    _appendixMode = value;
  }

  String get appendixMode {
    if(_appendixMode.isNotEmpty){
      return _appendixMode;
    }
    if(handoffToAiSlides){
      return SearchType.aiSlides.param;
    }
    if(handoffToDeepResearch){
      return SearchType.deepResearch.param;
    }
    if(handoffToAiDesign){
      return SearchType.aiDesign.param;
    }
    return SearchType.tools.param;
  }

  bool get handoffToAiSlides => handOff == "handoff_to_ai_slides()";

  bool get handoffToDeepResearch => handOff == "handoff_to_deep_research()" || handOff == "handoff_to_research()";

  bool get handoffToAiDesign => handOff == "handoff_to_ai_design()";


}

class Workflow {
  var conversationId = "";
  var handOff = "";
  RxList<RenderTask> renderList = <RenderTask>[].obs;
  ProductModel? productData;//生成物数据
}

//todo 是否需要保留整个页面的对话，交互过程？
class OnceChat {

}

class ToolCall{
  final String? tool_call_id;
  final String? tool_name;
  final ToolCallQuery? tool_input;
  String tool_result = "";

  ToolCall(this.tool_call_id, this.tool_name, this.tool_input);

  factory ToolCall.fromJson(Map<String, dynamic> json) {
    return ToolCall(
      json['tool_call_id'],
      json['tool_name'],
      ToolCallQuery.fromJson(json['tool_input'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tool_call_id': tool_call_id,
      'tool_name': tool_name,
      'tool_input': tool_input?.toJson(),
      'tool_result': tool_result,
    };
  }
}

class ToolCallQuery{
  final String? query;

  ToolCallQuery(this.query);
  factory ToolCallQuery.fromJson(Map<String, dynamic> json) {
    return ToolCallQuery(
      json['query'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'query': query,
    };
  }
}

class ProductModel{
  String? type;
  String? title;
  final List<String>? products;

  ProductModel(this.type, this.title, this.products);

  void addProduct(String product) {
    products?.add(product);
  }
}

class ProductImageModel{
  /**
   *
      {
      "url" : "https://s3.ap-southeast-1.amazonaws.com/agnes-test-sg/roles_output/google_nano_banana/58b8d1bd-c517-4a83-950e-0e834d62f7f9-20250902183849_1.png",
      "media_info" : {
      "width" : 1024,
      "height" : 1024
      },
      "thumbnail" : "https://s3.ap-southeast-1.amazonaws.com/agnes-test-sg/roles_output/google_nano_banana/58b8d1bd-c517-4a83-950e-0e834d62f7f9-20250902183849_1.webp",
      "id" : "6684cbe3-0b46-4306-bfda-8d5a699d940e",
      "type" : "image",
      "title" : "南京紫金山全景",
      "version" : 1,
      "index" : 1
      }
   */

  final String? url;
  final Map<String, dynamic>? media_info;
  final String? thumbnail;
  final String? id;
  final String? type;
  final String? title;
  final int? version;
  final int? index;
  ProductImageModel(this.url, this.media_info, this.thumbnail, this.id, this.type, this.title, this.version, this.index);

  factory ProductImageModel.fromJson(Map<String, dynamic> json) {
    return ProductImageModel(
      json['url'],
      json['media_info'],
      json['thumbnail'],
      json['id'],
      json['vtype'] == null ? json['type'] : json['vtype'],
      json['title'],
      json['version'],
      json['index'],
    );
  }
}
