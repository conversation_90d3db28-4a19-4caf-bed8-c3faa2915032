import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/ApiProvider.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';

import '../Api.dart';
import '../StorageService.dart';

class DioStream {
  static final DioStream _instance = DioStream._internal();

  // 公开访问点
  static DioStream get instance => _instance;

  // Dio 实例
  late final Dio dio;

  // 私有构造函数
  DioStream._internal() {
    // 基础配置
    dio = Dio(BaseOptions(
      baseUrl: Api.baseUrl,
      headers: {
        'User-Agent': 'Agnes',
        'X-User-Language': '${ApiProvider.getLanguageCode()}'
      },
      connectTimeout: const Duration(seconds: 150),
      receiveTimeout: const Duration(seconds: 600),
      sendTimeout: const Duration(seconds: 10),
      responseType: ResponseType.stream,
    ));
    // 统一错误处理拦截器
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        bool isRegistered = Get.isRegistered<StorageService>();
        if (isRegistered) {
          String token =
              Get.find<StorageService>().getLoginData().accessToken ?? "";
          if (token.isNotEmpty) {
            options.headers["Authorization"] = "Bearer $token";
          }
        }
        return handler.next(options);
      },
      onError: (error, handler) {
        final errorMessage = _parseDioError(error);
        logError(errorMessage);
        return handler.next(error);
      },
    ));
  }

  // 错误解析
  String _parseDioError(DioException error) {
    // todo 国际化
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'connectionTimeout';
      case DioExceptionType.sendTimeout:
        return 'sendTimeout';
      case DioExceptionType.receiveTimeout:
        return 'receiveTimeout';
      case DioExceptionType.badCertificate:
        return 'badCertificate';
      case DioExceptionType.badResponse:
        return 'badResponse: ${error.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'cancel';
      case DioExceptionType.connectionError:
        return 'connectionError';
      case DioExceptionType.unknown:
        return 'unknown: ${error.message}';
    }
  }
}
