enum StreamHandoffEnum{
  handoff_to_deep_research,
  handoff_to_research,
  handoff_to_ai_slides,
  handoff_to_ai_design,
  handoff_to_chat,
  unknown
}

extension StreamHandoffEnumExtension on StreamHandoffEnum {
  String get value {
    switch (this) {
      case StreamHandoffEnum.handoff_to_deep_research:
        return "handoff_to_deep_research()";
      case StreamHandoffEnum.handoff_to_research:
        return "handoff_to_research()";
      case StreamHandoffEnum.handoff_to_ai_slides:
        return "handoff_to_ai_slides()";
      case StreamHandoffEnum.handoff_to_ai_design:
        return "handoff_to_ai_design()";
      case StreamHandoffEnum.handoff_to_chat:
        return "handoff_to_chat()";
      case StreamHandoffEnum.unknown:
        return "unknown";
    }
  }
}