import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/StorageService.dart';
import 'package:new_agnes/api/stream/stream_ai_enum.dart';
import 'package:new_agnes/api/stream/stream_result_analysis.dart';

import 'dio_stream.dart';
import 'stream_result_entity.dart';

class StreamRequest {
  final CancelToken cancelToken = CancelToken();

  /**
   *
      Authorization
      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkZTBjODY4OS04ZjgwLTQxYmMtOTUyNi0yZTczMmQ3NGNiY2QiLCJleHAiOjE3NTYzNjcxNjN9.herbdcAR6ENFtNFHlkrxdGgdlmh6vpp121rVRUmG6SQ
   */

  /**
   * {"messages":[{"id":"z-KXzkWgGdxVVEOCj2Xdr","role":"user","type":"text","content":"南京天气"}],"docs":[],"conversation_id":"1753948079664926","mode":1,"is_doc_changed":false,"is_cooperation":false,"cooperation_comment_ids":[],"debug":false}
   */
  final String? id;
  final Map<String, dynamic>? body;
  final String url;
  final List<int> _utf8Buffer = [];
  final Map<String,StreamStatus> status;

  StreamRequest({required this.url, this.body,this.id,this.status = const {}});

  void cancelRequest() {
    cancelToken.cancel();
  }

  void sendRequest(
      {ValueChanged<StreamResultEntity>? onSuccess,
      ValueChanged<String>? onError,
      VoidCallback? onDone,required String model}) async {
    StreamResultAnalysis.instance.onInit();
    try{
      final response = await DioStream.instance.dio.post(url,
          data: body,
          cancelToken: cancelToken);
      if (response.statusCode == 200 &&
          response.data != null &&
          response.data.stream is Stream) {
        Stream stream = response.data.stream;
        stream.listen((data) {
          saveRequestStatus(StreamStatus.waiting);
          _utf8Buffer.addAll(data as List<int>);

          // 尝试解码缓冲区中的完整数据
          try {
            String result = utf8.decode(_utf8Buffer);

            _utf8Buffer.clear();

            StreamResultAnalysis.instance.onAnalysisData(result,
                onResult: (StreamResultEntity resultEntity) {
                  onSuccess?.call(resultEntity);
                },model:  model);
          } catch (e) {
            // 如果解码失败，保留缓冲区数据，等待更多数据到达
            // 不清空缓冲区，下次数据到达时一起解码
          }

          // String result = utf8.decode(data as List<int>, allowMalformed: true);
          // StreamResultAnalysis.instance.onAnalysisData(result,
          //     onResult: (StreamResultEntity resultEntity) {
          //   onSuccess?.call(resultEntity);
          // });
        }, onError: (error, stack) {
          saveRequestStatus(StreamStatus.error);
          StreamResultAnalysis.instance.onError();
          onError?.call(error.toString());
        }, onDone: () {
          // 处理剩余的缓冲区数据
          if (_utf8Buffer.isNotEmpty) {
            try {
              String result = utf8.decode(_utf8Buffer);
              StreamResultAnalysis.instance.onAnalysisData(result,
                  onResult: (StreamResultEntity resultEntity) {
                    onSuccess?.call(resultEntity);
                  },model:  model);
            } catch (e) {
              onError?.call('数据解析失败');
            }
            _utf8Buffer.clear();
          }
          saveRequestStatus(StreamStatus.done);
          StreamResultAnalysis.instance.onDone();
          onDone?.call();
        });
      } else {
        //todo 国际化
        saveRequestStatus(StreamStatus.error);
        onError?.call(response.statusMessage ?? "Retry");
        StreamResultAnalysis.instance.onError();
      }
    }catch(e){
      saveRequestStatus(StreamStatus.error);
      onError?.call(e.toString());
    }
  }

  void saveRequestStatus(StreamStatus curStatus){
    String? conversation_id = body?["conversation_id"];
    if(conversation_id != null && conversation_id.isNotEmpty){
      if(status.containsKey(conversation_id) && status[conversation_id] == StreamStatus.error){
        //如果有报错状态，先不赋值
      }else {
        status[conversation_id] = curStatus;
      }
    }
  }
}
