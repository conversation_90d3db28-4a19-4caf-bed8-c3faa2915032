import 'package:flutter/material.dart';
import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_message_type_enum.dart';
import '../stream_result_entity.dart';

class GoButtonEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "go_button";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Go button Handler");

    final data = getValidData(entity);
    if (data == null) return;

    // 目前直接创建Go任务，如果需要根据display_go判断，可以取消注释下面的代码
    // final buttonData = data["message"];
    // if (buttonData != null && buttonData["display_go"] == true) {
    _createGoTask("Go", context);
    // }
  }

  void _createGoTask(String goContent, RenderContext context) {
    // 创建JSON RenderTask
    RenderTask jsonTask = RenderTask();
    jsonTask.agentName = context.currentAgentName ?? "";
    jsonTask.content.value = goContent;
    jsonTask.type.value = StreamMessageTypeEnum.message_go_button;
    jsonTask.handOff = context.currentHandOff ?? "";
    // 添加到工作流
    addTaskAndRefresh(context.workflow.renderList, jsonTask);
  }
}