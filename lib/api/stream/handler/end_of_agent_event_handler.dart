import 'package:flutter/material.dart';
import 'package:new_agnes/api/stream/handler/base_event_handler.dart';
import 'package:new_agnes/api/stream/render/StreamEventHandler.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';

class EndOfAgentEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "end_of_agent";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("End of agent handler");
    context.reset();
  }
}