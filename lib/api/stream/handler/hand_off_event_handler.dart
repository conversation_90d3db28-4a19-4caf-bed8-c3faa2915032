import 'package:flutter/material.dart';
import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../stream_result_entity.dart';

class HandoffEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "handoff";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Handoff Handler");

    final data = getValidData(entity);
    if (data == null) return;

    final handoffMessage = getStringValue(data, "message");
    if (handoffMessage != null) {
      context.currentHandOff = handoffMessage;
      context.currentTask?.handOff = handoffMessage;
      context.workflow.handOff = handoffMessage;
    }
  }
}