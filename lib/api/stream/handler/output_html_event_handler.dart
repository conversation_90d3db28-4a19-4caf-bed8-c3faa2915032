import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_result_entity.dart';

class OutputHtmlEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "output_html";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    context.workflow.productData ??= ProductModel("", "", []);
    context.workflow.productData?.addProduct(entity.data);
  }
}