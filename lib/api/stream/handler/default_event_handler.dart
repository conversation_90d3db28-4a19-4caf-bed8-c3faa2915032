import 'package:new_agnes/api/stream/handler/base_event_handler.dart';
import 'package:new_agnes/api/stream/render/StreamEventHandler.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';

class DefaultEventHandler extends BaseEventHandler {
  @override
  bool canHandle(String eventType) {
    logWarning("${this.runtimeType.toString()}:无法处理的事件：$eventType");
    return false;
  }

  @override
  void handle(StreamResultEntity entity, RenderContext context) {

  }

}