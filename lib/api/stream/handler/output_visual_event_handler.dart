import 'dart:convert';

import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_message_type_enum.dart';
import '../stream_result_entity.dart';

class OutputVisualEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "output_visual";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    context.workflow.productData ??= ProductModel("", "", []);
    context.workflow.productData?.addProduct(entity.data);
    //添加design 预加载
    String jsonData = jsonEncode(context.workflow.productData!.products??[]);
    List result = jsonDecode(jsonData);
    List<ProductImageModel> images = [];
    try {
      for (var r in result) {
        images.add(ProductImageModel.fromJson(jsonDecode(r)));
      }
    } catch (e) {

    }
    if (context.workflow.renderList.isNotEmpty) {
      RenderTask lastTask = context.workflow.renderList.last;
      if (lastTask.type() == StreamMessageTypeEnum.design_cache) {
        lastTask.productImages = images;
      } else {
        RenderTask task = RenderTask()
          ..type.value = StreamMessageTypeEnum.design_cache
          ..productImages = images
          ..content.value = "";
        context.workflow.renderList.add(task);
      }
      context.workflow.renderList.refresh();
    }
  }
}