import 'package:new_agnes/api/stream/handler/base_event_handler.dart';
import 'package:new_agnes/api/stream/render/StreamEventHandler.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';

import '../render/entity.dart';
import '../stream_message_type_enum.dart';

class MessageFoldEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "message_fold";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    if(hasFilter(context.currentHandOff,context.currentAgentName)){
      return;
    }
    final data = getValidData(entity);
    if (data == null) return;

    final content = getStringValue(data, "data");
    final type = getStringValue(data, "type");
    final title = getStringValue(data, "title");

    if (title != null) {
      final usingTool = RenderTask()
        ..agentName = context.currentAgentName ?? ""
        ..content.value = title
        ..type.value = StreamMessageTypeEnum.tag
        ..handOff = context.workflow.handOff;

      context.workflow.renderList.add(usingTool);
    }

    if (content != null && type != null) {
      StreamMessageTypeEnum typeEnum = StreamMessageTypeEnum.values.firstWhere(
              (element) => element.name == type,
          orElse: () => StreamMessageTypeEnum.message);
      final contentTask = RenderTask()
        ..agentName = context.currentAgentName ?? ""
        ..content.value = content
        ..type.value = typeEnum
        ..handOff = context.workflow.handOff
        ..fold.value = true;

      context.workflow.renderList.add(contentTask);
    }

    context.workflow.renderList.refresh();
  }

}