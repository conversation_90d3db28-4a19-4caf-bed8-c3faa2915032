import 'package:get/get.dart';
import 'package:new_agnes/api/stream/enum/stream_handoff_enum.dart';

import '../../../utils/inAppLogUtil.dart';
import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_result_entity.dart';

abstract class BaseEventHandler{
  bool canHandle(String eventType);

  void handle(StreamResultEntity entity, RenderContext context);

  void addToLog(StreamResultEntity entity, RenderContext context) {

  }

  bool hasFilter(String? handOff,String? agentName) {
    if (handOff == null || isTrigae(agentName) || isCoordinator(agentName)) {
      return false;
    }
    if (handOff.isEmpty) {
      return false;
    }
    StreamHandoffEnum handOffEnum = StreamHandoffEnum.values.firstWhere(
        (e) => e.value == handOff,
        orElse: () => StreamHandoffEnum.unknown);
    if(handOffEnum == StreamHandoffEnum.unknown){
      logError("未出现的模式-过滤-handOff = $handOff  agentName = $agentName");
      return true;
    }
    return false;
  }

  bool isDeepSearchHandOff(String? handOff) =>
      handOff == "handoff_to_deep_research()" ||
          handOff == "handoff_to_research()";

  bool isPlanner(String? agentName) => agentName == "planner";

  bool isResearcher(String? agentName) => agentName == "researcher";

  bool isTrigae(String? agentName) => agentName == "triage";

  bool isCoordinator(String? agentName) => agentName == "coordinator";

  bool isReporter(String? agentName) => agentName == "reporter";

  void addTaskAndRefresh(RxList<RenderTask> list, RenderTask? task) {
    if (task != null) {
      list.add(task);
      list.refresh();
    }
  }

  // 新增：通用数据验证方法
  Map<String, dynamic>? getValidData(StreamResultEntity entity) {
    var data = entity.dataJson();
    return data.isNotEmpty ? data : null;
  }

  // 新增：安全获取字符串值
  String? getStringValue(Map<String, dynamic> data, String key) {
    return data[key]?.toString();
  }

  // 新增：安全获取布尔值
  bool getBoolValue(Map<String, dynamic> data, String key,
      {bool defaultValue = false}) {
    return data[key] is bool ? data[key] : defaultValue;
  }
}