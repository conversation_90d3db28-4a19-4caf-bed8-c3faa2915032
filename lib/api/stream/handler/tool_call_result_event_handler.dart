import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../stream_result_entity.dart';

class ToolCallResultEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "tool_call_result";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    final data = getValidData(entity);
    if (data == null) return;

    final toolResult = getStringValue(data, "tool_result");
    final toolCallId = getStringValue(data, "tool_call_id");

    if (toolCallId != null && context.currentTask?.toolCalls != null) {
      for (final toolCall in context.currentTask!.toolCalls!) {
        if (toolCall.tool_call_id == toolCallId) {
          toolCall.tool_result = toolResult ?? "";
          final json = jsonEncode(toolCall.toJson());
          context.currentTask?.content.value += "\n```json\n$json\n```";
          debugPrint("ToolCallResultHandler result = $json");
          break;
        }
      }
    }
  }
}