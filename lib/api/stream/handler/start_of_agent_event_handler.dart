import 'package:flutter/material.dart';
import 'package:new_agnes/api/stream/handler/base_event_handler.dart';
import 'package:new_agnes/api/stream/render/StreamEventHandler.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';

import '../render/entity.dart';
import '../stream_message_type_enum.dart';

class StartOfAgentEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) {
    return eventType == "start_of_agent";
  }

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Start of agent handler");
    context.isTaskRunning = true;

    final data = getValidData(entity);
    if (data == null) return;

    context.currentAgentName = getStringValue(data, "agent_name");
    context.currentAgentId = getStringValue(data, "agent_id");
    context.messageBuffer.clear();
    context.jsonContentBuffer.clear();
    context.insideJsonBlock = false;

    if (context.currentAgentName == StreamMessageTypeEnum.reporter.name) {
      List<RenderTask> result = context.workflow.renderList;
      for (int i = result.length - 1; i >= 0; i--) {
        if (result[i].thinkTasks.isNotEmpty) {
          result[i]
              .thinkTasks
              .add(RenderTask()..type.value = StreamMessageTypeEnum.reporter);
          break;
        }
      }
    }
  }

}