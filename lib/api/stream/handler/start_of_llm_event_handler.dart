import 'package:flutter/material.dart';
import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../stream_result_entity.dart';

class StartOfLlmEventHandler extends BaseEventHandler {
  @override
  bool canHandle(String eventType) => eventType == "start_of_llm";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Start of llm handler");
  }
}