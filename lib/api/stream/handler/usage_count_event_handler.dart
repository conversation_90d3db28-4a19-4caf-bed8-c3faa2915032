import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_message_type_enum.dart';
import '../stream_result_entity.dart';

class UsageCountEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "usage_count";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    Map<String, dynamic> data = entity.dataJson();
    if (data.containsKey("kind")) {
      String kind = data["kind"].toString();
      context.workflow.productData ??= ProductModel("", "", []);
      context.workflow.productData?.type = kind;
    }
    int index = -1;
    for(int i = context.workflow.renderList.length - 1 ; i >= 0; i--){
      RenderTask itemTask = context.workflow.renderList[i];
      if(itemTask.type() == StreamMessageTypeEnum.design_cache){
        index = i;
        break;
      }
    }
    if(index != -1){
      context.workflow.renderList.removeAt(index);
    }
  }
}