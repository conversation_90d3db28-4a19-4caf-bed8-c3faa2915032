import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_message_type_enum.dart';
import '../stream_result_entity.dart';

class UpgradeMembershipEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "upgrade_membership";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    //最近的一个添加reporter
    if (isDeepSearchHandOff(context.currentHandOff)) {
      List<RenderTask> result = context.workflow.renderList;
      for (int i = result.length - 1; i >= 0; i--) {
        if (result[i].type == StreamMessageTypeEnum.message_thinking) {
          result[i]
              .thinkTasks
              .add(RenderTask()..type.value = StreamMessageTypeEnum.reporter);
          break;
        }
      }
    }
    String message = entity.dataJson()["text"] ?? "Error";
    RenderTask updateTask = RenderTask()
      ..agentName = context.currentAgentName ?? ""
      ..type.value = StreamMessageTypeEnum.error
      ..handOff = context.workflow.handOff
      ..content.value = message;
    context.currentTask = updateTask;
    context.workflow.renderList.add(updateTask);
  }
}