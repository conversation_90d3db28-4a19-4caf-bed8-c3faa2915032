import 'dart:convert';

import 'package:new_agnes/api/stream/handler/base_event_handler.dart';
import 'package:new_agnes/api/stream/render/StreamEventHandler.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';

import '../render/entity.dart';
import '../stream_message_type_enum.dart';

class SearchUrlsEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "search_urls";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    final data = getValidData(entity);
    if (data == null) return;

    final content = data["data"];
    if (content != null) {
      String value = jsonEncode(content);
      final task = RenderTask()
        ..type.value = StreamMessageTypeEnum.search_urls
        ..content.value = value;

      addTaskAndRefresh(context.workflow.renderList, task);
    }
  }

}