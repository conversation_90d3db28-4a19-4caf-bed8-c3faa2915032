import 'package:flutter/material.dart';
import 'package:new_agnes/api/stream/handler/base_event_handler.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';

import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_message_type_enum.dart';

class EndOfLlmEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "end_of_llm";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("End of llm handler");
    if (isTrigae(context.currentAgentName)) {
      // 存在一种场景,triage的时候,ai分辨不了,会反问一些信息,这时候需要区分是否要显示
      // handoff_开头,并且存在()的,类似handoff_to_ai_slides(),说明是方法,ai意图明确,不需要显示,否则需要显示
      if (context.triageBuffer.toString().startsWith("handoff_") &&
          context.triageBuffer.toString().endsWith("()")) {
        context.currentHandOff = context.triageBuffer.toString();
        context.currentTask?.handOff = context.currentHandOff ?? "";
        context.triageBuffer.clear();
        return;
      } else {
        context.workflow.renderList.add(RenderTask()
          ..type.value = StreamMessageTypeEnum.message
          ..content.value = context.triageBuffer.toString());
      }
    }
  }
}