import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_message_type_enum.dart';
import '../stream_result_entity.dart';

class ToolTagEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "tool_tag";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    final data = getValidData(entity);
    if (data == null) return;

    final content = getStringValue(data, "message");
    final type = getStringValue(data, "type");

    if (type == "fold_end") {
      return;
    }

    if (content != null) {
      final tagTask = RenderTask()
        ..agentName = context.currentAgentName ?? ""
        ..content.value = content
        ..type.value = StreamMessageTypeEnum.tag;

      addTaskAndRefresh(context.workflow.renderList, tagTask);
    }
  }
}