import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_message_type_enum.dart';
import '../stream_result_entity.dart';

class MessageEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "message";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Message Handler");
    if(hasFilter(context.currentHandOff,context.currentAgentName)){
      return;
    }
    try {
      final data = getValidData(entity);
      if (data == null) return;

      final delta = data["delta"];
      final messageId = getStringValue(data, "message_id");

      if (delta != null && delta["content"] != null) {
        final content = delta["content"];
        if (isTrigae(context.currentAgentName)) {
          context.triageBuffer.write(content);
        } else {
          context.messageBuffer.write(content);

          if (isCoordinator(context.currentAgentName)) {
            // coordinator 需要判断字符长度20
            if (context.messageBuffer.isEmpty ||
                context.messageBuffer.length <= 20) {
              return;
            }
          }

          _ensureCurrentTask(messageId, context);
          context.currentTask?.content.value = context.messageBuffer.toString();

          _addTaskToAppropriateList(context, content);
        }
      }
    } catch (e) {
      debugPrint("MessageHandler error: $e");
      debugPrint("_handleMessage entity: ${entity}");
    }
  }

  void _ensureCurrentTask(String? messageId, RenderContext context) {
    if (context.currentTask?.messageId != messageId) {
      final messageTask = RenderTask()
        ..messageId = messageId ?? ""
        ..agentName = context.currentAgentName ?? ""
        ..handOff = context.currentHandOff ?? "";
      addTaskThinking(messageTask, context);
      messageTask.type.value = getMessageType(messageTask, context);
      context.currentTask = messageTask;
    }
  }

  void _addTaskToAppropriateList(RenderContext context, String content) {
    final handOff = context.workflow.handOff;
    final agentName = context.currentAgentName;
    RxList<RenderTask>? targetList;
    if (isDeepSearchHandOff(handOff) &&
        (isPlanner(agentName) || isResearcher(agentName))) {
      final resultList = context.workflow.renderList;
      for (int i = context.workflow.renderList.length - 1; i >= 0; i--) {
        if (resultList[i].type == StreamMessageTypeEnum.message_thinking) {
          resultList[i].content.value += content;
          targetList = resultList[i].thinkTasks;
          ;
          break;
        }
      }
    } else {
      // 存在一种场景,triage的时候,ai分辨不了,会反问一些信息,这时候需要区分是否要显示
      // handoff_开头,并且存在()的,类似handoff_to_ai_slides(),说明是方法,ai意图明确,不需要显示,否则需要显示
      targetList = context.workflow.renderList;
    }

    if (targetList != null && context.currentTask != null) {
      if (!targetList.contains(context.currentTask)) {
        addTaskAndRefresh(targetList, context.currentTask!);
      } else {
        targetList.refresh();
      }
    }
  }

  void addTaskThinking(RenderTask messageTask, RenderContext context) {
    if (!messageTask.handoffToDeepResearch) return;
    //如果消息的agentName是planner或者research的话
    // 最后一条消息不是thinking，应该要把类型改为message_think
    List<RenderTask> taskList = context.workflow.renderList();
    RenderTask? lastTask;
    if (taskList.isNotEmpty) {
      lastTask = taskList.last;
    }
    bool lastNotThinking =
    (lastTask?.type() != StreamMessageTypeEnum.message_thinking);
    bool plannerOrResearch =
        isPlanner(messageTask.agentName) || isResearcher(messageTask.agentName);
    if (plannerOrResearch && lastNotThinking) {
      RenderTask renderTask = new RenderTask()
        ..type.value = StreamMessageTypeEnum.message_thinking;
      renderTask.isHistory = false;
      context.workflow.renderList.add(renderTask);
    }
  }

  StreamMessageTypeEnum getMessageType(
      RenderTask messageTask, RenderContext context) {
    if (messageTask.handoffToAiSlides) {
      if (messageTask.agentName == "planner") {
        return StreamMessageTypeEnum.plan_schedule;
      } else {
        return StreamMessageTypeEnum.slide_design;
      }
    } else if (messageTask.handoffToDeepResearch) {
      return StreamMessageTypeEnum.message;
    } else if (messageTask.handoffToAiDesign) {
      if (messageTask.agentName == "planner") {
        return StreamMessageTypeEnum.plan_schedule;
      }
      return StreamMessageTypeEnum.values.firstWhere(
              (e) => e.name == messageTask.agentName,
          orElse: () => StreamMessageTypeEnum.message);
    }
    return StreamMessageTypeEnum.message;
  }
}