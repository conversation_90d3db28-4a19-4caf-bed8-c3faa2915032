import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_result_entity.dart';

class ToolCallEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "tool_call";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    final data = getValidData(entity);
    if (data == null) return;

    final toolCall = ToolCall.fromJson(data);
    if (toolCall.tool_name != "crawl_tool") {
      context.currentTask?.toolCalls ??= [];
      context.currentTask?.toolCalls?.add(toolCall);
    }
  }
}