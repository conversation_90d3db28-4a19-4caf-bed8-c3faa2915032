import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:new_agnes/api/stream/handler/base_event_handler.dart';

import '../../../utils/inAppLogUtil.dart';
import '../render/StreamEventHandler.dart';
import '../render/entity.dart';
import '../stream_message_type_enum.dart';
import '../stream_result_entity.dart';

class FinalSessionStateEventHandler extends BaseEventHandler{
  @override
  bool canHandle(String eventType) => eventType == "final_session_state";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    debugPrint("Final session state Handler ${entity.model}");
    RenderTask? task =
    appendProductMessage(entity, context.workflow.productData);
    if (task != null) {
      context.currentTask?.pptCompletedText = true;
      context.workflow.renderList.add(task);
      context.currentTask = task;
    }
    context.isTaskRunning = false;
    if (context.workflow.renderList.isNotEmpty) {
      context.workflow.renderList.last.isComplete.value = true;
      if (context.workflow.renderList.last.type ==
          StreamMessageTypeEnum.message_go_button &&
          context.workflow.renderList.length > 2) {
        context.workflow.renderList[context.workflow.renderList.length - 2]
            .isComplete.value = true;
      }
      context.workflow.renderList.last.isComplete.value = true;
      context.workflow.renderList.last.isComplete.refresh();
    }
    context.currentTask = null;
    context.workflow.productData = null;
  }

  RenderTask? appendProductMessage(
      StreamResultEntity entity, ProductModel? task) {
    if (task == null) return null;
    ProductModel productModel = task;
    if (productModel.type == "ai_slides_pages") {
      Map<dynamic, dynamic> jsonData = jsonDecode(entity.data);
      String conversationId = jsonData["conversation_id"] ?? "";
      if (jsonData.containsKey("messages")) {
        dynamic messageData = jsonData["messages"];
        if (messageData! is List && messageData.isEmpty) {
          return null;
        }
        Map<dynamic, dynamic> role = messageData[0];
        String pptTitle = role.containsKey("content") ? role["content"] : "";
        RenderTask task = RenderTask()
          ..type.value = StreamMessageTypeEnum.ppt
          ..conversationId = conversationId
          ..content.value = pptTitle;
        return task;
      }
    } else if (productModel.type == "design_image_shots" ||
        productModel.type == "design_video_seconds") {
      String jsonData = jsonEncode(productModel.products ?? []);
      List result = jsonDecode(jsonData);
      List<ProductImageModel> images = [];
      try {
        for (var r in result) {
          images.add(ProductImageModel.fromJson(jsonDecode(r)));
        }
      } catch (e) {
        logError("${productModel.type}=======" + e.toString());
      }
      RenderTask task = RenderTask()
        ..type.value = StreamMessageTypeEnum.visual
        ..productImages = images
        ..content.value = "";
      return task;
    }
    return null;
  }
}