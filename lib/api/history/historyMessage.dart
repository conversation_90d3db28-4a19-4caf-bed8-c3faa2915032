// [{
//   "id": "1754214962901-feb0c9de",
//   "message": "好的，这是一份关于南京的PPT大纲，涵盖了南京的历史、文化、经济和旅游等方面。希望你喜欢！\n\n```json\n{\n  \"action\": \"create\",\n  \"slides\": [\n    {\n      \"title\": \"古都金陵：南京概览\",\n      \"index\": 1,\n      \"subtitle\": \"一座历史与现代交织的城市\",\n      \"presenter\": \"Sapiens AI\"\n    },\n    {\n      \"title\": \"六朝古都：历史沿革\",\n      \"index\": 2,\n      \"sections\": [\n        \"建都史：六朝、明朝、中华民国\",\n        \"重要历史事件：南京大屠杀与抗日战争\",\n        \"历史遗迹：城墙、明孝陵、中山陵\"\n      ]\n    },\n    {\n      \"title\": \"文化名城：人文底蕴\",\n      \"index\": 3,\n      \"sections\": [\n        \"文学艺术：秦淮文化、金陵画派\",\n        \"教育科研：高等院校与科研机构\",\n        \"民俗风情：非物质文化遗产\",\n        \"特色美食：秦淮小吃、盐水鸭\"\n      ]\n    },\n    {\n      \"title\": \"经济发展：现代南京\",\n      \"index\": 4,\n      \"sections\": [\n        \"产业结构：支柱产业与新兴产业\",\n        \"科技创新：高新技术园区发展\",\n        \"交通枢纽：长江航运与高铁网络\"\n      ]\n    },\n    {\n      \"title\": \"旅游胜地：魅力南京\",\n      \"index\": 5,\n      \"sections\": [\n        \"自然风光：紫金山、玄武湖\",\n        \"人文景观：夫子庙、总统府\",\n        \"特色体验：夜游秦淮、文创街区\",\n        \"周边游览：句容、扬州等\"\n      ]\n    },\n    {\n      \"title\": \"南京的未来展望\",\n      \"index\": 6,\n      \"sections\": [\n        \"城市规划与可持续发展\",\n        \"国际交流与合作\",\n        \"打造宜居宜业的现代化都市\"\n      ],\n      \"contact\": \"<EMAIL>\"\n    }\n  ]\n}\n```",
//   "sender": "llm",
//   "type": "message",
//   "idx": 7,
//   "docs": [],
//   "appendix": "{\"mode\": 3}",
//   "ref_id": null
// },
// {
//   "id": "1754214962901-92e5029f",
//   "message": "做一份关于南京的PPT",
//   "sender": "user",
//   "type": "message",
//   "idx": 1,
//   "docs": [],
//   "appendix": null,
//   "ref_id": null
// }]
import 'dart:convert';

import 'package:new_agnes/api/stream/stream_message_type_enum.dart';

class HistoryMessage {
  final String id;
  final String message;
  final String sender;
  final String type;
  final int idx;
  final List<dynamic> docs;
  final String? appendix;
  final String? refId;

  HistoryMessage({
    required this.id,
    required this.message,
    required this.sender,
    required this.type,
    required this.idx,
    required this.docs,
    required this.appendix,
    required this.refId,
  });

  factory HistoryMessage.fromJson(Map<String, dynamic> json) {
    return HistoryMessage(
      id: json['id'] as String,
      message: json['message'] as String,
      sender: json['sender'] as String,
      type: json['type'] as String,
      idx: json['idx'] as int,
      docs: json['docs'] ?? [],
      appendix: json['appendix'] as String?,
      refId: json['ref_id'] as String?,
    );
  }

  bool get isUserMessage {
    return sender == 'user';
  }

  bool get isLlmMessage {
    return sender == 'llm';
  }

  bool get isSys{
    return sender == 'sys';
  }

  bool get isFold {
    return type.endsWith("_fold");
  }

  StreamMessageTypeEnum get typeIgnoreFold {
    if(type.endsWith("_fold")){
      Map<String,dynamic> json = jsonDecode(message);
      if (json.containsKey('type')) {
        return StreamMessageTypeEnum.values.firstWhere(
            (element) => element.name == json['type'],
            orElse: () => StreamMessageTypeEnum.unknown);
      }
      return StreamMessageTypeEnum.unknown;
    }else {
      return StreamMessageTypeEnum.values.firstWhere(
              (element) => element.name == type,
          orElse: () => StreamMessageTypeEnum.unknown);
    }
  }
}
