import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/request/request.dart';
import 'package:new_agnes/api/api.dart';
import 'package:new_agnes/main_tab_logic.dart';
import 'package:new_agnes/module/account/login/page.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_logic.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../module/home/<USER>/input_widgets/home_drawer.dart';
import '../module/home/<USER>/logic.dart';
import '../module/mine/mine_info/logic.dart';
import 'StorageService.dart';

class ApiProvider extends GetConnect {
  static String? token = '';

  @override
  void onInit() {
    super.onInit();
    httpClient.baseUrl = Api.baseUrl;

    httpClient.defaultDecoder = (map) {
      if (map is Map<String, dynamic>) return map;
      return null;
    };

    httpClient.addRequestModifier<dynamic>((request) async {
      request.headers['Authorization'] = token ?? '';
      request.headers['X-User-Language'] = getLanguageCode();
      request.headers['User-Agent'] = 'Agnes';
      debugPrint('-------------------- Request Info --------------------');
      debugPrint('URL: ${request.url}');
      debugPrint('Method: ${request.method}');
      debugPrint('Headers: ${request.headers}');
      //debugPrint('Body: ${await request.bodyBytes.bytesToString()}');
      debugPrint('------------------------------------------------------');
      return request;
    });

    // 设置响应拦截器
    httpClient.addResponseModifier<dynamic>((request, response) async {
      debugPrint('-------------------- Response Info --------------------');
      debugPrint('URL: ${request.url}');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Headers: ${response.headers}');
      debugPrint('Body: ${response.body}');
      debugPrint('------------------------------------------------------');

      try {
        if (response.statusCode != 200 && response.statusCode != 201) {
          // showFailToast(response.body['detail'] ?? '${response.statusCode}');
        }
        if (response.statusCode == 401 &&
            !request.url.path.contains("login_by_userphone") &&
            !request.url.path.contains("token_by_email")) {
          clearToken();
          // 清空页面返回登录
          Get.offAll(() => LoginPage());
        }
      } on Exception catch (e) {
        debugPrint(e.toString());
      }
      return response;
    });

    httpClient.timeout = const Duration(seconds: 30);
  }

  static String getLanguageCode() {
    final locale = Localizations.localeOf(Get.context!);
    return locale.languageCode;
  }

  void setToken(String newToken, {String tokenType = 'Bearer'}) {
    token = '$tokenType $newToken';
    CmUtils.token = token!;
  }

  void clearToken() {
    token = null;
    Get.find<StorageService>().setLoginData('');
    Get.find<StorageService>().setUserInfoData('');
    Get.delete<MineInfoLogic>();
    Get.delete<HomeDrawerLogic>();
    Get.delete<RolesLogic>();
    Get.delete<RtcLogic>();
    Get.delete<MainTabLogic>();
  }
}

// 检查网络连接状态
Future<bool> checkNetworkConnection() async {
  List<ConnectivityResult> connectivityResultList = await (Connectivity().checkConnectivity());
  bool isConnectivity=false;
  connectivityResultList.forEach((connectivityResult){
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      isConnectivity=true; // 有网络连接
    }
  });
  return isConnectivity; // 无网络连接
}
