import 'dart:convert';
import 'dart:ui';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:new_agnes/module/account/login/model/LoginModel.dart';
import 'package:new_agnes/module/mine/model/UserInfoModel.dart';

import '../module/chat/model/grouplist/role_info_bean.dart';
import '../module/chat/model/third_sdk_model.dart';
import '../utils/FirebaseApi.dart';
import '../utils/inAppLogUtil.dart';

class StorageService extends GetxService {
  late GetStorage storage;

  @override
  void onInit() {
    super.onInit();
    storage = GetStorage();
  }

  GetStorage getStorage() => storage;

  String? getToken() {
    return storage.read('token');
  }

  void setToken(String token) {
    storage.write('token', token);
  }

  void setIdToken(String token) {
    storage.write('idToken', token);
  }

  String? getIdToken() {
    return storage.read('idToken');
  }

  //存储第三方token数据
  void setThirdSDKData(String data) {
    storage.write('thirdSDKData', data);
  }
  //存储角色信息
  void setRoleData(String data) {
    storage.write('roleData', data);
  }

  void setLanguage(String language) {
    storage.write('localLanguage', language);
  }

  String? getLanguage() {
    return storage.read('localLanguage');
  }

  //存储登录信息
  void setLoginData(String data) {
    storage.write('loginData', data);
    FirebaseApi().initToServerFCMToken();
  }

  //存储用户信息
  void setUserInfoData(String data) {
    storage.write('userInfoData', data);
  }

  void setHasSync(bool hasSync,String agoraUserId) {
    storage.write(agoraUserId, hasSync);
  }

  bool getHasSync(String agoraUserId) {
    return storage.read(agoraUserId)??false;
  }

  removeHasSync(String agoraUserId){
    storage.remove(agoraUserId);
  }

  setAgoraAppKey(String appKey){
    storage.write("agoraInitAppKey", appKey);
  }

  String getAgoraInitAppKey(){
    return storage.read("agoraInitAppKey")??"";
  }

  removeAppKey() {
    storage.remove("agoraInitAppKey");
  }

  void setDeleteConversation(String id) {
    String oldId = storage.read('deleteConversation')??"";
    if(oldId.isEmpty) {
      oldId = id;
    } else {
      oldId = oldId + "," + id;
    }
    logError("agora: cache-delete: $oldId");
    storage.write('deleteConversation', oldId);
  }

  String getDeleteConversation() {
    return storage.read('deleteConversation')??"";
  }

  removeDeleteConversation() {
    storage.remove('deleteConversation');
  }

  void removeDeleteConversationId(String id) {
    String current = getDeleteConversation();
    if (current.contains(id)) {
      logError("旧的id${current}");
      String newValue = current.replaceAll(id, "");
      storage.write('deleteConversation', newValue);
      logError("新的id${newValue}");
      // String escapedId = RegExp.escape(id);
      // String newValue = current.replaceAll(RegExp(r'(?:^|,)${escapedId}(?:,|$)'), ',');
      // newValue = newValue.replaceAll(RegExp(r'^,|,$'), '').replaceAll(',,', ',');
      // newValue.isEmpty ? removeDeleteConversation() : storage.write('deleteConversation', newValue);
    };
  }

  void setUserCountryCode(String data){
    storage.write('userCountryCode', data);
  }

  String? getUserCountryCode(){
    return storage.read('userCountryCode');
  }

  void setAgoraInfo(String agoraUserId, String agoraToken){
    storage.write('agoraUserId', agoraUserId);
    storage.write('agoraToken', agoraToken);
  }

  void removeAgoraInfo(){
    storage.remove('agoraUserId');
    storage.remove('agoraToken');
  }

  String getAgoraUserId(){
    return storage.read('agoraUserId')??"";
  }

  String getAgoraToken(){
    return storage.read('agoraToken')??"";
  }

  void setHotQuestionTimes(int hotQuestionTimes){
    storage.write("hotQuestionTimes", hotQuestionTimes);
  }

  int getHotQuestionTimes(){
    return storage.read('hotQuestionTimes')?? 0;
  }

  //获取角色id信息
  List<RoleInfoBean> getRoleInfoData() {
    final data = storage.read('roleData');
    if (data != null && data != '') {
      final List<dynamic> jsonList = jsonDecode(data);
      return jsonList.map((e) => RoleInfoBean.fromJson(e)).toList();
    }
    return [];
  }


  //获取第三方token数据
  ThirdSDKModel getThirdSDKData() {
    if (storage.read('thirdSDKData') != '' &&
        storage.read('thirdSDKData') != null) {
      ThirdSDKModel thirdSDKModel =
          ThirdSDKModel.fromJson(jsonDecode(storage.read('thirdSDKData')));
      return thirdSDKModel;
    }
    return new ThirdSDKModel();
  }

  removeThirdSDKData() {
    storage.remove('thirdSDKData');
  }

  //获取登录数据
  LoginModel getLoginData() {
    if (storage.read('loginData') != '' && storage.read('loginData') != null) {
      LoginModel loginModel =
          LoginModel.fromJson(jsonDecode(storage.read('loginData')));
      return loginModel;
    }
    return new LoginModel();
  }

  //获取用户数据
  UserInfoModel getUserInfoData() {
    if (storage.read('userInfoData') != '' &&
        storage.read('userInfoData') != null) {
      UserInfoModel userInfoModel =
          UserInfoModel.fromJson(jsonDecode(storage.read('userInfoData')));
      return userInfoModel;
    }
    return new UserInfoModel();
  }

  void clearToken() {
    storage.remove('token');
  }
}
