import 'package:new_agnes/module/debug/global.dart';

class Api {

  static String get baseUrl {
    return Global.globalBaseUrl;
  }


  static String get termsUrl => baseUrl + '/terms';
  static String get privacyUrl => baseUrl + '/privacy';
  static String get subscriptionUrl => baseUrl + '/subscription';
  static const String knowMore = "https://shop.agnes.life";

  // 注册
  static const String register = '/api/auth/register';

  // login by email  邮箱登录
  static const String emailLogin = "/api/auth/token_by_email";
  // login by phone  手机号验证码登录
  static const String phoneLogin = "/api/auth/app/login_by_userphone";
  // login by google  google服务登录
  static const String googleLogin = '/api/auth/google/login';
  // login by apple  apple服务登录
  static const String appleLogin = '/api/auth/apple/login';

  // send verify code  发送验证码
  static const String sendVerifyCode = '/api/validation/code/send';
  // code verify  验证码验证
  static const String codeVerify = "/api/validation/code/verify";

  // reset password  重置密码
  static const String resetPwd = "/api/auth/update_password";

  // apk version check  检查版本更新
  static const String checkApkVersion = '/api/version/latest';

  // get user info  获取用户信息
  static const String userInfo = '/api/auth/me';
  // update user name 更新用户名称
  static const String updateUserName = '/api/auth/update_user_name';
  // update user avatar 更新用户头像
  static const String updateUserAvatar = '/api/auth/update_user_avatar';
  // update user phone 绑定手机号
  static const String bindPhone = '/api/auth/bind_phone';

  // quota 获取配额
  static const String quotaRequest = '/api/auth/quota';
  // conversations 搜索历史
  static const String searchHistory = '/api/conversations';

  // export file 导出文件
  static const String exportFile = '/api/report/export'; //导出文件
  static const String exportFilePPT = '/api/report/export_ppt'; //导出ppt文件
  static const String getHotNews = '/api/file/news/hot-news';//获取热门新闻

  static const String chatStream = '/api/chat/stream';//ai接口请求
  static const String conversations = '/api/conversations';//获取话题id
  static const String feedback = '/api/feedback/';//反馈
  static const String ugc = '/api/ugc';//图片生成
  static const String chatResumeStream = '/api/resume/chat/stream';//ai接口请求
  static const String chat = '/api/chat';//ai接口请求

  static const String fetchThirdPartySDKParameter = '/api/security/parameter';// 获取调用第三方SDK的KEY

  static const String pptList = '/api/ppt/ppt_list';
  static const String groupList = '/api/group_chat/group_list';//群组列表
  static const String groupUserList = '/api/group_chat/group_users';//群组成员列表
  static const String renameGroup = '/api/group_chat/rename_group';//修改群名称

  static const String plansPersonal = '/api/membership/plans/personal';// 获取会员方案
  // 创建群聊
  static const String create_group = '/api/group_chat/create_group';
  // 添加成员
  static const String add_members = '/api/group_chat/add_members';
  // 获取声网的token和userId
  static const String getAgoraInfo = '/api/agora/generate_user_token';
  // 获取角色列表
  static const String getRoles = '/api/cooperation/roles';
  // 获取搜索成员信息
  static const String get_addable_users = '/api/group_chat/get_addable_users';
  // 获取群消息
  static const String groupMessage = '/api/cooperation/group_messages';
  // 创建语音群聊
  static const String createGroupChat = '/api/agora/generate_rtc_user_token/';
  //获取语音群聊数据
  static const String rtcGroupData = '/api/rtc/channel_status/';
  // 获取语音群聊用户信息
  static const String rtcGroupJoinUser = '/api/rtc/get_user_info/';
  // 根据声网group_id获取系统group_id
  static const String getSystemGroupId = '/api/group_chat/get_agnes_group_id';
  //群聊上传图片
  static const String groupUploadImage = '/api/file/upload';
  // 获取当前用户的所有系统消息。
  static const String getSystemMessages = '/api/group_chat/system_messages';
  //rtc 后台状态更换
  static const String rtcGroupOperationLog = '/api/rtc/rtc_operation_log';
  //rtc 插入agnes
  static const String rtcInviteAgnes = '/api/rtc/invite_agnes';

  //获取群文件
  static const String getGroup_files = '/api/group_chat/group_files';
  //修改群名
  static const String rename_group = '/api/group_chat/rename_group';
  //删除群成员
  static const String remove_member = '/api/group_chat/delete_members';
  //编辑群成员权限
  static const String edit_member_type = '/api/group_chat/update_members_role';
  // 获取当前频道状态
  static const String get_channel_status = '/api/rtc/channel_status/';
  // 开启 Agora 云转录（代替Azure语音识别）
  static const String start_agora_transcription = '/api/rtc/start_real_time_stt_agnes';
  // 关闭 Agora 云转录（代替Azure语音识别）
  static const String stop_agora_transcription = '/api/rtc/stop_real_time_stt_agnes';
  //获取当前用户的灰度权限
  static const String  featureAccess= '/api/feature/access';
  //获取上传图片的docId
  static const String  uploadRag = '/api/file/upload/rag';
  //上传fcm token
  static const String uploadFCMToken = '/api/auth/set_firebase_token';
  // 上传通讯录人员信息
  static const String checkAddressBook = '/api/group_chat/check_address_book';
}