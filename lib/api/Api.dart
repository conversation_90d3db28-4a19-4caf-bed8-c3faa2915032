import 'package:get/get.dart';
import 'package:new_agnes/utils/environment_config.dart';

class Api {
  // 动态获取baseUrl，支持环境切换
  static String get baseUrl {
    // if (Get.isRegistered<EnvironmentConfig>()) {
    //   return Get.find<EnvironmentConfig>().baseUrl;
    // }
    // 如果EnvironmentConfig还未注册，使用默认正式环境
    // 不要修改，如果需要修改，请不要提交！！！
    return "https://agnes-dev.kiwiar.com";

    // return "https://app.agnes-ai.com";
    // return "http://*********:8093";

  }


  static String get termsUrl => baseUrl + '/terms';
  static String get privacyUrl => baseUrl + '/privacy';
  static String get subscriptionUrl => baseUrl + '/subscription';
  static const String knowMore = "https://shop.agnes.life";

  // 注册
  static const String register = '/api/auth/register';

  // login by email  邮箱登录
  static const String emailLogin = "/api/auth/token_by_email";
  // login by phone  手机号验证码登录
  static const String phoneLogin = "/api/auth/app/login_by_userphone";
  // login by google  google服务登录
  static const String googleLogin = '/api/auth/google/login';
  // login by apple  apple服务登录
  static const String appleLogin = '/api/auth/apple/login';

  // send verify code  发送验证码
  static const String sendVerifyCode = '/api/validation/code/send';
  // code verify  验证码验证
  static const String codeVerify = "/api/validation/code/verify";

  // reset password  重置密码
  static const String resetPwd = "/api/auth/update_password";

  // apk version check  检查版本更新
  static const String checkApkVersion = '/api/version/latest';

  // get user info  获取用户信息
  static const String userInfo = '/api/auth/me';
  // update user name 更新用户名称
  static const String updateUserName = '/api/auth/update_user_name';
  // update user avatar 更新用户头像
  static const String updateUserAvatar = '/api/auth/update_user_avatar';

  // quota 获取配额
  static const String quotaRequest = '/api/auth/quota';
  // conversations 搜索历史
  static const String searchHistory = '/api/conversations';

  // export file 导出文件
  static const String exportFile = '/api/report/export'; //导出文件
  static const String exportFilePPT = '/api/report/export_ppt'; //导出ppt文件
  static const String getHotNews = '/api/file/news/hot-news'; //获取热门新闻

  static const String chatStream = '/api/chat/stream'; //ai接口请求
  static const String conversations = '/api/conversations'; //获取话题id
  static const String feedback = '/api/feedback/'; //反馈
  static const String ugc = '/api/ugc'; //图片生成
  static const String chatResumeStream = '/api/resume/chat/stream'; //ai接口请求
  static const String chat = '/api/chat'; //ai接口请求

  static const String fetchThirdPartySDKParameter =
      '/api/security/parameter'; // 获取调用第三方SDK的KEY

  static const String pptList = '/api/ppt/ppt_list';
  static const String groupList = '/api/group_chat/group_list'; //群组列表
  static const String groupUserList = '/api/group_chat/group_users'; //群组成员列表
  static const String renameGroup = '/api/group_chat/rename_group'; //修改群名称

  static const String plansPersonal =
      '/api/membership/plans/personal'; // 获取会员方案
  // 创建群聊
  static const String create_group = '/api/group_chat/create_group';
  // 添加成员
  static const String add_members = '/api/group_chat/add_members';
  // 获取声网的token和userId
  static const String getAgoraInfo = '/api/agora/generate_user_token';
  // 获取角色列表
  static const String getRoles = '/api/cooperation/roles';
  // 获取搜索成员信息
  static const String get_addable_users = '/api/group_chat/get_addable_users';
  // 获取群消息
  static const String groupMessage = '/api/cooperation/group_messages';
  // 创建语音群聊
  static const String createGroupChat = '/api/agora/generate_rtc_user_token/';
  //获取语音群聊数据
  static const String rtcGroupData = '/api/rtc/channel_status/';
  // 获取语音群聊用户信息
  static const String rtcGroupJoinUser = '/api/rtc/get_user_info/';
  // 根据声网group_id获取系统group_id
  static const String getSystemGroupId = '/api/group_chat/get_agnes_group_id';
  //群聊上传图片
  static const String groupUploadImage = '/api/file/upload';
  // 获取当前用户的所有系统消息。
  static const String getSystemMessages = '/api/group_chat/system_messages';
  //rtc 后台状态更换
  static const String rtcGroupOperationLog = '/api/rtc/rtc_operation_log';
  //rtc 插入agnes
  static const String rtcInviteAgnes = '/api/rtc/invite_agnes';

  //获取群文件
  static const String getGroup_files = '/api/group_chat/group_files';
  //修改群名
  static const String rename_group = '/api/group_chat/rename_group';
  //删除群成员
  static const String remove_member = '/api/group_chat/delete_members';
  //编辑群成员权限
  static const String edit_member_type = '/api/group_chat/update_members_role';
  // 获取当前频道状态
  static const String get_channel_status = '/api/rtc/channel_status/';

  // 通讯录相关接口
  static const String matchContactUsers = '/api/contacts/match_users'; // 匹配通讯录用户
  static const String inviteContacts = '/api/contacts/invite'; // 邀请通讯录联系人
  // 开启 Agora 云转录（代替Azure语音识别）
  static const String start_agora_transcription =
      '/api/rtc/start_real_time_stt_agnes';
  // 关闭 Agora 云转录（代替Azure语音识别）
  static const String stop_agora_transcription =
      '/api/rtc/stop_real_time_stt_agnes';
  //获取当前用户的灰度权限
  static const String  featureAccess= '/api/feature/access';
  //获取上传图片的docId
  static const String  uploadRag = '/api/file/upload/rag';

  ///社区 接口文档 https://z1cbluctr5l.feishu.cn/wiki/W3dvwuzfjifEoRkIdJkcZ1oInwd
  //获取社交逼余额
  static const String walletBalance = '/api/social/wallet/balance';
  //社交逼交易记录
  //参数
  // page
  // page_size
  // type
  // start_time
  // end_time
  //响应
  // trades[]
  // total
  static const String walletTransactions = '/api/social/wallet/transactions';

  //输入邀请码（验证/赋权/绑定关系）
  static const String submitInviteCode = '/api/social/invite/redeem';

  //获取可生成的邀请码列表
  static const String inviteCodes = '/api/social/invite/codes';

  // 获取单条动态完整信息
  static const String feedsInfo = "/api/social/feeds/info";

  // 获取动态一级评论列表
  static const String feedsComments = "/api/social/feeds/comments/first";

  // 根据一级评论ID获取二级评论列表
  static const String feedsCommentsSub = "/api/social/feeds/comments/sub";

  // 社区添加好友搜索成员信息
  static const String account_search = '/api/social/account/search';
  // 关注成员
  static const String follow = '/api/social/relations/follow';
  // 取消关注成员
  static const String unfollow = '/api/social/relations/unfollow';
  // 获取当前登录用户的关注、粉丝、互相关注列表，通过传入的type 参数不同返回不同的列表
  static const String get_relations = '/api/social/relations';
  // 获取其他用户的关注、粉丝、互相关注列表，通过传入的type 参数不同返回不同的列表
  static const String get_other_relations = '/api/social/relations/other';
  //社区推荐内容列表
  static const String commentsList = "/api/social/feeds";
  // 发布评论
  static const String  commentFeeds = "/api/social/feeds/comment";
  // 对指定社区动态/评论进行点踩或取消点踩
  static const String  feedsUnlike = "/api/social/feeds/unlike";
  // 对指定社区动态/评论进行点赞或取消点赞
  static const String  feedsLike = "/api/social/feeds/like";
  //用户发布社区动态支持图片、视频上传
  static const String  feeds_create= '/api/social/feeds/create';
  // 其它用户主页的主要信息
  static const String other_profile = '/api/social/account/profile';
  // 当前用户主页的主要信息
  static const String get_user_info = '/api/social/account/info';
  //二创图片
  static const String  secondCreationImage = "/api/social/feeds/remixes/images";
  //二创视频
  static const String  secondCreationVideo = "/api/social/feeds/remixes/videos";
  //查看其它用户主页——用户历史动态内容
  static const String  other_user_feeds = "/api/social/feeds/other";
  //获取当前登录用户历史动态列表（按时间）
  static const String  self_user_feeds = "/api/social/feeds/self";
  // 获取对应的消耗币
  static const String  corresponding_amounts = "/api/social/wallet/corresponding/amounts";
  //二创任务数返回
  static const String  remix_statas = "/api/social/feeds/remix/progress";
}
