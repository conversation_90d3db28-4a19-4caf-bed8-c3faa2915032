import 'package:get/get.dart';

import 'api/Api.dart';
import 'api/ApiProvider.dart';
import 'module/chat/group/group_chat/rtc_chat/FeatureAccessModel.dart';

class MainTabLogic extends GetxController {
  RxInt curIndex = 0.obs;
  RxBool isFeatureGroup = false.obs;

  /**
   * 获取用户灰度权限
   */

  void getFeatureAccess({Function? callback}) async {
    isFeatureGroup.value = true;
    final response = await Get.find<ApiProvider>().get("${Api.featureAccess}");
    if (response.statusCode == 200) {
      FeatureAccessModel featureAccessModel =
          FeatureAccessModel.fromJson(response.body);
      if (featureAccessModel != null) {
        featureAccessModel?.features?.forEach((data) {
          if (data!.featureCode == 'ai_chat_v2') {
            //AI聊天V2版本
          }
          if (data!.featureCode == 'ppt_generation') {
            //PPT生成功能
          }
          if (data!.featureCode == 'audio_chat') {
            //语音聊天
          }
          if (data!.featureCode == 'group_chat') {
            //群聊天
            isFeatureGroup.value = true;
          }
        });
      }
      callback?.call();
    }
  }
}
