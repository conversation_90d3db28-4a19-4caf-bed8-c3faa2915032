import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/gestures.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:new_agnes/widget/EditText.dart';
import 'dart:io';

import '../generated/l10n.dart';
import '../widget/GradientBackgroundContainer.dart';
import '../widget/GradientBorderContainer.dart';
import '../widget/TapHigh.dart';

class MessageDialog extends StatefulWidget {
  String title; //标题
  String titleTwo; //二级标题
  String data; //内容
  double height; //高度
  bool isEdit; //是否编辑
  int maxLine; //输入框最多几行
  String onLeftName; //左边按钮名称
  String onRightName; //右边按钮名称
  bool isTitleCenter;//标题是否居中
  Function onLeftTap; //左边点击效果
  Function onRightTap; //右边点击效果

  MessageDialog(
   @required this.onLeftTap,
      @required this.onRightTap, {
    this.isEdit = false,
    this.maxLine = 1,
    this.title = '',
    this.titleTwo = '',
    this.data = '',
    this.height = 200,
    this.isTitleCenter = false,
    this.onLeftName = '',
    this.onRightName = '',
  });

  @override
  State<MessageDialog> createState() => _XieYiDialogState();
}

class _XieYiDialogState extends State<MessageDialog> {
  TextEditingController controller = new TextEditingController();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if(widget.isEdit){
      controller.text=widget.data;
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    if(widget.isEdit){
      FocusManager.instance.primaryFocus?.unfocus();
    }
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Color(0xCD201034),
      backgroundColor: Color.fromRGBO(29, 19, 54, 0.8),
      body: Stack(
        children: [
          GestureDetector(
              child: Container(color: Colors.transparent),
              onTap: () {
                Navigator.pop(context);
              }),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: GradientBorderContainer(
                borderRadius: BorderRadius.all(Radius.circular(21)),
                strokeWidth: 2,
                gradients: [
                  LinearGradient(
                    colors: [
                      Color(0xFFFF3BDF),
                      Color(0x08FF3BDF),
                      Color(0xFF00FFFF),
                      Color(0xFF543C86),
                    ],
                    stops: [0, 0.34, 0.76, 1],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ],
                child: Container(
                  width: 1.sw,
                  height: widget.height,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                      image: DecorationImage(
                          image: AssetImage('assets/images/bg_message.png'),
                          fit: BoxFit.fill)),
                  child: Container(
                    margin: EdgeInsets.only(left: 20,right: 20),
                    child: Column(
                      crossAxisAlignment: widget.isTitleCenter?CrossAxisAlignment.center:CrossAxisAlignment.start,
                      children: [
                        SizedBox(height:28),
                        widget.title.isNotEmpty
                            ? Container(
                                child: Text(
                                  widget.title,
                                  style:
                                      TextStyle(color: Colors.white, fontSize: 20),
                                ),
                              )
                            : SizedBox(),
                        widget.titleTwo.isNotEmpty
                            ? Container(
                          margin: EdgeInsets.only( top: 24),
                          child: Text(
                            widget.titleTwo,
                            style:
                            TextStyle(color: Colors.white, fontSize: 16),
                          ),
                        )
                            : SizedBox(),
                        widget.isEdit
                            ?Container(
                            margin: EdgeInsets.only(top: 20,),
                            padding: EdgeInsets.only(
                                left: 10, right: 10, top: 10, bottom: 10),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(color: Color(0xFF8F8F8F)),
                            ),
                            alignment: Alignment.centerLeft,
                            child: EditText(
                              controller,
                              cursorColor: Colors.white,
                              size: 16,
                              maxLine: widget.maxLine,
                            )):SizedBox(),
                        widget.isEdit
                            ? SizedBox()
                            : Container(
                                margin:
                                    EdgeInsets.only( top: 20),
                                alignment: Alignment.center,
                                child: Text(
                                  widget.data,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      height: 1.4),
                                ),
                              ),
                        Expanded(child: SizedBox()),
                        Container(
                          margin: EdgeInsets.only(top: 20,bottom: 20),
                          child: Row(
                            children: [
                              Expanded(
                                  child: TapHigh(
                                    onTap: (){
                                      if(widget.onLeftTap!=null){
                                        widget.onLeftTap();
                                      }
                                    },
                                    child: Container(
                                        height: 48,
                                        padding: EdgeInsets.only(
                                            left: 5, right: 5, top: 5, bottom: 5),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10),
                                          // 圆角边框
                                          border: Border.all(
                                              color: Color(0xFFFF3BDF)), // 外层统一边框
                                        ),
                                        alignment: Alignment.center,
                                        child: Text(
                                          widget.onLeftName,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                              color: Color(0xFFFF3BDF), fontSize: 16),
                                        ))
                                  )),
                              SizedBox(
                                width: 15.w,
                              ),
                              Expanded(
                                  child: TapHigh(
                                      onTap: () {
                                        if(widget.onRightTap!=null){
                                          if(widget.isEdit){
                                            widget.onRightTap(controller.text);
                                          }else{
                                            widget.onRightTap();
                                          }
                                        }
                                      },
                                      child: Container(
                                          height: 48,
                                          padding: EdgeInsets.only(
                                              left: 5,
                                              right: 5,
                                              top: 5,
                                              bottom: 5),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10),
                                            gradient: LinearGradient(
                                              colors: [
                                                Color(0xFFFF91EE),
                                                Color(0xFFFF3BDF),
                                              ],
                                              begin: Alignment.topCenter,
                                              end: Alignment.bottomCenter,
                                            ),
                                          ),
                                          alignment: Alignment.center,
                                          child: Text(
                                            widget.onRightName,
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                color: Colors.black, fontSize: 16),
                                          ))))
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
