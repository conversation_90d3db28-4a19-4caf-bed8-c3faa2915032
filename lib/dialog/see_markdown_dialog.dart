import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/module/chat/widget/search_chat_item_widget.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';
import 'package:share_plus/share_plus.dart';

import '../widget/HuiTan.dart';

class SeeMarkdownDialog extends StatefulWidget {
  String content;
  String filePath;
  SeeMarkdownDialog(this.content,this.filePath);

  @override
  State<SeeMarkdownDialog> createState() => _SeeMarkdownDialogState();
}

class _SeeMarkdownDialogState extends State<SeeMarkdownDialog> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xCD201034),
      body: Stack(
        children: [
          GestureDetector(
              child: Container(color: Colors.transparent),
              onTap: () {
                Navigator.pop(context);
              }),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: GradientBorderContainer(
                borderRadius: BorderRadius.all(Radius.circular(21)),
                strokeWidth: 2,
                gradients: [
                  LinearGradient(
                    colors: [
                      Color(0xFFFF3BDF),
                      Color(0x08FF3BDF),
                      Color(0xFF00FFFF),
                      Color(0xFF543C86),
                    ],
                    stops: [0, 0.34, 0.76, 1],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ],
                child: Container(
                  width: 1.sw,
                  height: 600.w,
                  margin: EdgeInsets.only(
                    left: 20,
                    right: 20,
                  ),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: AssetImage('assets/images/bg_message.png'),
                          fit: BoxFit.fill)),
                  child: Container(
                    margin: EdgeInsets.only(
                        left: 20, right: 20, top: 15.w, bottom: 25.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            // 分享文件
                            await Share.shareXFiles(
                              [XFile(widget.filePath)], // 支持多个文件
                              text: '', // 可选：附加文本
                              subject: '', // 可选：主题（邮件等）
                            );
                          },
                          child: Container(
                            alignment: Alignment.centerRight,
                            padding: EdgeInsets.only(right: 0.w,left: 15.w,top: 4.w),
                            child: Image.asset(
                              "assets/images/ic_ppt_share.png",
                              width: 24.w,
                              height: 24.h,
                            ),
                          ),
                        ),
                       Expanded(child:  Container(
                           child: ScrollConfiguration(
                             behavior: NoShadowScrollBehavior(),
                             child: SingleChildScrollView(
                               child: Column(
                                 children: [
                                   SearchChatItemWidget(content: widget.content),
                                 ],
                               ),
                             ),
                           )))
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
