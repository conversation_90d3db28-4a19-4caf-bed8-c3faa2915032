import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/widget/TapHigh.dart';

import '../generated/l10n.dart';
import '../widget/ContainerBox.dart';

class PhotoDialog extends StatefulWidget {
  String title;
  double height;
  int length; //1 拍照  2 相册 3 视频
  var onTap;

  PhotoDialog(this.title, this.height, this.length, this.onTap);

  @override
  State<PhotoDialog> createState() => _PhotoDialogState();
}

class _PhotoDialogState extends State<PhotoDialog> {
  int selectIndex = 100;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xCC1D1336),
      body: Stack(
        children: [
          GestureDetector(
              child: Container(color: Colors.transparent),
              onTap: () => Get.back()),
          Container(
            alignment: Alignment.bottomCenter,
            child: ContainerBox(
              width: 1.sw,
              boxColor: Colors.black,
              borderColor: Colors.black,
              topRightRadius: 30,
              topLeftRadius: 30,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // widget.title.isNotEmpty
                  //     ? Container(
                  //   padding: EdgeInsets.only(top: 20.w, ),
                  //   child: Text(
                  //     widget.title,
                  //     style: TextStyle(
                  //         color: Colors.white,
                  //         fontSize: 20.sp,
                  //         fontWeight: FontWeight.bold),
                  //   ),
                  // )
                  //     : SizedBox(),
                  ContainerBox(
                      width: 54.w,
                      height: 4.w,
                      margin: EdgeInsets.only(top: 16.w, bottom: 16.w),
                      radius: 180),
                  dataLay(),
                  // _lineLay(top: 0, bottom: 0),
                  TapHigh(
                    onTap: () {
                      Get.back();
                    },
                    child: ContainerBox(
                        boxColor: Colors.transparent,
                        borderColor: Colors.transparent,
                        radius: 12,
                        padding: EdgeInsets.only(top: 12.w, bottom: 12.w),
                        margin: EdgeInsets.only(bottom: 20.w),
                        child: Text(
                          S.of(context).cancel,
                          style:
                              TextStyle(color: Colors.white, fontSize: 18.sp),
                        )),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _lineLay({double top = 15, double bottom = 14, double height = 10}) {
    return Container(
      margin: EdgeInsets.only(top: top.w, bottom: bottom.w),
      color: Colors.black,
      width: 1.sw,
      height: height.w,
    );
  }

  Widget dataLay() {
    return Container(
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      child: Column(
        children: [
          TapHigh(
              onTap: () {
                widget.onTap(0);
                setState(() {
                  selectIndex = 0;
                });
                Future.delayed(Duration(seconds: 1)).then((value) {
                  Get.back();
                });
              },
              child: ContainerBox(
                boxColor:
                    selectIndex == 0 ? Color(0x78733D7D) : Colors.transparent,
                borderColor:
                    selectIndex == 0 ? Color(0x78733D7D) : Colors.transparent,
                radius: 12,
                padding: EdgeInsets.only(top: 12.w, bottom: 12.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      S.of(context).takePhoto,
                      style: TextStyle(color: Colors.white, fontSize: 18.sp),
                    ),
                  ],
                ),
              )),
          // widget.length >= 2
          //     ? _lineLay(top: 0, bottom: 0, height: 1)
          //     : SizedBox(),
          widget.length >= 2
              ? TapHigh(
                  onTap: () {
                    widget.onTap(1);
                    setState(() {
                      selectIndex = 1;
                    });
                    Future.delayed(Duration(seconds: 1)).then((value) {
                      Get.back();
                    });
                  },
                  child: ContainerBox(
                    boxColor: selectIndex == 1
                        ? Color(0x78733D7D)
                        : Colors.transparent,
                    borderColor: selectIndex == 1
                        ? Color(0x78733D7D)
                        : Colors.transparent,
                    radius: 12,
                    padding: EdgeInsets.only(top: 12.w, bottom: 12.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          S.of(context).album,
                          style:
                              TextStyle(color: Colors.white, fontSize: 18.sp),
                        ),
                      ],
                    ),
                  ))
              : SizedBox(),
          // widget.length == 3
          //     ? _lineLay(top: 0, bottom: 0, height: 1)
          //     : SizedBox(),
          widget.length == 3
              ? TapHigh(
                  onTap: () {
                    widget.onTap(2);
                    setState(() {
                      selectIndex = 2;
                    });
                    Future.delayed(Duration(seconds: 1)).then((value) {
                      Get.back();
                    });
                  },
                  child: ContainerBox(
                    boxColor: selectIndex == 2
                        ? Color(0x78733D7D)
                        : Colors.transparent,
                    borderColor: selectIndex == 2
                        ? Color(0x78733D7D)
                        : Colors.transparent,
                    radius: 12,
                    padding: EdgeInsets.only(top: 12.w, bottom: 12.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '视频',
                          style:
                              TextStyle(color: Colors.white, fontSize: 18.sp),
                        ),
                      ],
                    ),
                  ))
              : SizedBox(),
        ],
      ),
    );
  }
}
