import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import 'package:new_agnes/widget/EditText.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';
import 'package:new_agnes/widget/TapHigh.dart';

import '../generated/l10n.dart';
import '../module/chat/logic.dart';
import '../module/chat/widget/chat_bottom_tip_widget.dart';

class FanKuiDialog extends StatefulWidget {
  FeedbackLogic feedbackLogic;

  FanKuiDialog(this.feedbackLogic, {super.key});

  @override
  State<FanKuiDialog> createState() => _FanKuiDialogState();
}

class _FanKuiDialogState extends State<FanKuiDialog> {
  // FeedbackLogic feedbackLogic = Get.find<FeedbackLogic>();
  ChatLogic chatLogic = Get.find<ChatLogic>();

  TextEditingController controller = new TextEditingController();
  List<String> dataList = [
    S.of(Get.context!).inaccurate,
    S.of(Get.context!).outOfDate,
    S.of(Get.context!).harmful,
    S.of(Get.context!).thisIsntHelpful,
  ];
  int selectIndex = 4;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xCD201034),
      body: Stack(
        children: [
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 0, sigmaY: 0),
            child: Container(
              decoration: BoxDecoration(color: Colors.black.withOpacity(0)),
            ),
          ),
          GestureDetector(
              child: Container(color: Colors.transparent),
              onTap: () {
                Navigator.pop(context);
              }),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: GradientBorderContainer(
                borderRadius: BorderRadius.all(Radius.circular(21)),
                strokeWidth: 2,
                gradients: [
                  LinearGradient(
                    colors: [
                      Color(0xFFFF3BDF),
                      Color(0x08FF3BDF),
                      Color(0xFF00FFFF),
                      Color(0xFF543C86),
                    ],
                    stops: [0, 0.34, 0.76, 1],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ],
                child: Container(
                  width: 1.sw,
                  height: 470.h,
                  padding: EdgeInsets.zero,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                      border: Border.all(width: 0),
                      image: DecorationImage(
                          image: AssetImage('assets/images/bg_message.png'),
                          fit: BoxFit.fill)),
                  child: Container(
                    margin: EdgeInsets.only(
                        left: 20, right: 20, top: 25.w, bottom: 25.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          child: Text(
                            S.of(Get.context!).helpUsImprove,
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 20.sp,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                        SizedBox(
                          height: 30.w,
                        ),
                        Container(
                          child: Text(
                            S.of(Get.context!).provideAdditionalFeedbackOnThisAnswer,
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w400),
                          ),
                        ),
                        SizedBox(
                          height: 15.w,
                        ),
                        Container(
                          height: 86.w,
                          child: ContainerBox(
                              boxColor: Colors.transparent,
                              borderWith: 1.w,
                              borderColor: Color(0xFF8F8F8F),
                              radius: 10,
                              padding: EdgeInsets.only(
                                  left: 15.w, right: 15.w, top: 10.w, bottom: 10.w),
                              child: EditText(
                                controller,
                                hint: S.of(Get.context!).writeSomething,
                                hintColor: Color(0xFF988B9A),
                              )),
                        ),
                        SizedBox(
                          height: 30.w,
                        ),
                        Container(
                          child: ListView.builder(
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemCount: dataList.length,
                              itemBuilder: (context, index) {
                                return TapHigh(
                                    onTap: () {
                                      setState(() {
                                        selectIndex = index;
                                      });
                                    },
                                    child: Container(
                                      padding:
                                          EdgeInsets.only(bottom: 8.w, top: 8.w),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Container(
                                              child: Text(
                                                dataList[index],
                                                style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 16.sp,
                                                    fontWeight: FontWeight.w400),
                                              ),
                                            ),
                                          ),
                                          Image.asset(
                                            index == selectIndex
                                                ? 'assets/images/ic_select_yes.webp'
                                                : 'assets/images/ic_select_no.webp',
                                            width: 16.w,
                                            height: 16.w,
                                          ),
                                        ],
                                      ),
                                    ));
                              }),
                        ),
                        Expanded(child: SizedBox()),
                        Container(
                          child: Row(
                            children: [
                              Expanded(child: SizedBox()),
                              SizedBox(
                                width: 20.w,
                              ),
                              Expanded(
                                  child: TapHigh(
                                      onTap: () {
                                        // if (controller.text.isNotEmpty &&
                                        //     selectIndex != 4) {
                                        widget.feedbackLogic.feedback(
                                            'dislike', chatLogic.conversationId);
                                        //showSuccessToast('Feedback Sent');
                                        Navigator.pop(context);
                                        //}
                                      },
                                      child: ContainerBox(
                                          padding: EdgeInsets.only(
                                              left: 30.w,
                                              right: 30.w,
                                              top: 12.w,
                                              bottom: 12.w),
                                          radius: 10,
                                          jBColors: [
                                            Color(0xFFFF91EE),
                                            Color(0xFFFF3BDF),
                                          ],
                                          alignment: Alignment.center,
                                          child: Text(
                                            S.of(Get.context!).submit,
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 16.sp),
                                          ))))
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
