import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/widget/deep_research_lay.dart';
import 'package:new_agnes/module/web/single_page_web/page.dart';
import 'package:new_agnes/widget/ImageUrl.dart';
import 'package:new_agnes/widget/TapHigh.dart';

import '../module/chat/logic.dart';
import '../module/chat/model/search_urls_model.dart';
import '../widget/ContainerBox.dart';

class SourcesDialog extends StatefulWidget {
  int type; //0 searchUrls 1 tool call
  List<SearchUrlsModel> searchList = [];

  SourcesDialog(this.searchList, {this.type = 0});

  @override
  State<SourcesDialog> createState() => _SourcesDialogState();
}

class _SourcesDialogState extends State<SourcesDialog> {

  @override
  Widget build(BuildContext context) {
    return ContainerBox(
      topRightRadius: 30,
      topLeftRadius: 30,
      height: 513.w,
      boxColor: Colors.black,
      borderColor: Colors.black,
      padding:
          EdgeInsets.only(left: 18.w, right: 18.w, top: 16.w, bottom: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _topLay(),
          Container(
            color: Color(0xFF2F2431),
            height: 1.w,
          ),
          Expanded(child: _listLay()),
        ],
      ),
    );
  }

  Widget _topLay() {
    return Container(
      child: Column(
        children: [
          Center(
            child: ContainerBox(
              radius: 180,
              width: 75.w,
              height: 4.w,
            ),
          ),
          SizedBox(
            height: 18.w,
          ),
          Container(
            alignment: Alignment.center,
            child: Text(
              '${widget.searchList.length} Search Sources',
              style: TextStyle(color: Colors.white, fontSize: 16.sp),
            ),
          ),
          SizedBox(
            height: 10.w,
          )
        ],
      ),
    );
  }

  Widget _listLay() {
    return Container(
      child: ListView.builder(
          itemCount: widget.searchList.length,
          itemBuilder: (context, index) {
            return _itemLay(index);
          }),
    );
  }

  Widget _itemLay(index) {
    SearchUrlsModel model = widget.searchList[index];
    return TapHigh(
        onTap: () {
          Get.to(SingleWidgetWebWidget(
            model.url,
            title: model.title,
          ));
        },
        child: Container(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ContainerBox(
                radius: 180,
                child: UrlImage('${model.image}',
                    width: 24.w,
                    height: 24.w,
                    radius: 180,
                    errorWidget: ErrorLay()),
              ),
              Expanded(
                  child: Container(
                padding: EdgeInsets.only(left: 16.w, right: 12.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 14.w,
                    ),
                    Container(
                      child: Text(
                        '${model.title}',
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    SizedBox(
                      height: 10.w,
                    ),
                    Container(
                      child: Text(
                        '${model.content}',
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: Color(0xFF988B9A),
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                    SizedBox(
                      height: 14.w,
                    ),
                    Container(
                      color: Color(0xFF2F2431),
                      height: 1.w,
                    ),
                  ],
                ),
              )),
            ],
          ),
        ));
  }
}
