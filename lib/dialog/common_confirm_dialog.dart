import 'package:flutter/material.dart';

class CommonConfirmDialog {
  static Future<void> show(
    BuildContext context, {
    required String content,
    required String leftButtonText,
    required String rightButtonText,
    required VoidCallback onLeftPressed,
    required VoidCallback onRightPressed,
    double minHeight = 160,
  }) {
    // 图片四周32px，centerSlice为中间可拉伸区域
    final Rect centerSlice = const Rect.fromLTWH(40, 40, 690 - 80, 404 - 80);

    final double dialogWidth = MediaQuery.of(context).size.width * 0.8;

    return showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.8), // 这里设置更深的背景色
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.symmetric(horizontal: 24),
          child: Container(
            width: dialogWidth,
            constraints: BoxConstraints(minHeight: minHeight),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Stack(
              children: [
                // 背景图片（九宫格拉伸）
                ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Image.asset(
                    'assets/groupChat/delete_confirm.png',
                    width: dialogWidth,
                    height: null,
                    fit: BoxFit.fill,
                    centerSlice: centerSlice,
                  ),
                ),
                // 内容
                Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        content,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Row(
                        children: [
                          // 左侧按钮
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                Navigator.of(context).pop();
                                onLeftPressed();
                              },
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 11),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color:
                                        const Color.fromRGBO(255, 59, 223, 1),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  leftButtonText,
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                      color: Color.fromRGBO(255, 59, 223, 1),
                                      fontSize: 16),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 右侧按钮
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                Navigator.of(context).pop();
                                onRightPressed();
                              },
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  gradient: const LinearGradient(
                                    colors: [
                                      Color.fromRGBO(255, 59, 223, 1),
                                      Color.fromRGBO(255, 145, 238, 1),
                                    ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                  ),
                                ),
                                child: Text(
                                  rightButtonText,
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                      color: Color.fromRGBO(13, 13, 13, 1),
                                      fontSize: 16),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
