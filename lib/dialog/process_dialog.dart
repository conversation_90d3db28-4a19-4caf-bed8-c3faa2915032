import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:new_agnes/api/stream/render/JsonChunkFinder.dart';
import 'package:new_agnes/module/chat/widget/search_chat_item_widget.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import '../api/stream/render/entity.dart';
import '../module/chat/logic.dart';
import '../module/chat/model/planning_model.dart';
import '../widget/ExpansionCustom.dart';

class ProcessDialog extends StatefulWidget {
  int index;
  ScrollController scrollController;

  ProcessDialog(this.index, this.scrollController);

  @override
  State<ProcessDialog> createState() => _ProcessDialogState();
}

class _ProcessDialogState extends State<ProcessDialog> {
  ChatLogic chatLogic = Get.find<ChatLogic>();

  @override
  void initState() {
    // TODO: implement initState

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ContainerBox(
      topRightRadius: 30,
      topLeftRadius: 30,
      // height: 513.w,
      boxColor: Colors.black,
      borderColor: Colors.black,
      padding:
          EdgeInsets.only(left: 18.w, right: 18.w, top: 16.w, bottom: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _topLay(),
          Expanded(child: _listLay()),
        ],
      ),
    );
  }

  Widget _topLay() {
    return Container(
      margin: EdgeInsets.only(bottom: 10.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: ContainerBox(
              radius: 180,
              width: 75.w,
              height: 4.w,
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 24.w),
            child: Stack(
              children: [
                Positioned(
                    bottom: 1.w,
                    child: ContainerBox(
                      jBColors: [
                        Color(0xFFFF3BDF),
                        Color(0xFFFF91EE),
                      ],
                      radius: 180,
                      width: 75.w,
                      height: 4.w,
                    )),
               Container(
                 width: 75.w,
                 alignment: Alignment.center,
                 child:  Text(
                   "PROCESS",
                   style: TextStyle(
                       color: Colors.white, fontWeight: FontWeight.w500,height: 1.0),
                 ),
               )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _listLay() {
    return Obx(() {
      return Container(
        child: ListView.builder(
            controller: widget.scrollController,
            itemCount: chatLogic.renderManager.workflowRx!.value
                .renderList[widget.index].thinkTasks.value.length,
            itemBuilder: (context, index) {
              RenderTask model = chatLogic.renderManager.workflowRx!.value
                  .renderList[widget.index].thinkTasks.value[index];
              return model.agentName.isEmpty
                  ? SizedBox()
                  : Obx(() {
                      return _itemLay(model, index);
                    });
            }),
      );
    });
  }

  Widget _itemLay(RenderTask model, index) {
    return Container(
      margin: EdgeInsets.only(
        top: 10.w,
      ),
      child: ExpansionCustom(
        title: model.agentName,
        children: <Widget>[
          _zheDieItemLay(model, index),
        ],
      ),
    );
  }

  Widget _zheDieItemLay(RenderTask model, index) {
    String data = '${model.content.value}';
    List<String> split1 = []; //split1[0]  json数据
    if (model.agentName == 'planner') {
      if (data.contains(JsonChunkFinder.START_MARKER)) {
        List<String> split0 = data.split(JsonChunkFinder.START_MARKER);
        if (split0.length > 1 &&
            split0[1].contains(JsonChunkFinder.END_MARKER)) {
          split1 = split0[1].split(JsonChunkFinder.END_MARKER);
        }
      }
    }

    return model.content.value.trim().isEmpty || model.content.value == '```'
        ? SizedBox()
        : Container(
            margin: EdgeInsets.only(left: 18.w),
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                  color: Color(0xFF988B9A),
                  width: 1.0,
                ),
              ),
            ),
            child: Row(
              children: [
                SizedBox(
                  width: 20.w,
                ),
                Expanded(
                    child: Container(
                  child: split1.length > 0 && model.agentName == 'planner'
                      ? _planningLay(split1[0], index)
                      : SearchChatItemWidget(
                          content: data,
                          fontSize: 14.sp,
                          showType: 2,
                          verticalSize: 10,
                        ),
                ))
              ],
            ),
          );
  }

  Widget _planningLay(String json, index) {
    try {
      PlanningModel model = PlanningModel.fromJson(jsonDecode(json));
      return model != null
          ? Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Step1：Planning',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        height: 1.4),
                  ),
                  SizedBox(
                    height: 8.w,
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 20.w),
                    child: Text(
                      '${model.title}',
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          height: 1.4),
                    ),
                  ),
                  SizedBox(
                    height: 4.w,
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 20.w),
                    child: ListView.builder(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: model.steps!.length,
                        itemBuilder: (context, index) {
                          PlanningModelSteps stepsModel = model.steps![index]!;
                          return Container(
                            child: Column(
                              children: [
                                Container(
                                  margin:
                                      EdgeInsets.only(top: 8.w, bottom: 8.w),
                                  child: Row(
                                    children: [
                                      ContainerBox(
                                          boxColor: Colors.white,
                                          width: 6.w,
                                          height: 6.w,
                                          radius: 180),
                                      SizedBox(
                                        width: 10.w,
                                      ),
                                      Expanded(
                                          child: Text(
                                        '${stepsModel.title}',
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.bold),
                                      ))
                                    ],
                                  ),
                                ),
                                Container(
                                  child: Text(
                                    '${stepsModel.description}',
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14.sp,
                                        height: 1.4),
                                  ),
                                  margin: EdgeInsets.only(left: 6.w),
                                ),
                              ],
                            ),
                          );
                        }),
                  )
                ],
              ),
            )
          : SizedBox();
    } catch (e) {
      return Container();
    }
  }
}
