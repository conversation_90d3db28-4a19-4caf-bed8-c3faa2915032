import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/dialog_utils.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';
import 'package:new_agnes/widget/TapHigh.dart';
import 'package:open_file/open_file.dart';

import '../generated/l10n.dart';
import '../module/chat/download_logic.dart';
import '../module/chat/widget/pdf_preview_widget.dart';

class DownloadDialog extends StatefulWidget {
  String id; //会话id
  String fileType; //pdf,ppt,md,
  String content; //生成文件的内容
  String fileName;
  int type; //0 exportFile接口  1 exportFilePPT 接口
  String pathUrl; //整个接口路劲
  DownloadDialog(this.id, this.fileType, this.content, this.fileName, this.type,
      {this.pathUrl = ''}); //文件名称（会话标题）

  @override
  State<DownloadDialog> createState() => _DownloadDialogState();
}

class _DownloadDialogState extends State<DownloadDialog> {
  Timer? timer;
  Rx<int> time = 0.obs;
  DownloadLogic downloadLogic = Get.isRegistered<DownloadLogic>()
      ? Get.find<DownloadLogic>()
      : Get.put(DownloadLogic());
  String get titleName{
    return widget.fileName
        .replaceAll(".ppt", "")
        .replaceAll(".pdf", "")
        .replaceAll(".md", "");
  }

  @override
  void dispose() {
    Get.delete<DownloadLogic>();
    downloadLogic.progress.value = 0;
    if (timer != null) {
      timer!.cancel();
    }
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      timer = Timer.periodic(Duration(seconds: 1), (Timer timer) {
        time.value++;
      });
      downloadLogic.downFile(
        id: widget.id,
        fileType: widget.fileType,
        content: widget.content,
        fileName: widget.fileName,
        type: widget.type,
        pathUrl: widget.pathUrl,
        onReuslt: (String savePath) async {
          if (timer != null) {
            timer!.cancel();
            timer = null;
          }
          Get.back();
          if (widget.fileType == 'md') {
            // DialogUtils.showSeeMarkDownDialog(widget.content,savePath);
            Get.to(PdfPreviewWidget(
              savePath,
              type: 2,
              content: widget.content,
            ));
          }
          if (widget.fileType == 'pdf') {
            Get.to(PdfPreviewWidget(savePath));
          }
          if (widget.fileType == 'pptx') {
            await OpenFile.open(
              savePath,
            );
          }
        },
      );
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
            child: Container(
              decoration: BoxDecoration(color: Colors.black.withOpacity(0)),
            ),
          ),
          GestureDetector(
              child: Container(color: Colors.transparent),
              onTap: () {
                Navigator.pop(context);
              }),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: GradientBorderContainer(
                borderRadius: BorderRadius.all(Radius.circular(21)),
                strokeWidth: 2,
                gradients: [
                  LinearGradient(
                    colors: [
                      Color(0xFFFF3BDF),
                      Color(0x08FF3BDF),
                      Color(0xFF00FFFF),
                      Color(0xFF543C86),
                    ],
                    stops: [0, 0.34, 0.76, 1],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ],
                child: Container(
                  width: 1.sw,
                  height: 240.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                      image: DecorationImage(
                          image: AssetImage('assets/images/bg_message.png'),
                          fit: BoxFit.fill)),
                  child: Container(
                    margin: EdgeInsets.only(
                        left: 20, right: 20, top: 34.w, bottom: 25.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/images/ic_file_down.webp',
                                width: 20.w,
                                height: 20.h,
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                              Expanded(
                                  child: Container(
                                alignment: Alignment.centerLeft,
                                child: RichText(
                                    textAlign: TextAlign.left,
                                    text: TextSpan(
                                        text: S.of(context).downloading,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16.sp,
                                        ),
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: '"${titleName}"',
                                            style: TextStyle(
                                              color: Color(0xFF00FFFF),
                                              fontSize: 16.sp,
                                            ),
                                          ),
                                        ])),
                              ))
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 18.h,
                        ),
                        Container(
                          child: Text(
                            "${S.of(context).pleaseWaitUntilTheDownloadIsCompleted}!",
                            style: TextStyle(
                              color: Color(0xFF988B9A),
                              fontSize: 14.sp,
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 18.h,
                        ),
                        Obx(() {
                          return _createProgressBar(
                              downloadLogic.progress.value);
                        }),
                        SizedBox(
                          height: 6.h,
                        ),
                        Obx(() {
                          return Container(
                              child: Text(
                                  "${(downloadLogic.progress.value * 100).toStringAsFixed(0)}% · ${time.value} seconds",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Color(0xFF988B9A),
                                  )));
                        }),
                        Expanded(child: SizedBox()),
                        Container(
                          child: Row(
                            children: [
                              Expanded(child: SizedBox()),
                              SizedBox(
                                width: 20.w,
                              ),
                              Expanded(
                                  child: TapHigh(
                                      onTap: () {
                                        Navigator.pop(context);
                                        downloadLogic.fileUtils
                                            .cancelDownload();
                                      },
                                      child: ContainerBox(
                                          padding: EdgeInsets.only(
                                              left: 30.w,
                                              right: 30.w,
                                              top: 12.w,
                                              bottom: 12.w),
                                          boxColor: Colors.transparent,
                                          borderColor: Color(0xFFFF3BDF),
                                          borderWith: 1.w,
                                          radius: 10,
                                          alignment: Alignment.center,
                                          child: Text(
                                            S.of(Get.context!).cancel,
                                            style: TextStyle(
                                                color: Color(0xFFFF3BDF),
                                                fontSize: 16.sp),
                                          ))))
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 生成进度条组件，进度从0 ~ 1
  _createProgressBar(double progress) {
    return LinearProgressIndicator(
      backgroundColor: Color(0x78733D7D),
      minHeight: 8.w,
      value: progress == 1.0 ? 1.0 : progress,
      borderRadius: BorderRadius.circular(10),
      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00FFFF)),
    );
  }
}
