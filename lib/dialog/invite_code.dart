/// <AUTHOR>
/// @Date 2025/9/1 11:50
///
/// @Description 展示邀请码
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InviteCodeDialog extends StatelessWidget {
  final List<String> codes;
  const InviteCodeDialog({super.key, required this.codes});

  @override
  Widget build(BuildContext context) {

    double width = MediaQuery.of(context).size.width - 32;
    double height = ((MediaQuery.of(context).size.height)/2) - 250;

    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        Image.asset("assets/images/bg_update_info.png",fit: BoxFit.fill,width: width,height: 345,),
        // 背景图
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  "Invitation Code",
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              ...codes.map((c) => _CodeRow(code: c)).toList(),
            ],
          ),
        ),

        // 关闭按钮
        Positioned(
          right: 16,
          top: height,
          child: InkWell(
            onTap: () => Navigator.of(context).pop(),
            borderRadius: BorderRadius.circular(20),
            child: Container(
              width: 36,
              height: 36,
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0x55FFFFFF),
              ),
              child: const Icon(Icons.close, size: 18, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }
}

class _CodeRow extends StatelessWidget {
  final String code;
  const _CodeRow({required this.code});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16,vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14),
        color: Colors.black.withOpacity(0.25),
        border: Border.all(
          color: const Color(0xFFffffff).withOpacity(0.6),
          width: 1,
        ),
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.white.withOpacity(0.25),
        //     blurRadius: 18,
        //     spreadRadius: 2,
        //   ),
        // ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              code,
              style: TextStyle(
                color: const Color(0xFF00E5FF),
                fontWeight: FontWeight.w700,
                letterSpacing: 0.5,
                // shadows: [
                //   Shadow(
                //     color: const Color(0xFF00E5FF).withOpacity(0.8),
                //     blurRadius: 8,
                //   ),
                // ],
              ),
            ),
          ),
          InkWell(
            onTap: () async {
              await Clipboard.setData(ClipboardData(text: code));
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Copied: $code')),
              );
            },
            borderRadius: BorderRadius.circular(10),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              // decoration: BoxDecoration(
              //   borderRadius: BorderRadius.circular(10),
              //   color: Colors.black.withOpacity(0.3),
              //   border: Border.all(
              //     color: const Color(0xFF9C9CFF).withOpacity(0.7),
              //   ),
              //   boxShadow: [
              //     BoxShadow(
              //       color: const Color(0xFF9C9CFF).withOpacity(0.8),
              //       blurRadius: 10,
              //     ),
              //   ],
              // ),
              child: const Text(
                'copy',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  shadows: [
                    Shadow(
                      color: Colors.blueAccent,
                      blurRadius: 6,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// showDialog(
// context: context,
// barrierColor: Colors.black54,
// builder: (_) => InviteCodeDialog(
// codes: const ["JAU629JAU629", "WCF982WCF982", "SAKDHKASDH78"],
// ),
// );
