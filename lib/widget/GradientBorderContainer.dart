import 'package:flutter/material.dart';

class GradientBorderContainer extends StatefulWidget {
  final double strokeWidth;
  final List<Gradient> gradients;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final Widget? child;

  const GradientBorderContainer({
    Key? key,
    required this.strokeWidth,
    required this.gradients,
    this.width,
    this.height,
    this.borderRadius = BorderRadius.zero,
    this.child,
  }) : super(key: key);

  // 为了向后兼容，提供一个命名构造函数来接收单个gradient
  GradientBorderContainer.single({
    Key? key,
    required double strokeWidth,
    required Gradient gradient,
    double? width,
    double? height,
    BorderRadius borderRadius = BorderRadius.zero,
    Widget? child,
  }) : this(
          key: key,
          strokeWidth: strokeWidth,
          gradients: [gradient],
          width: width,
          height: height,
          borderRadius: borderRadius,
          child: child,
        );

  @override
  State<GradientBorderContainer> createState() =>
      _GradientBorderContainerState();
}

class _GradientBorderContainerState extends State<GradientBorderContainer> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SizedBox(
          width: widget.width,
          height: widget.height,
          child: CustomPaint(
            painter: GradientBorderPainter(
              strokeWidth: widget.strokeWidth,
              gradients: widget.gradients,
              borderRadius: widget.borderRadius,
            ),
            child: Padding(
              padding: EdgeInsets.zero,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

class GradientBorderPainter extends CustomPainter {
  final double strokeWidth;
  final List<Gradient> gradients;
  final BorderRadius borderRadius;

  GradientBorderPainter({
    required this.strokeWidth,
    required this.gradients,
    this.borderRadius = BorderRadius.zero,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (gradients.isEmpty) return;

    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // 如果只有一个渐变，使用原始方法
    if (gradients.length == 1) {
      final rrect = RRect.fromRectAndCorners(
        rect.inflate(strokeWidth / 2),
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );

      final paint = Paint()
        ..shader = gradients[0].createShader(rect)
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke;

      canvas.drawRRect(rrect, paint);
    } else {
      // 多个渐变的情况 - 创建融合效果
      final outerRect = rect.inflate(strokeWidth / 2);
      final innerRect = rect.deflate(strokeWidth / 2);

      final outerRRect = RRect.fromRectAndCorners(
        outerRect,
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );

      final innerRRect = RRect.fromRectAndCorners(
        innerRect,
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );

      // 创建边框路径
      final borderPath = Path()
        ..addRRect(outerRRect)
        ..addRRect(innerRRect)
        ..fillType = PathFillType.evenOdd;

      canvas.save();
      canvas.clipPath(borderPath);

      // 使用多个图层混合实现渐变融合效果
      final paint = Paint();

      // 计算每个渐变的角度偏移
      final angleStep = 360.0 / gradients.length;

      for (int i = 0; i < gradients.length; i++) {
        // 创建一个覆盖整个边框区域的矩形
        final gradientRect = Rect.fromLTWH(
          -strokeWidth / 2,
          -strokeWidth / 2,
          size.width + strokeWidth,
          size.height + strokeWidth,
        );

        paint
          ..shader = gradients[i].createShader(gradientRect)
          ..style = PaintingStyle.fill
          ..blendMode = BlendMode.srcOver;

        // 对于多个渐变，使用部分透明度实现融合
        if (gradients.length > 1) {
          paint.color = Colors.white.withOpacity(1.0 / gradients.length);
        }

        canvas.drawRect(gradientRect, paint);
      }

      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is! GradientBorderPainter) return false;
    return strokeWidth != oldDelegate.strokeWidth ||
        gradients != oldDelegate.gradients ||
        borderRadius != oldDelegate.borderRadius;
  }
}
