import 'dart:ui';

import 'package:flutter/material.dart';

enum GradientType { linear, radial }

/// 渐变配置项
class GradientConfig {
  final GradientType type;
  /// 渐变颜色
  final List<Color> colors;
  /// 渐变色截至
  final List<double>? stops;
  final AlignmentGeometry begin;
  final AlignmentGeometry end;
  final Rect rect;

  const GradientConfig.linear({
    required this.colors,
    this.stops, // 可选参数
    this.begin = Alignment.topCenter,
    this.end = Alignment.bottomCenter,
    required this.rect,
  }) : type = GradientType.linear;

  const GradientConfig.radial({
    required this.colors,
    this.stops,
    this.begin = Alignment.center,
    this.end = Alignment.center,
    required this.rect,
  }) : type = GradientType.radial;

  Gradient createGradient() {
    switch (type) {
      case GradientType.linear:
        return LinearGradient(
          colors: colors,
          stops: stops, // 使用自定义 stops
          begin: begin as Alignment,
          end: end as Alignment,
        );
      case GradientType.radial:
        return RadialGradient(
          colors: colors,
          stops: stops, // 使用自定义 stops
          center: begin as Alignment,
          radius: 0.5,
        );
    }
  }
}

/// 可复用的渐变毛玻璃背景
class GradientGlassBackgroundContainer extends StatelessWidget {
  final double width;
  final double height;
  final Color backgroundColor;
  final BorderRadius borderRadius;
  final List<GradientConfig> gradients;
  final double blurRadius;
  final double frostedOpacity;
  final Color frostedColor;
  final Widget? child;

  const GradientGlassBackgroundContainer({
    super.key,
    this.width = double.infinity,
    this.height = double.infinity,
    this.backgroundColor = Colors.transparent,
    this.borderRadius = BorderRadius.zero,
    this.gradients = const [],
    this.blurRadius = 10,
    this.frostedOpacity = 0.15,
    this.frostedColor = Colors.white,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius,
      child: Stack(
        children: [
          // 背景渐变
          GradientBackgroundContainer(
            width: width,
            height: height,
            backgroundColor: backgroundColor,
            borderRadius: borderRadius,
            gradients: gradients,
          ),

          // 毛玻璃层
          FrostedGlassEffect(
            blurRadius: blurRadius,
            opacity: frostedOpacity,
            color: frostedColor,
            borderRadius: borderRadius,
            child: child,
          ),
        ],
      ),
    );
  }
}

/// 可复用的渐变背景（不带毛玻璃效果）
class GradientBackgroundContainer extends StatelessWidget {
  final double width;
  final double height;
  final Color backgroundColor;
  final BorderRadius borderRadius;
  final List<GradientConfig> gradients;
  final Widget? child;

  const GradientBackgroundContainer({
    super.key,
    this.width = double.infinity,
    this.height = double.infinity,
    this.backgroundColor = Colors.transparent,
    this.borderRadius = BorderRadius.zero,
    this.gradients = const [],
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        painter: GradientBackgroundPainter(
          backgroundColor: backgroundColor,
          borderRadius: borderRadius,
          gradients: gradients,
        ),
        child: child,
      ),
    );
  }
}

/// 渐变背景画笔
class GradientBackgroundPainter extends CustomPainter {
  final Color backgroundColor;
  final BorderRadius borderRadius;
  final List<GradientConfig> gradients;

  GradientBackgroundPainter({
    required this.backgroundColor,
    required this.borderRadius,
    required this.gradients,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Offset.zero & size;
    final rrect = RRect.fromRectAndCorners(
      rect,
      topLeft: borderRadius.topLeft,
      topRight: borderRadius.topRight,
      bottomLeft: borderRadius.bottomLeft,
      bottomRight: borderRadius.bottomRight,
    );

    // 1. 裁剪圆角区域
    canvas.save();
    canvas.clipRRect(rrect);

    // 2. 绘制背景色
    final backgroundPaint = Paint()..color = backgroundColor;
    canvas.drawRect(rect, backgroundPaint);

    // 3. 绘制每个渐变层
    for (var config in gradients) {
      final shader = config.createGradient().createShader(config.rect);
      final paint = Paint()..shader = shader;
      canvas.drawRect(config.rect, paint);
    }

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// 毛玻璃效果
class FrostedGlassEffect extends StatelessWidget {
  final double blurRadius;
  final double opacity;
  final Color color;
  final BorderRadius borderRadius;
  final Widget? child;

  const FrostedGlassEffect({
    super.key,
    required this.blurRadius,
    required this.opacity,
    required this.color,
    required this.borderRadius,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: ClipRRect(
        borderRadius: borderRadius,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blurRadius, sigmaY: blurRadius),
          child: ColoredBox(
            color: color.withOpacity(opacity),
            child: child,
          ),
        ),
      ),
    );
  }
}
