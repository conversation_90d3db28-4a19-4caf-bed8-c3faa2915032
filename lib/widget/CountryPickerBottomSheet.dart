import 'package:flutter/material.dart';

import '../data/countries.dart';
import 'GradientBorderContainer.dart';

typedef CountryPickerCallback = void Function(Country country);

class CountryPickerDialogX extends StatelessWidget {
  final List<Country> countryList;
  final Country? selectedCountry;
  final CountryPickerCallback onCountrySelected;
  final String searchText;
  final String languageCode;
  final CountryPickerStyle? style;

  const CountryPickerDialogX({
    Key? key,
    required this.countryList,
    required this.onCountrySelected,
    this.selectedCountry,
    this.searchText = "Search country",
    this.languageCode = "en",
    this.style,
  }) : super(key: key);

  static void show(
    BuildContext context, {
    required List<Country> countryList,
    Country? selectedCountry,
    required CountryPickerCallback onCountrySelected,
    String searchText = "Search country",
    String languageCode = "en",
    CountryPickerStyle? style,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // 允许高度自适应内容
      backgroundColor: Colors.transparent, // 设置背景透明
      builder: (context) => _CountryPickerDialogContent(
        countryList: countryList,
        selectedCountry: selectedCountry,
        onCountrySelected: onCountrySelected,
        searchText: searchText,
        languageCode: languageCode,
        style: style,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink(); // 不再需要构建 UI
  }
}

class _CountryPickerDialogContent extends StatefulWidget {
  final List<Country> countryList;
  final Country? selectedCountry;
  final CountryPickerCallback onCountrySelected;
  final String searchText;
  final String languageCode;
  final CountryPickerStyle? style;

  const _CountryPickerDialogContent({
    Key? key,
    required this.countryList,
    required this.onCountrySelected,
    this.selectedCountry,
    this.searchText = "Search country",
    this.languageCode = "en",
    this.style,
  }) : super(key: key);

  @override
  __CountryPickerDialogContentState createState() =>
      __CountryPickerDialogContentState();
}

class __CountryPickerDialogContentState
    extends State<_CountryPickerDialogContent> {
  late ScrollController _scrollController;
  late TextEditingController _searchController;
  late List<Country> _filteredCountries;
  late Country _selectedCountry;

  @override
  void initState() {
    super.initState();

    _scrollController = ScrollController();
    _searchController = TextEditingController();
    _selectedCountry = widget.selectedCountry ?? widget.countryList.first;
    _filteredCountries = widget.countryList;
    // _scrollController.addListener(_handleScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearch(String value) {
    setState(() {
      _filteredCountries = widget.countryList.stringSearch(value);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.zero, // 或者根据需要设置固定 padding
      // 自动避让软键盘
      decoration: BoxDecoration(
        color: widget.style?.backgroundColor ??
            Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Center(
              child: Container(
                width: 60,
                height: 5,
                margin: const EdgeInsets.only(bottom: 10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFF8F8F8F),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      autofocus: false,
                      decoration: InputDecoration(
                        hintText: widget.searchText,
                        hintStyle: const TextStyle(color: Color(0xFF988B9A)),
                        border: const OutlineInputBorder(
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: EdgeInsets.zero
                      ),
                      onChanged: _onSearch,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: Image.asset(
                      'assets/images/icon_country_search.webp',
                      fit: BoxFit.cover,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 500,
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _filteredCountries.length,
              padding: const EdgeInsets.only(top: 1, bottom: 16),
              itemBuilder: (ctx, index) {
                final country = _filteredCountries[index];
                final isSelected = country.code == _selectedCountry.code;

                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: isSelected
                      ? GradientBorderContainer.single(
                          strokeWidth: 0.5,
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFF7253FA),
                              Color(0xFFFF3BDF),
                              Color(0xFF5E57FE),
                            ],
                            stops: [0.0, 0.32, 1.0],
                            begin: Alignment.bottomLeft,
                            end: Alignment.topRight,
                            tileMode: TileMode.decal,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 0, vertical: 12),
                            decoration: BoxDecoration(
                              color: const Color(0x802E174F),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: _buildCountryItem(country),
                          ),
                        )
                      : _buildCountryItemContainer(country),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountryItem(Country country) {
    return InkWell(
      onTap: () {
        setState(() {
          _selectedCountry = country;
        });
        widget.onCountrySelected(country);
        // 可选延迟 pop，让点击反馈更自然
        Future.delayed(const Duration(milliseconds: 200), () {
          Navigator.of(context).pop();
        });
      },
      child: Row(
        children: [
          // 国家名
          Expanded(
            flex: 3,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                country.country,
                style: widget.style?.countryNameStyle ??
                    const TextStyle(fontSize: 14),
              ),
            ),
          ),
          // 区号
          Expanded(
            flex: 1,
            child: Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Text(
                '+${country.number}',
                textAlign: TextAlign.right,
                style: widget.style?.countryCodeStyle ??
                    const TextStyle(fontSize: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

// 默认状态下的 item 容器
  Widget _buildCountryItemContainer(Country country) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
      color: Colors.transparent,
      child: _buildCountryItem(country),
    );
  }
}

class CountryPickerStyle {
  final Color? backgroundColor;
  final Color? searchFieldCursorColor;
  final EdgeInsetsGeometry? padding;
  final EdgeInsets? searchFieldPadding;
  final TextStyle? countryNameStyle;
  final TextStyle? countryCodeStyle;
  final Widget? listTileDivider;
  final EdgeInsets? listTilePadding;
  final InputDecoration? searchFieldInputDecoration;

  // 👇 新增：顶部间距
  final double? topSpacing;

  const CountryPickerStyle({
    this.backgroundColor,
    this.searchFieldCursorColor,
    this.padding,
    this.searchFieldPadding,
    this.countryNameStyle,
    this.countryCodeStyle,
    this.listTileDivider,
    this.listTilePadding,
    this.searchFieldInputDecoration,
    this.topSpacing,
  });
}
