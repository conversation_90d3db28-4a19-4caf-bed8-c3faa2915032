import 'package:flutter/material.dart';

/// 带有 Header 和 Footer 的自定义列表视图
class CustomListView extends StatefulWidget {
  final Widget? header;
  final Widget? footer;
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  /// 是否自动滚动到底部（当用户在底部且有新数据时）
  final bool autoScrollToBottom;
  /// 判断是否接近底部的阈值（像素）
  final double bottomThreshold;
  /// 用于获取GlobalKey以便外部调用滚动检查
  final GlobalKey<CustomListViewState>? listViewKey;

  const CustomListView({
    Key? key,
    this.header,
    this.footer,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.autoScrollToBottom = false,
    this.bottomThreshold = 100.0,
    this.listViewKey,
  }) : super(key: key);

  @override
  CustomListViewState createState() => CustomListViewState();
}

class CustomListViewState extends State<CustomListView> {
  late ScrollController _scrollController;
  bool _isUserScrolling = false;
  int _previousItemCount = 0;
  double _previousMaxScrollExtent = 0.0;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _previousItemCount = widget.itemCount;
    
    // 监听滚动事件，检测用户是否在手动滚动
    _scrollController.addListener(_onScrollChanged);
  }

  @override
  void didUpdateWidget(CustomListView oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 延迟检查滚动范围变化，确保布局已经更新
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkForScrollChanges();
    });
    
    _previousItemCount = widget.itemCount;
  }

  /// 检查滚动变化（新数据或内容高度变化）
  void _checkForScrollChanges() {
    if (!widget.autoScrollToBottom || !_scrollController.hasClients) return;
    
    final currentMaxScrollExtent = _scrollController.position.maxScrollExtent;
    final hasItemCountIncreased = widget.itemCount > _previousItemCount;
    final hasContentExpanded = currentMaxScrollExtent > _previousMaxScrollExtent;
    
    // 如果有新数据添加或内容展开，且用户在底部附近，则自动滚动
    if ((hasItemCountIncreased || hasContentExpanded) && _isNearBottom()) {
      _scrollToBottom();
    }
    
    _previousMaxScrollExtent = currentMaxScrollExtent;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScrollChanged() {
    // 这里可以根据滚动速度判断是否为用户主动滚动
    _isUserScrolling = true;
    
    // 延迟重置标志，避免自动滚动被误判为用户滚动
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _isUserScrolling = false;
      }
    });
  }

  /// 检查是否接近底部
  bool _isNearBottom() {
    if (!_scrollController.hasClients) return true;
    
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    
    return (maxScroll - currentScroll) <= widget.bottomThreshold;
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (!_scrollController.hasClients) return;
    
    _scrollController.animateTo(
      _scrollController.position.maxScrollExtent,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  /// 外部调用：检查并执行自动滚动（用于展开收起等操作后）
  void checkAndScrollIfNeeded() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkForScrollChanges();
    });
  }

  /// 外部调用：针对特定item展开时的智能滚动
  void scrollForItemExpansion(int itemIndex, int totalItems) {
    if (!_scrollController.hasClients) return;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewportHeight = _scrollController.position.viewportDimension;
      final currentScroll = _scrollController.position.pixels;
      final maxScroll = _scrollController.position.maxScrollExtent;
      
      // 估算每个item的平均高度
      final estimatedItemHeight = maxScroll / totalItems;
      
      // 计算当前item在列表中的大概位置
      final itemStartPosition = itemIndex * estimatedItemHeight;
      final itemEndPosition = (itemIndex + 1) * estimatedItemHeight;
      
      // 判断是否是屏幕内的最后一个或倒数第二个可见item
      final visibleAreaStart = currentScroll;
      final visibleAreaEnd = currentScroll + viewportHeight;
      
      // 如果item在屏幕底部区域且展开后可能超出屏幕
      if (itemStartPosition >= visibleAreaEnd - estimatedItemHeight * 2 && 
          itemEndPosition >= visibleAreaEnd - estimatedItemHeight) {
        
        // 向上滚动一些距离，让展开的内容可见
        final scrollOffset = estimatedItemHeight * 0.8; // 滚动约0.8个item的高度
        final targetScroll = (currentScroll + scrollOffset).clamp(0.0, maxScroll);
        
        _scrollController.animateTo(
          targetScroll,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
        
        return; // 执行了特殊滚动，不再执行常规的底部检查
      }
      
      // 如果不在特殊位置，执行常规的底部自动滚动检查
      _checkForScrollChanges();
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      controller: _scrollController,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      slivers: [
        // Header
        if (widget.header != null)
          SliverToBoxAdapter(
            child: widget.header!,
          ),
        
        // 主要列表内容
        SliverPadding(
          padding: widget.padding ?? EdgeInsets.zero,
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              widget.itemBuilder,
              childCount: widget.itemCount,
            ),
          ),
        ),
        
        // Footer
        if (widget.footer != null)
          SliverToBoxAdapter(
            child: widget.footer!,
          ),
      ],
    );
  }
}

/// 带有分割线的自定义列表视图
class CustomListViewSeparated extends StatelessWidget {
  final Widget? header;
  final Widget? footer;
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final Widget Function(BuildContext context, int index) separatorBuilder;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const CustomListViewSeparated({
    Key? key,
    this.header,
    this.footer,
    required this.itemCount,
    required this.itemBuilder,
    required this.separatorBuilder,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      controller: controller,
      shrinkWrap: shrinkWrap,
      physics: physics,
      slivers: [
        // Header
        if (header != null)
          SliverToBoxAdapter(
            child: header!,
          ),
        
        // 主要列表内容（带分割线）
        SliverPadding(
          padding: padding ?? EdgeInsets.zero,
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final itemIndex = index ~/ 2;
                if (index.isEven) {
                  // 返回列表项
                  return itemBuilder(context, itemIndex);
                } else {
                  // 返回分割线
                  if (itemIndex < itemCount - 1) {
                    return separatorBuilder(context, itemIndex);
                  } else {
                    return const SizedBox.shrink();
                  }
                }
              },
              childCount: itemCount * 2 - 1,
            ),
          ),
        ),
        
        // Footer
        if (footer != null)
          SliverToBoxAdapter(
            child: footer!,
          ),
      ],
    );
  }
}

/// 可折叠 Header 的自定义列表视图
class CustomListViewWithCollapsibleHeader extends StatelessWidget {
  final Widget collapsibleHeader;
  final Widget? fixedHeader;
  final Widget? footer;
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final double collapsibleHeaderHeight;
  final bool pinHeader;

  const CustomListViewWithCollapsibleHeader({
    Key? key,
    required this.collapsibleHeader,
    this.fixedHeader,
    this.footer,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.collapsibleHeaderHeight = 200.0,
    this.pinHeader = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      controller: controller,
      shrinkWrap: shrinkWrap,
      physics: physics,
      slivers: [
        // 可折叠的 Header
        SliverAppBar(
          expandedHeight: collapsibleHeaderHeight,
          floating: false,
          pinned: pinHeader,
          automaticallyImplyLeading: false,
          flexibleSpace: FlexibleSpaceBar(
            background: collapsibleHeader,
          ),
        ),
        
        // 固定的 Header
        if (fixedHeader != null)
          SliverToBoxAdapter(
            child: fixedHeader!,
          ),
        
        // 主要列表内容
        SliverPadding(
          padding: padding ?? EdgeInsets.zero,
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              itemBuilder,
              childCount: itemCount,
            ),
          ),
        ),
        
        // Footer
        if (footer != null)
          SliverToBoxAdapter(
            child: footer!,
          ),
      ],
    );
  }
}

/// 使用示例的演示页面
class CustomListViewDemo extends StatelessWidget {
  const CustomListViewDemo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('自定义列表示例'),
      ),
      body: CustomListView(
        // 自定义 Header
        header: Container(
          height: 150,
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Colors.blue, Colors.purple],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star, color: Colors.white, size: 40),
                SizedBox(height: 8),
                Text(
                  '自定义 Header',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // 列表项数量
        itemCount: 20,
        
        // 列表项构建器
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Card(
              child: ListTile(
                leading: CircleAvatar(
                  child: Text('${index + 1}'),
                ),
                title: Text('列表项 ${index + 1}'),
                subtitle: Text('这是第 ${index + 1} 个项目'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('点击了项目 ${index + 1}')),
                  );
                },
              ),
            ),
          );
        },
        
        // 自定义 Footer
        footer: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              const Icon(Icons.done_all, size: 30, color: Colors.green),
              const SizedBox(height: 8),
              const Text(
                '列表结束',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '共 20 个项目',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }
}