import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Widget ContainerBox(
    {boxColor, //背景色
      borderColor, //边框色
      double? radius, //圆角
      double? topLeftRadius, //圆角
      double? topRightRadius, //圆角
      double? bottomLeftRadius, //圆角
      double? bottomRightRadius, //圆角
      alignment,//位置
      EdgeInsetsGeometry? margin, //外边距
      EdgeInsetsGeometry? padding, //内边距
      height, //高度
      width, //宽度
      jBStartColor, //背景开始渐变色
      jBEndColor, //背景结束渐变色
      borderWith,//边框宽度
      boxShadowColor,//阴影色
      blurRadius,//宽度
      dx=1.0,//偏移
      dy=1.0,//偏移
      begin,//渐变方向
      end,//渐变方向
      jBColors,//渐变颜色数组
      key,
      image,//背景图
      minHeight=0.0,//背景图
      minWidth=0.0,//背景图
      child //布局
    }) {
  return Container(
    key: key!=null?key:null,
    width: width != null ? width : null,
    height: height != null ? height : null,
    margin: margin != null ? margin : null,
    padding: padding != null ? padding : null,
    alignment: alignment!=null?alignment:null,
    constraints:BoxConstraints(
      minHeight: minHeight,
      minWidth: minWidth,
    ) ,
    decoration: new BoxDecoration(
      color: boxColor != null ? boxColor : Colors.white,
      boxShadow: boxShadowColor!=null?[BoxShadow(color: boxShadowColor, blurRadius: blurRadius!=null?blurRadius:15,offset:Offset (dx,dy))]:null,
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(radius != null ? radius.w : topLeftRadius!=null?topLeftRadius.w:0.w),
        topRight: Radius.circular(radius != null ? radius.w : topRightRadius!=null?topRightRadius.w:0.w),
        bottomLeft: Radius.circular(radius != null ? radius.w : bottomLeftRadius!=null?bottomLeftRadius.w:0.w),
        bottomRight: Radius.circular(radius != null ? radius.w : bottomRightRadius!=null?bottomRightRadius.w:0.w),
      ), image:image!=null? DecorationImage(
        image: AssetImage(image),
      fit: BoxFit.fill):null,
      border: new Border.all(
          width: borderWith!=null?borderWith:0.0, color: borderColor != null ? borderColor : Colors.transparent),
      gradient: jBStartColor != null||jBColors!=null
          ? LinearGradient(
        begin:begin!=null?begin: Alignment.centerLeft,
        end: end!=null?end:Alignment.centerRight,
        colors: jBColors!=null?jBColors:[
          jBStartColor,
          jBEndColor,
        ],
      )
          : null,
    ),
    child: child,
  );
}
