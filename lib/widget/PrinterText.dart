import 'package:flutter/material.dart';
import 'dart:async';

/// 打印机效果文本组件
/// 逐字显示文本内容，模拟打印机逐字打印的效果
class PrinterText extends StatefulWidget {
  /// 要显示的完整文本
  final String text;

  /// 每个字符打印的间隔时间
  final Duration charDuration;

  /// 文本样式
  final TextStyle? style;

  /// 是否一开始就显示所有文本
  final bool showAllAtOnce;

  /// 打印完成后的回调
  final VoidCallback? onPrinted;
  final TextAlign? textAlign;

  const PrinterText({
    Key? key,
    required this.text,
    this.charDuration = const Duration(milliseconds: 50),
    this.style,
    this.showAllAtOnce = false,
    this.onPrinted,
    this.textAlign,
  }) : super(key: key);

  @override
  State<PrinterText> createState() => _PrinterTextState();
}

class _PrinterTextState extends State<PrinterText> {
  /// 当前已显示的文本
  String _displayedText = '';

  /// 当前显示到的字符索引
  int _currentIndex = 0;

  /// 定时器
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    if (widget.showAllAtOnce) {
      _displayedText = widget.text;
    } else {
      _startPrinting();
    }
  }

  @override
  void didUpdateWidget(covariant PrinterText oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果文本发生变化，检查是否为增量更新
    if (oldWidget.text != widget.text) {
      // 如果新文本以旧文本开头，说明是增量更新
      if (widget.text.startsWith(oldWidget.text)) {
        // 只需要打印新增的部分
        // _currentIndex 保持不变，继续从之前的位置打印
      } else {
        // 如果不是增量更新（文本被替换），则重新开始打印
        _currentIndex = 0;
        _displayedText = '';
      }
      // 重新开始打印过程
      _timer.cancel();
      _startPrinting();
    }
  }

  /// 开始打印过程
  void _startPrinting() {
    _timer = Timer.periodic(widget.charDuration, (timer) {
      setState(() {
        if (_currentIndex < widget.text.length) {
          _displayedText += widget.text[_currentIndex];
          _currentIndex++;
        } else {
          timer.cancel();
          // 打印完成回调
          widget.onPrinted?.call();
        }
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _displayedText,
      textAlign:widget.textAlign!=null?widget.textAlign:null,
      style: widget.style,
    );
  }
}