import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

const Duration _kExpand = Duration(milliseconds: 200);

/* 折叠组件 */
class ExpansionCustom extends StatefulWidget {
  final ValueChanged<bool>? onExpansionChanged;
  final List<Widget>? children;
  final bool? initiallyExpanded;
  final bool? isExpanded;
  final EdgeInsetsGeometry? padding;
  Function? onCloseState;
  String? title;

   ExpansionCustom({
    this.onExpansionChanged,
    this.children = const <Widget>[],
    this.initiallyExpanded = false,
    this.isExpanded,
    this.padding,
    this.onCloseState,
    this.title='',
  });

  @override
  _ExpansionCustomState createState() => _ExpansionCustomState();
}

class _ExpansionCustomState extends State<ExpansionCustom>
    with SingleTickerProviderStateMixin {
  static final Animatable<double> _easeInTween =
      CurveTween(curve: Curves.easeIn);

  AnimationController? _controller;
  Animation<double>? _heightFactor;

  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: _kExpand, vsync: this);
    _heightFactor = _controller!.drive(_easeInTween);

    _isExpanded =
        PageStorage.of(context)?.readState(context) ?? widget.initiallyExpanded;
    if (_isExpanded) _controller!.value = 1.0;
  }

  diyHandle() {
    if (widget.isExpanded != null) {
      setState(() {
        if (widget.isExpanded!) {
          _controller!.forward();
        } else {
          _controller!.reverse().then<void>((void value) {
            if (!mounted) return;
            setState(() {});
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _controller!.dispose();
    super.dispose();
  }

  void _handleTap() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller!.forward();
      } else {
        _controller!.reverse().then<void>((void value) {
          if (!mounted) return;
          setState(() {});
        });
      }
      PageStorage.of(context)?.writeState(context, _isExpanded);
      if(widget.onCloseState!=null){
        widget.onCloseState!(_isExpanded);
      }
    });
    if (widget.onExpansionChanged != null)
      widget.onExpansionChanged!(_isExpanded);
  }

  @override
  Widget build(BuildContext context) {
    diyHandle();
    final bool closed = !_isExpanded && _controller!.isDismissed;
    return AnimatedBuilder(
      animation: _controller!.view,
      builder: (context, child) {
        return Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              GestureDetector(
                onTap: _handleTap,
                child: Container(
                  color: Colors.transparent,
                  width: double.infinity,
                  padding:
                      widget.padding ?? EdgeInsets.only(left: 10, right: 10),
                  child: Container(
                    margin: EdgeInsets.only(bottom: 10.w),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/ic_process_right.webp',
                          width: 16.w,
                          height: 16.h,
                        ),
                        SizedBox(
                          width: 12.w,
                        ),
                        Text(
                          '${widget.title}',
                          style: TextStyle(color: Colors.white, fontSize: 16.sp),
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Image.asset(
                          closed?'assets/images/ic_process_down.webp':'assets/images/ic_process_top.webp',
                          width: 14.w,
                          height: 14.h,
                        ),
                      ],
                    ),
                    alignment: Alignment.centerLeft,
                  ) ,
                ),
              ),
              // ClipRect(
              //   child: Align(
              //     heightFactor: _heightFactor!.value,
              //     child: child,
              //   ),
              // ),
              Container(
                child: child,
              )
            ],
          ),
        );
      },
      child: closed ? null : Column(children: widget.children!),
    );
  }
}
