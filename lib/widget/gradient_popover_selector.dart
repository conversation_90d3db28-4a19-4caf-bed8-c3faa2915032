import 'package:flutter/material.dart';
import 'package:popover/popover.dart';
import '../generated/l10n.dart';

class GradientPopoverSelector extends StatefulWidget {
  final List<Map<String, dynamic>> items;
  final int selectedIndex;
  final Function(int) onSelected;
  final Widget child;
  final bool showDeleteItem;
  final VoidCallback? onDelete;
  final double itemHeight;
  // 希望向上移动的距离（理想值）。
  final double lift;
  // 是否自动避免被 AppBar / 状态栏遮挡；true 时会在需要时减小实际位移。
  final bool autoClampSafeArea;
  // 覆盖在 AppBar 之上（使用自定义 Overlay，而不是库的 showPopover）。
  final bool coverAppBar;
  final double marginSize;
  final bool showSelected;

  const GradientPopoverSelector({
    Key? key,
    required this.items,
    required this.selectedIndex,
    required this.onSelected,
    required this.child,
    this.showDeleteItem = false,
    this.onDelete,
    this.itemHeight = 44.0,
    this.lift = 0,
    this.autoClampSafeArea = true,
    this.coverAppBar = false,
    this.marginSize = 16.0,
    this.showSelected = true,
  }) : super(key: key);

  @override
  State<GradientPopoverSelector> createState() =>
      _GradientPopoverSelectorState();
}

class _GradientPopoverSelectorState extends State<GradientPopoverSelector> {
  final GlobalKey _anchorKey = GlobalKey();
  OverlayEntry? _entry;

  void _removeEntry() {
    _entry?.remove();
    _entry = null;
  }

  void _showCustomOverlay(BuildContext context) {
    if (_entry != null) return; // 防止重复
    final overlay = Overlay.of(context, rootOverlay: true);
    final media = MediaQuery.of(context);
    RenderBox? rb = _anchorKey.currentContext?.findRenderObject() as RenderBox?;
    if (rb == null) return;
    Offset offset = rb.localToGlobal(Offset.zero);
    Size btnSize = rb.size;

    // 目标顶部位置：按钮底部向下展开，再减去提升
    double top = offset.dy + btnSize.height - widget.lift;
    // 允许覆盖 AppBar，不进行顶部裁剪，仅做最小防护
    if (top < media.padding.top * -1) top = media.padding.top * -1; // 不无限制往上
    double right = media.size.width -
        (offset.dx + btnSize.width) +
        16; // 与之前 margin right:16 对齐

    _entry = OverlayEntry(
      builder: (ctx) {
        return Stack(
          children: [
            // 半透明遮罩，点击关闭
            Positioned.fill(
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: _removeEntry,
                child: Container(color: Colors.black26),
              ),
            ),
            Positioned(
              top: top,
              right: right,
              child: Material(
                color: Colors.transparent,
                child: _buildPopoverContent(ctx, onClose: _removeEntry),
              ),
            ),
          ],
        );
      },
    );
    overlay.insert(_entry!);
  }

  void _showPopover(BuildContext context) {
    if (widget.coverAppBar) {
      _showCustomOverlay(context);
      return;
    }
    // 估算弹层高度
    final double popHeight = widget.items.length * widget.itemHeight +
        (widget.showDeleteItem ? widget.itemHeight : 0) +
        18;
    // 计算锚点位置
    RenderBox? rb = _anchorKey.currentContext?.findRenderObject() as RenderBox?;
    double effectiveLift = widget.lift;
    if (rb != null && widget.autoClampSafeArea && widget.lift > 0) {
      Offset offset = rb.localToGlobal(Offset.zero);
      double anchorTop = offset.dy; // 锚点顶部到屏幕顶部距离
      final media = MediaQuery.of(context);
      double safeTop = media.padding.top + kToolbarHeight; // 典型 AppBar 底部位置
      // 锚点与安全顶部之间可用空间
      double available = anchorTop - safeTop - 8; // 预留 8 像素缓冲
      if (available < 0) available = 0;
      // 不能超过可用空间，否则会被遮挡
      if (effectiveLift > available) {
        effectiveLift = available;
      }
      // 还要保证整体不会超出屏幕顶部（含状态栏）；如果弹层高度比可用空间还大，退回尽量安全位置
      if (anchorTop - effectiveLift < media.padding.top) {
        effectiveLift = anchorTop - media.padding.top - 8;
        if (effectiveLift < 0) effectiveLift = 0;
      }
    }

    double bo = MediaQuery.of(context).padding.bottom;
    showPopover(
      context: context,
      bodyBuilder: (context) {
        return Transform.translate(
          offset: Offset(0, -effectiveLift),
          child: _buildPopoverContent(context),
        );
      },
      direction: bo > 0 ? PopoverDirection.top : PopoverDirection.bottom,
      arrowHeight: 0,
      arrowWidth: 0,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black26,
      height: popHeight,
    );
  }

  Widget _buildPopoverContent(BuildContext context, {VoidCallback? onClose}) {
    return Material(
      color: Colors.transparent,
      child: Container(
        margin: EdgeInsets.only(right: widget.marginSize),
        decoration: BoxDecoration(
          border: Border.all(color: Color.fromRGBO(255, 59, 233, 1), width: 1),
          borderRadius: BorderRadius.circular(12),
          color: Color.fromRGBO(46, 23, 79, 0.9),
        ),
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: IntrinsicWidth(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              ...List.generate(widget.items.length, (index) {
                final item = widget.items[index];
                final isSelected = widget.selectedIndex == index;
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      onTap: () {
                        if (widget.coverAppBar) {
                          onClose?.call();
                        } else {
                          Navigator.of(context).pop();
                        }
                        FocusScope.of(context).unfocus();
                        Future.microtask(() => widget.onSelected(index));
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2.0, horizontal: 8.0),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: widget.showSelected
                                ? (isSelected
                                    ? const Color.fromRGBO(115, 61, 125, 0.47)
                                    : Colors.transparent)
                                : Colors.transparent,
                          ),
                          height: 40,
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Image.asset(
                                item['icon'],
                                width: 20,
                                height: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  item['text'],
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    if (index != widget.items.length - 1 &&
                        widget.showSelected == false)
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        height: 1,
                        color: const Color.fromRGBO(115, 61, 125, 0.47),
                      ),
                  ],
                );
              }),
              if (widget.showDeleteItem)
                GestureDetector(
                  onTap: () {
                    widget.onDelete?.call();
                    if (widget.coverAppBar) {
                      onClose?.call();
                    } else {
                      Navigator.of(context).pop();
                    }
                    FocusScope.of(context).unfocus();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 4.0, horizontal: 18.0),
                    child: Container(
                      height: 36,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.transparent,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Image.asset(
                            'assets/groupChat/delete.png',
                            width: 20,
                            height: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            S.of(context).remove,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color.fromRGBO(214, 66, 64, 1),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showPopover(context),
      child: KeyedSubtree(key: _anchorKey, child: widget.child),
    );
  }
}
