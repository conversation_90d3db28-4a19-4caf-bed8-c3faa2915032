import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

TextSpan RichTextWithLinks({
  required String text,
  required Map<String, VoidCallback> links,
  TextStyle? style,
}) {
  final List<TextSpan> spans = [];
  int currentIndex = 0;

  links.forEach((term, action) {
    final RegExp regex = RegExp(RegExp.escape(term));
    regex.allMatches(text).forEach((match) {
      if (currentIndex < match.start) {
        spans.add(TextSpan(text: text.substring(currentIndex, match.start)));
      }
      spans.add(
        TextSpan(
          text: term,
          style: const TextStyle(
            color: Colors.white,
            decoration: TextDecoration.underline,
          ),
          recognizer: TapGestureRecognizer()..onTap = action,
        ),
      );
      currentIndex = match.end;
    });
  });

  if (currentIndex < text.length) {
    spans.add(TextSpan(text: text.substring(currentIndex)));
  }

  return TextSpan(style: style, children: spans);
}
