import 'package:flutter/material.dart';

/// 悬浮可拖动组件
/// 
/// 功能特性：
/// - 支持拖动手势
/// - 边界检测，防止拖出屏幕
/// - 自动吸附到屏幕边缘
/// - 可自定义样式和内容
/// - 支持点击事件
/// - 支持设置初始位置
/// 
/// 使用示例：
/// ```dart
/// FloatingDragWidget(
///   child: Icon(Icons.chat, color: Colors.white),
///   onTap: () => print('点击了悬浮按钮'),
///   backgroundColor: Colors.blue,
///   size: Size(60, 60),
///   initialPosition: Offset(100, 200),
///   autoEdgeAdsorption: true,
/// )
/// ```
class FloatingDragWidget extends StatefulWidget {
  /// 子组件内容
  final Widget child;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 背景颜色
  final Color backgroundColor;
  
  /// 组件大小
  final Size size;
  
  /// 初始位置，如果不设置则默认在右下角
  final Offset? initialPosition;
  
  /// 是否启用边缘自动吸附
  final bool autoEdgeAdsorption;
  
  /// 边缘吸附的边距
  final double edgeMargin;
  
  /// 圆角半径
  final double borderRadius;
  
  /// 阴影
  final List<BoxShadow>? boxShadow;
  
  /// 拖动时的透明度
  final double dragOpacity;
  
  /// 是否可拖动
  final bool draggable;

  const FloatingDragWidget({
    Key? key,
    required this.child,
    this.onTap,
    this.backgroundColor = Colors.blue,
    this.size = const Size(56, 56),
    this.initialPosition,
    this.autoEdgeAdsorption = true,
    this.edgeMargin = 10.0,
    this.borderRadius = 28.0,
    this.boxShadow,
    this.dragOpacity = 0.8,
    this.draggable = true,
  }) : super(key: key);

  @override
  State<FloatingDragWidget> createState() => _FloatingDragWidgetState();
}

class _FloatingDragWidgetState extends State<FloatingDragWidget>
    with TickerProviderStateMixin {
  late Offset _position;
  bool _isDragging = false;
  late AnimationController _animationController;
  Animation<Offset>? _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // 如果设置了初始位置，直接使用；否则先设置一个临时位置，然后在下一帧设置真正的默认位置
    if (widget.initialPosition != null) {
      _position = widget.initialPosition!;
    } else {
      // 设置一个临时的初始位置，避免显示在(0,0)
      _position = const Offset(100, 100);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _setDefaultPosition();
      });
    }
  }

  @override
  void dispose() {
    _animation?.removeListener(_animationListener);
    _animationController.dispose();
    super.dispose();
  }

  /// 设置默认位置（屏幕中间偏右下，然后自动吸附）
  void _setDefaultPosition() {
    if (mounted) {
      final screenSize = MediaQuery.of(context).size;
      // 先设置到屏幕中间偏右下的位置
      final initialPos = Offset(
        screenSize.width * 0.7, // 屏幕宽度的70%位置
        screenSize.height * 0.7, // 屏幕高度的70%位置
      );
      
      setState(() {
        _position = initialPos;
      });
      
      // 如果启用了自动吸附，延迟一小段时间后执行吸附动画
      if (widget.autoEdgeAdsorption) {
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            print('FloatingDragWidget: 执行自动吸附，当前位置: $_position');
            _autoAdsorptionToEdge();
          }
        });
      }
    }
  }

  /// 处理拖动更新
  void _onPanUpdate(DragUpdateDetails details) {
    if (!widget.draggable) return;
    
    setState(() {
      _position += details.delta;
      _isDragging = true;
    });
  }

  /// 处理拖动结束
  void _onPanEnd(DragEndDetails details) {
    if (!widget.draggable) return;
    
    setState(() {
      _isDragging = false;
    });

    if (widget.autoEdgeAdsorption) {
      _autoAdsorptionToEdge();
    }
  }

  /// 自动吸附到屏幕边缘
  void _autoAdsorptionToEdge() {
    final screenSize = MediaQuery.of(context).size;
    final centerX = _position.dx + widget.size.width / 2;
    final centerY = _position.dy + widget.size.height / 2;

    // 计算最终位置
    Offset targetPosition;
    
    // 判断应该吸附到左边还是右边
    if (centerX < screenSize.width / 2) {
      // 吸附到左边
      targetPosition = Offset(
        widget.edgeMargin,
        _clampY(_position.dy, screenSize),
      );
      print('FloatingDragWidget: 吸附到左边，目标位置: $targetPosition');
    } else {
      // 吸附到右边
      targetPosition = Offset(
        screenSize.width - widget.size.width - widget.edgeMargin,
        _clampY(_position.dy, screenSize),
      );
      print('FloatingDragWidget: 吸附到右边，目标位置: $targetPosition');
    }

    // 执行动画
    print('FloatingDragWidget: 开始执行吸附动画，从 $_position 到 $targetPosition');
    _animateToPosition(targetPosition);
  }

  /// 限制Y坐标在屏幕范围内
  double _clampY(double y, Size screenSize) {
    final topSafeArea = MediaQuery.of(context).padding.top;
    final bottomSafeArea = MediaQuery.of(context).padding.bottom;
    
    return y.clamp(
      topSafeArea + widget.edgeMargin,
      screenSize.height - widget.size.height - bottomSafeArea - widget.edgeMargin,
    );
  }

  /// 限制X坐标在屏幕范围内
  double _clampX(double x, Size screenSize) {
    return x.clamp(
      widget.edgeMargin,
      screenSize.width - widget.size.width - widget.edgeMargin,
    );
  }

  /// 动画移动到指定位置
  void _animateToPosition(Offset targetPosition) {
    final begin = _position;
    final end = targetPosition;
    
    // 如果目标位置和当前位置相同，不需要动画
    if ((begin - end).distance < 1.0) {
      print('FloatingDragWidget: 位置相同，跳过动画');
      return;
    }
    
    print('FloatingDragWidget: 准备执行动画，距离: ${(begin - end).distance}');
    
    // 重置动画控制器
    _animationController.reset();
    
    _animation = Tween<Offset>(begin: begin, end: end).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    
    // 移除之前的监听器，避免重复添加
    _animation?.removeListener(_animationListener);
    _animation!.addListener(_animationListener);
    
    print('FloatingDragWidget: 开始动画执行');
    _animationController.forward();
  }
  
  /// 动画监听器
  void _animationListener() {
    if (mounted && _animation != null) {
      setState(() {
        _position = _animation!.value;
      });
    }
  }

  /// 确保位置在屏幕边界内
  Offset _ensureInBounds(Offset position, Size screenSize) {
    return Offset(
      _clampX(position.dx, screenSize),
      _clampY(position.dy, screenSize),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final safePosition = _ensureInBounds(_position, screenSize);

    return Positioned(
      left: safePosition.dx,
      top: safePosition.dy,
      child: GestureDetector(
        onTap: widget.onTap,
        onPanUpdate: _onPanUpdate,
        onPanEnd: _onPanEnd,
        child: AnimatedOpacity(
          opacity: _isDragging ? widget.dragOpacity : 1.0,
          duration: const Duration(milliseconds: 150),
          child: Container(
            width: widget.size.width,
            height: widget.size.height,
            decoration: BoxDecoration(
              color: widget.backgroundColor,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              boxShadow: widget.boxShadow ?? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(child: widget.child),
          ),
        ),
      ),
    );
  }
}

/// 悬浮拖动容器
/// 
/// 用于包装需要悬浮拖动功能的页面，提供一个Stack容器
/// 
/// 使用示例：
/// ```dart
/// FloatingDragContainer(
///   child: YourPageContent(),
///   floatingWidgets: [
///     FloatingDragWidget(
///       child: Icon(Icons.chat),
///       onTap: () => print('聊天'),
///     ),
///     FloatingDragWidget(
///       child: Icon(Icons.phone),
///       onTap: () => print('电话'),
///       initialPosition: Offset(50, 100),
///     ),
///   ],
/// )
/// ```
class FloatingDragContainer extends StatelessWidget {
  /// 主要内容
  final Widget child;
  
  /// 悬浮组件列表
  final List<FloatingDragWidget> floatingWidgets;

  const FloatingDragContainer({
    Key? key,
    required this.child,
    this.floatingWidgets = const [],
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        ...floatingWidgets,
      ],
    );
  }
}