import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


enum EditorInputType { text, num, twoDecimal, multiline }

//无背景的TextField
class EditText extends StatelessWidget {
  Color? hintColor; //提示颜色
  Color? textColor; //输入字颜色
  double size; //输入文字大小
  double pad; //输入框内部边框
  String? hint; //提示内容
  int? maxLine; //设置最小行数
  int? maxLength; //设置最大长度
  int? minLine; //设置最小行数
  TextInputType inputType = TextInputType.text; //输入框类型
  TextEditingController controller; //监听
  bool isInt = false; //是否整形
  Color? cursorColor; //光标颜色
  Function()? onEditingComplete; //回车监听
  EditorInputType editorInputType;
  FocusNode? focusNode;
  TextInputAction textInputAction;
  Function? onSubmitted; //动作按钮监听
  double? textHeight;
  bool isRight;
  bool obscureText;
  bool isBold; //是否加粗
  bool isAutofocus; //光标
  double? height;
  String? fontFamily;

  EditText(this.controller,
      {this.hintColor,
      this.textColor,
      this.size = 0,
      this.pad = 0,
      this.hint,
      this.maxLine,
      this.maxLength,
      this.editorInputType = EditorInputType.text,
      this.minLine,
      this.onEditingComplete,
      this.onSubmitted,
      this.height,
        this.focusNode,
      this.fontFamily,
      this.cursorColor,
      this.isBold = false,
      this.obscureText = false,
      this.isAutofocus = false,
      this.isRight = false,
      this.textHeight,
      this.textInputAction = TextInputAction.unspecified}) {
    switch (editorInputType) {
      case EditorInputType.text:
        inputType = TextInputType.text;
        isInt = false;
        break;
      case EditorInputType.num:
        inputType = TextInputType.number;
        isInt = true;
        break;
      case EditorInputType.twoDecimal:
        if (Platform.isAndroid) {
          inputType = TextInputType.number;
        } else {
          inputType = TextInputType.numberWithOptions(decimal: true);
        }
        isInt = false;
        break;
      case EditorInputType.multiline:
        inputType = TextInputType.multiline;
        isInt = false;
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isSub = false;
    return RawKeyboardListener(
        autofocus: true,
        onKey: (event) {
          if (event.runtimeType == RawKeyDownEvent) {
            if (event.physicalKey == PhysicalKeyboardKey.enter) {
              if (onSubmitted != null) {
                FocusScope.of(context).requestFocus(focusNode);
                onSubmitted!();
                isSub = true;
              }
            }
          }
        },
        focusNode: FocusNode(),
        child: Container(
            color: Colors.transparent,
            height: height != null ? height : null,
            child: new TextField(
              keyboardType: inputType,
              keyboardAppearance: Brightness.light,
              controller: controller,
              cursorColor:
                  cursorColor == null ? Colors.white : cursorColor,
              maxLines: maxLine,
              minLines: minLine,
              autofocus: isAutofocus,
              maxLength: maxLength,
              focusNode: focusNode,
              obscureText: obscureText,
              enableInteractiveSelection: true,
              textInputAction: textInputAction,
              onSubmitted: (v) {
                if (Platform.isIOS) {
                  FocusScope.of(context).requestFocus(focusNode);
                } else {
                  if (onSubmitted != null) {
                    FocusScope.of(context).requestFocus(focusNode);
                    if (isSub == false) {
                      onSubmitted!();
                    }
                  }
                }
              },
              onEditingComplete:
                  onEditingComplete == null ? null : onEditingComplete,
              decoration: InputDecoration(
                counterText: '',
                isDense: true,
                contentPadding:
                    EdgeInsets.all(ScreenUtil().setSp(pad == 0 ? 1 : pad)),
                hintText: hint == null ? '' : hint,
                border: InputBorder.none,
                hintStyle: TextStyle(
                    color:
                        hintColor == null ? Colors.grey : hintColor,
                    fontFamily: fontFamily != null ? fontFamily : null,
                    fontSize: ScreenUtil().setSp(size == 0 ? 16 : size),
                    fontWeight:
                        isBold ? FontWeight.bold: null ),
              ),
              textAlign: this.isRight ? TextAlign.right : TextAlign.left,
              style: TextStyle(
                  fontFamily: fontFamily != null ? fontFamily : null,
                  color: textColor == null ? Colors.white : textColor,
                  fontSize: ScreenUtil().setSp(size == 0 ? 16 : size),
                  height: textHeight != null ? textHeight : 1.4,
                  fontWeight: isBold ? FontWeight.bold : null),
            )));
  }
}
