import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

Widget UrlImage(String url,
    {int radius = 0,
    width,
    height,
    memHeight = 500,
    memWidth = 700,
    fit = BoxFit.cover,
    errorWidget,
    isZheng = true}) {
  return ClipRRect(
    borderRadius: BorderRadius.circular(radius.w),
    child: url.contains('.svg')
        ? SvgPicture.network(
            url,
            width: width,
            height: height,
            fit: fit,
            placeholderBuilder: (context) => Container(
              child: errorWidget != null ? errorWidget : SizedBox(),
            ),
          )
        : CachedNetworkImage(
            imageUrl: url,
            // memCacheHeight: isZheng ? memHeight : null,
            memCacheWidth: isZheng ? memWidth : null,
            width: width,
            height: height,
            fit: fit,
            placeholder: (context, url) => Container(
              child: errorWidget != null ? errorWidget : SizedBox(),
            ),
            errorWidget: (context, url, error) {
              return errorWidget != null ? errorWidget : SizedBox();
            },
          ),
  );
}
