import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MessageInputSucessCopyWidget extends StatelessWidget {
  const MessageInputSucessCopyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 206.w,
      height: 36.w,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: Color.fromRGBO(13, 13, 13, 0.8)),
      child: Row(
        children: [
          Image.asset("assets/groupChat/copied.png"),
          Text(
            'Message Copied',
            style: TextStyle(
                fontSize: 16, color: Colors.white, fontWeight: FontWeight.w400),
          )
        ],
      ),
    );
  }
}
