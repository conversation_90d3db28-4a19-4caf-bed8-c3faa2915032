import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class ComAppBar extends AppBar {
  ComAppBar(
      BuildContext context,
      String title, {
        double elevation = 0,
        List<Widget>? actions,
        Color? textColor,
        Color? backgroundColor,
        bool showLeading = true,
        bool iSCenterTitle = true,
        icBack,
        Function? onPop,
        Widget? leading,
        EdgeInsetsGeometry? margin,
        Color? leadingColor,
        double titleFontSize = 16,
        FontWeight? titleFontWeight ,
      }) : super(
    title: Container(
      margin: margin,
      child: Text(
        title,
        maxLines: 1,
        textAlign: TextAlign.center,
        style: TextStyle(
          color: textColor == null ? Colors.white : textColor,
          fontSize: titleFontSize,
          fontWeight: titleFontWeight!=null?titleFontWeight:null,
        ),
      ),
    ),
    titleSpacing: 0.1,
    leading: showLeading
        ? InkWell(
      onTap: () {
        if (onPop != null) {
          onPop();
        } else {
          Get.back();
        }
      },
      child: Container(
        padding: EdgeInsets.only(
          left: 15,
          right: 15,
        ),
        child: Image.asset(
          icBack != null ? icBack : 'assets/images/icon_back.webp',
          width: 41,
          height: 41,
          color: leadingColor ?? Colors.white,//默认统一是白色，不要修改
        ),
      ),
    )
        : leading,
    centerTitle: iSCenterTitle,
    elevation: elevation,
    actions: actions,
    toolbarHeight: kToolbarHeight,
    iconTheme: IconThemeData(color: Colors.black87),
    backgroundColor:
    backgroundColor == null ? Colors.transparent : backgroundColor,
  );
}
