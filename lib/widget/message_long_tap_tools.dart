import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/logger_utils.dart';

import '../generated/l10n.dart';
import '../module/chat/group/message_chat/enum/group_chat_message_enum.dart';

class MessageLongTapTools {
  static OverlayEntry? _overlayEntry;

  double _scale = 0.3;
  final List<MessageItemModel> messageItems = [
    MessageItemModel(
        message: "引用",
        imgStr: "assets/icon/icon_quote.png",
        type: MessageItemType.quote),
    MessageItemModel(
        message: "复制",
        imgStr: "assets/icon/icon_copy.png",
        type: MessageItemType.copy),
  ];

  static void showCustomOverlay(BuildContext context,
      ValueChanged<MessageItemType> onSelected, bool isRight,
      {bool isOnlyCopy = false, Function? onDismiss,GroupChatMessageContentType? contentType}) {
    final overlay = Overlay.of(context);
    Widget child = context.widget;
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final offset = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    final screenHeight = MediaQuery.of(context).size.height;

    final double maxHeight = screenHeight -
        MediaQuery.of(context).padding.bottom -
        kBottomNavigationBarHeight -
        kToolbarHeight -
        84.w;
    final showAbove = offset.dy + size.height > maxHeight;
    bool isScale = size.height > maxHeight;
    LoggerUtils.e("showAbove:$showAbove");
    LoggerUtils.e("maxHeight-----${maxHeight}");
    LoggerUtils.e("offset.dy------${offset.dy}");
    _overlayEntry = OverlayEntry(builder: (ctx) {
      return AnimatedScaleOverlay(
        offset: offset,
        isOnlyCopy: isOnlyCopy,
        contentType: contentType,
        size: size,
        showAbove: showAbove,
        isRight: isRight,
        isScale: isScale,
        maxHeight: maxHeight,
        widgetHeight: size.height,
        onSelected: onSelected,
        onDismiss: () {
          hideCustomOverlay();
          onDismiss?.call();
        },
        child: child,
      );
    });

    overlay.insert(_overlayEntry!);
  }

  static void hideCustomOverlay() {
    if (_overlayEntry == null) return;
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

class AnimatedScaleOverlay extends StatefulWidget {
  final Offset offset;
  final Size size;
  final bool showAbove;
  final bool isRight;
  final GroupChatMessageContentType?  contentType;
  final Widget child;
  final bool isScale;
  final bool isOnlyCopy;
  final double maxHeight;
  final double widgetHeight;
  final Function(MessageItemType) onSelected;
  final VoidCallback onDismiss;

  const AnimatedScaleOverlay({
    super.key,
    required this.offset,
    required this.size,
    required this.showAbove,
    required this.isRight,
    required this.child,
    required this.onSelected,
    required this.onDismiss,
    required this.isOnlyCopy, required this.isScale, required this.maxHeight, required this.widgetHeight,this.contentType,
  });

  @override
  State<AnimatedScaleOverlay> createState() => _AnimatedScaleOverlayState();
}

class _AnimatedScaleOverlayState extends State<AnimatedScaleOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  double sizeWidth = 0;
  Offset? offset;
  bool? isShowAbove;
  double radio = 0.25;
  double maxHeight = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // widget.widgetHeight / widget.maxHeight
    radio = widget.isScale ? 0.75 : 0.95;
    _scaleAnimation = Tween<double>(
      begin: 1,
      end: radio,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    // 启动进入动画
    _controller.forward();
    sizeWidth = widget.isOnlyCopy ? 71.w : 152.w;
    isShowAbove = widget.showAbove;
    if(widget.isScale) {
      offset = Offset(widget.offset.dx, 0.w);
      isShowAbove = false;
    } else {
      offset = widget.offset;
    }
    maxHeight =  widget.isScale ? 1.sh - kToolbarHeight - kBottomNavigationBarHeight : widget.maxHeight;
    CmUtils.zhenDong();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // 添加退出动画方法
  Future<void> animateOut() async {
    await _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        // 先播放退出动画，再执行回调
        await animateOut();
        widget.onDismiss();
      },
      child: Stack(
        children: [
          Positioned.fill(
            child: BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: ColoredBox(color: const Color(0xCC1D1236)),
            ),
          ),
          Positioned(
            left: 0,
            top: offset?.dy,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: 1.sw,
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _scaleAnimation.value,
                        child: Opacity(
                          opacity: _opacityAnimation.value,
                          child: widget.isScale ? ConstrainedBox(
                            constraints: BoxConstraints(
                              maxHeight: maxHeight,
                            ),
                            child: SingleChildScrollView(
                              physics: const ClampingScrollPhysics(),
                              padding: EdgeInsets.only(left: 9.w, right: 9.w,top: 50.w,bottom: 35.w),
                                child: IgnorePointer(child: widget.child)),
                          ) : IgnorePointer(child: widget.child),
                        ),
                      );
                    }),
              ),
            ),
          ),
          _buildPositionedMenu(),
        ],
      ),
    );
  }

  Widget _buildPositionedMenu() {
    double? left, right, top;

    if (widget.isOnlyCopy) {
      if (widget.isScale) {
        top = maxHeight * radio +10.w + kToolbarHeight + MediaQuery.paddingOf(context).top;
        right = 46.w / radio;
      } else  {
        right = 16.w;
        left = null;
        if (isShowAbove!) {
          top = widget.offset.dy - 47.w - 5.w;
        } else {
          top = widget.offset.dy + widget.size.height + 5.w;
        }
      }

    } else {
      if (widget.isScale) {
        if (widget.isRight) {
          right = 68.w / radio;
          left = null;
        } else {
          left = 68.w / radio;
          right = null;
        }
        top = maxHeight * radio +14.w + kToolbarHeight + MediaQuery.paddingOf(context).top;
      } else {
        if (widget.isRight) {
          right = 58.w;
          left = null;
        } else {
          left = 58.w;
          right = null;
        }
        if (isShowAbove!) {
          if (widget.contentType == null) {
            top = widget.offset.dy - 47.w + 8.w;
          } else {
            top = widget.offset.dy - 47.w + (widget.contentType == GroupChatMessageContentType.file ? -4.w : 8.w);
          }
        } else {
          if (widget.contentType == null) {
            top = widget.offset.dy + widget.size.height + 8.w;
          } else {
            top = widget.offset.dy +
                widget.size.height -
                (widget.contentType == GroupChatMessageContentType.textAndFile ? 30.w : (widget.contentType == GroupChatMessageContentType.file ? 4.w :8.w));
          }
        }
      }
    }

    return Positioned(
      left: left,
      right: right,
      top: top,
      child: CustomPaint(
        size: Size(sizeWidth, 47.w),
        painter: SvgShapePainter(
          triangleUp:isShowAbove!,
          isFromMe: widget.isRight,
        ),
        child: Container(
          width: sizeWidth,
          height: 47.w,
          alignment: Alignment.center,
          padding: isShowAbove!
              ? EdgeInsets.only(bottom: 8.w)
              : EdgeInsets.only(top: 8.w),
          child: widget.isOnlyCopy
              ? _buildMenuButton(S.of(Get.context!).copy, MessageItemType.copy)
              : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildMenuButton(
                        S.of(Get.context!).copy, MessageItemType.copy),
                    Container(
                      width: 1.w,
                      height: 12.w,
                      color: Colors.white.withValues(alpha: 0.30),
                    ),
                    _buildMenuButton(
                        S.of(Get.context!).quote, MessageItemType.quote),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(String text, MessageItemType type) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () async {
        // 先播放退出动画，再执行回调
        await animateOut();
        widget.onDismiss();
        widget.onSelected(type);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w,vertical: 8.w),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}

class MessageItemModel {
  final String message;
  final String imgStr;
  final MessageItemType type;
  const MessageItemModel(
      {required this.type, required this.message, required this.imgStr});
}

enum MessageItemType { quote, copy }

class MessageItems extends StatelessWidget {
  final String message;
  final String imgStr;
  final Function? onTap;
  const MessageItems(
      {super.key, required this.message, required this.imgStr, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap?.call();
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.w),
        child: Row(children: [
          Text(
            message ?? '',
            style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.w400),
          ),
          VerticalDivider(
            width: 12,
            color: Colors.white,
          ),
          Text(
            message ?? '',
            style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.w400),
          ),
        ]),
      ),
    );
  }
}

class SvgShapePainter extends CustomPainter {
  final bool triangleUp;
  final bool isFromMe;
  SvgShapePainter({required this.triangleUp, required this.isFromMe});

  @override
  void paint(Canvas canvas, Size size) {
    final path = Path();

    // 尺寸
    final width = size.width;
    final height = size.height;

    // 统一圆角半径（原图里左右上下圆角都是 8）
    final borderRadius = 8.0 * (width / 154.0); // 按比例缩放

    // 三角形的宽和高
    final triWidth = 8.0 * (width / 154.0);
    final triHeight = 5.5 * (height / 59.0);

    // 内容区域高度
    final rectTop = triHeight;
    final rectBottom = height;

    // 圆角矩形部分
    final rrect = RRect.fromLTRBR(
      0,
      triangleUp ? 0 : rectTop,
      width,
      triangleUp ? rectBottom - rectTop : rectBottom,
      Radius.circular(borderRadius),
    );
    path.addRRect(rrect);
    // 三角形部分
    if (!triangleUp) {
      // 三角形在上方
      if (isFromMe) {
        path.moveTo(width * 5 / 6 - triWidth / 2, rectTop);
        path.lineTo(width * 5 / 6, 0);
        path.lineTo(width * 5 / 6 + triWidth / 2, rectTop);
      } else {
        path.moveTo(width / 6 - triWidth / 2, rectTop);
        path.lineTo(width / 6, 0);
        path.lineTo(width / 6 + triWidth / 2, rectTop);
      }
    } else {
      if (isFromMe) {
        // 三角形在下方
        path.moveTo(width * 5 / 6 - triWidth / 2, rectBottom - rectTop);
        path.lineTo(width * 5 / 6, height);
        path.lineTo(width * 5 / 6 + triWidth / 2, rectBottom - rectTop);
      } else {
        // 三角形在下方
        path.moveTo(width / 6 - triWidth / 2, rectBottom - rectTop);
        path.lineTo(width / 6, height);
        path.lineTo(width / 6 + triWidth / 2, rectBottom - rectTop);
      }
    }
    path.close();

    // 阴影
    canvas.drawShadow(path, Colors.black.withOpacity(0.25), 3, false);

    // 填充
    final paint = Paint()
      ..color = const Color(0xFF0D0D0D).withOpacity(0.8)
      ..style = PaintingStyle.fill;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant SvgShapePainter oldDelegate) => false;
}
