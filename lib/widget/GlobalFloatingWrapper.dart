import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/debug/debug_page.dart';
import 'FloatingDragWidget.dart';

/// 全局悬浮组件包装器
/// 
/// 用于在应用的任何页面上显示全局悬浮组件
/// 可以根据当前页面动态显示不同的悬浮按钮
class GlobalFloatingWrapper extends StatefulWidget {
  /// 子页面内容
  final Widget child;
  
  /// 是否显示悬浮组件
  final bool showDebugFloating;

  const GlobalFloatingWrapper({
    Key? key,
    required this.child,
    this.showDebugFloating = false,
  }) : super(key: key);

  @override
  State<GlobalFloatingWrapper> createState() => _GlobalFloatingWrapperState();
}

class _GlobalFloatingWrapperState extends State<GlobalFloatingWrapper> {
  void _showDebugInfo() {
    Get.to(DebugPage());
  }

  /// 根据当前页面获取悬浮组件列表
  List<FloatingDragWidget> _getFloatingWidgets() {
    // 基础悬浮组件列表
    List<FloatingDragWidget> widgets = [];

    // 调试按钮 - 在所有页面显示，不设置初始位置让它自动吸附
    widgets.add(
      FloatingDragWidget(
        child: const Icon(Icons.bug_report, color: Colors.white, size: 20),
        onTap: _showDebugInfo,
        backgroundColor: Colors.orange,
        size: const Size(50, 50),
        // 移除 initialPosition，让它自动吸附到边缘
        borderRadius: 25,
        autoEdgeAdsorption: true,
      ),
    );
    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showDebugFloating) {
      return widget.child;
    }

    return FloatingDragContainer(
      child: widget.child,
      floatingWidgets: _getFloatingWidgets(),
    );
  }
}