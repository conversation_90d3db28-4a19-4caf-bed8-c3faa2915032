import 'dart:convert';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../module/chat/group/message_chat/enum/group_chat_message_enum.dart';
import '../module/chat/group/message_chat/logic/group_chat_message_controller.dart';
import '../module/chat/group/message_chat/model/group_chat_message_model.dart';
import '../utils/video_thumbnail_generator.dart';

// 引用消息自身文件预览类型 & 数据结构（需放在顶层，Dart 不支持类内再定义 enum/class）
enum _QuotePreviewType { image, video, ppt, report, unknown }

class _QuoteFilePreviewInfo {
  final _QuotePreviewType type;
  final String? url;
  const _QuoteFilePreviewInfo(this.type, this.url);
}

class MessageInputQuoteWidget extends StatefulWidget {
  const MessageInputQuoteWidget({super.key});

  @override
  State<MessageInputQuoteWidget> createState() =>
      _MessageInputQuoteWidgetState();
}

class _MessageInputQuoteWidgetState extends State<MessageInputQuoteWidget> {
  final groupChatController = Get.isRegistered<GroupChatMessageController>()
      ? Get.find<GroupChatMessageController>()
      : Get.put(GroupChatMessageController());
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => groupChatController.quoteMessage.value != null
          ? Container(
              margin: const EdgeInsets.only(left: 8, right: 8, top: 12),
              height: 32,
              decoration: BoxDecoration(
                color: const Color(0x660D0D0D), // 0x66 = 40% alpha
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                  bottomRight: Radius.circular(4),
                  bottomLeft: Radius.circular(4),
                ),
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 8,
                  ),
                  Image.asset(
                    'assets/groupChat/quote.png',
                    width: 24.w,
                    height: 24.w,
                  ),
                  SizedBox(
                    width: 4,
                  ),
                  _buildQuoteContent(groupChatController.quoteMessage.value),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      groupChatController.quoteMessage.value = null;
                    },
                    child: Image.asset(
                      'assets/groupChat/quote_close.png',
                      width: 24.w,
                      height: 24.w,
                    ),
                  ),
                  SizedBox(
                    width: 8,
                  ),
                ],
              ),
            )
          : SizedBox.shrink(),
    );
  }

  Widget _buildQuoteContent(GroupChatMessageModel? message) {
    if (message?.contentType == GroupChatMessageContentType.text) {
      return Expanded(
          child: Text(
        _formatParentContent(message?.content),
        style: TextStyle(
            fontSize: 16,
            color: Color.fromRGBO(152, 139, 154, 1),
            fontWeight: FontWeight.w400),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ));
    }
    if (message?.contentType == GroupChatMessageContentType.file) {
      final preview = _getSelfFilePreview(message);
      return Expanded(
        child: Align(
          alignment: Alignment.centerLeft,
          child: _buildSelfFilePreviewWidget(
              preview, message?.payload?.title ?? ''),
        ),
      );
    }
    if (message?.contentType == GroupChatMessageContentType.textAndFile) {
      return Expanded(
          child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.network(
              message?.attachments?.first.attachment_url ?? '',
              width: 53,
              height: 30,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(
            width: 10,
          ),
          Expanded(
              child: Text(
            _formatParentContent(message?.content),
            style: TextStyle(
                fontSize: 16,
                color: Color.fromRGBO(152, 139, 154, 1),
                fontWeight: FontWeight.w400),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ))
        ],
      ));
    }
    if (message?.productStatusType != GroupChatMessageProductStatusType.none) {
      return Expanded(
        child: Align(
          alignment: Alignment.centerLeft,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: CachedNetworkImage(
              imageUrl: message?.attachments?.first.attachment_url ?? '',
              width: 53,
              height: 30,
              fit: BoxFit.cover,
            ),
          ),
        ),
      );
    }
    return SizedBox.shrink();
  }

  _QuoteFilePreviewInfo _getSelfFilePreview(GroupChatMessageModel? message) {
    if (message == null)
      return const _QuoteFilePreviewInfo(_QuotePreviewType.unknown, null);
    // 如果是助手生成物结束的消息，解析 message.messageContent
    try {
      if (message.assistantActionType ==
          GroupAssistantActionType.generateThingEnd) {
        GroupCreateMessageModel m = GroupCreateMessageModel.fromJson(
            jsonDecode(message.messageContent ?? '{}'));
        if (m.mode == 4 && m.vtype == 'image') {
          return _QuoteFilePreviewInfo(_QuotePreviewType.image, m.link_url);
        }
        if (m.mode == 4 && m.vtype == 'video') {
          return _QuoteFilePreviewInfo(_QuotePreviewType.video, m.link_url);
        }
        if (m.mode == 3) {
          return const _QuoteFilePreviewInfo(_QuotePreviewType.ppt, null);
        }
        if (m.mode == 6) {
          return const _QuoteFilePreviewInfo(_QuotePreviewType.report, null);
        }
      }
    } catch (_) {}

    final attach = message.attachments?.first;
    if (attach != null) {
      switch (attach.fileType) {
        case AttachmentType.image:
          return _QuoteFilePreviewInfo(
              _QuotePreviewType.image, attach.attachment_url);
        case AttachmentType.video:
          return _QuoteFilePreviewInfo(
              _QuotePreviewType.video, attach.attachment_url);
        default:
          break;
      }
    }
    return const _QuoteFilePreviewInfo(_QuotePreviewType.unknown, null);
  }

  Widget _buildSelfFilePreviewWidget(_QuoteFilePreviewInfo info, String title) {
    const double w = 53;
    const double h = 30;
    BorderRadius radius = BorderRadius.circular(4);
    switch (info.type) {
      case _QuotePreviewType.image:
        if ((info.url ?? '').isEmpty) {
          return _buildEmptyBox(w, h);
        }
        return Row(
          children: [
            ClipRRect(
              borderRadius: radius,
              child: CachedNetworkImage(
                imageUrl: info.url!,
                width: w,
                height: h,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: 10),
            title.isNotEmpty
                ? Expanded(
                    child: Text(
                    title,
                    style: TextStyle(
                        fontSize: 16,
                        color: Color.fromRGBO(152, 139, 154, 1),
                        fontWeight: FontWeight.w400),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ))
                : SizedBox.shrink(),
          ],
        );
      case _QuotePreviewType.video:
        if ((info.url ?? '').isEmpty) return _buildEmptyBox(w, h);
        return FutureBuilder<Uint8List?>(
          future: info.url == null
              ? null
              : VideoThumbnailGenerator.generateThumbnail(
                  videoUrl: info.url!,
                  maxWidth: w.toInt(),
                  maxHeight: h.toInt(),
                  quality: 75,
                ),
          builder: (context, snapshot) {
            Widget thumb;
            if (snapshot.connectionState == ConnectionState.done &&
                snapshot.hasData &&
                snapshot.data != null) {
              thumb = Image.memory(snapshot.data!,
                  width: w, height: h, fit: BoxFit.cover);
            } else {
              thumb = _buildEmptyBox(w, h);
            }
            return Row(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    ClipRRect(borderRadius: radius, child: thumb),
                    Image.asset(
                      'assets/images/ic_design_video.png',
                      width: 12,
                      height: 12,
                      fit: BoxFit.contain,
                    )
                  ],
                ),
                SizedBox(width: 10),
                title.isNotEmpty
                    ? Expanded(
                        child: Text(
                        title,
                        style: TextStyle(
                            fontSize: 16,
                            color: Color.fromRGBO(152, 139, 154, 1),
                            fontWeight: FontWeight.w400),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ))
                    : SizedBox.shrink(),
              ],
            );
          },
        );
      case _QuotePreviewType.ppt:
        return _buildTagBox(title.isNotEmpty ? title : 'PPT');
      case _QuotePreviewType.report:
        return _buildTagBox(title.isNotEmpty ? title : '报告');
      case _QuotePreviewType.unknown:
        return _buildEmptyBox(w, h);
    }
  }

  Widget _buildEmptyBox(double w, double h) {
    return Container(
      width: w,
      height: h,
      decoration: BoxDecoration(
        color: const Color(0x1FFFFFFF),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: const Color(0x33FFFFFF), width: 0.5),
      ),
      alignment: Alignment.center,
      child: const Icon(Icons.insert_drive_file,
          size: 14, color: Color(0xFF988B9A)),
    );
  }

  Widget _buildTagBox(String text) {
    return Container(
      width: 40.w,
      height: 30.h,
      child: Column(
        children: [
          Container(
            width: 40.w,
            height: 23.5.h,
            decoration: BoxDecoration(
              color: Color(0xFFFF5555),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4.w),
                topRight: Radius.circular(4.w),
              ),
            ),
            child: Center(
              child: Text(
                text,
                style: TextStyle(
                    fontSize: 4.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white),
              ),
            ),
          ),
          Container(
            width: 40.w,
            height: 6.5.h,
            alignment: Alignment.centerRight,
            padding: EdgeInsets.only(right: 2.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(4.w),
                bottomRight: Radius.circular(4.w),
              ),
            ),
            child: Text(
              text,
              style: TextStyle(
                  fontSize: 3.sp,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF838384)),
            ),
          )
        ],
      ),
    );
  }

  String _formatParentContent(String? content) {
    debugPrint(
        'quote content: ${groupChatController.quoteMessage.value?.attachments}');
    if (content == null || content.isEmpty) return '';
    final raw = content;
    try {
      final decoded = jsonDecode(raw);
      if (decoded is Map) {
        if (decoded['add_task_items'] is List &&
            decoded['add_task_items'].isNotEmpty) {
          final items = decoded['add_task_items'] as List;
          final contents = items
              .map((e) => e is Map && e['content'] is String
                  ? e['content'] as String
                  : null)
              .whereType<String>()
              .toList();
          if (contents.isNotEmpty) {
            return contents.join('；');
          }
        } else if (decoded['link_content'] is String) {
          return decoded['link_content'] as String;
        } else if (decoded['update_task_items'] is List &&
            decoded['update_task_items'].isNotEmpty) {
          final items = decoded['update_task_items'] as List;
          final contents = items
              .map((e) => e is Map && e['content'] is String
                  ? e['content'] as String
                  : null)
              .whereType<String>()
              .toList();
          if (contents.isNotEmpty) {
            return contents.join('；');
          }
        }
      }
    } catch (_) {
      // ignore parse errors and fallback to raw string
    }
    return raw;
  }
}
