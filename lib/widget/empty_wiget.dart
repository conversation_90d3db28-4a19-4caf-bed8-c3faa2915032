import 'package:flutter/material.dart';

class EmptyWidget extends StatelessWidget {
  final String imagePath;
  final String message;
  final double topPadding;
  final double width;

  const EmptyWidget({
    Key? key,
    required this.imagePath,
    required this.message,
    this.topPadding = 100,
    this.width = 130,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Padding(
        padding: EdgeInsets.only(top: topPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              imagePath,
              width: width,
              fit: BoxFit.fitWidth,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
