import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:new_agnes/module/chat/widget/rotating_loading_image.dart';

/// 自定义手势识别器，用于解决PageView和InteractiveViewer的手势冲突
class _CustomPanGestureRecognizer extends PanGestureRecognizer {
  @override
  void addAllowedPointer(PointerDownEvent event) {
    super.addAllowedPointer(event);
  }

  @override
  void handleEvent(PointerEvent event) {
    if (event is PointerMoveEvent) {
      // 如果水平滑动距离大于垂直滑动距离，则处理手势
      if (event.delta.dx.abs() > event.delta.dy.abs()) {
        super.handleEvent(event);
      }
    } else {
      super.handleEvent(event);
    }
  }
}

/// 通用图片浏览组件
/// 支持侧滑浏览、缩放、下载等功能
class ImageViewerWidget extends StatefulWidget {
  /// 图片列表
  final List<ImageItem> images;
  
  /// 当前显示的图片索引
  final int initialIndex;
  
  /// 是否显示下载按钮
  final bool showDownloadButton;
  
  /// 是否显示导出按钮
  final bool showExportButton;
  
  /// 是否显示底部按钮区域（当下载和导出都隐藏时，整个底部区域都会隐藏）
  final bool showBottomButtons;
  
  /// 下载按钮点击回调
  final ValueChanged<int>? onDownload;
  
  /// 导出按钮点击回调
  final ValueChanged<int>? onExport;
  
  /// 图片点击回调
  final ValueChanged<int>? onImageTap;
  
  /// 页面切换回调
  final ValueChanged<int>? onPageChanged;
  
  /// 是否显示页面指示器
  final bool showPageIndicator;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 是否显示顶部拖拽指示器
  final bool showDragIndicator;

  const ImageViewerWidget({
    Key? key,
    required this.images,
    this.initialIndex = 0,
    this.showDownloadButton = true,
    this.showExportButton = false,
    this.showBottomButtons = true,
    this.onDownload,
    this.onExport,
    this.onImageTap,
    this.onPageChanged,
    this.showPageIndicator = true,
    this.backgroundColor,
    this.showDragIndicator = true,
  }) : super(key: key);

  @override
  State<ImageViewerWidget> createState() => _ImageViewerWidgetState();
}

class _ImageViewerWidgetState extends State<ImageViewerWidget> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isScaling = false; // 跟踪是否正在缩放
  int _pointerCount = 0; // 跟踪触摸点数量
  
  // 缩放相关控制器
  final Map<int, TransformationController> _transformationControllers = {};
  final Map<int, double> _scaleFactors = {};
  
  // 缩放参数
  static const double _minScale = 1.0;
  static const double _maxScale = 5.0;
  static const double _defaultScale = 1.0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    // 清理缩放控制器
    for (var controller in _transformationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
  
  /// 获取或创建指定索引的变换控制器
  TransformationController _getTransformationController(int index) {
    if (!_transformationControllers.containsKey(index)) {
      _transformationControllers[index] = TransformationController();
      _scaleFactors[index] = _defaultScale;
    }
    return _transformationControllers[index]!;
  }
  
  /// 重置缩放
  void _resetZoom(int index) {
    final controller = _getTransformationController(index);
    controller.value = Matrix4.identity();
    _scaleFactors[index] = _defaultScale;
  }
  
  /// 放大
  void _zoomIn(int index) {
    final controller = _getTransformationController(index);
    final currentScale = _scaleFactors[index]!;
    final newScale = (currentScale * 1.5).clamp(_minScale, _maxScale);
    
    if (newScale != currentScale) {
      final matrix = Matrix4.identity()..scale(newScale);
      controller.value = matrix;
      _scaleFactors[index] = newScale;
    }
  }
  
  /// 缩小
  void _zoomOut(int index) {
    final controller = _getTransformationController(index);
    final currentScale = _scaleFactors[index]!;
    final newScale = (currentScale / 1.5).clamp(_minScale, _maxScale);
    
    if (newScale != currentScale) {
      final matrix = Matrix4.identity()..scale(newScale);
      controller.value = matrix;
      _scaleFactors[index] = newScale;
    }
  }
  
  /// 双击缩放
  void _handleDoubleTap(int index) {
    final controller = _getTransformationController(index);
    final currentScale = _scaleFactors[index]!;
    
    if (currentScale > _defaultScale) {
      // 如果已缩放，则重置
      _resetZoom(index);
    } else {
      // 如果未缩放，则放大到2倍
      final matrix = Matrix4.identity()..scale(2.0);
      controller.value = matrix;
      _scaleFactors[index] = 2.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Color(0xFF000A19),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          // 顶部拖拽指示器
          if (widget.showDragIndicator)
            Container(
              padding: EdgeInsets.all(16),
              child: Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            ),

          // 页面指示器
          if (widget.showPageIndicator && widget.images.length > 1)
            Container(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Text(
                '${_currentIndex + 1} / ${widget.images.length}',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14.sp,
                ),
              ),
            ),

          // 图片查看区域
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              physics: _pointerCount >= 2 ? NeverScrollableScrollPhysics() : BouncingScrollPhysics(),
              scrollDirection: Axis.horizontal,
              allowImplicitScrolling: true, // 允许隐式滚动
              padEnds: false, // 不填充两端，提高滑动灵敏度
              onPageChanged: (index) {
                // 重置上一张图片的缩放状态
                if (_currentIndex != index) {
                  _resetZoom(_currentIndex);
                }
                setState(() {
                  _currentIndex = index;
                });
                widget.onPageChanged?.call(index);
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                final imageItem = widget.images[index];
                return _buildImagePage(imageItem, index);
              },
            ),
          ),

          // 底部按钮区域
          if (widget.showBottomButtons && (widget.showDownloadButton || widget.showExportButton))
            Container(
              margin: EdgeInsets.only(bottom: 32, top: 36),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // 下载按钮
                  if (widget.showDownloadButton)
                    GestureDetector(
                      onTap: (){
                        widget.onDownload?.call(_currentIndex);
                      },
                      child: Container(
                        width: 40.w,
                        height: 40.h,
                        child: Image.asset("assets/icon/ic_icon_download.png"),
                      ),
                    ),

                  // 导出按钮
                  if (widget.showExportButton)
                    GestureDetector(
                      onTap: (){
                        widget.onExport?.call(_currentIndex);
                      },
                      child: Container(
                        width: 40.w,
                        height: 40.h,
                        child: Image.asset("assets/icon/ic_icon_export.png"),
                      ),
                    ),
                ],
              ),
            ),
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  /// 构建图片页面
  Widget _buildImagePage(ImageItem imageItem, int index) {
    return GestureDetector(
      onTap: () => widget.onImageTap?.call(index),
      onDoubleTap: () => _handleDoubleTap(index),
      child: Listener(
        onPointerDown: (event) {
          setState(() {
            _pointerCount++;
          });
        },
        onPointerUp: (event) {
          setState(() {
            _pointerCount--;
          });
        },
        onPointerCancel: (event) {
          setState(() {
            _pointerCount--;
          });
        },
        child: ClipRect(
          clipBehavior: Clip.none,
          child: InteractiveViewer(
            transformationController: _getTransformationController(index),
            minScale: _minScale,
            maxScale: _maxScale,
            boundaryMargin: EdgeInsets.zero,
            clipBehavior: Clip.none,
            onInteractionStart: (details) {
              setState(() {
                _isScaling = _pointerCount >= 2;
              });
            },
            onInteractionEnd: (details) {
              setState(() {
                _isScaling = false;
              });
              // 更新当前缩放比例
              final controller = _getTransformationController(index);
              final scale = controller.value.getMaxScaleOnAxis();
              _scaleFactors[index] = scale;
            },
            child: Center(
              child: _buildImageWidget(imageItem),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建图片组件
  Widget _buildImageWidget(ImageItem imageItem) {
    if (imageItem.url != null) {
      return CachedNetworkImage(
        imageUrl: imageItem.url!,
        fit: BoxFit.cover,
        placeholder: (context, url) => Stack(
          children: [
            if((imageItem.thumbnail ?? "").startsWith("http"))...[
              Center(
                child: CachedNetworkImage(
                  imageUrl: imageItem.thumbnail ?? "",
                  fit: BoxFit.cover,
                ),
              )
            ],
            if (imageItem.thumbnail != null &&
                imageItem.thumbnail!.isNotEmpty &&
                !((imageItem.thumbnail ?? "").startsWith("http"))) ...[
              Center(
                child: Image.file(
                  File(imageItem.thumbnail ?? ""),
                  fit: BoxFit.cover,
                ),
              )
            ],
            Container(
              color: Colors.black.withValues(alpha: 0.3),
            ),
            Center(
                child: RotatingLoadingImage(
              imagePath: "assets/images/icon_load.webp",
              width: 30.w,
              height: 30.h,
            ))
          ],
        ),
        errorWidget: (context, url, error) => Center(
          child: Icon(
            Icons.error_outline,
            color: Colors.white,
            size: 48,
          ),
        ),
      );
    } else if (imageItem.assetPath != null) {
      return Image.asset(
        imageItem.assetPath!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Center(
          child: Icon(
            Icons.error_outline,
            color: Colors.white,
            size: 48,
          ),
        ),
      );
    } else {
      return Center(
        child: Icon(
          Icons.image,
          color: Colors.white,
          size: 48,
        ),
      );
    }
  }

  /// 跳转到指定页面
  void jumpToPage(int index) {
    if (index >= 0 && index < widget.images.length) {
      _pageController.animateToPage(
        index,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 获取当前页面索引
  int get currentIndex => _currentIndex;
}

/// 图片数据模型
class ImageItem {
  /// 网络图片URL
  final String? url;
  
  /// 本地资源路径
  final String? assetPath;
  
  /// 图片标题
  final String? title;
  
  /// 图片类型
  final String? type;

  ///缩略图
  final String? thumbnail;

  const ImageItem({
    this.url,
    this.assetPath,
    this.title,
    this.type,
    this.thumbnail
  });

  /// 从网络URL创建
  factory ImageItem.network(String url, {String? title, String? type,String? thumbnail}) {
    return ImageItem(
      url: url,
      title: title,
      type: type,
      thumbnail: thumbnail
    );
  }

  /// 从本地资源创建
  factory ImageItem.asset(String assetPath, {String? title, String? type}) {
    return ImageItem(
      assetPath: assetPath,
      title: title,
      type: type,
    );
  }
}

/// 图片浏览弹窗工具类
class ImageViewerDialog {
  /// 显示图片浏览弹窗
  static Future<void> show({
    required BuildContext context,
    required List<ImageItem> images,
    int initialIndex = 0,
    bool showDownloadButton = true,
    bool showExportButton = false,
    bool showBottomButtons = true,
    ValueChanged<int>? onDownload,
    ValueChanged<int>? onExport,
    ValueChanged<int>? onImageTap,
    ValueChanged<int>? onPageChanged,
    bool showPageIndicator = true,
    Color? backgroundColor,
    bool showDragIndicator = true,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ImageViewerWidget(
        images: images,
        initialIndex: initialIndex,
        showDownloadButton: showDownloadButton,
        showExportButton: showExportButton,
        showBottomButtons: showBottomButtons,
        onDownload: onDownload,
        onExport: onExport,
        onImageTap: onImageTap,
        onPageChanged: onPageChanged,
        showPageIndicator: showPageIndicator,
        backgroundColor: backgroundColor,
        showDragIndicator: showDragIndicator,
      ),
    );
  }
}