import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_logic.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_speech_send_message_utils.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_utils.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:mime/mime.dart';
import 'package:new_agnes/api/Api.dart';
import 'package:new_agnes/api/ApiProvider.dart';
import 'package:path/path.dart' as path;

import '../data/caseAndQuestion.dart';
import '../generated/l10n.dart';
import '../module/chat/group/message_chat/logic/group_chat_message_controller.dart';
import '../module/home/<USER>/input_widgets/audio_wave_widget.dart';
import '../utils/ble/blueToothManager.dart';
import '../utils/image_upload_util.dart';
import '../utils/stt/AzureSpeechManager.dart';
import 'GradientBorderContainer.dart';
import 'message_input_quote_widget.dart';

typedef MessageInputSendCallBack = Function(String, SearchType, MessageAtModel);

typedef MessageInputTextChangeCallBack = Function(
    String, String, Rx<MessageAtModel> atModel);

typedef MessageInputRetryUploadCallBack = Function(int index);

/// 消息输入组件
class MessageInputWidget extends StatefulWidget {
  final MessageInputSendCallBack onSendMessage;
  final VoidCallback? onAddFilePressed;
  final MessageInputTextChangeCallBack? onTextChanged;
  final VoidCallback? voicePressed;
  final MessageInputRetryUploadCallBack? onRetryUpload;
  final double height;
  final bool switchMode;
  final List<MessageUploadModel>? uploadFiles;
  final String hintText;

  const MessageInputWidget(
      {super.key,
      required this.onSendMessage,
      required this.height,
      this.onAddFilePressed,
      this.onTextChanged,
      this.voicePressed,
      this.onRetryUpload,
      this.switchMode = false,
      this.uploadFiles,
      this.hintText = ""});

  /// 外部调用：长按头像直接插入@某人 功能入口
  /// 不影响原有用户手动输入 @ 再选择的弹窗逻辑
  /// 如果当前已经存在一个@对象，则忽略本次插入，避免重复
  static void insertMentionExternal(String atUserId, String atUserName) {
    _MessageInputWidgetState._lastInstance
        ?._insertMentionExternal(atUserId, atUserName);
  }

  @override
  State<MessageInputWidget> createState() => _MessageInputWidgetState();
}

class _MessageInputWidgetState extends State<MessageInputWidget> {
  late MessageInputController controller;
  SearchType searchType = SearchType.tools;
  String _previousText = '';

  Rx<MessageAtModel> atModel = MessageAtModel().obs;
  final GlobalKey _sendButtonKey = GlobalKey();


  // 记录最后一个实例，方便聊天列表里的头像长按调用，不引入复杂事件总线，减少性能消耗
  static _MessageInputWidgetState? _lastInstance;
  bool _externalMentionInserting = false; // 外部插入时避免监听重复追加

  @override
  void initState() {
    super.initState();
    controller = Get.put(MessageInputController());
    controller.hasFiles.value = widget.uploadFiles?.isNotEmpty == true;
    // 初始化时检查上传状态
    controller.checkUploadStatus(widget.uploadFiles);
    controller.addOnSendListener((text) {
      widget.onSendMessage(text, searchType, MessageAtModel());
    });
    _lastInstance = this; // 缓存实例
    ever(atModel, (data) {
      String curText = controller.textEditingController.text;
      if (atModel().atId != null) {
        // 外部直接插入时已经包含了 @ 用户名，此处跳过默认拼接逻辑
        if (_externalMentionInserting) {
          _externalMentionInserting = false;
          return;
        }
        // 原逻辑：用户手动输入 @ 之后，通过弹窗选择人，只需补齐用户名（不补 @）
        int startIndex = controller.textEditingController.selection.start;
        if(startIndex == -1){
          startIndex = curText.length;
        }
        String startText = curText.substring(
            0, startIndex);
        String endText =
            curText.substring(startIndex);
        controller.textEditingController.text =
            startText + '${atModel().atName} ' + endText;
        _previousText = controller.textEditingController.text;
      }
    });
  }

  @override
  void dispose() {
    controller.stopRecognize();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      var audioWaveLogic = Get.find<AudioWaveLogic>();
      audioWaveLogic.isPressedFinished.value = true;
      audioWaveLogic.isLoading.value = false;
    });
    if (identical(_lastInstance, this)) {
      _lastInstance = null; // 释放引用
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant MessageInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    controller.hasFiles.value = widget.uploadFiles?.isNotEmpty == true;
    controller.checkUploadStatus(widget.uploadFiles);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => AzureSpeechManager.isRecognizing.value &&
            AzureSpeechManager.listening() == controller.key
        ? AudioWaveWidget(
            onCancel: () {
              var audioWaveLogic = Get.find<AudioWaveLogic>();
              audioWaveLogic.isLoading.value = false;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                controller.stopRecognize();
              });
            },
            onDone: () {
              var audioWaveLogic = Get.find<AudioWaveLogic>();
              audioWaveLogic.isPressedFinished.value = true;
              audioWaveLogic.isLoading.value = true;
            },
            type: 1,
          )
        : Container(
         padding: EdgeInsets.symmetric(horizontal: 16.w),
            margin: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom + 12.h),
            child: Obx(() => GradientBorderContainer.single(
                  strokeWidth: 2,
                  // 移除固定高度约束，让容器根据内容自适应
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFFF3BDF),
                      Color(0xFF5E57FE),
                      Color(0xFF2DDAF2),
                      Color(0xFF4D84FA),
                      Color(0xFFFF3BDF),
                    ],
                    stops: [0.0, 0.24, 0.51, 0.78, 1.0],
                    begin: Alignment.bottomLeft,
                    end: Alignment.topRight,
                    tileMode: TileMode.decal,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  child: IntrinsicHeight(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        //引用消息展示
                        MessageInputQuoteWidget(),
                        // 文件展示区域
                        if (controller.hasFiles())
                          FileDisplayWidget(
                            files: widget.uploadFiles!,
                            onRemoveFile: (index) {
                              widget.uploadFiles?.removeAt(index);
                              controller.hasFiles.value =
                                  widget.uploadFiles!.isNotEmpty;
                              controller.hasFiles.refresh();
                              // 删除文件后重新检查上传状态
                              controller.checkUploadStatus(widget.uploadFiles);
                            },
                            onRetryUpload: (index) {
                              widget.onRetryUpload?.call(index);
                            },
                          ),
                        // TextField区域
                        Container(
                          margin: EdgeInsets.only(
                              top: 6.h, left: 12.w, right: 12.w),
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              minHeight: 40.h, // 设置固定的最小高度
                              maxHeight: 120.h, // 限制最大高度，约5-6行文本
                            ),
                            child: TextField(
                              autofocus: false,
                              onTapOutside: (event) {
                                RenderObject? renderObject = _sendButtonKey
                                    .currentContext
                                    ?.findRenderObject();
                                if (renderObject is RenderBox) {
                                  Offset offset =
                                      renderObject.localToGlobal(Offset.zero);
                                  Size buttonSize = renderObject.size;
                                  Rect buttonRect = Rect.fromLTWH(
                                      offset.dx,
                                      offset.dy,
                                      offset.dx + buttonSize.width,
                                      offset.dy + buttonSize.height);
                                  bool isInside =
                                      buttonRect.contains(event.position);
                                  if (!isInside) {
                                    FocusScope.of(context).unfocus();
                                  }
                                }
                              },
                              controller: controller.textEditingController,
                              focusNode: controller.focusNode,
                              textAlignVertical: TextAlignVertical.top,
                              style:
                                  TextStyle(fontSize: 16, color: Colors.white),
                              onChanged: (text) {
                                if (!text.contains("@${atModel().atName}") &&
                                    _previousText
                                        .contains("@${atModel().atName}")) {
                                  //上一个文本含有名字，当前文本没有名字，则删除名字跟@
                                  controller.textEditingController.text =
                                      _previousText.replaceAll(
                                          "@${atModel().atName}", "");
                                  _previousText =
                                      controller.textEditingController.text;
                                  atModel.value =
                                      MessageAtModel(atName: atModel().atName);
                                  return;
                                }
                                widget.onTextChanged
                                    ?.call(_previousText, text, atModel);
                                _previousText = text;
                              },
                              maxLines: null,
                              // 允许多行
                              minLines: 1,
                              // 最少一行
                              // 移除 expands: true，让 TextField 根据内容自适应
                              decoration: InputDecoration(
                                hintText: widget.hintText,
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                isDense: true,
                                hintStyle: TextStyle(
                                    fontSize: 16, color: Color(0xFF988B9A)),
                              ),
                              textCapitalization: TextCapitalization.sentences,
                              onSubmitted: (_) => _handleSend(),
                            ),
                          ),
                        ),
                        // 底部工具栏
                        Container(
                          margin: EdgeInsets.only(left: 12.w, right: 8.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // 附件按钮
                              Container(
                                margin: EdgeInsets.only(bottom: 12.h),
                                child: GestureDetector(
                                  onTap: () {
                                    widget.onAddFilePressed?.call();
                                  },
                                  child: Image.asset(
                                    "assets/images/ic_message_input_add_file.png",
                                    width: 24.w,
                                    height: 24.h,
                                  ),
                                ),
                              ),
                              if (widget.switchMode) ...[
                                SizedBox(width: 8.w),
                                MessageInputModeWidget(onChanged: (item) {
                                  searchType = item;
                                }),
                              ],
                              Spacer(),
                              Container(
                                margin: EdgeInsets.only(
                                    bottom: 12.h),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    MessageInputSpeechingWidget(),
                                    SizedBox(width: 8.w),
                                    Obx(() => MessageInputSendWidget(
                                          key: _sendButtonKey,
                                          hasText: controller.hasText() ||
                                              controller.hasFiles(),
                                          canSend:
                                              !controller.hasFailedUploads(),
                                          size: Size(70.w, 33.h),
                                          onSendMessage: () {
                                            if (!controller
                                                .hasFailedUploads()) {
                                              widget.onSendMessage(
                                                  controller
                                                      .textEditingController
                                                      .text,
                                                  searchType,
                                                  atModel());
                                              _cleanText();
                                            }
                                          }
                                        )),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                )),
          ));
  }

  void _handleSend() {
    final text = controller.textEditingController.text.trim();
    if (text.isNotEmpty) {
      widget.onSendMessage(text, searchType, atModel());
      _cleanText();
    }
  }

  void _cleanText() {
    controller.textEditingController.clear();
    controller.hasText.value = false;
    atModel.value = MessageAtModel();
    _previousText = '';
  }

  /// 外部触发的 @ 插入（例如长按头像）
  /// 规则：
  /// 1. 若当前已经存在一个 @ 对象 (atModel.atId != null) 则忽略（保持与单 @ 发送逻辑一致）
  /// 2. 在当前文本末尾或光标位置插入："@用户名 "
  /// 3. 不触发原有 ever(atModel) 的再次补齐
  void _insertMentionExternal(String atUserId, String atUserName) {
    if (atUserId.isEmpty || atUserName.isEmpty) return;
    if (atModel().atId != null) return; // 已存在 @，不重复插入

    final textController = controller.textEditingController;
    int insertPos = textController.selection.isValid
        ? textController.selection.start
        : textController.text.length;
    String raw = textController.text;
    String prefix = raw.substring(0, insertPos);
    String suffix = raw.substring(insertPos);
    String mentionText = '@$atUserName ';
    String newText = prefix + mentionText + suffix;
    textController.text = newText;
    textController.selection =
        TextSelection.collapsed(offset: (prefix + mentionText).length);
    _previousText = newText;
    _externalMentionInserting = true; // 告诉监听不要再次追加用户名
    atModel.value = MessageAtModel(atId: atUserId, atName: atUserName);
    controller.hasText.value = textController.text.trim().isNotEmpty;
    controller.focusNode.requestFocus();
  }
}

class MessageInputSendWidget extends StatefulWidget {
  final bool hasText;
  final bool canSend;
  final Size size;
  final VoidCallback onSendMessage;

  const MessageInputSendWidget(
      {super.key,
      required this.hasText,
      this.canSend = false,
      required this.size,
      required this.onSendMessage});

  @override
  State<MessageInputSendWidget> createState() => _MessageInputSendWidgetState();
}

class _MessageInputSendWidgetState extends State<MessageInputSendWidget> {
  final controller = Get.put(MessageInputController());

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.size.width,
      height: widget.size.height,
      child: GestureDetector(
        onTap: () {
          if (widget.hasText && widget.canSend) {
            widget.onSendMessage.call();
          }
        },
        child: buildSendOrVoice(),
      ),
    );
  }

  Widget buildSendOrVoice() {
    if (widget.hasText && widget.canSend) {
      return Image.asset('assets/images/icon_go_yes.webp',
          width: widget.size.width, height: widget.size.height);
    }
    return Image.asset('assets/images/icon_go_no.webp',
        width: widget.size.width, height: widget.size.height);
  }
}

class MessageInputSpeechingWidget extends StatefulWidget {
  const MessageInputSpeechingWidget({super.key});

  @override
  State<MessageInputSpeechingWidget> createState() =>
      _MessageInputSpeechingWidgetState();
}

class _MessageInputSpeechingWidgetState
    extends State<MessageInputSpeechingWidget> {
  bool get speeching => AzureSpeechManager.isRecognizing();
  final controller = Get.put(MessageInputController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        debugPrint("准备语音识别");
        try {
          if (controller.hasUse) {
            return;
          }
          if (!AzureSpeechManager.isFromBle) {
            // 非耳机模式下，按键语音识别，耳机模式下，禁止点击按钮触发
            if (speeching) {
              // 延迟一小段时间再执行，避免在构建过程中触发状态更新
              controller.stopRecognize();
            } else {
              var audioWaveLogic = Get.find<AudioWaveLogic>();
              audioWaveLogic.isPressedFinished.value = false;
              // 延迟一小段时间再执行，避免在构建过程中触发状态更新
              controller.startRecognition();
            }
          }
        } catch (e) {
          debugPrint("语音识别操作失败: $e");
        }
      },
      child: Visibility(
        visible: AzureSpeechManager.isInitialized.value,
        child: Obx(() => Image.asset(
              'assets/icon/icon_mic.webp',
              width: 24.w,
              height: 24.h,
              color: controller.hasUse ? Color(0xFF988BA9) : null,
            )),
      ),
    );
  }
}

class MessageInputModeWidget extends StatefulWidget {
  final ValueChanged<SearchType> onChanged;

  MessageInputModeWidget({super.key, required this.onChanged});

  @override
  State<MessageInputModeWidget> createState() => _MessageInputModeWidgetState();
}

class _MessageInputModeWidgetState extends State<MessageInputModeWidget> {
  SearchType _selectedItem = SearchType.tools;
  final GlobalKey _buttonKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  bool _isShowing = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: _buttonKey,
      onTap: () {
        _showOverlay();
      },
      child: GradientBorderContainer.single(
          strokeWidth: 1,
          gradient: LinearGradient(
            colors: _selectedItem != SearchType.tools
                ? [
                    Color(0xFF7253FA),
                    Color(0xFFFF3BDF),
                    Color(0xFF5E57FE),
                  ]
                : [
                    Colors.transparent,
                    Colors.transparent,
                    Colors.transparent,
                  ],
            stops: const [0.0, 0.32, 1.0],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
            tileMode: TileMode.decal,
          ),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: _selectedItem != SearchType.tools
                    ? Color(0x8F604276)
                    : Colors.transparent),
            child: Padding(
              padding: _selectedItem != SearchType.tools
                  ? EdgeInsets.symmetric(horizontal: 8, vertical: 2)
                  : EdgeInsets.symmetric(horizontal: 0, vertical: 0),
              child: Row(
                children: [
                  Image.asset(
                    _selectedItem != SearchType.tools
                        ? 'assets/images/icon_search_type.webp'
                        : 'assets/images/icon_search.webp',
                    width: 24,
                    height: 24,
                  ),
                  if (_selectedItem != SearchType.tools) ...{
                    SizedBox(
                      width: 8,
                    ),
                    Text(
                      "|",
                      style: TextStyle(fontSize: 12, color: Colors.white),
                    ),
                    SizedBox(
                      width: 8,
                    ),
                    SvgPicture.asset(
                      _selectedItem.icon,
                      width: 20,
                      height: 20,
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Text(
                      _selectedItem.name(context),
                      style: TextStyle(fontSize: 14, color: Colors.white),
                    ),
                    SizedBox(
                      width: 8,
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedItem = SearchType.tools;
                          widget.onChanged(_selectedItem);
                        });
                      },
                      child: Image.asset(
                        "assets/images/icon_home_delete.webp",
                        width: 12,
                        height: 12,
                      ),
                    )
                  }
                ],
              ),
            ),
          )),
    );
  }

  void _showOverlay() {
    if (_isShowing) {
      _removeOverlay();
      return;
    }

    final RenderBox renderBox =
        _buttonKey.currentContext?.findRenderObject() as RenderBox;
    final offset = renderBox.localToGlobal(Offset.zero);
    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 半透明背景层，点击可关闭
          Positioned.fill(
            child: GestureDetector(
              onTap: _removeOverlay,
              child: Container(
                color: Colors.black.withValues(alpha: 0.3),
              ),
            ),
          ),
          // 下拉框内容
          Positioned(
            left: offset.dx,
            top: offset.dy - 130.h,
            child: Material(
              elevation: 4,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                constraints: const BoxConstraints(
                  minWidth: 100,
                ),
                decoration: BoxDecoration(
                  border: Border.fromBorderSide(
                      BorderSide(color: Color(0xFFFF3BDF), width: 1.w)),
                  color: Color(0xE52E174F),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x40000000),
                      blurRadius: 4,
                      spreadRadius: 0,
                    )
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: SearchType.values.map((item) {
                      if (item == SearchType.tools) {
                        return const SizedBox();
                      }
                      return Material(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.transparent,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 6),
                          child: _buildItem(item),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    Overlay.of(_buttonKey.currentContext!).insert(_overlayEntry!);
    _isShowing = true;
  }

  Widget _buildItem(SearchType item) {
    return InkWell(
        splashColor: Colors.purple.withValues(alpha: 0.3),
        highlightColor: Colors.purple.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          setState(() {
            _selectedItem = item;
          });
          _overlayEntry!.markNeedsBuild();
          _removeOverlay();
        },
        child: GradientBorderContainer.single(
          strokeWidth: 1,
          gradient: LinearGradient(
            colors: item == _selectedItem
                ? [
                    Color(0xFF7253FA),
                    Color(0xFFFF3BDF),
                    Color(0xFF5E57FE),
                  ]
                : [
                    Colors.transparent,
                    Colors.transparent,
                    Colors.transparent,
                  ],
            stops: const [0.0, 0.32, 1.0],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
            tileMode: TileMode.decal,
          ),
          borderRadius: BorderRadius.circular(10),
          child: Container(
            height: 36.h,
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            constraints: BoxConstraints(
              minWidth: 151.w,
            ),
            child: Row(
              children: [
                SvgPicture.asset(
                  item.icon,
                  width: 20.w,
                  height: 20.h,
                ),
                SizedBox(width: 8.w),
                Text(
                  item.name(context),
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry?.remove();
      _overlayEntry = null;
      _isShowing = false;
      widget.onChanged(_selectedItem);
    }
  }
}

class MessageInputController extends GetxController {
  final hasText = false.obs;
  final hasFiles = false.obs;
  final hasFailedUploads = false.obs;
  var completedText = "";
  Timer? _navigationTimer;

  // final speeching = false.obs;
  final TextEditingController textEditingController = TextEditingController();
  final FocusNode focusNode = FocusNode();
  ValueChanged<String>? onSend;

  final isJoin = false.obs;

  int get key => this.hashCode;

  bool get hasUse {
    bool isRegister = Get.isRegistered<RtcLogic>();
    if (isRegister) {
      isJoin.value = Get.find<RtcLogic>().rxJoin();
    }
    return isJoin.value;
  }

  void addOnSendListener(ValueChanged<String>? onSend) {
    this.onSend = onSend;
  }

  @override
  void onInit() {
    super.onInit();
    textEditingController.addListener(_onTextChanged);
    Get.find<BlueToothManager>().init();
    Get.find<BlueToothManager>().setNotificationCallback((data) async {
      debugPrint("收到蓝牙数据: $data");
      if ((data["type"] ?? "") == "double_click") {
        // 检查是否已经初始化，否则弹窗提醒
        if (AzureSpeechManager.isInitialized.value) {
          // 启动语音识别
          startRecognition(isFromBle: true);
        } else {
          AzureSpeechManager.showUnInitializingDialog();
        }
      }
    });
  }

  void startRecognition({bool isFromBle = false}) {
    var audioWaveLogic = Get.find<AudioWaveLogic>();
    AzureSpeechManager.setSpeechEventListener(this.hashCode, (event) {
      print("收到语音事件: $event");
      switch (event['event']) {
        case 'recognizing':
          debugPrint("正在识别: ${event['text']}");
          _cancelPendingNavigation();
          changeSpeech(true);
          break;
        case 'recognized':
          debugPrint("识别完成: ${event['text']}");
          // String completedText = textEditingController.text;
          completedText += event['text'].toString();
          completedText = completedText.replaceAll("。", ".");
          //textEditingController.text = completedText;
          if (audioWaveLogic.isPressedFinished.value) {
            print("completedText:${completedText}");
            textEditingController.text = completedText;
            completedText = "";
            stopRecognize();
          } else {
            if (AzureSpeechManager.isFromBle) {
              _scheduleNavigation(completedText);
            }
          }
          break;
        case 'sessionStarted':
          debugPrint("开始识别");
          _cancelPendingNavigation();
          audioWaveLogic.isLoading.value = false;
          audioWaveLogic.isPressedFinished.value = false;
          completedText = textEditingController.text;
          changeSpeech(true);
          break;
        case 'sessionStopped':
          debugPrint("识别结束");
          audioWaveLogic.isLoading.value = false;
          changeSpeech(false);
          break;
        case 'canceled':
          debugPrint("取消识别: ${event['details'] ?? '未知原因'}");
          _cancelPendingNavigation();
          audioWaveLogic.isLoading.value = false;
          changeSpeech(false);
          break;
        default:
          debugPrint("未知事件类型: ${event['event']}");
      }
    });
    AzureSpeechManager.switchMode(RecognizerMode.normal);
    AzureSpeechManager.startContinuousRecognition(isFromBle);
  }

  void _scheduleNavigation(String text) {
    // 取消之前的导航定时器
    _cancelPendingNavigation();

    // 设置新的2秒延迟导航
    _navigationTimer = Timer(Duration(seconds: 2), () {
      if (AzureSpeechManager.isFromBle) {
        // 不发送空消息
        if (text.trim().isEmpty) {
          return;
        }
        var audioWaveLogic = Get.find<AudioWaveLogic>();
        audioWaveLogic.isPressedFinished.value = true;

        //textEditingController.text = completedText;
        _handleSend(text);
        completedText = "";
      }
      stopRecognize();
    });
  }

  // 取消待处理的导航
  void _cancelPendingNavigation() {
    _navigationTimer?.cancel();
    _navigationTimer = null;
  }

  void _handleSend(String text) {
    onSend?.call(text);
  }

  void stopRecognize() {
    AzureSpeechManager.stopContinuousRecognition();
    AzureSpeechManager.removeSpeechEventListener();
  }

  void _onTextChanged() {
    hasText.value = textEditingController.text.trim().isNotEmpty;
  }

  void changeSpeech(bool speeching) {
    // if (this.speeching() != speeching) {
    //   this.speeching.value = speeching;
    // }
  }

  /// 重试上传指定索引的文件
  void retryUpload(int index) async {
    // 这个方法需要在使用的地方传入uploadFiles列表
    // 这里暂时留空，具体实现在controller中
  }

  /// 检查上传文件状态并更新hasFailedUploads
  void checkUploadStatus(List<MessageUploadModel>? uploadFiles) {
    if (uploadFiles == null || uploadFiles.isEmpty) {
      hasFailedUploads.value = false;
      return;
    }

    bool hasFailed =
        uploadFiles.any((file) => file.uploadStatus != UploadStatus.success);
    hasFailedUploads.value = hasFailed;
  }

  @override
  void dispose() {
    _cancelPendingNavigation();
    textEditingController.removeListener(_onTextChanged);
    textEditingController.dispose();
    focusNode.dispose();
    super.dispose();
  }
}

/// 文件展示组件
class FileDisplayWidget extends StatelessWidget {
  final List<MessageUploadModel> files;
  final ValueChanged<int>? onRemoveFile;
  final ValueChanged<int>? onRetryUpload;

  const FileDisplayWidget({
    super.key,
    required this.files,
    this.onRemoveFile,
    this.onRetryUpload,
  });

  @override
  Widget build(BuildContext context) {
    if (files.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.only(top: 8.h, left: 12.w, right: 12.w, bottom: 8.h),
      alignment: Alignment.centerLeft,
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children: List.generate(
            files.length, (index) => _buildFileItem(files[index], index)),
      ),
    );
  }

  Widget _buildFileItem(MessageUploadModel file, int index) {
    bool isImage = file.mime_type?.startsWith('image/') ?? false;
    if (!isImage) {
      //其他文件暂时不支持
      return const SizedBox();
    }

    // 确定要显示的图片路径
    String? imagePath = file.localFilePath ?? file.file_url;
    if (imagePath == null) {
      return const SizedBox();
    }

    return Container(
      constraints: BoxConstraints(maxWidth: 200.w),
      child: _buildItemAndClose(
          43.w,
          57.h,
          index,
          Container(
            width: 43.w,
            height: 57.h,
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(6.r),
                  child: Image.file(
                    File(imagePath),
                    width: 43.w,
                    height: 57.h,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) =>
                        _buildFileIcon(),
                  ),
                ),
                // 根据上传状态显示不同的覆盖层
                if (file.uploadStatus == UploadStatus.loading)
                  _buildLoadingOverlay(),
                if (file.uploadStatus == UploadStatus.failed)
                  _buildRetryOverlay(index),
              ],
            ),
          )),
    );
  }

  Widget _buildItemAndClose(
      double width, double height, int index, Widget item) {
    return Container(
      width: width + 5.w,
      height: height + 5.h,
      child: Stack(
        fit: StackFit.expand,
        children: [
          Positioned(bottom: 0, left: 0, child: item),
          Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: () {
                onRemoveFile?.call(index);
              },
              child: Container(
                width: 20.w,
                height: 20.h,
                alignment: Alignment.topRight,
                padding: EdgeInsets.only(top: 1.h, right: 1.w),
                child: Image.asset("assets/images/ic_message_input_close.png",
                    width: 10.w, height: 10.h),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileIcon() {
    return Icon(
      Icons.insert_drive_file,
      color: Color(0xFF988B9A),
      size: 24.w,
    );
  }

  Widget _buildLoadingOverlay() {
    return Positioned.fill(
        child: Center(
      child: SizedBox(
        width: 18,
        height: 18,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFCB53BA)),
        ),
      ),
    ));
  }

  Widget _buildRetryOverlay(int index) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.5),
          borderRadius: BorderRadius.circular(6.r),
        ),
        child: Center(
          child: GestureDetector(
            onTap: () {
              onRetryUpload?.call(index);
            },
            child: Container(
              width: 20.w,
              height: 20.h,
              decoration: BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.refresh,
                color: Colors.white,
                size: 14.w,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class MessageUploadModel {
  /**
      {"file_url" : "https://s3.ap-southeast-1.amazonaws.com/agnes-sg/2c9a49a4-2ec4-4c55-bc30-89208fcbc793.jpg","file_title" : "1000026127.jpg","mime_type" : "image/jpeg","size" : 0,"content" : null}
   */
  final String? file_url;
  final String? file_title;
  final String? mime_type;
  final int? size;
  final String? content;
  final UploadStatus uploadStatus;
  final String? localFilePath; // 本地文件路径，用于显示未上传的图片
  final bool export;

  MessageUploadModel(
      {this.file_url,
      this.file_title,
      this.mime_type,
      this.size,
      this.content,
      this.uploadStatus = UploadStatus.loading,
      this.localFilePath,
      this.export = false});

  factory MessageUploadModel.fromJson(Map<String, dynamic> json) {
    return MessageUploadModel(
      file_url: json['file_url'],
      file_title: json['file_title'],
      mime_type: json['mime_type'],
      size: json['size'],
      content: json['content'],
      uploadStatus: UploadStatus.success, // API返回的认为是已成功上传
    );
  }

  Map<String, dynamic> toRequestDocIdJson() {
    final data = <String, dynamic>{};
    data['file_url'] = file_url;
    data['file_title'] = file_title;
    data['mime_type'] = mime_type;
    data['size'] = size;
    data['content'] = content;
    return data;
  }

  Map<String, dynamic> toDocJson() {
    final data = <String, dynamic>{};
    data['url'] = file_url;
    data['title'] = file_title;
    data['mime_type'] = mime_type;
    return data;
  }

  MessageUploadModel copyWith({
    String? file_url,
    String? file_title,
    String? mime_type,
    int? size,
    String? content,
    UploadStatus? uploadStatus,
    String? localFilePath,
  }) {
    return MessageUploadModel(
      file_url: file_url ?? this.file_url,
      file_title: file_title ?? this.file_title,
      mime_type: mime_type ?? this.mime_type,
      size: size ?? this.size,
      content: content ?? this.content,
      uploadStatus: uploadStatus ?? this.uploadStatus,
      localFilePath: localFilePath ?? this.localFilePath,
    );
  }
}

class MessageAtModel {
  final String? atName;
  final String? atId;

  MessageAtModel({this.atName, this.atId});
}
