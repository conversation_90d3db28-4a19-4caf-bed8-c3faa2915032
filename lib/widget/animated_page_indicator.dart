import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AnimatedPageIndicator extends StatelessWidget {
  final int currentPage;
  final int pageCount;
  final Color activeColor;
  final Color inactiveColor;
  final double activeSize;
  final double inactiveSize;
  final double spacing;

  const AnimatedPageIndicator({
    Key? key,
    required this.currentPage,
    required this.pageCount,
    this.activeColor = Colors.white,
    this.inactiveColor = Colors.grey,
    this.activeSize = 10.0,
    this.inactiveSize = 8.0,
    this.spacing = 4.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(pageCount, (index) {
        final bool isActive = index == currentPage;
        return AnimatedContainer(
          duration: Duration(milliseconds: 300),
          width: (isActive ? activeSize : inactiveSize).w,
          height: (isActive ? activeSize : inactiveSize).w,
          margin: EdgeInsets.symmetric(horizontal: spacing.w),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? activeColor : inactiveColor,
          ),
        );
      }),
    );
  }
}