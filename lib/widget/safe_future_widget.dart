import 'package:flutter/material.dart';

/// <AUTHOR>
/// @Date 2025/8/22 18:55
///
/// @Description 使用FutureBuilder造成的重复刷新,
/// 注意：需要重复刷新的场景，请不要使用SafeFutureBuilder
class SafeFutureBuilder<T> extends StatefulWidget {
  final Future<T> Function() futureBuilder;
  final Widget Function(BuildContext, T) onData;
  final Widget Function(BuildContext, Object)? onError;
  final Widget Function(BuildContext)? onLoading;

  const SafeFutureBuilder({
    Key? key,
    required this.futureBuilder,
    required this.onData,
    this.onError,
    this.onLoading,
  }) : super(key: key);

  @override
  _SafeFutureBuilderState<T> createState() => _SafeFutureBuilderState<T>();
}

class _SafeFutureBuilderState<T> extends State<SafeFutureBuilder<T>> {
  late Future<T> futureTask;

  @override
  void initState() {
    super.initState();
    futureTask = widget.futureBuilder();
  }

  void refresh() {
    setState(() {
      futureTask = widget.futureBuilder();
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<T>(
      future: futureTask,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return widget.onLoading?.call(context) ??
              const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return widget.onError?.call(context, snapshot.error!) ??
              Center(child: Text('Error: ${snapshot.error}'));
        } else if (snapshot.hasData) {
          return widget.onData(context, snapshot.data as T);
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }
}
