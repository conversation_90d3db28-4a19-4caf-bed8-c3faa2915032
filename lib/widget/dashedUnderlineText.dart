
import 'package:flutter/material.dart';

class DashedUnderlineText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final Color dashColor;
  final double dashHeight;
  final double dashWidth;
  final double dashSpace;

  const DashedUnderlineText({
    required this.text,
    this.style,
    this.dashColor = Colors.black,
    this.dashHeight = 1,
    this.dashWidth = 5,
    this.dashSpace = 0,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
    )..layout();

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(text, style: style),
        SizedBox(height: 2),
        Container(
          width: textPainter.width,
          height: dashHeight,
          child: CustomPaint(
            painter: _DashedLinePainter(
              color: dashColor,
              dashWidth: dashWidth,
              dashHeight: dashHeight,
              dashSpace: dashSpace,
            ),
          ),
        ),
      ],
    );
  }
}

class _DashedLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashHeight;
  final double dashSpace;

  _DashedLinePainter({
    required this.color,
    required this.dashWidth,
    required this.dashHeight,
    required this.dashSpace,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = dashHeight
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}