// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(termsOfService, privacyPolicy) =>
      "By signing in, you are agreeing to the\n${termsOfService} and ${privacyPolicy}.";

  static String m1(value) => "Research for ${value} seconds";

  static String m2(title) =>
      "Sorry, your ${title} task failed. Please try again later.";

  static String m3(value) => "Supports uploading up to ${value} images.";

  static String m4(remaining_count) =>
      "Tasks deleted as requested. Current pending tasks: ${remaining_count}. Would you like me to show all pending tasks?";

  static String m5(total) => "There are currently ${total} pending tasks:";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "User ${user_name} has insufficient quota, slides remaining: ${slides_remaining_count}, slides required quota: ${slides_plan_count}, research remaining: ${research_remaining_count}, research required quota: ${research_plan_count}. You can choose tasks with enough quota to execute.";

  static String m7(xxx) => "Verification code sent to ${xxx}@email.com";

  static String m8(version) => "Version ${version} is now available.";

  static String m9(x) => "${x} members in voice...";

  static String m10(xxx) => "${xxx} changed the group name";

  static String m11(xxx) =>
      "${xxx} has invited you to collaborate on editing \"${xxx}\".";

  static String m12(xxx) =>
      "${xxx} has invited you to collaborate on reviewing \"${xxx}\".";

  static String m13(xxx) => "${xxx} has started a voice call";

  static String m14(xxx, xyz) =>
      "${xxx} invites you to join the \"${xyz}\" group call.";

  static String m15(title) =>
      "Your ${title} task has been successfully completed!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond": MessageLookupByLibrary.simpleMessage("About Diamond"),
        "accept": MessageLookupByLibrary.simpleMessage("Accept"),
        "account": MessageLookupByLibrary.simpleMessage("Account"),
        "accountDeletionSuccessful":
            MessageLookupByLibrary.simpleMessage("Account deletion successful"),
        "accountNotCreatedYet":
            MessageLookupByLibrary.simpleMessage("Account not created yet"),
        "accountSignoutSuccessful":
            MessageLookupByLibrary.simpleMessage("Account signout successful"),
        "activeAccount": MessageLookupByLibrary.simpleMessage("Active Account"),
        "add": MessageLookupByLibrary.simpleMessage("Add"),
        "addComment": MessageLookupByLibrary.simpleMessage("Add Comment"),
        "addPeopleToChat":
            MessageLookupByLibrary.simpleMessage("Add People To Chat"),
        "addToChat": MessageLookupByLibrary.simpleMessage("Add to Chat"),
        "addUserEmailAndEnterToInvite": MessageLookupByLibrary.simpleMessage(
            "Add user email and \"enter\" to invite"),
        "addedToGroupSuccessfully":
            MessageLookupByLibrary.simpleMessage("Added to group successfully"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Agnes can make mistakes. Check important info."),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage(
                "@Agnes for summaries, reports, or slides."),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn":
            MessageLookupByLibrary.simpleMessage("Agree and Log In"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("AI Design"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("AI Slides"),
        "album": MessageLookupByLibrary.simpleMessage("Album"),
        "allMembers": MessageLookupByLibrary.simpleMessage("All Members"),
        "answerCall": MessageLookupByLibrary.simpleMessage("Answer Call"),
        "appUpdate": MessageLookupByLibrary.simpleMessage("App Update"),
        "archived": MessageLookupByLibrary.simpleMessage("Archived"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure you want to delete your account?"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure you want to sign out your account?"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Ask Agnes"),
        "askAgnesToEditSlides":
            MessageLookupByLibrary.simpleMessage("Ask Agnes to edit slides"),
        "assignATaskToBegin":
            MessageLookupByLibrary.simpleMessage("Assign a task to begin"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "At least six characters long with letters, numbers & symbols"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "At present, there is no consensus on the tasks to be done. Feel free to discuss further and ask questions if you have any!"),
        "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
        "back": MessageLookupByLibrary.simpleMessage("Back"),
        "bookmarkRemoved":
            MessageLookupByLibrary.simpleMessage("Bookmark removed"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("Bookmarked"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined": MessageLookupByLibrary.simpleMessage("Call declined"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "canvas": MessageLookupByLibrary.simpleMessage("Canvas"),
        "chapter": MessageLookupByLibrary.simpleMessage("Chapter"),
        "chat": MessageLookupByLibrary.simpleMessage("Chat"),
        "chatSettings": MessageLookupByLibrary.simpleMessage("Chat Settings"),
        "chooseImage": MessageLookupByLibrary.simpleMessage("Choose Image"),
        "close": MessageLookupByLibrary.simpleMessage("Close"),
        "code": MessageLookupByLibrary.simpleMessage("Code"),
        "collect": MessageLookupByLibrary.simpleMessage("Collect"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("Coming Soon"),
        "comment": MessageLookupByLibrary.simpleMessage("Comment"),
        "commenter": MessageLookupByLibrary.simpleMessage("Commenter"),
        "commentsTasks": MessageLookupByLibrary.simpleMessage("Comments Tasks"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "confirmDelete": MessageLookupByLibrary.simpleMessage("Confirm Delete"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirm Password"),
        "confirmPasswordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Confirm password cannot be empty"),
        "confirmSignOut":
            MessageLookupByLibrary.simpleMessage("Confirm Sign Out"),
        "connect": MessageLookupByLibrary.simpleMessage("Connect"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage(
                "Connect the AI headset and chat with Agnes at any time"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage(
                "Connect to Agnes VibePods and chat anytime."),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Connect with Friends to Start a Group Chat"),
        "connected": MessageLookupByLibrary.simpleMessage("Connected"),
        "connectionFailed":
            MessageLookupByLibrary.simpleMessage("Connection failed"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "Content copied—go ahead and paste it on your favorite platform!"),
        "continueWithApple":
            MessageLookupByLibrary.simpleMessage("Continue with Apple"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("Continue with Google"),
        "continueWithOtherWays":
            MessageLookupByLibrary.simpleMessage("Continue with other ways"),
        "continueWord": MessageLookupByLibrary.simpleMessage("Continue"),
        "copied": MessageLookupByLibrary.simpleMessage("Copied"),
        "copy": MessageLookupByLibrary.simpleMessage("Copy"),
        "cost": MessageLookupByLibrary.simpleMessage("cost"),
        "cover": MessageLookupByLibrary.simpleMessage("Cover"),
        "createANewTaskToGetStarted": MessageLookupByLibrary.simpleMessage(
            "Create a new task to get started"),
        "createAnAccountToSignUpForAgnes": MessageLookupByLibrary.simpleMessage(
            "Create an account to sign up for Agnes"),
        "createGroupChat":
            MessageLookupByLibrary.simpleMessage("Create Group Chat"),
        "currentlyInACall":
            MessageLookupByLibrary.simpleMessage("Currently in a call..."),
        "deepResearch": MessageLookupByLibrary.simpleMessage("Deep Research"),
        "deepresearch": MessageLookupByLibrary.simpleMessage("DeepResearch"),
        "delete": MessageLookupByLibrary.simpleMessage("Delete"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete Account"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Delete Chat"),
        "doYouWantToExecuteTheseTasks": MessageLookupByLibrary.simpleMessage(
            "Do you want to execute these tasks?"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                " doesn’t seem to be an Agnes user. Enter their email to send an invite."),
        "done": MessageLookupByLibrary.simpleMessage("done"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Don’t have an account?"),
        "downloading": MessageLookupByLibrary.simpleMessage("Downloading"),
        "eachNewTaskConsumesOneDiamond": MessageLookupByLibrary.simpleMessage(
            "1. Each new task consumes one diamond."),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. Each user receives 30 diamonds per month."),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "editGroupName":
            MessageLookupByLibrary.simpleMessage("Edit group name"),
        "editor": MessageLookupByLibrary.simpleMessage("Editor"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("Email cannot be empty"),
        "emailFormatIncorrect":
            MessageLookupByLibrary.simpleMessage("Email format incorrect"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage(
                "Enhance productivity and creativity through extended access"),
        "enterConfirmPassword":
            MessageLookupByLibrary.simpleMessage("Enter Confirm Password"),
        "enterGroupName":
            MessageLookupByLibrary.simpleMessage("Enter group name"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Enter Password"),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("Enter Verification Code"),
        "export": MessageLookupByLibrary.simpleMessage("Export"),
        "file": MessageLookupByLibrary.simpleMessage("File"),
        "floatingWindowNotEnabled":
            MessageLookupByLibrary.simpleMessage("Floating Window Not Enabled"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage(
                "For the full experience, please use the PC version"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Forgot Password"),
        "general": MessageLookupByLibrary.simpleMessage("General"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Get Agnes AI Plus"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Get Agnes AI Pro"),
        "giveMeATaskToWorkOn":
            MessageLookupByLibrary.simpleMessage("Give me a task to work on…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("Go To Shop"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage(
                "Got it! I\'m working on the task. Please wait a moment."),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Group created successfully"),
        "groupFiles": MessageLookupByLibrary.simpleMessage("Group Files"),
        "groupName": MessageLookupByLibrary.simpleMessage("Group name"),
        "groupNameOptional":
            MessageLookupByLibrary.simpleMessage("Group Name (Optional)"),
        "harmful": MessageLookupByLibrary.simpleMessage("Harmful"),
        "helpUsImprove":
            MessageLookupByLibrary.simpleMessage("Help us improve"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage(
                "I assist with search, writing, and answers"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage(
                "If you answer the incoming call yourcurrent call will be ended."),
        "imAgnes": MessageLookupByLibrary.simpleMessage("I\'m Agnes"),
        "inAVoiceCall": MessageLookupByLibrary.simpleMessage("In a voice call"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("Inaccurate"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "In-app purchases are currently unavailable, Please try again later"),
        "interfaceLanguage":
            MessageLookupByLibrary.simpleMessage("Interface Language"),
        "invite": MessageLookupByLibrary.simpleMessage("Invite"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage(
                "Invite by username, email, or phone number"),
        "inviteFriends": MessageLookupByLibrary.simpleMessage("Invite Friends"),
        "inviteOthersToCollaborate": MessageLookupByLibrary.simpleMessage(
            "Invite others to collaborate"),
        "inviteToCollaborate":
            MessageLookupByLibrary.simpleMessage("Invite to collaborate"),
        "isEditing": MessageLookupByLibrary.simpleMessage("is editing"),
        "join": MessageLookupByLibrary.simpleMessage("Join"),
        "knowMore": MessageLookupByLibrary.simpleMessage("Know More"),
        "language": MessageLookupByLibrary.simpleMessage("English"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Let Agnes help you create a better sharing format"),
        "letAgnesResolve":
            MessageLookupByLibrary.simpleMessage("Let Agnes resolve"),
        "linkCopied": MessageLookupByLibrary.simpleMessage("Link copied"),
        "listening": MessageLookupByLibrary.simpleMessage("Listening"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading"),
        "makeMeAShareableImagePost": MessageLookupByLibrary.simpleMessage(
            "Make me a shareable image post"),
        "makeMeAnHtmlWebPage":
            MessageLookupByLibrary.simpleMessage("Make me an HTML web page"),
        "manageMembers": MessageLookupByLibrary.simpleMessage("Manage Members"),
        "manuallyEndTheTask":
            MessageLookupByLibrary.simpleMessage("Manually end the task."),
        "markAsResolved":
            MessageLookupByLibrary.simpleMessage("Mark as Resolved"),
        "messageCopied":
            MessageLookupByLibrary.simpleMessage("Message Copied."),
        "multipleChoices":
            MessageLookupByLibrary.simpleMessage("Multiple choices"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("My Favorites"),
        "narrate": MessageLookupByLibrary.simpleMessage("Narrate"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Network connection failed. Please try again."),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("Network connection lost"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("Network unavailable"),
        "newPassword": MessageLookupByLibrary.simpleMessage("New Password"),
        "newProject": MessageLookupByLibrary.simpleMessage("New Project"),
        "newTask": MessageLookupByLibrary.simpleMessage("New Task"),
        "newTitle": MessageLookupByLibrary.simpleMessage("New Title"),
        "noMoreFiles": MessageLookupByLibrary.simpleMessage("No More Files"),
        "noMoreMessage":
            MessageLookupByLibrary.simpleMessage("no more message..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "No pending tasks found. All tasks have been completed!"),
        "notAvailableForPurchase":
            MessageLookupByLibrary.simpleMessage("Not Available for Purchase"),
        "notConnected": MessageLookupByLibrary.simpleMessage("Not connected"),
        "notNow": MessageLookupByLibrary.simpleMessage("Not now"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
        "notifyUserViaEmail":
            MessageLookupByLibrary.simpleMessage("Notify user via email"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "Okay,  I noticed you don’t have the required permissions. Please ask the owner or an editor to do it."),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage(
                "Okay, I\'ll summarize the consensus tasks:"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage(
                "Once deleted, group chat history will be cleared."),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("Operation Successful!"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage(
                "Or let me know if you have any questions."),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage(
                "Other group members will be notified after the group name is changed."),
        "outOfDate": MessageLookupByLibrary.simpleMessage("Out of date"),
        "owner": MessageLookupByLibrary.simpleMessage("Owner"),
        "pages": MessageLookupByLibrary.simpleMessage("Pages"),
        "passwordCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("Password cannot be empty"),
        "pending": MessageLookupByLibrary.simpleMessage("Pending"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage(
                "Permission to execute tasks in the background is not granted"),
        "phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
        "phoneNumberCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Phone number cannot be empty"),
        "photo": MessageLookupByLibrary.simpleMessage("Photo"),
        "pleaseSelectASlideToEdit": MessageLookupByLibrary.simpleMessage(
            "Please select a slide to edit."),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "Please update to the latest version for new features and improvements."),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage(
                "Please wait for Agnes to complete the task"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Please wait until the download is completed"),
        "presenter": MessageLookupByLibrary.simpleMessage("Presenter"),
        "preview": MessageLookupByLibrary.simpleMessage("Preview"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage(
                "Provide additional feedback on this answer"),
        "quote": MessageLookupByLibrary.simpleMessage("Quote"),
        "quotedMessageHasExpired":
            MessageLookupByLibrary.simpleMessage("Quoted message has expired."),
        "reconnect": MessageLookupByLibrary.simpleMessage("Reconnect"),
        "reject": MessageLookupByLibrary.simpleMessage("Reject"),
        "remove": MessageLookupByLibrary.simpleMessage("Remove"),
        "removedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Removed successfully"),
        "rename": MessageLookupByLibrary.simpleMessage("Rename"),
        "reopenTheApp": MessageLookupByLibrary.simpleMessage("Reopen the app"),
        "requestTimedOutPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Request timed out. Please try again."),
        "research": MessageLookupByLibrary.simpleMessage("Research"),
        "researchForZdSeconds": m1,
        "resetPassword": MessageLookupByLibrary.simpleMessage("Reset Password"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Reset Your Password"),
        "role": MessageLookupByLibrary.simpleMessage("Role"),
        "save": MessageLookupByLibrary.simpleMessage("Save"),
        "saved": MessageLookupByLibrary.simpleMessage("Saved"),
        "saving": MessageLookupByLibrary.simpleMessage("Saving…"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "searchByNameEmailOrPhone": MessageLookupByLibrary.simpleMessage(
            "Search by name, email or phone"),
        "searchForACountry":
            MessageLookupByLibrary.simpleMessage("Search for a country"),
        "seconds": MessageLookupByLibrary.simpleMessage("Seconds"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage(
                "Seems to be disconnected from the internet"),
        "selectMembers": MessageLookupByLibrary.simpleMessage("Select Members"),
        "send": MessageLookupByLibrary.simpleMessage("Send"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Send Message"),
        "serverConnectionFailed":
            MessageLookupByLibrary.simpleMessage("Server connection failed."),
        "serviceIsStillStarting":
            MessageLookupByLibrary.simpleMessage("Service is still starting."),
        "sessionExpiredPleaseLogInAgain": MessageLookupByLibrary.simpleMessage(
            "Session expired. Please log in again."),
        "setNow": MessageLookupByLibrary.simpleMessage("Set Now"),
        "settings": MessageLookupByLibrary.simpleMessage("Settings"),
        "share": MessageLookupByLibrary.simpleMessage("Share"),
        "signIn": MessageLookupByLibrary.simpleMessage("Sign In"),
        "signOut": MessageLookupByLibrary.simpleMessage("Sign Out"),
        "signUp": MessageLookupByLibrary.simpleMessage("Sign Up"),
        "someoneMe": MessageLookupByLibrary.simpleMessage("Someone @me"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "Sorry, we’re at full capacity right now. Please try again tomorrow."),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking": MessageLookupByLibrary.simpleMessage("Start speaking"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry": MessageLookupByLibrary.simpleMessage(
            "Switch Wi-Fi/mobile data and retry"),
        "system": MessageLookupByLibrary.simpleMessage("System"),
        "systemBusyPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "System busy, please try again."),
        "takePhoto": MessageLookupByLibrary.simpleMessage("Take Photo"),
        "taskStoppedManually":
            MessageLookupByLibrary.simpleMessage("Task stopped manually."),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService":
            MessageLookupByLibrary.simpleMessage("Terms of Service"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage(
                "2. The diamond count updates on the 1st of every month."),
        "theGroupIsCurrentlyInACall": MessageLookupByLibrary.simpleMessage(
            "The group is currently in a call..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage(
                "The language used in the user interface"),
        "thePhoneNumberIsInvalid":
            MessageLookupByLibrary.simpleMessage("The phone number is invalid"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "This app requires camera access to let you take a photo for your profile picture."),
        "thisIsntHelpful":
            MessageLookupByLibrary.simpleMessage("This isn\'t helpful"),
        "thisSlideIsBeingEdited":
            MessageLookupByLibrary.simpleMessage("This slide is being edited."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "To allow you to send images for recognition, interaction, or setting your avatar, please allow Agnes to access your camera."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "To allow you to send images for recognition, interaction, or setting your avatar, please allow Agnes to access your photo library."),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "To save generated images, please allow Agnes to save images to your photo library."),
        "transcribing": MessageLookupByLibrary.simpleMessage("Transcribing..."),
        "transcriptionEnabledLoadingData": MessageLookupByLibrary.simpleMessage(
            "Transcription enabled. Loading data…"),
        "tryOneOfThese":
            MessageLookupByLibrary.simpleMessage("Try one of these:"),
        "twoPasswordsAreInconsistent": MessageLookupByLibrary.simpleMessage(
            "Two passwords are inconsistent"),
        "update": MessageLookupByLibrary.simpleMessage("Update"),
        "updateFailedPleaseTryAgainLater": MessageLookupByLibrary.simpleMessage(
            "Update failed. Please try again later."),
        "updatePassword":
            MessageLookupByLibrary.simpleMessage("Update Password"),
        "updatePasswordSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Update password successfully"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage(
                "Upgrade your plan to continue: Upgrade"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Use 6+ characters with letters, numbers & symbols"),
        "userAccess": MessageLookupByLibrary.simpleMessage("User Access"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("Username"),
        "usersWithAccess":
            MessageLookupByLibrary.simpleMessage("Users with access"),
        "usingTool": MessageLookupByLibrary.simpleMessage("Using Tool"),
        "verificationCode":
            MessageLookupByLibrary.simpleMessage("Verification Code"),
        "verificationCodeCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Verification code cannot be empty"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("View"),
        "viewAllComments":
            MessageLookupByLibrary.simpleMessage("View all comments"),
        "viewer": MessageLookupByLibrary.simpleMessage("Viewer"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Voice call creation failed, possibly due to network instability. Please try again."),
        "voiceCallEnded":
            MessageLookupByLibrary.simpleMessage("Voice call ended"),
        "waiting": MessageLookupByLibrary.simpleMessage("Waiting"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "We need access to save downloaded images or videos to your photo library."),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "We need access to your microphone for voice interaction."),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "We need access to your photo library so you can select a picture for your profile."),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage(
                "We need to access your photo library to select images"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "We need to recognize your speech to convert it to text for interaction."),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "We need to save images to your photo library"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "We need to use Bluetooth to connect to your headset for voice interaction."),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage(
                "We need to use your camera to take photos"),
        "welcomeToAgnes":
            MessageLookupByLibrary.simpleMessage("Welcome to Agnes"),
        "writeSomething":
            MessageLookupByLibrary.simpleMessage("Write Something"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("You"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage(
                "You can execute, modify, or remove, how would you like to proceed?"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "You can manually edit the slides or edit through conversation with Agnes."),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage(
                "You can select content and share your feedback."),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "You can\'t minimize a video call as AGNES isn\'t authorized to use floating windows."),
        "youCreatedTheGroup":
            MessageLookupByLibrary.simpleMessage("You created the group."),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "You have been removed from the group chat"),
        "youJoinedTheGroup":
            MessageLookupByLibrary.simpleMessage("You joined the group."),
        "yourCurrentPlan":
            MessageLookupByLibrary.simpleMessage("Your current plan"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "Your phone has not authorized Agnes to use the floating window permission. Agnes will not be able to continue performing tasks in the background for you."),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Your request for the research task limit has been reached."),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Your request for the slide task limit has been reached."),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Your request for this task limit has been reached."),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd":
            MessageLookupByLibrary.simpleMessage("You’ve reached the end")
      };
}
