// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fr';

  static String m0(termsOfService, privacyPolicy) =>
      "En vous connectant, vous acceptez les\n${termsOfService} et la ${privacyPolicy}.";

  static String m1(value) => "Recherche pendant ${value} secondes";

  static String m2(title) =>
      "Désolé, votre tâche ${title} a échoué. Veuillez réessayer plus tard.";

  static String m3(value) =>
      "Prend en charge le téléchargement jusqu’à ${value} images.";

  static String m4(remaining_count) =>
      "Tâches supprimées comme demandé. Nombre actuel de tâches en attente : ${remaining_count}. Voulez-vous que j’affiche toutes les tâches en attente ?";

  static String m5(total) => "Il y a actuellement ${total} tâches en attente :";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "L\'utilisateur ${user_name} n\'a pas assez de quota, diapositives restantes: ${slides_remaining_count}, quota requis pour les diapositives: ${slides_plan_count}, recherche restante: ${research_remaining_count}, quota requis pour la recherche: ${research_plan_count}. Vous pouvez choisir des tâches avec assez de quota pour exécuter.";

  static String m7(xxx) => "Code de vérification envoyé à ${xxx}@email.com";

  static String m8(version) =>
      "La version ${version} est maintenant disponible";

  static String m9(x) => "${x} membres en vocal...";

  static String m10(xxx) => "${xxx} a changé le nom du groupe";

  static String m11(xxx) =>
      "${xxx} vous a invité à collaborer à la modification de \"${xxx}\".";

  static String m12(xxx) =>
      "${xxx} vous a invité à collaborer à la relecture de \"${xxx}\".";

  static String m13(xxx) => "${xxx} a démarré un appel vocal";

  static String m14(xxx, xyz) =>
      "${xxx} vous invite à rejoindre l’appel de groupe \"${xyz}\".";

  static String m15(title) =>
      "Votre tâche ${title} a été terminée avec succès !";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond":
            MessageLookupByLibrary.simpleMessage("À propos des Diamants"),
        "accept": MessageLookupByLibrary.simpleMessage("Accepter"),
        "account": MessageLookupByLibrary.simpleMessage("Compte"),
        "accountDeletionSuccessful": MessageLookupByLibrary.simpleMessage(
            "Suppression du compte réussie"),
        "accountNotCreatedYet":
            MessageLookupByLibrary.simpleMessage("Compte non encore créé"),
        "accountSignoutSuccessful": MessageLookupByLibrary.simpleMessage(
            "Déconnexion du compte réussie"),
        "activeAccount": MessageLookupByLibrary.simpleMessage("Compte actif"),
        "add": MessageLookupByLibrary.simpleMessage("Ajouter"),
        "addComment":
            MessageLookupByLibrary.simpleMessage("Ajouter un commentaire"),
        "addPeopleToChat": MessageLookupByLibrary.simpleMessage(
            "Ajouter des personnes au chat"),
        "addToChat": MessageLookupByLibrary.simpleMessage("Ajouter au chat"),
        "addUserEmailAndEnterToInvite": MessageLookupByLibrary.simpleMessage(
            "Ajoutez l\'email de l\'utilisateur et appuyez sur \"entrée\" pour inviter"),
        "addedToGroupSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Ajouté au groupe avec succès"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Agnes peut se tromper. Vérifiez les informations importantes."),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage(
                "@Agnes pour des résumés, rapports ou diapositives."),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn":
            MessageLookupByLibrary.simpleMessage("Accepter et se connecter"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("Conception IA"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("Diapositives IA"),
        "album": MessageLookupByLibrary.simpleMessage("Album"),
        "allMembers": MessageLookupByLibrary.simpleMessage("Tous les membres"),
        "answerCall":
            MessageLookupByLibrary.simpleMessage("Répondre à l’appel"),
        "appUpdate": MessageLookupByLibrary.simpleMessage(
            "Mise à jour de l’application"),
        "archived": MessageLookupByLibrary.simpleMessage("Archivé"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Êtes-vous sûr de vouloir supprimer votre compte ?"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Êtes-vous sûr de vouloir vous déconnecter de votre compte ?"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Demander à Agnes"),
        "askAgnesToEditSlides": MessageLookupByLibrary.simpleMessage(
            "Demander à Agnes de modifier les diapositives"),
        "assignATaskToBegin": MessageLookupByLibrary.simpleMessage(
            "Attribuez une tâche pour commencer"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Au moins six caractères avec lettres, chiffres et symboles"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "Actuellement, il n\'y a pas de consensus sur les tâches à accomplir. N\'hésitez pas à discuter davantage et à poser des questions si vous en avez!"),
        "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
        "back": MessageLookupByLibrary.simpleMessage("Retour"),
        "bookmarkRemoved":
            MessageLookupByLibrary.simpleMessage("Favori supprimé"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("Mis en favori"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined": MessageLookupByLibrary.simpleMessage("Appel refusé"),
        "cancel": MessageLookupByLibrary.simpleMessage("Annuler"),
        "canvas": MessageLookupByLibrary.simpleMessage("Canevas"),
        "chapter": MessageLookupByLibrary.simpleMessage("Chapitre"),
        "chat": MessageLookupByLibrary.simpleMessage("Discussion"),
        "chatSettings":
            MessageLookupByLibrary.simpleMessage("Paramètres du chat"),
        "chooseImage":
            MessageLookupByLibrary.simpleMessage("Choisir une image"),
        "close": MessageLookupByLibrary.simpleMessage("Fermer"),
        "code": MessageLookupByLibrary.simpleMessage("Code"),
        "collect": MessageLookupByLibrary.simpleMessage("collecter"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("Bientôt"),
        "comment": MessageLookupByLibrary.simpleMessage("Commentaire"),
        "commenter": MessageLookupByLibrary.simpleMessage("Commentateur"),
        "commentsTasks":
            MessageLookupByLibrary.simpleMessage("Tâches de Commentaires"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirmer"),
        "confirmDelete":
            MessageLookupByLibrary.simpleMessage("Confirmer la suppression"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirmer le mot de passe"),
        "confirmPasswordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "La confirmation du mot de passe ne peut pas être vide"),
        "confirmSignOut":
            MessageLookupByLibrary.simpleMessage("Confirmer la déconnexion"),
        "connect": MessageLookupByLibrary.simpleMessage("Connecter"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage(
                "Connectez le casque IA et discutez avec Agnes à tout moment"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage(
                "Connectez-vous aux Agnes VibePods et discutez à tout moment."),
        "connectWithFriendsToStartAGroupChat": MessageLookupByLibrary.simpleMessage(
            "Connectez-vous avec des amis pour démarrer une discussion de groupe"),
        "connected": MessageLookupByLibrary.simpleMessage("Connecté"),
        "connectionFailed":
            MessageLookupByLibrary.simpleMessage("Échec de la connexion"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "Contenu copié — allez-y, collez-le sur votre plateforme préférée !"),
        "continueWithApple":
            MessageLookupByLibrary.simpleMessage("Continuer avec Apple"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("Continuer avec Google"),
        "continueWithOtherWays":
            MessageLookupByLibrary.simpleMessage("Continuer autrement"),
        "continueWord": MessageLookupByLibrary.simpleMessage("Continuer"),
        "copied": MessageLookupByLibrary.simpleMessage("Copié"),
        "copy": MessageLookupByLibrary.simpleMessage("Copier"),
        "cost": MessageLookupByLibrary.simpleMessage("coût"),
        "cover": MessageLookupByLibrary.simpleMessage("Couverture"),
        "createANewTaskToGetStarted": MessageLookupByLibrary.simpleMessage(
            "Créer une nouvelle tâche pour commencer"),
        "createAnAccountToSignUpForAgnes": MessageLookupByLibrary.simpleMessage(
            "Créez un compte pour vous inscrire à Agnes"),
        "createGroupChat": MessageLookupByLibrary.simpleMessage(
            "Créer une discussion de groupe"),
        "currentlyInACall":
            MessageLookupByLibrary.simpleMessage("Actuellement en appel..."),
        "deepResearch":
            MessageLookupByLibrary.simpleMessage("Recherche approfondie"),
        "deepresearch":
            MessageLookupByLibrary.simpleMessage("Recherche Approfondie"),
        "delete": MessageLookupByLibrary.simpleMessage("Supprimer"),
        "deleteAccount":
            MessageLookupByLibrary.simpleMessage("Supprimer le compte"),
        "deleteChat":
            MessageLookupByLibrary.simpleMessage("Supprimer la discussion"),
        "doYouWantToExecuteTheseTasks": MessageLookupByLibrary.simpleMessage(
            "Voulez-vous exécuter ces tâches ?"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                " ne semble pas être un utilisateur d’Agnes. Entrez leur e-mail pour envoyer une invitation"),
        "done": MessageLookupByLibrary.simpleMessage("Terminé"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Vous n’avez pas de compte ?"),
        "downloading": MessageLookupByLibrary.simpleMessage("Téléchargement"),
        "eachNewTaskConsumesOneDiamond": MessageLookupByLibrary.simpleMessage(
            "1. Chaque nouvelle tâche consomme un diamant."),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. Chaque utilisateur reçoit 30 diamants par mois."),
        "edit": MessageLookupByLibrary.simpleMessage("Modifier"),
        "editGroupName":
            MessageLookupByLibrary.simpleMessage("Modifier le nom du groupe"),
        "editor": MessageLookupByLibrary.simpleMessage("Éditeur"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "emailCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "L’email ne peut pas être vide"),
        "emailFormatIncorrect": MessageLookupByLibrary.simpleMessage(
            "Le format de l’email est incorrect"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage(
                "Améliorez la productivité et la créativité grâce à un accès étendu"),
        "enterConfirmPassword": MessageLookupByLibrary.simpleMessage(
            "Entrez le mot de passe de confirmation"),
        "enterGroupName":
            MessageLookupByLibrary.simpleMessage("Saisir le nom du groupe"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Entrer le mot de passe"),
        "enterVerificationCode": MessageLookupByLibrary.simpleMessage(
            "Entrez le code de vérification"),
        "export": MessageLookupByLibrary.simpleMessage("Exporter"),
        "file": MessageLookupByLibrary.simpleMessage("Fichier"),
        "floatingWindowNotEnabled": MessageLookupByLibrary.simpleMessage(
            "Fenêtre flottante non activée"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage(
                "Pour profiter pleinement de l’expérience, utilisez la version PC"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Mot de passe oublié"),
        "general": MessageLookupByLibrary.simpleMessage("Général"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Obtenir Agnes AI Plus"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Obtenir Agnes AI Pro"),
        "giveMeATaskToWorkOn": MessageLookupByLibrary.simpleMessage(
            "Donnez-moi une tâche sur laquelle travailler…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("Aller au magasin"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage(
                "Compris ! Je travaille sur la tâche. Veuillez patienter un instant."),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Groupe créé avec succès"),
        "groupFiles":
            MessageLookupByLibrary.simpleMessage("Fichiers du groupe"),
        "groupName": MessageLookupByLibrary.simpleMessage("Nom du groupe"),
        "groupNameOptional":
            MessageLookupByLibrary.simpleMessage("Nom du groupe (Optionnel)"),
        "harmful": MessageLookupByLibrary.simpleMessage("Nuisible"),
        "helpUsImprove":
            MessageLookupByLibrary.simpleMessage("Aidez-nous à nous améliorer"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage(
                "J’aide à la recherche, à l’écriture et aux réponses"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage(
                "Si vous répondez à l\'appel entrant, votre appel en cours sera terminé."),
        "imAgnes": MessageLookupByLibrary.simpleMessage("Je suis Agnes"),
        "inAVoiceCall": MessageLookupByLibrary.simpleMessage("En appel vocal"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("Inexact"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Les achats intégrés ne sont pas disponibles pour le moment, veuillez réessayer plus tard"),
        "interfaceLanguage":
            MessageLookupByLibrary.simpleMessage("Langue de l\'interface"),
        "invite": MessageLookupByLibrary.simpleMessage("Inviter"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage(
                "Inviter par nom d’utilisateur, e-mail ou numéro de téléphone"),
        "inviteFriends":
            MessageLookupByLibrary.simpleMessage("Inviter des amis"),
        "inviteOthersToCollaborate": MessageLookupByLibrary.simpleMessage(
            "Inviter d\'autres à collaborer"),
        "inviteToCollaborate":
            MessageLookupByLibrary.simpleMessage("Inviter à collaborer"),
        "isEditing":
            MessageLookupByLibrary.simpleMessage("est en train de modifier"),
        "join": MessageLookupByLibrary.simpleMessage("Rejoindre"),
        "knowMore": MessageLookupByLibrary.simpleMessage("En savoir plus"),
        "language": MessageLookupByLibrary.simpleMessage("Anglais"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Laissez Agnes vous aider à créer un meilleur format de partage"),
        "letAgnesResolve":
            MessageLookupByLibrary.simpleMessage("Laissez Agnes résoudre"),
        "linkCopied": MessageLookupByLibrary.simpleMessage("Lien copié"),
        "listening": MessageLookupByLibrary.simpleMessage("Écoute"),
        "loading": MessageLookupByLibrary.simpleMessage("Chargement"),
        "makeMeAShareableImagePost": MessageLookupByLibrary.simpleMessage(
            "Faites-moi un post d’image partageable"),
        "makeMeAnHtmlWebPage":
            MessageLookupByLibrary.simpleMessage("Créez-moi une page web HTML"),
        "manageMembers":
            MessageLookupByLibrary.simpleMessage("Gérer les membres"),
        "manuallyEndTheTask": MessageLookupByLibrary.simpleMessage(
            "Terminer la tâche manuellement"),
        "markAsResolved":
            MessageLookupByLibrary.simpleMessage("Marquer comme résolu"),
        "messageCopied": MessageLookupByLibrary.simpleMessage("Message Copié."),
        "multipleChoices":
            MessageLookupByLibrary.simpleMessage("Choix multiples"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("Mes favoris"),
        "narrate": MessageLookupByLibrary.simpleMessage("Raconter"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Échec de la connexion réseau. Veuillez réessayer."),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("Connexion réseau perdue"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("Réseau indisponible"),
        "newPassword":
            MessageLookupByLibrary.simpleMessage("Nouveau mot de passe"),
        "newProject": MessageLookupByLibrary.simpleMessage("Nouveau projet"),
        "newTask": MessageLookupByLibrary.simpleMessage("Nouvelle tâche"),
        "newTitle": MessageLookupByLibrary.simpleMessage("Nouveau titre"),
        "noMoreFiles": MessageLookupByLibrary.simpleMessage("Plus de fichiers"),
        "noMoreMessage":
            MessageLookupByLibrary.simpleMessage("Plus de messages..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Aucune tâche en attente trouvée. Toutes les tâches ont été complétées !"),
        "notAvailableForPurchase":
            MessageLookupByLibrary.simpleMessage("Non disponible à l\'achat"),
        "notConnected": MessageLookupByLibrary.simpleMessage("Non connecté"),
        "notNow": MessageLookupByLibrary.simpleMessage("Pas maintenant"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
        "notifyUserViaEmail": MessageLookupByLibrary.simpleMessage(
            "Notifier l’utilisateur par e-mail"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "D\'accord, j\'ai remarqué que vous n\'avez pas les autorisations requises. Veuillez demander au propriétaire ou à un éditeur de le faire."),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage(
                "D’accord, je vais résumer les tâches de consensus :"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage(
                "Une fois supprimé, l’historique de la discussion de groupe sera effacé."),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("Opération réussie !"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage(
                "Ou faites-moi savoir si vous avez des questions"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage(
                "Les autres membres du groupe seront informés après le changement du nom du groupe."),
        "outOfDate": MessageLookupByLibrary.simpleMessage("Obsolète"),
        "owner": MessageLookupByLibrary.simpleMessage("Propriétaire"),
        "pages": MessageLookupByLibrary.simpleMessage("Pages"),
        "passwordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Le mot de passe ne peut pas être vide"),
        "pending": MessageLookupByLibrary.simpleMessage("En attente"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage(
                "Permission d\'exécuter des tâches en arrière-plan non accordée"),
        "phone": MessageLookupByLibrary.simpleMessage("Téléphone"),
        "phoneNumber":
            MessageLookupByLibrary.simpleMessage("Numéro de téléphone"),
        "phoneNumberCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Le numéro de téléphone ne peut pas être vide"),
        "photo": MessageLookupByLibrary.simpleMessage("Photo"),
        "pleaseSelectASlideToEdit": MessageLookupByLibrary.simpleMessage(
            "Veuillez sélectionner une diapositive à modifier."),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "Veuillez mettre à jour vers la dernière version pour les nouvelles fonctionnalités et améliorations"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage(
                "Veuillez attendre qu’Agnes termine la tâche"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Veuillez attendre la fin du téléchargement"),
        "presenter": MessageLookupByLibrary.simpleMessage("Présentateur"),
        "preview": MessageLookupByLibrary.simpleMessage("Aperçu"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage(
            "Politique de Confidentialité"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage(
                "Fournir des commentaires supplémentaires sur cette réponse"),
        "quote": MessageLookupByLibrary.simpleMessage("Citation"),
        "quotedMessageHasExpired":
            MessageLookupByLibrary.simpleMessage("Message cité a expiré."),
        "reconnect": MessageLookupByLibrary.simpleMessage("Reconnecter"),
        "reject": MessageLookupByLibrary.simpleMessage("Rejeter"),
        "remove": MessageLookupByLibrary.simpleMessage("Supprimer"),
        "removedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Supprimé avec succès"),
        "rename": MessageLookupByLibrary.simpleMessage("renommer"),
        "reopenTheApp":
            MessageLookupByLibrary.simpleMessage("Rouvrez l’application"),
        "requestTimedOutPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "La requête a expiré. Veuillez réessayer."),
        "research": MessageLookupByLibrary.simpleMessage("Recherche"),
        "researchForZdSeconds": m1,
        "resetPassword": MessageLookupByLibrary.simpleMessage(
            "Réinitialiser le mot de passe"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage(
            "Réinitialisez votre mot de passe"),
        "role": MessageLookupByLibrary.simpleMessage("Rôle"),
        "save": MessageLookupByLibrary.simpleMessage("Enregistrer"),
        "saved": MessageLookupByLibrary.simpleMessage("Enregistré"),
        "saving": MessageLookupByLibrary.simpleMessage("Enregistrement…"),
        "search": MessageLookupByLibrary.simpleMessage("Rechercher"),
        "searchByNameEmailOrPhone": MessageLookupByLibrary.simpleMessage(
            "Rechercher par nom, e-mail ou téléphone"),
        "searchForACountry":
            MessageLookupByLibrary.simpleMessage("Rechercher un pays"),
        "seconds": MessageLookupByLibrary.simpleMessage("Secondes"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage(
                "Semble être déconnecté d’internet"),
        "selectMembers":
            MessageLookupByLibrary.simpleMessage("Sélectionner des Membres"),
        "send": MessageLookupByLibrary.simpleMessage("Envoyer"),
        "sendMessage":
            MessageLookupByLibrary.simpleMessage("Envoyer un message"),
        "serverConnectionFailed": MessageLookupByLibrary.simpleMessage(
            "Échec de la connexion au serveur."),
        "serviceIsStillStarting":
            MessageLookupByLibrary.simpleMessage("Le service démarre encore."),
        "sessionExpiredPleaseLogInAgain": MessageLookupByLibrary.simpleMessage(
            "La session a expiré. Veuillez vous reconnecter."),
        "setNow": MessageLookupByLibrary.simpleMessage("Définir maintenant"),
        "settings": MessageLookupByLibrary.simpleMessage("Paramètres"),
        "share": MessageLookupByLibrary.simpleMessage("Partager"),
        "signIn": MessageLookupByLibrary.simpleMessage("Se connecter"),
        "signOut": MessageLookupByLibrary.simpleMessage("Se déconnecter"),
        "signUp": MessageLookupByLibrary.simpleMessage("S’inscrire"),
        "someoneMe":
            MessageLookupByLibrary.simpleMessage("Quelqu’un m’a @mentionné"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "Désolé, nous sommes complets pour le moment. Veuillez réessayer demain."),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking":
            MessageLookupByLibrary.simpleMessage("Commencer à parler"),
        "submit": MessageLookupByLibrary.simpleMessage("Soumettre"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry": MessageLookupByLibrary.simpleMessage(
            "Changez de Wi-Fi/données mobiles et réessayez"),
        "system": MessageLookupByLibrary.simpleMessage("Système"),
        "systemBusyPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Système occupé, veuillez réessayer"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("Prendre une photo"),
        "taskStoppedManually":
            MessageLookupByLibrary.simpleMessage("Tâche arrêtée manuellement"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService":
            MessageLookupByLibrary.simpleMessage("Conditions d’Utilisation"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage(
                "2. Le nombre de diamants est mis à jour le 1er de chaque mois."),
        "theGroupIsCurrentlyInACall": MessageLookupByLibrary.simpleMessage(
            "Le groupe est actuellement en appel..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage(
                "La langue utilisée dans l’interface"),
        "thePhoneNumberIsInvalid": MessageLookupByLibrary.simpleMessage(
            "Le numéro de téléphone est invalide"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Cette application nécessite l’accès à la caméra pour prendre une photo de profil."),
        "thisIsntHelpful":
            MessageLookupByLibrary.simpleMessage("Ceci n’est pas utile"),
        "thisSlideIsBeingEdited": MessageLookupByLibrary.simpleMessage(
            "Cette diapositive est en cours de modification."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "Pour vous permettre d\'envoyer des images pour la reconnaissance, l\'interaction ou la configuration de votre avatar, veuillez autoriser Agnes à accéder à votre caméra"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Pour vous permettre d\'envoyer des images pour la reconnaissance, l\'interaction ou la configuration de votre avatar, veuillez autoriser Agnes à accéder à votre bibliothèque de photos"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Pour enregistrer les images générées, veuillez autoriser Agnes à les enregistrer dans votre bibliothèque de photos"),
        "transcribing": MessageLookupByLibrary.simpleMessage("Transcription…"),
        "transcriptionEnabledLoadingData": MessageLookupByLibrary.simpleMessage(
            "Transcription activée. Chargement des données…"),
        "tryOneOfThese": MessageLookupByLibrary.simpleMessage(
            "Essayez l’une de ces options :"),
        "twoPasswordsAreInconsistent": MessageLookupByLibrary.simpleMessage(
            "Les deux mots de passe ne correspondent pas"),
        "update": MessageLookupByLibrary.simpleMessage("Mettre à jour"),
        "updateFailedPleaseTryAgainLater": MessageLookupByLibrary.simpleMessage(
            "Échec de la mise à jour. Veuillez réessayer plus tard."),
        "updatePassword": MessageLookupByLibrary.simpleMessage(
            "Mettre à jour le mot de passe"),
        "updatePasswordSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Mot de passe mis à jour avec succès"),
        "upgradeYourPlanToContinueUpgrade": MessageLookupByLibrary.simpleMessage(
            "Mettez à niveau votre forfait pour continuer : Mettre à niveau"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Utilisez 6+ caractères avec lettres, chiffres et symboles"),
        "userAccess": MessageLookupByLibrary.simpleMessage("Accès utilisateur"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("Nom d\'utilisateur"),
        "usersWithAccess":
            MessageLookupByLibrary.simpleMessage("Utilisateurs ayant accès"),
        "usingTool": MessageLookupByLibrary.simpleMessage("Utiliser l’outil"),
        "verificationCode":
            MessageLookupByLibrary.simpleMessage("Code de vérification"),
        "verificationCodeCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Le code de vérification ne peut pas être vide"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("Voir"),
        "viewAllComments":
            MessageLookupByLibrary.simpleMessage("Voir tous les commentaires"),
        "viewer": MessageLookupByLibrary.simpleMessage("Lecteur"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Échec de la création de l\'appel vocal, probablement en raison d\'une instabilité du réseau. Veuillez réessayer."),
        "voiceCallEnded":
            MessageLookupByLibrary.simpleMessage("Appel vocal terminé"),
        "waiting": MessageLookupByLibrary.simpleMessage("En attente"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Nous avons besoin d’accéder pour enregistrer les images ou vidéos téléchargées dans votre bibliothèque."),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Nous devons accéder à votre microphone pour l\'interaction vocale"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "Nous avons besoin d’accéder à votre bibliothèque de photos pour choisir une photo de profil."),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage(
                "Nous devons accéder à votre galerie pour sélectionner des images"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Nous devons reconnaître votre voix pour la convertir en texte pour l\'interaction"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Nous devons enregistrer des images dans votre galerie"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Nous devons utiliser le Bluetooth pour connecter votre casque pour l\'interaction vocale"),
        "weNeedToUseYourCameraToTakePhotos": MessageLookupByLibrary.simpleMessage(
            "Nous devons utiliser votre appareil photo pour prendre des photos"),
        "welcomeToAgnes":
            MessageLookupByLibrary.simpleMessage("Bienvenue sur Agnes"),
        "writeSomething":
            MessageLookupByLibrary.simpleMessage("Écrire quelque chose"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("Vous"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage(
                "Vous pouvez exécuter, modifier ou supprimer. Comment souhaitez-vous procéder ?"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "Vous pouvez modifier les diapositives manuellement ou via une conversation avec Agnes."),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage(
                "Vous pouvez sélectionner du contenu et partager vos commentaires."),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "Vous ne pouvez pas réduire un appel vidéo car AGNES n\'est pas autorisé à utiliser les fenêtres flottantes."),
        "youCreatedTheGroup":
            MessageLookupByLibrary.simpleMessage("Vous avez créé le groupe."),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Vous avez été supprimé du chat de groupe"),
        "youJoinedTheGroup": MessageLookupByLibrary.simpleMessage(
            "Vous avez rejoint le groupe."),
        "yourCurrentPlan":
            MessageLookupByLibrary.simpleMessage("Votre plan actuel"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "Votre téléphone n’a pas autorisé Agnes à utiliser la permission de fenêtre flottante. Agnes ne pourra pas continuer à effectuer des tâches en arrière-plan pour vous."),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Vous avez atteint la limite de demande pour la tâche de recherche."),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Vous avez atteint la limite de demande pour la tâche de diapositive."),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Vous avez atteint la limite de cette tâche."),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd":
            MessageLookupByLibrary.simpleMessage("Vous avez atteint la fin")
      };
}
