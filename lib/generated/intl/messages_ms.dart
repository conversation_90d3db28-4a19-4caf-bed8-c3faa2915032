// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ms locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ms';

  static String m0(termsOfService, privacyPolicy) =>
      "Dengan log masuk, anda bersetuju dengan\n${termsOfService} dan ${privacyPolicy}.";

  static String m1(value) => "Penyelidikan selama ${value} saat";

  static String m2(title) =>
      "Maaf, tugas ${title} anda gagal. Sila cuba lagi kemudian.";

  static String m3(value) => "Menyokong muat naik sehingga ${value} imej.";

  static String m4(remaining_count) =>
      "Tugasan telah dipadam seperti diminta. Bilangan tugasan tertunda semasa: ${remaining_count}. Mahukah saya tunjukkan semua tugasan tertunda?";

  static String m5(total) => "Terdapat ${total} tugasan tertunda:";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "Pengguna ${user_name} mempunyai kuota tidak mencukupi, slide baki: ${slides_remaining_count}, kuota slide yang diperlukan: ${slides_plan_count}, penyelidikan baki: ${research_remaining_count}, kuota penyelidikan yang diperlukan: ${research_plan_count}. Anda boleh memilih tugas dengan kuota mencukupi untuk dilaksanakan.";

  static String m7(xxx) => "Kod pengesahan dihantar ke ${xxx}@email.com";

  static String m8(version) => "Versi ${version} kini tersedia";

  static String m9(x) => "${x} ahli dalam panggilan suara...";

  static String m10(xxx) => "${xxx} menukar nama kumpulan";

  static String m11(xxx) =>
      "${xxx} menjemput anda untuk bekerjasama menyunting \"${xxx}\".";

  static String m12(xxx) =>
      "${xxx} menjemput anda untuk bekerjasama menyemak \"${xxx}\".";

  static String m13(xxx) => "${xxx} telah memulakan panggilan suara";

  static String m14(xxx, xyz) =>
      "${xxx} menjemput anda untuk menyertai panggilan kumpulan \"${xyz}\".";

  static String m15(title) =>
      "Tugasan ${title} anda telah berjaya diselesaikan!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond":
            MessageLookupByLibrary.simpleMessage("Mengenai Berlian"),
        "accept": MessageLookupByLibrary.simpleMessage("Terima"),
        "account": MessageLookupByLibrary.simpleMessage("Akaun"),
        "accountDeletionSuccessful":
            MessageLookupByLibrary.simpleMessage("Berjaya padam akaun"),
        "accountNotCreatedYet":
            MessageLookupByLibrary.simpleMessage("Akaun belum dibuat"),
        "accountSignoutSuccessful":
            MessageLookupByLibrary.simpleMessage("Berjaya log keluar akaun"),
        "activeAccount": MessageLookupByLibrary.simpleMessage("Akaun Aktif"),
        "add": MessageLookupByLibrary.simpleMessage("Tambah"),
        "addComment": MessageLookupByLibrary.simpleMessage("Tambah Komen"),
        "addPeopleToChat":
            MessageLookupByLibrary.simpleMessage("Tambah Orang ke Chat"),
        "addToChat": MessageLookupByLibrary.simpleMessage("Tambah ke Chat"),
        "addUserEmailAndEnterToInvite": MessageLookupByLibrary.simpleMessage(
            "Tambah emel pengguna dan tekan \"enter\" untuk jemput"),
        "addedToGroupSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Berjaya ditambah ke kumpulan"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Agnes boleh buat silap. Sila semak maklumat penting."),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage(
                "@Agnes untuk ringkasan, laporan, atau slaid."),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn":
            MessageLookupByLibrary.simpleMessage("Setuju dan Log masuk"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("Reka Bentuk AI"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("Slaid AI"),
        "album": MessageLookupByLibrary.simpleMessage("Album"),
        "allMembers": MessageLookupByLibrary.simpleMessage("Semua Ahli"),
        "answerCall": MessageLookupByLibrary.simpleMessage("Jawab Panggilan"),
        "appUpdate":
            MessageLookupByLibrary.simpleMessage("Kemas kini Aplikasi"),
        "archived": MessageLookupByLibrary.simpleMessage("Diarkibkan"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Adakah anda pasti mahu memadam akaun anda?"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Adakah anda pasti ingin log keluar akaun anda?"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Tanya Agnes"),
        "askAgnesToEditSlides":
            MessageLookupByLibrary.simpleMessage("Minta Agnes sunting slaid"),
        "assignATaskToBegin": MessageLookupByLibrary.simpleMessage(
            "Tugaskan tugas untuk bermula"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Sekurang-kurangnya enam aksara dengan huruf, nombor & simbol"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "Pada masa ini, belum ada konsensus mengenai tugas yang perlu dilakukan. Sila bincangkan lebih lanjut dan tanya soalan jika ada!"),
        "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
        "back": MessageLookupByLibrary.simpleMessage("Kembali"),
        "bookmarkRemoved":
            MessageLookupByLibrary.simpleMessage("Bookmark dihapus"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("Ditandai"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined":
            MessageLookupByLibrary.simpleMessage("Panggilan ditolak"),
        "cancel": MessageLookupByLibrary.simpleMessage("Batal"),
        "canvas": MessageLookupByLibrary.simpleMessage("Kanvas"),
        "chapter": MessageLookupByLibrary.simpleMessage("Bab"),
        "chat": MessageLookupByLibrary.simpleMessage("Sembang"),
        "chatSettings": MessageLookupByLibrary.simpleMessage("Tetapan Chat"),
        "chooseImage": MessageLookupByLibrary.simpleMessage("Pilih Imej"),
        "close": MessageLookupByLibrary.simpleMessage("Tutup"),
        "code": MessageLookupByLibrary.simpleMessage("Kod"),
        "collect": MessageLookupByLibrary.simpleMessage("kumpul"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("Segera Datang"),
        "comment": MessageLookupByLibrary.simpleMessage("Komen"),
        "commenter": MessageLookupByLibrary.simpleMessage("Pengulas"),
        "commentsTasks": MessageLookupByLibrary.simpleMessage("Tugas Komen"),
        "confirm": MessageLookupByLibrary.simpleMessage("Sahkan"),
        "confirmDelete": MessageLookupByLibrary.simpleMessage("Sahkan Padam"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Sahkan Kata Laluan"),
        "confirmPasswordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Sahkan kata laluan tidak boleh kosong"),
        "confirmSignOut":
            MessageLookupByLibrary.simpleMessage("Sahkan Log Keluar"),
        "connect": MessageLookupByLibrary.simpleMessage("Sambung"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage(
                "Sambungkan fon kepala AI dan berbual dengan Agnes bila-bila masa"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage(
                "Sambungkan ke Agnes VibePods dan bersembang bila-bila masa."),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Berhubung dengan Rakan untuk Mulakan Sembang Kumpulan"),
        "connected": MessageLookupByLibrary.simpleMessage("Bersambung"),
        "connectionFailed":
            MessageLookupByLibrary.simpleMessage("Sambungan gagal"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "Kandungan disalin—sila tampal pada platform kegemaran anda!"),
        "continueWithApple":
            MessageLookupByLibrary.simpleMessage("Teruskan dengan Apple"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("Teruskan dengan Google"),
        "continueWithOtherWays":
            MessageLookupByLibrary.simpleMessage("Teruskan dengan cara lain"),
        "continueWord": MessageLookupByLibrary.simpleMessage("Teruskan"),
        "copied": MessageLookupByLibrary.simpleMessage("Disalin"),
        "copy": MessageLookupByLibrary.simpleMessage("Salin"),
        "cost": MessageLookupByLibrary.simpleMessage("kos"),
        "cover": MessageLookupByLibrary.simpleMessage("Kulit Muka"),
        "createANewTaskToGetStarted": MessageLookupByLibrary.simpleMessage(
            "Buat tugas baru untuk memulakan"),
        "createAnAccountToSignUpForAgnes": MessageLookupByLibrary.simpleMessage(
            "Cipta akaun untuk daftar Agnes"),
        "createGroupChat":
            MessageLookupByLibrary.simpleMessage("Cipta Sembang Kumpulan"),
        "currentlyInACall":
            MessageLookupByLibrary.simpleMessage("Sedang dalam panggilan..."),
        "deepResearch": MessageLookupByLibrary.simpleMessage("Kajian Mendalam"),
        "deepresearch":
            MessageLookupByLibrary.simpleMessage("Penyelidikan Mendalam"),
        "delete": MessageLookupByLibrary.simpleMessage("Padam"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("Padam Akaun"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Padamkan Chat"),
        "doYouWantToExecuteTheseTasks": MessageLookupByLibrary.simpleMessage(
            "Adakah anda mahu melaksanakan tugasan ini?"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                " nampaknya bukan pengguna Agnes. Masukkan e-mel mereka untuk hantar jemputan"),
        "done": MessageLookupByLibrary.simpleMessage("Selesai"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Tiada akaun?"),
        "downloading": MessageLookupByLibrary.simpleMessage("Memuat turun"),
        "eachNewTaskConsumesOneDiamond": MessageLookupByLibrary.simpleMessage(
            "1. Setiap tugas baharu menggunakan satu berlian."),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. Setiap pengguna menerima 30 berlian sebulan."),
        "edit": MessageLookupByLibrary.simpleMessage("Sunting"),
        "editGroupName":
            MessageLookupByLibrary.simpleMessage("Edit nama kumpulan"),
        "editor": MessageLookupByLibrary.simpleMessage("Penyunting"),
        "email": MessageLookupByLibrary.simpleMessage("E-mel"),
        "emailCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("E-mel tidak boleh kosong"),
        "emailFormatIncorrect":
            MessageLookupByLibrary.simpleMessage("Format e-mel tidak betul"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage(
                "Tingkatkan produktiviti dan kreativiti melalui akses lanjutan"),
        "enterConfirmPassword":
            MessageLookupByLibrary.simpleMessage("Masukkan Sahkan Kata Laluan"),
        "enterGroupName":
            MessageLookupByLibrary.simpleMessage("Masukkan nama kumpulan"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Masukkan Kata Laluan"),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("Masukkan Kod Pengesahan"),
        "export": MessageLookupByLibrary.simpleMessage("Eksport"),
        "file": MessageLookupByLibrary.simpleMessage("Fail"),
        "floatingWindowNotEnabled": MessageLookupByLibrary.simpleMessage(
            "Tetingkap Terapung Tidak Didayakan"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage(
                "Untuk pengalaman penuh, sila gunakan versi PC"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Lupa Kata Laluan"),
        "general": MessageLookupByLibrary.simpleMessage("Umum"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Dapatkan Agnes AI Plus"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Dapatkan Agnes AI Pro"),
        "giveMeATaskToWorkOn": MessageLookupByLibrary.simpleMessage(
            "Berikan saya tugas untuk dilakukan…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("Pergi ke Kedai"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage(
                "Faham! Saya sedang mengerjakan tugasan. Sila tunggu sebentar."),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Kumpulan berjaya dibuat"),
        "groupFiles": MessageLookupByLibrary.simpleMessage("Fail Kumpulan"),
        "groupName": MessageLookupByLibrary.simpleMessage("Nama Kumpulan"),
        "groupNameOptional":
            MessageLookupByLibrary.simpleMessage("Nama Kumpulan (Pilihan)"),
        "harmful": MessageLookupByLibrary.simpleMessage("Berbahaya"),
        "helpUsImprove":
            MessageLookupByLibrary.simpleMessage("Bantu kami meningkatkan"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage(
                "Saya membantu dalam carian, penulisan dan jawapan"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage(
                "Jika anda menjawab panggilan masuk, panggilan semasa anda akan ditamatkan."),
        "imAgnes": MessageLookupByLibrary.simpleMessage("Saya Agnes"),
        "inAVoiceCall":
            MessageLookupByLibrary.simpleMessage("Dalam panggilan suara"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("Tidak Tepat"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Pembelian dalam aplikasi sedang tidak tersedia, sila cuba lagi nanti"),
        "interfaceLanguage":
            MessageLookupByLibrary.simpleMessage("Bahasa Antara Muka"),
        "invite": MessageLookupByLibrary.simpleMessage("Jemput"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage(
                "Jemput mengikut nama pengguna, e-mel atau nombor telefon"),
        "inviteFriends": MessageLookupByLibrary.simpleMessage("Jemput Rakan"),
        "inviteOthersToCollaborate": MessageLookupByLibrary.simpleMessage(
            "Jemput orang lain untuk bekerjasama"),
        "inviteToCollaborate":
            MessageLookupByLibrary.simpleMessage("Jemput untuk bekerjasama"),
        "isEditing": MessageLookupByLibrary.simpleMessage("Sedang menyunting"),
        "join": MessageLookupByLibrary.simpleMessage("Sertai"),
        "knowMore":
            MessageLookupByLibrary.simpleMessage("Ketahui Lebih Lanjut"),
        "language": MessageLookupByLibrary.simpleMessage("Bahasa Inggeris"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Biarkan Agnes membantu anda membuat format perkongsian yang lebih baik"),
        "letAgnesResolve":
            MessageLookupByLibrary.simpleMessage("Biarkan Agnes selesaikan"),
        "linkCopied": MessageLookupByLibrary.simpleMessage("Pautan disalin"),
        "listening": MessageLookupByLibrary.simpleMessage("Sedang mendengar"),
        "loading": MessageLookupByLibrary.simpleMessage("Memuat"),
        "makeMeAShareableImagePost": MessageLookupByLibrary.simpleMessage(
            "Buatkan saya pos imej yang boleh dikongsi"),
        "makeMeAnHtmlWebPage": MessageLookupByLibrary.simpleMessage(
            "Buatkan saya halaman web HTML"),
        "manageMembers": MessageLookupByLibrary.simpleMessage("Urus Ahli"),
        "manuallyEndTheTask": MessageLookupByLibrary.simpleMessage(
            "Tamatkan tugas secara manual"),
        "markAsResolved":
            MessageLookupByLibrary.simpleMessage("Tandakan sebagai Selesai"),
        "messageCopied":
            MessageLookupByLibrary.simpleMessage("Mesej Telah Disalin."),
        "multipleChoices":
            MessageLookupByLibrary.simpleMessage("Pilihan berganda"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("Kegemaran Saya"),
        "narrate": MessageLookupByLibrary.simpleMessage("Narasikan"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Sambungan rangkaian gagal. Sila cuba lagi"),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("Sambungan rangkaian hilang"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("Rangkaian tidak tersedia"),
        "newPassword":
            MessageLookupByLibrary.simpleMessage("Kata Laluan Baharu"),
        "newProject": MessageLookupByLibrary.simpleMessage("Projek Baru"),
        "newTask": MessageLookupByLibrary.simpleMessage("Tugas Baru"),
        "newTitle": MessageLookupByLibrary.simpleMessage("Tajuk Baharu"),
        "noMoreFiles": MessageLookupByLibrary.simpleMessage("Tiada Lagi Fail"),
        "noMoreMessage":
            MessageLookupByLibrary.simpleMessage("Tiada lagi mesej..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Tiada tugas tertunda ditemui. Semua tugas telah selesai!"),
        "notAvailableForPurchase": MessageLookupByLibrary.simpleMessage(
            "Tidak tersedia untuk pembelian"),
        "notConnected":
            MessageLookupByLibrary.simpleMessage("Tidak bersambung"),
        "notNow": MessageLookupByLibrary.simpleMessage("Tidak sekarang"),
        "notifications": MessageLookupByLibrary.simpleMessage("Pemberitahuan"),
        "notifyUserViaEmail": MessageLookupByLibrary.simpleMessage(
            "Maklumkan pengguna melalui emel"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "Baik, saya perasan anda tidak mempunyai kebenaran yang diperlukan. Sila minta pemilik atau editor untuk melakukannya."),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage(
                "Baiklah, saya akan meringkaskan tugasan konsensus:"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage(
                "Setelah dipadam, sejarah sembang kumpulan akan dipadam."),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("Operasi Berjaya!"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage(
                "Atau beritahu saya jika ada pertanyaan"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage(
                "Ahli kumpulan lain akan dimaklumkan selepas nama kumpulan ditukar."),
        "outOfDate": MessageLookupByLibrary.simpleMessage("Tamat tempoh"),
        "owner": MessageLookupByLibrary.simpleMessage("Pemilik"),
        "pages": MessageLookupByLibrary.simpleMessage("Halaman"),
        "passwordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Kata laluan tidak boleh kosong"),
        "pending": MessageLookupByLibrary.simpleMessage("Tertunda"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage(
                "Kebenaran untuk melaksanakan tugasan di latar belakang tidak diberikan"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Nombor Telefon"),
        "phoneNumberCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Nombor telefon tidak boleh kosong"),
        "photo": MessageLookupByLibrary.simpleMessage("Foto"),
        "pleaseSelectASlideToEdit": MessageLookupByLibrary.simpleMessage(
            "Sila pilih slaid untuk disunting."),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "Sila kemas kini ke versi terkini untuk ciri-ciri dan peningkatan baru"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage(
                "Sila tunggu Agnes menyelesaikan tugas"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Sila tunggu sehingga muat turun selesai"),
        "presenter": MessageLookupByLibrary.simpleMessage("Penyampai"),
        "preview": MessageLookupByLibrary.simpleMessage("Pratonton"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("Dasar Privasi"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage(
                "Berikan maklum balas tambahan mengenai jawapan ini"),
        "quote": MessageLookupByLibrary.simpleMessage("Kutipan"),
        "quotedMessageHasExpired": MessageLookupByLibrary.simpleMessage(
            "Pesan yang dikutip telah luput tempoh."),
        "reconnect": MessageLookupByLibrary.simpleMessage("Sambung semula"),
        "reject": MessageLookupByLibrary.simpleMessage("Tolak"),
        "remove": MessageLookupByLibrary.simpleMessage("Keluarkan"),
        "removedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Berjaya dipadam"),
        "rename": MessageLookupByLibrary.simpleMessage("tukar nama"),
        "reopenTheApp":
            MessageLookupByLibrary.simpleMessage("Buka semula aplikasi"),
        "requestTimedOutPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Permintaan tamat tempoh. Sila cuba lagi."),
        "research": MessageLookupByLibrary.simpleMessage("Penyelidikan"),
        "researchForZdSeconds": m1,
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Tetapkan Semula Kata Laluan"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage(
            "Tetapkan Semula Kata Laluan Anda"),
        "role": MessageLookupByLibrary.simpleMessage("Peranan"),
        "save": MessageLookupByLibrary.simpleMessage("Simpan"),
        "saved": MessageLookupByLibrary.simpleMessage("Disimpan"),
        "saving": MessageLookupByLibrary.simpleMessage("Menyimpan…"),
        "search": MessageLookupByLibrary.simpleMessage("Cari"),
        "searchByNameEmailOrPhone": MessageLookupByLibrary.simpleMessage(
            "Cari mengikut nama, e-mel atau telefon"),
        "searchForACountry":
            MessageLookupByLibrary.simpleMessage("Cari negara"),
        "seconds": MessageLookupByLibrary.simpleMessage("Saat"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage(
                "Nampaknya terputus dari internet"),
        "selectMembers": MessageLookupByLibrary.simpleMessage("Pilih Ahli"),
        "send": MessageLookupByLibrary.simpleMessage("Hantar"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Hantar Mesej"),
        "serverConnectionFailed":
            MessageLookupByLibrary.simpleMessage("Koneksi pelayan gagal."),
        "serviceIsStillStarting": MessageLookupByLibrary.simpleMessage(
            "Perkhidmatan masih dimulakan."),
        "sessionExpiredPleaseLogInAgain": MessageLookupByLibrary.simpleMessage(
            "Sesi telah tamat. Sila log masuk semula."),
        "setNow": MessageLookupByLibrary.simpleMessage("Tetapkan Sekarang"),
        "settings": MessageLookupByLibrary.simpleMessage("Tetapan"),
        "share": MessageLookupByLibrary.simpleMessage("Kongsi"),
        "signIn": MessageLookupByLibrary.simpleMessage("Log Masuk"),
        "signOut": MessageLookupByLibrary.simpleMessage("Log keluar"),
        "signUp": MessageLookupByLibrary.simpleMessage("Daftar"),
        "someoneMe": MessageLookupByLibrary.simpleMessage("Seseorang @saya"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "Maaf, kapasiti penuh sekarang. Sila cuba lagi esok."),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking": MessageLookupByLibrary.simpleMessage("Mula bercakap"),
        "submit": MessageLookupByLibrary.simpleMessage("Hantar"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry": MessageLookupByLibrary.simpleMessage(
            "Tukar Wi-Fi/data mudah alih dan cuba lagi"),
        "system": MessageLookupByLibrary.simpleMessage("Sistem"),
        "systemBusyPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("Sistem sibuk, cuba lagi"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("Ambil Gambar"),
        "taskStoppedManually": MessageLookupByLibrary.simpleMessage(
            "Tugas dihentikan secara manual"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService":
            MessageLookupByLibrary.simpleMessage("Terma Perkhidmatan"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage(
                "2. Bilangan berlian dikemas kini pada 1 haribulan setiap bulan."),
        "theGroupIsCurrentlyInACall": MessageLookupByLibrary.simpleMessage(
            "Kumpulan sedang dalam panggilan..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage(
                "Bahasa yang digunakan dalam antara muka"),
        "thePhoneNumberIsInvalid":
            MessageLookupByLibrary.simpleMessage("Nombor telefon tidak sah"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Aplikasi ini memerlukan akses kamera supaya anda boleh ambil gambar untuk gambar profil."),
        "thisIsntHelpful":
            MessageLookupByLibrary.simpleMessage("Ini tidak membantu"),
        "thisSlideIsBeingEdited":
            MessageLookupByLibrary.simpleMessage("Slaid ini sedang disunting."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "Untuk membolehkan anda menghantar imej untuk pengecaman, interaksi, atau menetapkan avatar anda, benarkan Agnes mengakses kamera anda"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Untuk membolehkan anda menghantar imej untuk pengecaman, interaksi, atau menetapkan avatar anda, benarkan Agnes mengakses pustaka foto anda"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Untuk menyimpan imej yang dijana, benarkan Agnes menyimpan imej ke pustaka foto anda"),
        "transcribing":
            MessageLookupByLibrary.simpleMessage("Sedang menyalin…"),
        "transcriptionEnabledLoadingData": MessageLookupByLibrary.simpleMessage(
            "Transkripsi diaktifkan. Memuat data…"),
        "tryOneOfThese": MessageLookupByLibrary.simpleMessage(
            "Cuba salah satu daripada ini:"),
        "twoPasswordsAreInconsistent": MessageLookupByLibrary.simpleMessage(
            "Dua kata laluan tidak sepadan"),
        "update": MessageLookupByLibrary.simpleMessage("Kemas kini"),
        "updateFailedPleaseTryAgainLater": MessageLookupByLibrary.simpleMessage(
            "Kemas kini gagal. Sila cuba lagi nanti."),
        "updatePassword":
            MessageLookupByLibrary.simpleMessage("Kemas kini Kata Laluan"),
        "updatePasswordSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Kata laluan berjaya dikemas kini"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage(
                "Naik taraf pelan anda untuk meneruskan: Naik taraf"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Gunakan 6+ aksara dengan huruf, nombor & simbol"),
        "userAccess": MessageLookupByLibrary.simpleMessage("Akses Pengguna"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("Nama Pengguna"),
        "usersWithAccess":
            MessageLookupByLibrary.simpleMessage("Pengguna dengan akses"),
        "usingTool": MessageLookupByLibrary.simpleMessage("Menggunakan Alat"),
        "verificationCode":
            MessageLookupByLibrary.simpleMessage("Kod Pengesahan"),
        "verificationCodeCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Kod pengesahan tidak boleh kosong"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("Lihat"),
        "viewAllComments":
            MessageLookupByLibrary.simpleMessage("Lihat semua komen"),
        "viewer": MessageLookupByLibrary.simpleMessage("Penonton"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Penciptaan panggilan suara gagal, mungkin kerana rangkaian tidak stabil. Sila cuba lagi."),
        "voiceCallEnded":
            MessageLookupByLibrary.simpleMessage("Panggilan suara tamat"),
        "waiting": MessageLookupByLibrary.simpleMessage("Menunggu"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlukan akses untuk menyimpan imej atau video yang dimuat turun ke pustaka foto anda."),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu mengakses mikrofon anda untuk interaksi suara"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlukan akses ke pustaka foto anda supaya anda boleh pilih gambar profil."),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu mengakses pustaka foto anda untuk memilih imej"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu mengenali ucapan anda untuk menukarnya menjadi teks bagi interaksi"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu menyimpan imej ke pustaka foto anda"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu menggunakan Bluetooth untuk menyambungkan fon kepala anda untuk interaksi suara"),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu menggunakan kamera anda untuk mengambil gambar"),
        "welcomeToAgnes":
            MessageLookupByLibrary.simpleMessage("Selamat datang ke Agnes"),
        "writeSomething": MessageLookupByLibrary.simpleMessage("Tulis Sesuatu"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("Anda"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage(
                "Anda boleh jalankan, ubah suai, atau padam. Bagaimana anda mahu teruskan?"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "Anda boleh sunting slaid secara manual atau melalui perbualan dengan Agnes."),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage(
                "Anda boleh pilih kandungan dan kongsi maklum balas."),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "Anda tidak boleh meminimumkan panggilan video kerana AGNES tidak dibenarkan menggunakan tetingkap terapung."),
        "youCreatedTheGroup": MessageLookupByLibrary.simpleMessage(
            "Anda telah mencipta kumpulan."),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Anda telah dikeluarkan dari chat kumpulan"),
        "youJoinedTheGroup": MessageLookupByLibrary.simpleMessage(
            "Anda telah menyertai kumpulan."),
        "yourCurrentPlan":
            MessageLookupByLibrary.simpleMessage("Pelan semasa anda"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "Telefon anda belum memberi kebenaran kepada Agnes untuk menggunakan kebenaran tetingkap terapung. Agnes tidak akan dapat meneruskan tugas di latar belakang untuk anda."),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Permintaan anda untuk had tugasan penyelidikan telah dicapai."),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Permintaan anda untuk had tugasan slaid telah dicapai."),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Permintaan anda telah mencapai had tugasan."),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd":
            MessageLookupByLibrary.simpleMessage("Anda telah sampai ke hujung")
      };
}
