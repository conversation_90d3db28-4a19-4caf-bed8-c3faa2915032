// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ko locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ko';

  static String m0(termsOfService, privacyPolicy) =>
      "로그인하면\n${termsOfService} 및 ${privacyPolicy}에 동의하게 됩니다.";

  static String m1(value) => "${value}초 동안 연구";

  static String m2(title) => "죄송합니다, ${title} 작업이 실패했습니다. 나중에 다시 시도해주세요.";

  static String m3(value) => "최대 ${value} 개 이미지 업로드 지원";

  static String m4(remaining_count) =>
      "요청하신 대로 작업이 삭제되었습니다. 현재 보류 중인 작업 수: ${remaining_count}. 모든 보류 작업을 표시할까요?";

  static String m5(total) => "현재 ${total}개의 보류 중인 작업이 있습니다:";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "사용자 ${user_name}의 할당량이 부족합니다. 슬라이드 남음：${slides_remaining_count}、슬라이드에 필요한 할당량：${slides_plan_count}、조사 남음：${research_remaining_count}、조사에 필요한 할당량：${research_plan_count}。할당량이 충분한 작업을 선택하여 실행할 수 있습니다.";

  static String m7(xxx) => "인증 코드가 ${xxx}@email.com 으로 전송되었습니다";

  static String m8(version) => "버전 ${version} 사용 가능";

  static String m9(x) => "x명이 음성 통화 중...";

  static String m10(xxx) => "${xxx}가 그룹 이름을 변경했습니다";

  static String m11(xxx) => "${xxx}님이 \"${xxx}\" 편집 협업에 초대했습니다.";

  static String m12(xxx) => "${xxx}님이 \"${xxx}\" 검토 협업에 초대했습니다.";

  static String m13(xxx) => "${xxx}가 음성 통화를 시작했습니다";

  static String m14(xxx, xyz) => "${xxx}님이 \"${xyz}\" 그룹 통화에 초대했습니다.";

  static String m15(title) => "${title} 작업이 성공적으로 완료되었습니다!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond": MessageLookupByLibrary.simpleMessage("다이아몬드 정보"),
        "accept": MessageLookupByLibrary.simpleMessage("수락"),
        "account": MessageLookupByLibrary.simpleMessage("계정"),
        "accountDeletionSuccessful":
            MessageLookupByLibrary.simpleMessage("계정이 성공적으로 삭제되었습니다"),
        "accountNotCreatedYet":
            MessageLookupByLibrary.simpleMessage("계정이 아직 생성되지 않았습니다"),
        "accountSignoutSuccessful":
            MessageLookupByLibrary.simpleMessage("계정이 성공적으로 로그아웃되었습니다"),
        "activeAccount": MessageLookupByLibrary.simpleMessage("활성 계정"),
        "add": MessageLookupByLibrary.simpleMessage("추가"),
        "addComment": MessageLookupByLibrary.simpleMessage("댓글 추가"),
        "addPeopleToChat": MessageLookupByLibrary.simpleMessage("채팅에 사용자 추가"),
        "addToChat": MessageLookupByLibrary.simpleMessage("채팅에 추가"),
        "addUserEmailAndEnterToInvite": MessageLookupByLibrary.simpleMessage(
            "사용자 이메일을 입력하고 \"Enter\"를 눌러 초대"),
        "addedToGroupSuccessfully":
            MessageLookupByLibrary.simpleMessage("그룹에 성공적으로 추가됨"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Agnes는 실수를 할 수 있습니다. 중요한 정보를 확인하세요."),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage("요약, 보고서 또는 슬라이드는 @Agnes 사용."),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn": MessageLookupByLibrary.simpleMessage("동의하고 로그인"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("AI 디자인"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("AI 슬라이드"),
        "album": MessageLookupByLibrary.simpleMessage("앨범"),
        "allMembers": MessageLookupByLibrary.simpleMessage("모든 멤버"),
        "answerCall": MessageLookupByLibrary.simpleMessage("전화 받기"),
        "appUpdate": MessageLookupByLibrary.simpleMessage("앱 업데이트"),
        "archived": MessageLookupByLibrary.simpleMessage("보관됨"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage("계정을 삭제하시겠습니까?"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage("계정에서 로그아웃하시겠습니까?"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Agnes에게 물어보기"),
        "askAgnesToEditSlides":
            MessageLookupByLibrary.simpleMessage("Agnes에게 슬라이드 편집 요청"),
        "assignATaskToBegin":
            MessageLookupByLibrary.simpleMessage("작업을 할당하여 시작하세요"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage("최소 6자 이상, 문자·숫자·기호 포함"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "현재 수행할 작업에 대한 합의가 이루어지지 않았습니다. 필요한 경우 추가로 토론하고, 질문이 있다면 자유롭게 질문해 주세요!"),
        "avatar": MessageLookupByLibrary.simpleMessage("아바타"),
        "back": MessageLookupByLibrary.simpleMessage("뒤로"),
        "bookmarkRemoved": MessageLookupByLibrary.simpleMessage("북마크 제거됨"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("북마크됨"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined": MessageLookupByLibrary.simpleMessage("통화가 거절되었습니다"),
        "cancel": MessageLookupByLibrary.simpleMessage("취소"),
        "canvas": MessageLookupByLibrary.simpleMessage("캔버스"),
        "chapter": MessageLookupByLibrary.simpleMessage("장"),
        "chat": MessageLookupByLibrary.simpleMessage("채팅"),
        "chatSettings": MessageLookupByLibrary.simpleMessage("채팅 설정"),
        "chooseImage": MessageLookupByLibrary.simpleMessage("이미지 선택"),
        "close": MessageLookupByLibrary.simpleMessage("닫기"),
        "code": MessageLookupByLibrary.simpleMessage("코드"),
        "collect": MessageLookupByLibrary.simpleMessage("수집"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("곧 출시"),
        "comment": MessageLookupByLibrary.simpleMessage("댓글"),
        "commenter": MessageLookupByLibrary.simpleMessage("댓글 작성자"),
        "commentsTasks": MessageLookupByLibrary.simpleMessage("댓글 작업"),
        "confirm": MessageLookupByLibrary.simpleMessage("확인"),
        "confirmDelete": MessageLookupByLibrary.simpleMessage("삭제 확인"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("비밀번호 확인"),
        "confirmPasswordCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("확인 비밀번호를 비워둘 수 없습니다"),
        "confirmSignOut": MessageLookupByLibrary.simpleMessage("로그아웃 확인"),
        "connect": MessageLookupByLibrary.simpleMessage("연결"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage("AI 헤드셋을 연결하고 언제든지 Agnes와 채팅"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage(
                "Agnes VibePods에 연결하고 언제든지 채팅하세요."),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage("친구들과 연결하여 그룹 채팅 시작"),
        "connected": MessageLookupByLibrary.simpleMessage("연결됨"),
        "connectionFailed": MessageLookupByLibrary.simpleMessage("연결 실패"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "콘텐츠가 복사되었습니다 — 선호하는 플랫폼에 붙여넣으세요!"),
        "continueWithApple": MessageLookupByLibrary.simpleMessage("Apple로 계속"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("Google로 계속"),
        "continueWithOtherWays":
            MessageLookupByLibrary.simpleMessage("다른 방법으로 계속하기"),
        "continueWord": MessageLookupByLibrary.simpleMessage("계속"),
        "copied": MessageLookupByLibrary.simpleMessage("복사됨"),
        "copy": MessageLookupByLibrary.simpleMessage("복사"),
        "cost": MessageLookupByLibrary.simpleMessage("비용"),
        "cover": MessageLookupByLibrary.simpleMessage("표지"),
        "createANewTaskToGetStarted":
            MessageLookupByLibrary.simpleMessage("시작하려면 새 작업 만들기"),
        "createAnAccountToSignUpForAgnes":
            MessageLookupByLibrary.simpleMessage("Agnes에 가입하려면 계정을 만드세요"),
        "createGroupChat": MessageLookupByLibrary.simpleMessage("그룹 채팅 만들기"),
        "currentlyInACall": MessageLookupByLibrary.simpleMessage("현재 통화 중..."),
        "deepResearch": MessageLookupByLibrary.simpleMessage("심층 연구"),
        "deepresearch": MessageLookupByLibrary.simpleMessage("심층 연구"),
        "delete": MessageLookupByLibrary.simpleMessage("삭제"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("계정 삭제"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("채팅 삭제"),
        "doYouWantToExecuteTheseTasks":
            MessageLookupByLibrary.simpleMessage("이 작업들을 실행하시겠습니까?"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                "는 Agnes 사용자가 아닌 것 같습니다. 초대장을 보내려면 이메일을 입력하세요"),
        "done": MessageLookupByLibrary.simpleMessage("완료"),
        "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage("계정이 없으신가요?"),
        "downloading": MessageLookupByLibrary.simpleMessage("다운로드 중"),
        "eachNewTaskConsumesOneDiamond":
            MessageLookupByLibrary.simpleMessage("1. 새 작업마다 다이아몬드 1개를 사용합니다."),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. 각 사용자는 매월 다이아몬드 30개를 받습니다."),
        "edit": MessageLookupByLibrary.simpleMessage("편집"),
        "editGroupName": MessageLookupByLibrary.simpleMessage("그룹 이름 편집"),
        "editor": MessageLookupByLibrary.simpleMessage("편집자"),
        "email": MessageLookupByLibrary.simpleMessage("이메일"),
        "emailCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("이메일을 비워둘 수 없습니다"),
        "emailFormatIncorrect":
            MessageLookupByLibrary.simpleMessage("이메일 형식이 잘못되었습니다"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage("확장된 액세스를 통해 생산성과 창의성 향상"),
        "enterConfirmPassword":
            MessageLookupByLibrary.simpleMessage("확인 비밀번호 입력"),
        "enterGroupName": MessageLookupByLibrary.simpleMessage("그룹 이름 입력"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("비밀번호 입력"),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("인증 코드 입력"),
        "export": MessageLookupByLibrary.simpleMessage("내보내기"),
        "file": MessageLookupByLibrary.simpleMessage("파일"),
        "floatingWindowNotEnabled":
            MessageLookupByLibrary.simpleMessage("플로팅 창이 활성화되지 않았습니다"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage("전체 경험을 위해 PC 버전을 사용하세요"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage("비밀번호를 잊으셨나요?"),
        "general": MessageLookupByLibrary.simpleMessage("일반"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Agnes AI Plus 받기"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Agnes AI Pro 받기"),
        "giveMeATaskToWorkOn":
            MessageLookupByLibrary.simpleMessage("작업할 과제를 주세요…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("상점으로 이동"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage(
                "알겠습니다! 작업을 진행 중입니다. 잠시 기다려 주세요."),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("그룹이 성공적으로 생성됨"),
        "groupFiles": MessageLookupByLibrary.simpleMessage("그룹 파일"),
        "groupName": MessageLookupByLibrary.simpleMessage("그룹 이름"),
        "groupNameOptional":
            MessageLookupByLibrary.simpleMessage("그룹 이름 (선택 사항)"),
        "harmful": MessageLookupByLibrary.simpleMessage("해로운"),
        "helpUsImprove": MessageLookupByLibrary.simpleMessage("개선에 도움을 주세요"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage("검색, 글쓰기 및 답변을 도와드립니다"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage("수신 전화를 받으면 현재 통화가 종료됩니다."),
        "imAgnes": MessageLookupByLibrary.simpleMessage("저는 Agnes입니다"),
        "inAVoiceCall": MessageLookupByLibrary.simpleMessage("음성 통화 중"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("부정확"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "앱 내 구매가 현재 사용 불가합니다. 나중에 다시 시도하세요"),
        "interfaceLanguage": MessageLookupByLibrary.simpleMessage("인터페이스 언어"),
        "invite": MessageLookupByLibrary.simpleMessage("초대"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage("사용자 이름, 이메일 또는 전화번호로 초대"),
        "inviteFriends": MessageLookupByLibrary.simpleMessage("친구 초대"),
        "inviteOthersToCollaborate":
            MessageLookupByLibrary.simpleMessage("다른 사람을 초대하여 협업하기"),
        "inviteToCollaborate": MessageLookupByLibrary.simpleMessage("협업 초대"),
        "isEditing": MessageLookupByLibrary.simpleMessage("편집 중"),
        "join": MessageLookupByLibrary.simpleMessage("참여"),
        "knowMore": MessageLookupByLibrary.simpleMessage("자세히 보기"),
        "language": MessageLookupByLibrary.simpleMessage("영어"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Agnes가 더 나은 공유 형식을 만들도록 도와드립니다"),
        "letAgnesResolve":
            MessageLookupByLibrary.simpleMessage("Agnes가 해결하도록 하기"),
        "linkCopied": MessageLookupByLibrary.simpleMessage("링크가 복사되었습니다"),
        "listening": MessageLookupByLibrary.simpleMessage("듣는 중"),
        "loading": MessageLookupByLibrary.simpleMessage("로딩 중"),
        "makeMeAShareableImagePost":
            MessageLookupByLibrary.simpleMessage("공유 가능한 이미지 게시물을 만들어 주세요"),
        "makeMeAnHtmlWebPage":
            MessageLookupByLibrary.simpleMessage("HTML 웹페이지를 만들어 주세요"),
        "manageMembers": MessageLookupByLibrary.simpleMessage("멤버 관리"),
        "manuallyEndTheTask": MessageLookupByLibrary.simpleMessage("작업 수동 종료"),
        "markAsResolved": MessageLookupByLibrary.simpleMessage("해결됨으로 표시"),
        "messageCopied": MessageLookupByLibrary.simpleMessage("메시지가 복사되었습니다."),
        "multipleChoices": MessageLookupByLibrary.simpleMessage("다중 선택"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("내 즐겨찾기"),
        "narrate": MessageLookupByLibrary.simpleMessage("내레이션"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("네트워크 연결에 실패했습니다. 다시 시도하세요"),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("네트워크 연결 끊김"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("네트워크 사용 불가"),
        "newPassword": MessageLookupByLibrary.simpleMessage("새 비밀번호"),
        "newProject": MessageLookupByLibrary.simpleMessage("새 프로젝트"),
        "newTask": MessageLookupByLibrary.simpleMessage("새 작업"),
        "newTitle": MessageLookupByLibrary.simpleMessage("새 제목"),
        "noMoreFiles": MessageLookupByLibrary.simpleMessage("더 이상 파일 없음"),
        "noMoreMessage": MessageLookupByLibrary.simpleMessage("더 이상 메시지 없음..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "보류 중인 작업이 없습니다. 모든 작업이 완료되었습니다!"),
        "notAvailableForPurchase":
            MessageLookupByLibrary.simpleMessage("구매 불가"),
        "notConnected": MessageLookupByLibrary.simpleMessage("연결 안됨"),
        "notNow": MessageLookupByLibrary.simpleMessage("지금은 아니요"),
        "notifications": MessageLookupByLibrary.simpleMessage("알림"),
        "notifyUserViaEmail":
            MessageLookupByLibrary.simpleMessage("이메일로 사용자에게 알림"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "알겠습니다. 필요한 권한이 없습니다. 소유자나 편집자에게 요청하세요."),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage("좋아요, 합의된 작업을 요약하겠습니다:"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage("삭제하면 그룹 채팅 기록이 지워집니다."),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("작업이 성공적으로 완료되었습니다!"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage("질문이 있으면 알려주세요"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage(
                "그룹 이름이 변경되면 다른 멤버들에게 알림이 갑니다."),
        "outOfDate": MessageLookupByLibrary.simpleMessage("만료됨"),
        "owner": MessageLookupByLibrary.simpleMessage("소유자"),
        "pages": MessageLookupByLibrary.simpleMessage("페이지"),
        "passwordCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("비밀번호를 비워둘 수 없습니다"),
        "pending": MessageLookupByLibrary.simpleMessage("보류 중"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage("백그라운드에서 작업을 실행할 권한이 없습니다"),
        "phone": MessageLookupByLibrary.simpleMessage("전화"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("전화번호"),
        "phoneNumberCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("전화번호를 비워둘 수 없습니다"),
        "photo": MessageLookupByLibrary.simpleMessage("사진"),
        "pleaseSelectASlideToEdit":
            MessageLookupByLibrary.simpleMessage("편집할 슬라이드를 선택하세요."),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "새로운 기능과 개선 사항을 위해 최신 버전으로 업데이트하세요"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage("Agnes가 작업을 완료할 때까지 기다려주세요"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage("다운로드가 완료될 때까지 기다려주세요"),
        "presenter": MessageLookupByLibrary.simpleMessage("발표자"),
        "preview": MessageLookupByLibrary.simpleMessage("미리보기"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("개인정보 처리방침"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage("이 답변에 대한 추가 피드백을 제공하세요"),
        "quote": MessageLookupByLibrary.simpleMessage("인용"),
        "quotedMessageHasExpired":
            MessageLookupByLibrary.simpleMessage("인용 메시지가 무효가 되었습니다."),
        "reconnect": MessageLookupByLibrary.simpleMessage("다시 연결"),
        "reject": MessageLookupByLibrary.simpleMessage("거절"),
        "remove": MessageLookupByLibrary.simpleMessage("제거"),
        "removedSuccessfully":
            MessageLookupByLibrary.simpleMessage("성공적으로 제거됨"),
        "rename": MessageLookupByLibrary.simpleMessage("이름 변경"),
        "reopenTheApp": MessageLookupByLibrary.simpleMessage("앱을 다시 열기"),
        "requestTimedOutPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("요청 시간이 초과되었습니다. 다시 시도하세요"),
        "research": MessageLookupByLibrary.simpleMessage("연구"),
        "researchForZdSeconds": m1,
        "resetPassword": MessageLookupByLibrary.simpleMessage("비밀번호 재설정"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage("비밀번호 재설정"),
        "role": MessageLookupByLibrary.simpleMessage("역할"),
        "save": MessageLookupByLibrary.simpleMessage("저장"),
        "saved": MessageLookupByLibrary.simpleMessage("저장됨"),
        "saving": MessageLookupByLibrary.simpleMessage("저장 중…"),
        "search": MessageLookupByLibrary.simpleMessage("검색"),
        "searchByNameEmailOrPhone":
            MessageLookupByLibrary.simpleMessage("이름, 이메일 또는 전화로 검색"),
        "searchForACountry": MessageLookupByLibrary.simpleMessage("국가 검색"),
        "seconds": MessageLookupByLibrary.simpleMessage("초"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage("인터넷 연결이 끊긴 것 같습니다"),
        "selectMembers": MessageLookupByLibrary.simpleMessage("멤버 선택"),
        "send": MessageLookupByLibrary.simpleMessage("보내기"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("메시지 보내기"),
        "serverConnectionFailed":
            MessageLookupByLibrary.simpleMessage("서버 연결에 실패했습니다."),
        "serviceIsStillStarting":
            MessageLookupByLibrary.simpleMessage("서비스가 아직 시작 중입니다."),
        "sessionExpiredPleaseLogInAgain":
            MessageLookupByLibrary.simpleMessage("세션이 만료되었습니다. 다시 로그인해주세요"),
        "setNow": MessageLookupByLibrary.simpleMessage("지금 설정"),
        "settings": MessageLookupByLibrary.simpleMessage("설정"),
        "share": MessageLookupByLibrary.simpleMessage("공유"),
        "signIn": MessageLookupByLibrary.simpleMessage("로그인"),
        "signOut": MessageLookupByLibrary.simpleMessage("로그아웃"),
        "signUp": MessageLookupByLibrary.simpleMessage("회원가입"),
        "someoneMe": MessageLookupByLibrary.simpleMessage("누군가 나를 @멘션함"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "죄송합니다. 현재 최대 용량입니다. 내일 다시 시도해주세요."),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking": MessageLookupByLibrary.simpleMessage("말하기 시작"),
        "submit": MessageLookupByLibrary.simpleMessage("제출"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry":
            MessageLookupByLibrary.simpleMessage("Wi-Fi/모바일 데이터를 전환하고 다시 시도"),
        "system": MessageLookupByLibrary.simpleMessage("시스템"),
        "systemBusyPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("시스템이 바쁩니다. 다시 시도하세요"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("사진 찍기"),
        "taskStoppedManually":
            MessageLookupByLibrary.simpleMessage("작업이 수동으로 중단되었습니다"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService": MessageLookupByLibrary.simpleMessage("서비스 약관"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage(
                "2. 다이아몬드 개수는 매월 1일에 업데이트됩니다."),
        "theGroupIsCurrentlyInACall":
            MessageLookupByLibrary.simpleMessage("그룹이 현재 통화 중입니다..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage("사용자 인터페이스에서 사용되는 언어"),
        "thePhoneNumberIsInvalid":
            MessageLookupByLibrary.simpleMessage("전화번호가 유효하지 않습니다"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "이 앱은 프로필 사진을 찍기 위해 카메라 접근 권한이 필요합니다."),
        "thisIsntHelpful": MessageLookupByLibrary.simpleMessage("도움이 되지 않음"),
        "thisSlideIsBeingEdited":
            MessageLookupByLibrary.simpleMessage("이 슬라이드는 편집 중입니다."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "인식, 상호작용 또는 아바타 설정을 위해 이미지를 전송할 수 있도록 Agnes가 카메라에 접근하도록 허용하세요"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "인식, 상호작용 또는 아바타 설정을 위해 이미지를 전송할 수 있도록 Agnes가 사진 라이브러리에 접근하도록 허용하세요"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "생성된 이미지를 저장하려면 Agnes가 사진 라이브러리에 이미지를 저장하도록 허용하세요"),
        "transcribing": MessageLookupByLibrary.simpleMessage("필기 중…"),
        "transcriptionEnabledLoadingData":
            MessageLookupByLibrary.simpleMessage("필기 기능이 활성화되었습니다. 데이터 로딩 중…"),
        "tryOneOfThese":
            MessageLookupByLibrary.simpleMessage("다음 중 하나를 시도하세요:"),
        "twoPasswordsAreInconsistent":
            MessageLookupByLibrary.simpleMessage("두 비밀번호가 일치하지 않습니다"),
        "update": MessageLookupByLibrary.simpleMessage("업데이트"),
        "updateFailedPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage("업데이트에 실패했습니다. 나중에 다시 시도하세요."),
        "updatePassword": MessageLookupByLibrary.simpleMessage("비밀번호 업데이트"),
        "updatePasswordSuccessfully":
            MessageLookupByLibrary.simpleMessage("비밀번호가 성공적으로 업데이트되었습니다"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage("계속하려면 요금제를 업그레이드하세요: 업그레이드"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage("문자, 숫자 및 기호 포함 6자 이상 사용"),
        "userAccess": MessageLookupByLibrary.simpleMessage("사용자 접근"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("사용자 이름"),
        "usersWithAccess":
            MessageLookupByLibrary.simpleMessage("접근 권한이 있는 사용자"),
        "usingTool": MessageLookupByLibrary.simpleMessage("도구 사용"),
        "verificationCode": MessageLookupByLibrary.simpleMessage("인증 코드"),
        "verificationCodeCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("인증 코드는 비워둘 수 없습니다"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("보기"),
        "viewAllComments": MessageLookupByLibrary.simpleMessage("모든 댓글 보기"),
        "viewer": MessageLookupByLibrary.simpleMessage("뷰어"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "음성 통화 생성에 실패했습니다. 네트워크 불안정으로 인해 발생할 수 있습니다. 다시 시도하세요."),
        "voiceCallEnded": MessageLookupByLibrary.simpleMessage("음성 통화 종료"),
        "waiting": MessageLookupByLibrary.simpleMessage("대기 중"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "다운로드한 이미지나 동영상을 사진 보관함에 저장하려면 접근 권한이 필요합니다."),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage("음성 상호작용을 위해 마이크에 접근해야 합니다"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "프로필 사진을 선택하려면 사진 보관함 접근 권한이 필요합니다."),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage("이미지를 선택하려면 사진 보관함에 접근해야 합니다"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "음성을 인식하여 상호작용을 위해 텍스트로 변환해야 합니다"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage("사진 보관함에 이미지를 저장해야 합니다"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "음성 상호작용을 위해 블루투스를 사용하여 헤드셋을 연결해야 합니다"),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage("사진을 찍기 위해 카메라를 사용해야 합니다"),
        "welcomeToAgnes":
            MessageLookupByLibrary.simpleMessage("Agnes에 오신 것을 환영합니다"),
        "writeSomething": MessageLookupByLibrary.simpleMessage("무언가 작성"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("당신"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage(
                "실행, 수정 또는 삭제할 수 있습니다. 어떻게 진행하시겠습니까?"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "슬라이드를 수동으로 편집하거나 Agnes와 대화로 편집할 수 있습니다."),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage("콘텐츠를 선택하고 피드백을 공유할 수 있습니다."),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "AGNES는 플로팅 창 사용 권한이 없어 영상 통화를 최소화할 수 없습니다."),
        "youCreatedTheGroup":
            MessageLookupByLibrary.simpleMessage("그룹을 생성했습니다."),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage("그룹 채팅에서 제거되었습니다"),
        "youJoinedTheGroup":
            MessageLookupByLibrary.simpleMessage("그룹에 가입했습니다."),
        "yourCurrentPlan": MessageLookupByLibrary.simpleMessage("현재 요금제"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "휴대폰에서 Agnes가 플로팅 윈도우 권한을 사용하도록 승인하지 않았습니다. Agnes는 백그라운드에서 작업을 계속 수행할 수 없습니다."),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage("연구 작업 요청 한도에 도달했습니다."),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage("슬라이드 작업 요청 한도에 도달했습니다."),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage("이 작업 한도에 도달했습니다."),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd": MessageLookupByLibrary.simpleMessage("끝에 도달했습니다")
      };
}
