// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ja locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ja';

  static String m0(termsOfService, privacyPolicy) =>
      "サインインすることで、\n${termsOfService}と${privacyPolicy}に同意したことになります。";

  static String m1(value) => "${value} 秒間リサーチ";

  static String m2(title) => "申し訳ありません、${title}のタスクが失敗しました。後で再試行してください。";

  static String m3(value) => "最大 ${value} 枚の画像をアップロード可能";

  static String m4(remaining_count) =>
      "要求通りタスクを削除しました。現在の保留中のタスク数: ${remaining_count}。すべてのタスクを表示しますか？";

  static String m5(total) => "現在 ${total} 件の保留中のタスクがあります:";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "ユーザー ${user_name} のクォータが不足しています。スライド残り：${slides_remaining_count}、スライドに必要なクォータ：${slides_plan_count}、調査残り：${research_remaining_count}、調査に必要なクォータ：${research_plan_count}。クォータが十分なタスクを選択して実行できます。";

  static String m7(xxx) => "認証コードを ${xxx}@email.com に送信しました";

  static String m8(version) => "バージョン ${version} が利用可能です";

  static String m9(x) => "${x} 人が音声中…";

  static String m10(xxx) => "${xxx} がグループ名を変更しました";

  static String m11(xxx) => "${xxx} が「${xxx}」の編集に招待しました。";

  static String m12(xxx) => "${xxx} が「${xxx}」のレビューに招待しました。";

  static String m13(xxx) => "${xxx}が音声通話を開始しました";

  static String m14(xxx, xyz) => "${xxx} さんが「${xyz}」グループ通話に招待しています。";

  static String m15(title) => "${title}のタスクが正常に完了しました！";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond": MessageLookupByLibrary.simpleMessage("ダイヤモンドについて"),
        "accept": MessageLookupByLibrary.simpleMessage("受け入れる"),
        "account": MessageLookupByLibrary.simpleMessage("アカウント"),
        "accountDeletionSuccessful":
            MessageLookupByLibrary.simpleMessage("アカウントの削除が成功しました"),
        "accountNotCreatedYet":
            MessageLookupByLibrary.simpleMessage("アカウントはまだ作成されていません"),
        "accountSignoutSuccessful":
            MessageLookupByLibrary.simpleMessage("アカウントのサインアウトが成功しました"),
        "activeAccount": MessageLookupByLibrary.simpleMessage("有効なアカウント"),
        "add": MessageLookupByLibrary.simpleMessage("追加"),
        "addComment": MessageLookupByLibrary.simpleMessage("コメントを追加"),
        "addPeopleToChat": MessageLookupByLibrary.simpleMessage("チャットにメンバーを追加"),
        "addToChat": MessageLookupByLibrary.simpleMessage("チャットに追加"),
        "addUserEmailAndEnterToInvite":
            MessageLookupByLibrary.simpleMessage("ユーザーのメールを入力し「Enter」で招待"),
        "addedToGroupSuccessfully":
            MessageLookupByLibrary.simpleMessage("グループに追加されました"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Agnes は間違えることがあります。重要な情報を確認してください。"),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage(
                "要約、レポート、またはスライドには @Agnes を使用。"),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn": MessageLookupByLibrary.simpleMessage("同意してログイン"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("AIデザイン"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("AIスライド"),
        "album": MessageLookupByLibrary.simpleMessage("アルバム"),
        "allMembers": MessageLookupByLibrary.simpleMessage("すべてのメンバー"),
        "answerCall": MessageLookupByLibrary.simpleMessage("通話に応答"),
        "appUpdate": MessageLookupByLibrary.simpleMessage("アプリのアップデート"),
        "archived": MessageLookupByLibrary.simpleMessage("アーカイブ済み"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage("アカウントを削除してもよろしいですか？"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage("アカウントからサインアウトしてもよろしいですか？"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Agnesに質問する"),
        "askAgnesToEditSlides":
            MessageLookupByLibrary.simpleMessage("Agnes にスライドの編集を依頼"),
        "assignATaskToBegin":
            MessageLookupByLibrary.simpleMessage("タスクを割り当てて開始してください"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage("6文字以上で、文字・数字・記号を含めてください"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "現在、実施すべきタスクについてはコンセンサスが形成されていません。必要に応じて更に議論し、質問があればお気軽にお問い合わせください！"),
        "avatar": MessageLookupByLibrary.simpleMessage("アバター"),
        "back": MessageLookupByLibrary.simpleMessage("戻る"),
        "bookmarkRemoved": MessageLookupByLibrary.simpleMessage("ブックマークを削除"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("ブックマーク済み"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined": MessageLookupByLibrary.simpleMessage("通話が拒否されました"),
        "cancel": MessageLookupByLibrary.simpleMessage("キャンセル"),
        "canvas": MessageLookupByLibrary.simpleMessage("キャンバス"),
        "chapter": MessageLookupByLibrary.simpleMessage("章"),
        "chat": MessageLookupByLibrary.simpleMessage("チャット"),
        "chatSettings": MessageLookupByLibrary.simpleMessage("チャット設定"),
        "chooseImage": MessageLookupByLibrary.simpleMessage("画像を選択"),
        "close": MessageLookupByLibrary.simpleMessage("閉じる"),
        "code": MessageLookupByLibrary.simpleMessage("コード"),
        "collect": MessageLookupByLibrary.simpleMessage("収集"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("近日公開"),
        "comment": MessageLookupByLibrary.simpleMessage("コメント"),
        "commenter": MessageLookupByLibrary.simpleMessage("コメント者"),
        "commentsTasks": MessageLookupByLibrary.simpleMessage("コメントタスク"),
        "confirm": MessageLookupByLibrary.simpleMessage("確認する"),
        "confirmDelete": MessageLookupByLibrary.simpleMessage("削除を確認"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("パスワードを確認"),
        "confirmPasswordCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("確認用パスワードを空にすることはできません"),
        "confirmSignOut": MessageLookupByLibrary.simpleMessage("サインアウトを確認"),
        "connect": MessageLookupByLibrary.simpleMessage("接続"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage(
                "AIヘッドセットを接続して、いつでもAgnesとチャット"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage("Agnes VibePodsに接続していつでもチャット"),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage("友達とつながってグループチャットを開始"),
        "connected": MessageLookupByLibrary.simpleMessage("接続済み"),
        "connectionFailed": MessageLookupByLibrary.simpleMessage("接続に失敗しました"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "コンテンツをコピーしました — お気に入りのプラットフォームに貼り付けてください。"),
        "continueWithApple": MessageLookupByLibrary.simpleMessage("Appleで続行"),
        "continueWithGoogle": MessageLookupByLibrary.simpleMessage("Googleで続行"),
        "continueWithOtherWays":
            MessageLookupByLibrary.simpleMessage("他の方法で続行"),
        "continueWord": MessageLookupByLibrary.simpleMessage("続ける"),
        "copied": MessageLookupByLibrary.simpleMessage("コピーしました"),
        "copy": MessageLookupByLibrary.simpleMessage("コピー"),
        "cost": MessageLookupByLibrary.simpleMessage("コスト"),
        "cover": MessageLookupByLibrary.simpleMessage("表紙"),
        "createANewTaskToGetStarted":
            MessageLookupByLibrary.simpleMessage("開始するために新しいタスクを作成"),
        "createAnAccountToSignUpForAgnes": MessageLookupByLibrary.simpleMessage(
            "Agnes にサインアップするにはアカウントを作成してください"),
        "createGroupChat": MessageLookupByLibrary.simpleMessage("グループチャットを作成"),
        "currentlyInACall": MessageLookupByLibrary.simpleMessage("現在通話中です..."),
        "deepResearch": MessageLookupByLibrary.simpleMessage("ディープリサーチ"),
        "deepresearch": MessageLookupByLibrary.simpleMessage("深いリサーチ"),
        "delete": MessageLookupByLibrary.simpleMessage("削除"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("アカウントを削除"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("チャットを削除"),
        "doYouWantToExecuteTheseTasks":
            MessageLookupByLibrary.simpleMessage("これらのタスクを実行しますか？"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                " はAgnesのユーザーではないようです。招待を送るにはメールアドレスを入力してください"),
        "done": MessageLookupByLibrary.simpleMessage("完了"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("アカウントをお持ちでないですか？"),
        "downloading": MessageLookupByLibrary.simpleMessage("ダウンロード中"),
        "eachNewTaskConsumesOneDiamond":
            MessageLookupByLibrary.simpleMessage("1. 新しいタスクごとにダイヤモンドを1つ消費します。"),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. 各ユーザーは毎月30個のダイヤモンドを受け取ります。"),
        "edit": MessageLookupByLibrary.simpleMessage("編集"),
        "editGroupName": MessageLookupByLibrary.simpleMessage("グループ名を編集"),
        "editor": MessageLookupByLibrary.simpleMessage("編集者"),
        "email": MessageLookupByLibrary.simpleMessage("メール"),
        "emailCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("メールアドレスを空にすることはできません"),
        "emailFormatIncorrect":
            MessageLookupByLibrary.simpleMessage("メールアドレスの形式が正しくありません"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage("拡張アクセスで生産性と創造性を向上"),
        "enterConfirmPassword":
            MessageLookupByLibrary.simpleMessage("確認用パスワードを入力"),
        "enterGroupName": MessageLookupByLibrary.simpleMessage("グループ名を入力"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("パスワードを入力"),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("認証コードを入力"),
        "export": MessageLookupByLibrary.simpleMessage("エクスポート"),
        "file": MessageLookupByLibrary.simpleMessage("ファイル"),
        "floatingWindowNotEnabled":
            MessageLookupByLibrary.simpleMessage("フローティングウィンドウが有効になっていません"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage("フル体験するには、PC版を使用してください"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage("パスワードをお忘れですか？"),
        "general": MessageLookupByLibrary.simpleMessage("一般"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Agnes AI Plusを取得"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Agnes AI Proを取得"),
        "giveMeATaskToWorkOn":
            MessageLookupByLibrary.simpleMessage("作業を与えてください…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("ショップへ"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage("了解！タスクを実行中です。少々お待ちください。"),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("グループが作成されました"),
        "groupFiles": MessageLookupByLibrary.simpleMessage("グループファイル"),
        "groupName": MessageLookupByLibrary.simpleMessage("グループ名"),
        "groupNameOptional": MessageLookupByLibrary.simpleMessage("グループ名（任意）"),
        "harmful": MessageLookupByLibrary.simpleMessage("有害"),
        "helpUsImprove": MessageLookupByLibrary.simpleMessage("改善にご協力ください"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage("検索、執筆、回答をサポートします"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage("着信に応答すると、現在の通話は終了します。"),
        "imAgnes": MessageLookupByLibrary.simpleMessage("私は Agnes です"),
        "inAVoiceCall": MessageLookupByLibrary.simpleMessage("音声通話中"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("不正確"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "アプリ内購入は現在利用できません。後でもう一度お試しください"),
        "interfaceLanguage": MessageLookupByLibrary.simpleMessage("インターフェース言語"),
        "invite": MessageLookupByLibrary.simpleMessage("招待"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage("ユーザー名、メール、または電話番号で招待"),
        "inviteFriends": MessageLookupByLibrary.simpleMessage("友達を招待"),
        "inviteOthersToCollaborate":
            MessageLookupByLibrary.simpleMessage("他の人を招待して共同作業する"),
        "inviteToCollaborate":
            MessageLookupByLibrary.simpleMessage("共同作業に招待する"),
        "isEditing": MessageLookupByLibrary.simpleMessage("編集中"),
        "join": MessageLookupByLibrary.simpleMessage("参加"),
        "knowMore": MessageLookupByLibrary.simpleMessage("詳細はこちら"),
        "language": MessageLookupByLibrary.simpleMessage("英語"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Agnesに、より良い共有形式を作成するのを手伝わせましょう"),
        "letAgnesResolve": MessageLookupByLibrary.simpleMessage("Agnes に任せる"),
        "linkCopied": MessageLookupByLibrary.simpleMessage("リンクをコピーしました"),
        "listening": MessageLookupByLibrary.simpleMessage("聴取中"),
        "loading": MessageLookupByLibrary.simpleMessage("読み込み中"),
        "makeMeAShareableImagePost":
            MessageLookupByLibrary.simpleMessage("共有可能な画像投稿を作成してください"),
        "makeMeAnHtmlWebPage":
            MessageLookupByLibrary.simpleMessage("HTMLページを作ってください"),
        "manageMembers": MessageLookupByLibrary.simpleMessage("メンバーを管理"),
        "manuallyEndTheTask": MessageLookupByLibrary.simpleMessage("手動でタスクを終了"),
        "markAsResolved": MessageLookupByLibrary.simpleMessage("解決済みにする"),
        "messageCopied":
            MessageLookupByLibrary.simpleMessage("メッセージがコピーされました。"),
        "multipleChoices": MessageLookupByLibrary.simpleMessage("複数選択"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("お気に入り"),
        "narrate": MessageLookupByLibrary.simpleMessage("ナレーション"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("ネットワーク接続に失敗しました。再試行してください"),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("ネットワーク接続が失われました"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("ネットワークが利用できません"),
        "newPassword": MessageLookupByLibrary.simpleMessage("新しいパスワード"),
        "newProject": MessageLookupByLibrary.simpleMessage("新しいプロジェクト"),
        "newTask": MessageLookupByLibrary.simpleMessage("新しいタスク"),
        "newTitle": MessageLookupByLibrary.simpleMessage("新しいタイトル"),
        "noMoreFiles": MessageLookupByLibrary.simpleMessage("これ以上ファイルはありません"),
        "noMoreMessage":
            MessageLookupByLibrary.simpleMessage("これ以上メッセージはありません..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "保留中のタスクは見つかりませんでした。すべてのタスクが完了しました！"),
        "notAvailableForPurchase":
            MessageLookupByLibrary.simpleMessage("購入できません"),
        "notConnected": MessageLookupByLibrary.simpleMessage("接続されていません"),
        "notNow": MessageLookupByLibrary.simpleMessage("今はしない"),
        "notifications": MessageLookupByLibrary.simpleMessage("通知"),
        "notifyUserViaEmail":
            MessageLookupByLibrary.simpleMessage("メールでユーザーに通知"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "了解しました。必要な権限がありません。オーナーまたは編集者に依頼してください。"),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage("了解しました。合意されたタスクをまとめます:"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage("削除すると、グループチャットの履歴が消去されます。"),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("操作が成功しました！"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage("または質問があれば教えてください"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage("グループ名を変更すると、他のメンバーに通知されます。"),
        "outOfDate": MessageLookupByLibrary.simpleMessage("期限切れ"),
        "owner": MessageLookupByLibrary.simpleMessage("所有者"),
        "pages": MessageLookupByLibrary.simpleMessage("ページ"),
        "passwordCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("パスワードを空にすることはできません"),
        "pending": MessageLookupByLibrary.simpleMessage("保留中"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage("バックグラウンドでタスクを実行する権限がありません"),
        "phone": MessageLookupByLibrary.simpleMessage("電話"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("電話番号"),
        "phoneNumberCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("電話番号を空にすることはできません"),
        "photo": MessageLookupByLibrary.simpleMessage("写真"),
        "pleaseSelectASlideToEdit":
            MessageLookupByLibrary.simpleMessage("編集するスライドを選択してください。"),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "新しい機能や改善のために最新バージョンにアップデートしてください"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage("Agnesがタスクを完了するまでお待ちください"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage("ダウンロードが完了するまでお待ちください"),
        "presenter": MessageLookupByLibrary.simpleMessage("発表者"),
        "preview": MessageLookupByLibrary.simpleMessage("プレビュー"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("プライバシーポリシー"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage("この回答について追加のフィードバックを提供する"),
        "quote": MessageLookupByLibrary.simpleMessage("引用"),
        "quotedMessageHasExpired":
            MessageLookupByLibrary.simpleMessage("引用メッセージは無効になりました。"),
        "reconnect": MessageLookupByLibrary.simpleMessage("再接続"),
        "reject": MessageLookupByLibrary.simpleMessage("拒否"),
        "remove": MessageLookupByLibrary.simpleMessage("削除"),
        "removedSuccessfully": MessageLookupByLibrary.simpleMessage("削除成功"),
        "rename": MessageLookupByLibrary.simpleMessage("名前を変更"),
        "reopenTheApp": MessageLookupByLibrary.simpleMessage("アプリを再度開く"),
        "requestTimedOutPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("リクエストのタイムアウトです。再試行してください"),
        "research": MessageLookupByLibrary.simpleMessage("リサーチ"),
        "researchForZdSeconds": m1,
        "resetPassword": MessageLookupByLibrary.simpleMessage("パスワードをリセット"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage("パスワードをリセット"),
        "role": MessageLookupByLibrary.simpleMessage("役割"),
        "save": MessageLookupByLibrary.simpleMessage("保存"),
        "saved": MessageLookupByLibrary.simpleMessage("保存済み"),
        "saving": MessageLookupByLibrary.simpleMessage("保存中…"),
        "search": MessageLookupByLibrary.simpleMessage("検索"),
        "searchByNameEmailOrPhone":
            MessageLookupByLibrary.simpleMessage("名前、メール、電話番号で検索"),
        "searchForACountry": MessageLookupByLibrary.simpleMessage("国を検索"),
        "seconds": MessageLookupByLibrary.simpleMessage("秒"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage("インターネットから切断されているようです"),
        "selectMembers": MessageLookupByLibrary.simpleMessage("メンバーを選択"),
        "send": MessageLookupByLibrary.simpleMessage("送信"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("メッセージを送信"),
        "serverConnectionFailed":
            MessageLookupByLibrary.simpleMessage("サーバー接続に失敗しました。"),
        "serviceIsStillStarting":
            MessageLookupByLibrary.simpleMessage("サービスはまだ起動中です。"),
        "sessionExpiredPleaseLogInAgain": MessageLookupByLibrary.simpleMessage(
            "セッションの有効期限が切れました。もう一度ログインしてください"),
        "setNow": MessageLookupByLibrary.simpleMessage("今すぐ設定"),
        "settings": MessageLookupByLibrary.simpleMessage("設定"),
        "share": MessageLookupByLibrary.simpleMessage("共有"),
        "signIn": MessageLookupByLibrary.simpleMessage("サインイン"),
        "signOut": MessageLookupByLibrary.simpleMessage("サインアウト"),
        "signUp": MessageLookupByLibrary.simpleMessage("サインアップ"),
        "someoneMe": MessageLookupByLibrary.simpleMessage("誰かが @私"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "申し訳ありません。現在満員です。明日もう一度お試しください。"),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking": MessageLookupByLibrary.simpleMessage("話し始める"),
        "submit": MessageLookupByLibrary.simpleMessage("送信"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry":
            MessageLookupByLibrary.simpleMessage("Wi-Fi/モバイルデータを切り替えて再試行"),
        "system": MessageLookupByLibrary.simpleMessage("システム"),
        "systemBusyPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("システムが混雑しています。もう一度お試しください"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("写真を撮る"),
        "taskStoppedManually":
            MessageLookupByLibrary.simpleMessage("タスクが手動で停止されました"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService": MessageLookupByLibrary.simpleMessage("利用規約"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage("2. ダイヤモンド数は毎月1日に更新されます。"),
        "theGroupIsCurrentlyInACall":
            MessageLookupByLibrary.simpleMessage("グループは現在通話中です..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage("インターフェースで使用される言語"),
        "thePhoneNumberIsInvalid":
            MessageLookupByLibrary.simpleMessage("電話番号が無効です"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "このアプリはプロフィール写真を撮るためにカメラへのアクセスが必要です。"),
        "thisIsntHelpful": MessageLookupByLibrary.simpleMessage("これは役に立ちません"),
        "thisSlideIsBeingEdited":
            MessageLookupByLibrary.simpleMessage("このスライドは編集中です。"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "認識や操作、アバター設定のために画像を送信できるように、Agnesがカメラにアクセスすることを許可してください"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "認識や操作、アバター設定のために画像を送信できるように、Agnesが写真ライブラリにアクセスすることを許可してください"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "作成した画像を保存するには、Agnesが写真ライブラリに画像を保存することを許可してください"),
        "transcribing": MessageLookupByLibrary.simpleMessage("書き起こし中…"),
        "transcriptionEnabledLoadingData":
            MessageLookupByLibrary.simpleMessage("書き起こしが有効になりました。データを読み込み中…"),
        "tryOneOfThese":
            MessageLookupByLibrary.simpleMessage("次のいずれかを試してください："),
        "twoPasswordsAreInconsistent":
            MessageLookupByLibrary.simpleMessage("2つのパスワードが一致しません"),
        "update": MessageLookupByLibrary.simpleMessage("アップデート"),
        "updateFailedPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage("更新に失敗しました。後で再試行してください。"),
        "updatePassword": MessageLookupByLibrary.simpleMessage("パスワードを更新"),
        "updatePasswordSuccessfully":
            MessageLookupByLibrary.simpleMessage("パスワードが正常に更新されました"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage(
                "続行するにはプランをアップグレードしてください: アップグレード"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage("文字、数字、記号を含む6文字以上を使用"),
        "userAccess": MessageLookupByLibrary.simpleMessage("ユーザーアクセス"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("ユーザー名"),
        "usersWithAccess": MessageLookupByLibrary.simpleMessage("アクセス権を持つユーザー"),
        "usingTool": MessageLookupByLibrary.simpleMessage("ツールを使用"),
        "verificationCode": MessageLookupByLibrary.simpleMessage("認証コード"),
        "verificationCodeCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("確認コードを空にすることはできません"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("表示"),
        "viewAllComments": MessageLookupByLibrary.simpleMessage("すべてのコメントを見る"),
        "viewer": MessageLookupByLibrary.simpleMessage("閲覧者"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "音声通話の作成に失敗しました。ネットワークが不安定な可能性があります。再試行してください。"),
        "voiceCallEnded": MessageLookupByLibrary.simpleMessage("音声通話が終了しました"),
        "waiting": MessageLookupByLibrary.simpleMessage("待機中"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "ダウンロードした画像や動画をフォトライブラリに保存するためのアクセスが必要です。"),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage("音声操作のためにマイクにアクセスする必要があります"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "プロフィール写真を選ぶためにフォトライブラリへのアクセスが必要です。"),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage(
                "写真を選択するためにフォトライブラリにアクセスする必要があります"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "音声を認識してテキストに変換し、操作のために使用する必要があります"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage("画像をフォトライブラリに保存する必要があります"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "音声操作のためにBluetoothでヘッドセットに接続する必要があります"),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage("写真を撮るためにカメラを使用する必要があります"),
        "welcomeToAgnes": MessageLookupByLibrary.simpleMessage("Agnesへようこそ"),
        "writeSomething": MessageLookupByLibrary.simpleMessage("何かを書く"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("あなた"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage("実行、編集、または削除できます。どうしますか？"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "手動でスライドを編集するか、Agnes と会話して編集できます。"),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage("コンテンツを選んでフィードバックを共有できます。"),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "AGNES がフローティングウィンドウを使用する権限がないため、ビデオ通話を最小化できません。"),
        "youCreatedTheGroup":
            MessageLookupByLibrary.simpleMessage("グループを作成しました。"),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage("グループチャットから削除されました"),
        "youJoinedTheGroup":
            MessageLookupByLibrary.simpleMessage("グループに参加しました。"),
        "yourCurrentPlan": MessageLookupByLibrary.simpleMessage("現在のプラン"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "お使いの電話は Agnes にフローティングウィンドウの権限を許可していません。Agnes はバックグラウンドでタスクを実行できません。"),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage("リサーチタスクのリクエスト上限に達しました。"),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage("スライドタスクのリクエスト上限に達しました。"),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage("このタスクの上限に達しました。"),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd": MessageLookupByLibrary.simpleMessage("最後まで到達しました")
      };
}
