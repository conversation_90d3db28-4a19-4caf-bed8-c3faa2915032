// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  static String m0(termsOfService, privacyPolicy) =>
      "登录即表示您同意\n${termsOfService}和${privacyPolicy}。";

  static String m1(value) => "研究 ${value} 秒";

  static String m2(title) => "抱歉，您的${title}任务失败。请稍后重试。";

  static String m3(value) => "支持上传最多 ${value} 张图片。";

  static String m4(remaining_count) =>
      "已按要求删除任务。当前待处理任务数：${remaining_count}。要显示所有待处理任务吗？";

  static String m5(total) => "当前有 ${total} 个待处理任务：";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "用户 ${user_name} 额度不足，幻灯片剩余：${slides_remaining_count}，幻灯片所需额度：${slides_plan_count}，调研剩余：${research_remaining_count}，调研所需额度：${research_plan_count}。您可选择额度充足的任务执行。";

  static String m7(xxx) => "验证码已发送到 ${xxx}@email.com";

  static String m8(version) => "版本 ${version} 已可用";

  static String m9(x) => "${x} 位成员在语音中…";

  static String m10(xxx) => "${xxx} 修改了群组名称";

  static String m11(xxx) => "${xxx} 邀请你协作编辑《${xxx}》。";

  static String m12(xxx) => "${xxx} 邀请你协作审阅《${xxx}》。";

  static String m13(xxx) => "${xxx}已开始语音通话";

  static String m14(xxx, xyz) => "${xxx} 邀请你加入 “${xyz}” 群组通话。";

  static String m15(title) => "您的${title}任务已成功完成！";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond": MessageLookupByLibrary.simpleMessage("关于钻石"),
        "accept": MessageLookupByLibrary.simpleMessage("接受"),
        "account": MessageLookupByLibrary.simpleMessage("账号"),
        "accountDeletionSuccessful":
            MessageLookupByLibrary.simpleMessage("账号删除成功"),
        "accountNotCreatedYet": MessageLookupByLibrary.simpleMessage("尚未创建账号"),
        "accountSignoutSuccessful":
            MessageLookupByLibrary.simpleMessage("账号退出成功"),
        "activeAccount": MessageLookupByLibrary.simpleMessage("活跃账号"),
        "add": MessageLookupByLibrary.simpleMessage("添加"),
        "addComment": MessageLookupByLibrary.simpleMessage("添加评论"),
        "addPeopleToChat": MessageLookupByLibrary.simpleMessage("添加人员到聊天"),
        "addToChat": MessageLookupByLibrary.simpleMessage("添加到聊天"),
        "addUserEmailAndEnterToInvite":
            MessageLookupByLibrary.simpleMessage("输入用户邮箱并按回车邀请"),
        "addedToGroupSuccessfully":
            MessageLookupByLibrary.simpleMessage("添加到群成功"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage("Agnes 可能会出错，请检查重要信息。"),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage("@Agnes 获取摘要、报告或幻灯片。"),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn": MessageLookupByLibrary.simpleMessage("同意并登录"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("AI设计"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("AI 幻灯片"),
        "album": MessageLookupByLibrary.simpleMessage("相册"),
        "allMembers": MessageLookupByLibrary.simpleMessage("所有成员"),
        "answerCall": MessageLookupByLibrary.simpleMessage("接听电话"),
        "appUpdate": MessageLookupByLibrary.simpleMessage("应用更新"),
        "archived": MessageLookupByLibrary.simpleMessage("已归档"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage("确定要删除你的账号吗？"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage("您确定要登出账号吗？"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("向 Agnes 提问"),
        "askAgnesToEditSlides":
            MessageLookupByLibrary.simpleMessage("让 Agnes 编辑幻灯片"),
        "assignATaskToBegin": MessageLookupByLibrary.simpleMessage("分配一个任务以开始"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage("至少 6 个字符，包含字母、数字和符号"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "当前尚未达成有共识的待办任务。如有需要，可进一步讨论，有问题也可随时提出！"),
        "avatar": MessageLookupByLibrary.simpleMessage("头像"),
        "back": MessageLookupByLibrary.simpleMessage("返回"),
        "bookmarkRemoved": MessageLookupByLibrary.simpleMessage("取消收藏"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("已收藏"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined": MessageLookupByLibrary.simpleMessage("通话已拒绝"),
        "cancel": MessageLookupByLibrary.simpleMessage("取消"),
        "canvas": MessageLookupByLibrary.simpleMessage("画布"),
        "chapter": MessageLookupByLibrary.simpleMessage("章节"),
        "chat": MessageLookupByLibrary.simpleMessage("聊天"),
        "chatSettings": MessageLookupByLibrary.simpleMessage("聊天设置"),
        "chooseImage": MessageLookupByLibrary.simpleMessage("选择图片"),
        "close": MessageLookupByLibrary.simpleMessage("关闭"),
        "code": MessageLookupByLibrary.simpleMessage("代码"),
        "collect": MessageLookupByLibrary.simpleMessage("收集"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("即将上线"),
        "comment": MessageLookupByLibrary.simpleMessage("评论"),
        "commenter": MessageLookupByLibrary.simpleMessage("评论者"),
        "commentsTasks": MessageLookupByLibrary.simpleMessage("评论任务"),
        "confirm": MessageLookupByLibrary.simpleMessage("确认"),
        "confirmDelete": MessageLookupByLibrary.simpleMessage("确认删除"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("确认密码"),
        "confirmPasswordCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("确认密码不能为空"),
        "confirmSignOut": MessageLookupByLibrary.simpleMessage("确认登出"),
        "connect": MessageLookupByLibrary.simpleMessage("连接"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage("连接 AI 头戴设备，可随时与 Agnes 聊天"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage("连接Agnes VibePods，随时聊天。"),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage("与好友连接以开始群聊"),
        "connected": MessageLookupByLibrary.simpleMessage("已连接"),
        "connectionFailed": MessageLookupByLibrary.simpleMessage("连接失败"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage("内容已复制——快去粘贴到你喜欢的平台吧！"),
        "continueWithApple":
            MessageLookupByLibrary.simpleMessage("使用 Apple 登录"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("使用 Google 登录"),
        "continueWithOtherWays":
            MessageLookupByLibrary.simpleMessage("以其他方式继续"),
        "continueWord": MessageLookupByLibrary.simpleMessage("继续"),
        "copied": MessageLookupByLibrary.simpleMessage("已复制"),
        "copy": MessageLookupByLibrary.simpleMessage("复制"),
        "cost": MessageLookupByLibrary.simpleMessage("消费"),
        "cover": MessageLookupByLibrary.simpleMessage("封面"),
        "createANewTaskToGetStarted":
            MessageLookupByLibrary.simpleMessage("创建新任务以开始"),
        "createAnAccountToSignUpForAgnes":
            MessageLookupByLibrary.simpleMessage("创建账号以注册 Agnes"),
        "createGroupChat": MessageLookupByLibrary.simpleMessage("创建群聊"),
        "currentlyInACall": MessageLookupByLibrary.simpleMessage("当前正在通话..."),
        "deepResearch": MessageLookupByLibrary.simpleMessage("深度研究"),
        "deepresearch": MessageLookupByLibrary.simpleMessage("深度研究"),
        "delete": MessageLookupByLibrary.simpleMessage("删除"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("删除账号"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("删除聊天"),
        "doYouWantToExecuteTheseTasks":
            MessageLookupByLibrary.simpleMessage("你要执行这些任务吗？"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(" 似乎不是Agnes用户。请输入其邮箱以发送邀请。"),
        "done": MessageLookupByLibrary.simpleMessage("完成"),
        "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage("没有账号？"),
        "downloading": MessageLookupByLibrary.simpleMessage("下载中"),
        "eachNewTaskConsumesOneDiamond":
            MessageLookupByLibrary.simpleMessage("1. 每个新任务消耗一颗钻石。"),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage("3. 每个用户每月获得30颗钻石。"),
        "edit": MessageLookupByLibrary.simpleMessage("编辑"),
        "editGroupName": MessageLookupByLibrary.simpleMessage("编辑群组名称"),
        "editor": MessageLookupByLibrary.simpleMessage("编辑者"),
        "email": MessageLookupByLibrary.simpleMessage("邮箱"),
        "emailCannotBeEmpty": MessageLookupByLibrary.simpleMessage("邮箱不能为空"),
        "emailFormatIncorrect": MessageLookupByLibrary.simpleMessage("邮箱格式不正确"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage("通过扩展访问提升生产力和创造力"),
        "enterConfirmPassword": MessageLookupByLibrary.simpleMessage("输入确认密码"),
        "enterGroupName": MessageLookupByLibrary.simpleMessage("输入群组名称"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("输入密码"),
        "enterVerificationCode": MessageLookupByLibrary.simpleMessage("输入验证码"),
        "export": MessageLookupByLibrary.simpleMessage("导出"),
        "file": MessageLookupByLibrary.simpleMessage("文件"),
        "floatingWindowNotEnabled":
            MessageLookupByLibrary.simpleMessage("未启用悬浮窗"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage("想获得完整体验，请使用PC端"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage("忘记密码"),
        "general": MessageLookupByLibrary.simpleMessage("通用"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("获取 Agnes AI Plus"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("获取 Agnes AI Pro"),
        "giveMeATaskToWorkOn":
            MessageLookupByLibrary.simpleMessage("给我一个任务来执行…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("前往商店"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage("明白！我正在处理任务，请稍等。"),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("创建群成功"),
        "groupFiles": MessageLookupByLibrary.simpleMessage("群组文件"),
        "groupName": MessageLookupByLibrary.simpleMessage("群名称"),
        "groupNameOptional": MessageLookupByLibrary.simpleMessage("群名称（可选）"),
        "harmful": MessageLookupByLibrary.simpleMessage("有害"),
        "helpUsImprove": MessageLookupByLibrary.simpleMessage("帮助我们改进"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage("我可以帮你搜索、写作和回答"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage("如果你接听来电，当前通话将会结束。"),
        "imAgnes": MessageLookupByLibrary.simpleMessage("我是 Agnes"),
        "inAVoiceCall": MessageLookupByLibrary.simpleMessage("语音通话中"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("不准确"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage("应用内购买暂不可用，请稍后重试"),
        "interfaceLanguage": MessageLookupByLibrary.simpleMessage("界面语言"),
        "invite": MessageLookupByLibrary.simpleMessage("邀请"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage("按用户名、邮箱或手机号邀请"),
        "inviteFriends": MessageLookupByLibrary.simpleMessage("邀请好友"),
        "inviteOthersToCollaborate":
            MessageLookupByLibrary.simpleMessage("邀请他人协作"),
        "inviteToCollaborate": MessageLookupByLibrary.simpleMessage("邀请协作"),
        "isEditing": MessageLookupByLibrary.simpleMessage("正在编辑"),
        "join": MessageLookupByLibrary.simpleMessage("加入"),
        "knowMore": MessageLookupByLibrary.simpleMessage("了解更多"),
        "language": MessageLookupByLibrary.simpleMessage("英语"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage("让Agnes帮你创建更好的分享格式"),
        "letAgnesResolve": MessageLookupByLibrary.simpleMessage("让 Agnes 处理"),
        "linkCopied": MessageLookupByLibrary.simpleMessage("链接已复制"),
        "listening": MessageLookupByLibrary.simpleMessage("正在监听"),
        "loading": MessageLookupByLibrary.simpleMessage("加载中"),
        "makeMeAShareableImagePost":
            MessageLookupByLibrary.simpleMessage("帮我生成可分享的图片帖子"),
        "makeMeAnHtmlWebPage":
            MessageLookupByLibrary.simpleMessage("帮我生成一个 HTML 网页"),
        "manageMembers": MessageLookupByLibrary.simpleMessage("管理成员"),
        "manuallyEndTheTask": MessageLookupByLibrary.simpleMessage("手动结束任务"),
        "markAsResolved": MessageLookupByLibrary.simpleMessage("标记为已解决"),
        "messageCopied": MessageLookupByLibrary.simpleMessage("消息已复制。"),
        "multipleChoices": MessageLookupByLibrary.simpleMessage("多选"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("我的收藏"),
        "narrate": MessageLookupByLibrary.simpleMessage("旁白"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("网络连接失败，请稍后重试"),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("网络连接已中断"),
        "networkUnavailable": MessageLookupByLibrary.simpleMessage("网络不可用"),
        "newPassword": MessageLookupByLibrary.simpleMessage("新密码"),
        "newProject": MessageLookupByLibrary.simpleMessage("新建项目"),
        "newTask": MessageLookupByLibrary.simpleMessage("新任务"),
        "newTitle": MessageLookupByLibrary.simpleMessage("新标题"),
        "noMoreFiles": MessageLookupByLibrary.simpleMessage("没有更多文件"),
        "noMoreMessage": MessageLookupByLibrary.simpleMessage("没有更多消息..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage("未发现待处理任务。所有任务均已完成！"),
        "notAvailableForPurchase": MessageLookupByLibrary.simpleMessage("无法购买"),
        "notConnected": MessageLookupByLibrary.simpleMessage("未连接"),
        "notNow": MessageLookupByLibrary.simpleMessage("暂不"),
        "notifications": MessageLookupByLibrary.simpleMessage("通知"),
        "notifyUserViaEmail": MessageLookupByLibrary.simpleMessage("通过邮件通知用户"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "好的，我注意到您没有所需权限。请联系拥有者或编辑进行操作。"),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage("好的，我来总结一下共识任务："),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage("删除后，群聊记录将被清除。"),
        "operationSuccessful": MessageLookupByLibrary.simpleMessage("操作成功！"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage("或者告诉我你是否有问题"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage("群组名称更改后，其他成员将收到通知。"),
        "outOfDate": MessageLookupByLibrary.simpleMessage("已过期"),
        "owner": MessageLookupByLibrary.simpleMessage("拥有者"),
        "pages": MessageLookupByLibrary.simpleMessage("页面"),
        "passwordCannotBeEmpty": MessageLookupByLibrary.simpleMessage("密码不能为空"),
        "pending": MessageLookupByLibrary.simpleMessage("待处理"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage("未授予后台执行任务的权限"),
        "phone": MessageLookupByLibrary.simpleMessage("电话"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("手机号"),
        "phoneNumberCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("手机号不能为空"),
        "photo": MessageLookupByLibrary.simpleMessage("照片"),
        "pleaseSelectASlideToEdit":
            MessageLookupByLibrary.simpleMessage("请选择要编辑的幻灯片。"),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage("请更新到最新版本以获取新功能和改进"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage("请等待Agnes完成任务"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage("请等待下载完成"),
        "presenter": MessageLookupByLibrary.simpleMessage("演讲者"),
        "preview": MessageLookupByLibrary.simpleMessage("预览"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("隐私政策"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage("提供关于此回答的额外反馈"),
        "quote": MessageLookupByLibrary.simpleMessage("引用"),
        "quotedMessageHasExpired":
            MessageLookupByLibrary.simpleMessage("引用消息已失效"),
        "reconnect": MessageLookupByLibrary.simpleMessage("重新连接"),
        "reject": MessageLookupByLibrary.simpleMessage("拒绝"),
        "remove": MessageLookupByLibrary.simpleMessage("移除"),
        "removedSuccessfully": MessageLookupByLibrary.simpleMessage("取消成功"),
        "rename": MessageLookupByLibrary.simpleMessage("重命名"),
        "reopenTheApp": MessageLookupByLibrary.simpleMessage("重新打开应用"),
        "requestTimedOutPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("请求超时，请稍后重试"),
        "research": MessageLookupByLibrary.simpleMessage("研究"),
        "researchForZdSeconds": m1,
        "resetPassword": MessageLookupByLibrary.simpleMessage("重置密码"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage("重置密码"),
        "role": MessageLookupByLibrary.simpleMessage("角色"),
        "save": MessageLookupByLibrary.simpleMessage("保存"),
        "saved": MessageLookupByLibrary.simpleMessage("已保存"),
        "saving": MessageLookupByLibrary.simpleMessage("正在保存…"),
        "search": MessageLookupByLibrary.simpleMessage("搜索"),
        "searchByNameEmailOrPhone":
            MessageLookupByLibrary.simpleMessage("按姓名、邮箱或手机号搜索"),
        "searchForACountry": MessageLookupByLibrary.simpleMessage("搜索国家"),
        "seconds": MessageLookupByLibrary.simpleMessage("秒"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage("似乎已断开与互联网的连接"),
        "selectMembers": MessageLookupByLibrary.simpleMessage("选择成员"),
        "send": MessageLookupByLibrary.simpleMessage("发送"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("发送消息"),
        "serverConnectionFailed":
            MessageLookupByLibrary.simpleMessage("服务器连接失败。"),
        "serviceIsStillStarting":
            MessageLookupByLibrary.simpleMessage("服务尚在启动中。"),
        "sessionExpiredPleaseLogInAgain":
            MessageLookupByLibrary.simpleMessage("登录已失效，请重新登录"),
        "setNow": MessageLookupByLibrary.simpleMessage("立即设置"),
        "settings": MessageLookupByLibrary.simpleMessage("设置"),
        "share": MessageLookupByLibrary.simpleMessage("分享"),
        "signIn": MessageLookupByLibrary.simpleMessage("登录"),
        "signOut": MessageLookupByLibrary.simpleMessage("退出登录"),
        "signUp": MessageLookupByLibrary.simpleMessage("注册"),
        "someoneMe": MessageLookupByLibrary.simpleMessage("有人 @我"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage("抱歉，当前容量已满，请明天再试。"),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking": MessageLookupByLibrary.simpleMessage("开始说话"),
        "submit": MessageLookupByLibrary.simpleMessage("提交"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry":
            MessageLookupByLibrary.simpleMessage("切换 Wi-Fi/移动数据后重试"),
        "system": MessageLookupByLibrary.simpleMessage("系统"),
        "systemBusyPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("系统繁忙，请重试"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("拍照"),
        "taskStoppedManually": MessageLookupByLibrary.simpleMessage("任务已被手动停止"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService": MessageLookupByLibrary.simpleMessage("服务条款"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage("2. 钻石数量在每月1日更新。"),
        "theGroupIsCurrentlyInACall":
            MessageLookupByLibrary.simpleMessage("群组当前正在通话..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage("界面使用的语言"),
        "thePhoneNumberIsInvalid":
            MessageLookupByLibrary.simpleMessage("电话号码无效"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage("本应用需要使用相机，以便您拍摄头像照片。"),
        "thisIsntHelpful": MessageLookupByLibrary.simpleMessage("这没有帮助"),
        "thisSlideIsBeingEdited":
            MessageLookupByLibrary.simpleMessage("此幻灯片正在编辑。"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "为了让您可以发送图片进行识别、互动或设置头像，请允许 Agnes 访问您的相机"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "为了让您可以发送图片进行识别、互动或设置头像，请允许 Agnes 访问您的相册"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "为了保存生成的图片，请允许 Agnes 将图片保存到您的相册"),
        "transcribing": MessageLookupByLibrary.simpleMessage("转录中…"),
        "transcriptionEnabledLoadingData":
            MessageLookupByLibrary.simpleMessage("已启用转录。正在加载数据…"),
        "tryOneOfThese": MessageLookupByLibrary.simpleMessage("试试以下方法："),
        "twoPasswordsAreInconsistent":
            MessageLookupByLibrary.simpleMessage("两次输入的密码不一致"),
        "update": MessageLookupByLibrary.simpleMessage("更新"),
        "updateFailedPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage("更新失败，请稍后重试。"),
        "updatePassword": MessageLookupByLibrary.simpleMessage("更新密码"),
        "updatePasswordSuccessfully":
            MessageLookupByLibrary.simpleMessage("密码更新成功"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage("升级您的方案以继续使用：升级"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage("使用至少6个字符，包含字母、数字和符号"),
        "userAccess": MessageLookupByLibrary.simpleMessage("用户访问"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("用户名"),
        "usersWithAccess": MessageLookupByLibrary.simpleMessage("有权限的用户"),
        "usingTool": MessageLookupByLibrary.simpleMessage("使用工具"),
        "verificationCode": MessageLookupByLibrary.simpleMessage("验证码"),
        "verificationCodeCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("验证码不能为空"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("查看"),
        "viewAllComments": MessageLookupByLibrary.simpleMessage("查看所有评论"),
        "viewer": MessageLookupByLibrary.simpleMessage("查看者"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage("语音通话创建失败，可能由于网络不稳定。请重试。"),
        "voiceCallEnded": MessageLookupByLibrary.simpleMessage("语音通话已结束"),
        "waiting": MessageLookupByLibrary.simpleMessage("等待中"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage("我们需要访问您的相册，以保存下载的图片或视频。"),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage("我们需要访问麦克风以进行语音交互"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage("我们需要访问您的相册，以便您选择头像照片。"),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage("我们需要访问你的相册以选择图片"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage("我们需要识别您的语音以转换为文字进行交互"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage("我们需要将图片保存到你的相册"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage("我们需要使用蓝牙连接耳机以进行语音交互"),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage("我们需要使用你的相机拍照"),
        "welcomeToAgnes": MessageLookupByLibrary.simpleMessage("欢迎使用 Agnes"),
        "writeSomething": MessageLookupByLibrary.simpleMessage("写点东西"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("你"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage("您可以执行、修改或删除，想要如何操作？"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "你可以手动编辑幻灯片，也可以通过与 Agnes 对话来编辑。"),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage("你可以选择内容并分享反馈。"),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "由于 AGNES 没有使用悬浮窗的权限，无法最小化视频通话。"),
        "youCreatedTheGroup": MessageLookupByLibrary.simpleMessage("你创建了群组。"),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage("您已被移出群聊"),
        "youJoinedTheGroup": MessageLookupByLibrary.simpleMessage("你加入了群组。"),
        "yourCurrentPlan": MessageLookupByLibrary.simpleMessage("您当前的方案"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "您的手机尚未授权 Agnes 使用悬浮窗权限。Agnes 将无法继续在后台为您执行任务。"),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage("您的研究任务请求已达上限。"),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage("您的幻灯片任务请求已达上限。"),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage("您的任务次数已用完。"),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd": MessageLookupByLibrary.simpleMessage("已经到底部")
      };
}
