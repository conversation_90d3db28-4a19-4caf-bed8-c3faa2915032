// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a pt locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'pt';

  static String m0(termsOfService, privacyPolicy) =>
      "Ao entrar, você concorda com os\n${termsOfService} e a ${privacyPolicy}.";

  static String m1(value) => "Pesquisa por ${value} segundos";

  static String m2(title) =>
      "Desculpe, sua tarefa ${title} falhou. Por favor, tente novamente mais tarde.";

  static String m3(value) => "Suporta upload de até ${value} imagens.";

  static String m4(remaining_count) =>
      "Tarefas removidas conforme solicitado. Tarefas pendentes atuais: ${remaining_count}. Deseja que eu mostre todas as tarefas pendentes?";

  static String m5(total) => "Atualmente existem ${total} tarefas pendentes:";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "O usuário ${user_name} não tem quota suficiente, slides restantes: ${slides_remaining_count}, quota requerida para slides: ${slides_plan_count}, pesquisa restante: ${research_remaining_count}, quota requerida para pesquisa: ${research_plan_count}. Você pode escolher tarefas com quota suficiente para executar.";

  static String m7(xxx) =>
      "Código de verificação enviado para ${xxx}@email.com";

  static String m8(version) => "A versão ${version} já está disponível";

  static String m9(x) => "${x} membros em voz...";

  static String m10(xxx) => "${xxx} alterou o nome do grupo";

  static String m11(xxx) =>
      "${xxx} convidou você para colaborar na edição de \"${xxx}\".";

  static String m12(xxx) =>
      "${xxx} convidou você para colaborar na revisão de \"${xxx}\".";

  static String m13(xxx) => "${xxx} iniciou uma chamada de voz";

  static String m14(xxx, xyz) =>
      "${xxx} convida você a participar da chamada em grupo \"${xyz}\".";

  static String m15(title) => "Sua tarefa ${title} foi concluída com sucesso!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond": MessageLookupByLibrary.simpleMessage("Sobre Diamantes"),
        "accept": MessageLookupByLibrary.simpleMessage("Aceitar"),
        "account": MessageLookupByLibrary.simpleMessage("Conta"),
        "accountDeletionSuccessful": MessageLookupByLibrary.simpleMessage(
            "Exclusão da conta bem-sucedida"),
        "accountNotCreatedYet":
            MessageLookupByLibrary.simpleMessage("Conta ainda não criada"),
        "accountSignoutSuccessful": MessageLookupByLibrary.simpleMessage(
            "Logout da conta bem-sucedido"),
        "activeAccount": MessageLookupByLibrary.simpleMessage("Conta ativa"),
        "add": MessageLookupByLibrary.simpleMessage("Adicionar"),
        "addComment":
            MessageLookupByLibrary.simpleMessage("Adicionar comentário"),
        "addPeopleToChat":
            MessageLookupByLibrary.simpleMessage("Adicionar pessoas ao Chat"),
        "addToChat": MessageLookupByLibrary.simpleMessage("Adicionar ao Chat"),
        "addUserEmailAndEnterToInvite": MessageLookupByLibrary.simpleMessage(
            "Adicione o e-mail do usuário e pressione \"enter\" para convidar"),
        "addedToGroupSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Adicionado ao grupo com sucesso"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Agnes pode cometer erros. Verifique as informações importantes."),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage(
                "@Agnes para resumos, relatórios ou slides."),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn":
            MessageLookupByLibrary.simpleMessage("Concordar e entrar"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("Design de IA"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("Slides de IA"),
        "album": MessageLookupByLibrary.simpleMessage("Álbum"),
        "allMembers": MessageLookupByLibrary.simpleMessage("Todos os membros"),
        "answerCall": MessageLookupByLibrary.simpleMessage("Atender chamada"),
        "appUpdate": MessageLookupByLibrary.simpleMessage("Atualização do App"),
        "archived": MessageLookupByLibrary.simpleMessage("Arquivado"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Tem certeza de que deseja excluir sua conta?"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Tem certeza de que deseja sair da sua conta?"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Perguntar à Agnes"),
        "askAgnesToEditSlides": MessageLookupByLibrary.simpleMessage(
            "Peça à Agnes para editar os slides"),
        "assignATaskToBegin": MessageLookupByLibrary.simpleMessage(
            "Atribua uma tarefa para começar"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Pelo menos seis caracteres com letras, números e símbolos"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "Atualmente, não há consenso sobre as tarefas a serem feitas. Sinta-se à vontade para discutir mais e fazer perguntas se houver!"),
        "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
        "back": MessageLookupByLibrary.simpleMessage("Voltar"),
        "bookmarkRemoved":
            MessageLookupByLibrary.simpleMessage("Marcador removido"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("Marcado"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined":
            MessageLookupByLibrary.simpleMessage("Chamada recusada"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
        "canvas": MessageLookupByLibrary.simpleMessage("Tela"),
        "chapter": MessageLookupByLibrary.simpleMessage("Capítulo"),
        "chat": MessageLookupByLibrary.simpleMessage("Chat"),
        "chatSettings":
            MessageLookupByLibrary.simpleMessage("Configurações do Chat"),
        "chooseImage": MessageLookupByLibrary.simpleMessage("Escolher imagem"),
        "close": MessageLookupByLibrary.simpleMessage("Fechar"),
        "code": MessageLookupByLibrary.simpleMessage("Código"),
        "collect": MessageLookupByLibrary.simpleMessage("coletar"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("Em Breve"),
        "comment": MessageLookupByLibrary.simpleMessage("Comentar"),
        "commenter": MessageLookupByLibrary.simpleMessage("Comentarista"),
        "commentsTasks":
            MessageLookupByLibrary.simpleMessage("Tarefas de Comentários"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirmar"),
        "confirmDelete":
            MessageLookupByLibrary.simpleMessage("Confirmar exclusão"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirmar senha"),
        "confirmPasswordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "A confirmação da senha não pode estar vazia"),
        "confirmSignOut":
            MessageLookupByLibrary.simpleMessage("Confirmar saída"),
        "connect": MessageLookupByLibrary.simpleMessage("Conectar"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage(
                "Conecte o headset de IA e converse com Agnes a qualquer momento"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage(
                "Conecte-se aos Agnes VibePods e converse a qualquer momento."),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Conectar com amigos para iniciar um chat em grupo"),
        "connected": MessageLookupByLibrary.simpleMessage("Conectado"),
        "connectionFailed":
            MessageLookupByLibrary.simpleMessage("Conexão falhou"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "Conteúdo copiado — cole na sua plataforma favorita!"),
        "continueWithApple":
            MessageLookupByLibrary.simpleMessage("Continuar com Apple"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("Continuar com Google"),
        "continueWithOtherWays": MessageLookupByLibrary.simpleMessage(
            "Continuar de outras maneiras"),
        "continueWord": MessageLookupByLibrary.simpleMessage("Continuar"),
        "copied": MessageLookupByLibrary.simpleMessage("Copiado"),
        "copy": MessageLookupByLibrary.simpleMessage("Copiar"),
        "cost": MessageLookupByLibrary.simpleMessage("costo"),
        "cover": MessageLookupByLibrary.simpleMessage("Capa"),
        "createANewTaskToGetStarted": MessageLookupByLibrary.simpleMessage(
            "Criar uma nova tarefa para começar"),
        "createAnAccountToSignUpForAgnes": MessageLookupByLibrary.simpleMessage(
            "Crie uma conta para se inscrever no Agnes"),
        "createGroupChat":
            MessageLookupByLibrary.simpleMessage("Criar bate-papo em grupo"),
        "currentlyInACall": MessageLookupByLibrary.simpleMessage(
            "Atualmente em uma chamada..."),
        "deepResearch":
            MessageLookupByLibrary.simpleMessage("Pesquisa aprofundada"),
        "deepresearch":
            MessageLookupByLibrary.simpleMessage("Pesquisa Profunda"),
        "delete": MessageLookupByLibrary.simpleMessage("Excluir"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("Excluir conta"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Excluir chat"),
        "doYouWantToExecuteTheseTasks": MessageLookupByLibrary.simpleMessage(
            "Você quer executar essas tarefas?"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                " não parece ser um usuário do Agnes. Digite o e-mail deles para enviar um convite"),
        "done": MessageLookupByLibrary.simpleMessage("Pronto"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Não tem conta?"),
        "downloading": MessageLookupByLibrary.simpleMessage("A descarregar"),
        "eachNewTaskConsumesOneDiamond": MessageLookupByLibrary.simpleMessage(
            "1. Cada nova tarefa consome um diamante."),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. Cada usuário recebe 30 diamantes por mês."),
        "edit": MessageLookupByLibrary.simpleMessage("Editar"),
        "editGroupName":
            MessageLookupByLibrary.simpleMessage("Editar nome do grupo"),
        "editor": MessageLookupByLibrary.simpleMessage("Editor"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "emailCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "O e-mail não pode estar vazio"),
        "emailFormatIncorrect": MessageLookupByLibrary.simpleMessage(
            "O formato do e-mail está incorreto"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage(
                "Aumente a produtividade e criatividade através de acesso estendido"),
        "enterConfirmPassword": MessageLookupByLibrary.simpleMessage(
            "Digite a senha de confirmação"),
        "enterGroupName":
            MessageLookupByLibrary.simpleMessage("Digite o nome do grupo"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Digite a senha"),
        "enterVerificationCode": MessageLookupByLibrary.simpleMessage(
            "Digite o código de verificação"),
        "export": MessageLookupByLibrary.simpleMessage("Exportar"),
        "file": MessageLookupByLibrary.simpleMessage("Arquivo"),
        "floatingWindowNotEnabled": MessageLookupByLibrary.simpleMessage(
            "Janela flutuante não ativada"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage(
                "Para uma experiência completa, use a versão para PC"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Esqueceu a senha"),
        "general": MessageLookupByLibrary.simpleMessage("Geral"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Obter Agnes AI Plus"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Obter Agnes AI Pro"),
        "giveMeATaskToWorkOn": MessageLookupByLibrary.simpleMessage(
            "Dê-me uma tarefa para trabalhar…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("Ir para a Loja"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage(
                "Entendido! Estou trabalhando na tarefa. Por favor, aguarde um momento."),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Grupo criado com sucesso"),
        "groupFiles": MessageLookupByLibrary.simpleMessage("Arquivos do grupo"),
        "groupName": MessageLookupByLibrary.simpleMessage("Nome do Grupo"),
        "groupNameOptional":
            MessageLookupByLibrary.simpleMessage("Nome do Grupo (Opcional)"),
        "harmful": MessageLookupByLibrary.simpleMessage("Prejudicial"),
        "helpUsImprove":
            MessageLookupByLibrary.simpleMessage("Ajude-nos a melhorar"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage(
                "Eu ajudo com pesquisas, redação e respostas"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage(
                "Se você atender a chamada recebida, sua chamada atual será encerrada."),
        "imAgnes": MessageLookupByLibrary.simpleMessage("Eu sou a Agnes"),
        "inAVoiceCall":
            MessageLookupByLibrary.simpleMessage("Em uma chamada de voz"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("Inaccurado"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Compras no aplicativo estão indisponíveis no momento, tente novamente mais tarde"),
        "interfaceLanguage":
            MessageLookupByLibrary.simpleMessage("Idioma da interface"),
        "invite": MessageLookupByLibrary.simpleMessage("Convidar"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage(
                "Convidar por nome de usuário, email ou número de telefone"),
        "inviteFriends":
            MessageLookupByLibrary.simpleMessage("Convidar amigos"),
        "inviteOthersToCollaborate": MessageLookupByLibrary.simpleMessage(
            "Convidar outros para colaborar"),
        "inviteToCollaborate":
            MessageLookupByLibrary.simpleMessage("Convidar para colaborar"),
        "isEditing": MessageLookupByLibrary.simpleMessage("está editando"),
        "join": MessageLookupByLibrary.simpleMessage("Participar"),
        "knowMore": MessageLookupByLibrary.simpleMessage("Saiba Mais"),
        "language": MessageLookupByLibrary.simpleMessage("Inglês"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Deixe a Agnes ajudá-lo a criar um formato de compartilhamento melhor"),
        "letAgnesResolve":
            MessageLookupByLibrary.simpleMessage("Deixe a Agnes resolver"),
        "linkCopied": MessageLookupByLibrary.simpleMessage("Link copiado"),
        "listening": MessageLookupByLibrary.simpleMessage("Ouvindo"),
        "loading": MessageLookupByLibrary.simpleMessage("Carregando"),
        "makeMeAShareableImagePost": MessageLookupByLibrary.simpleMessage(
            "Faça para mim uma postagem de imagem compartilhável"),
        "makeMeAnHtmlWebPage": MessageLookupByLibrary.simpleMessage(
            "Faça-me uma página web em HTML"),
        "manageMembers":
            MessageLookupByLibrary.simpleMessage("Gerenciar membros"),
        "manuallyEndTheTask": MessageLookupByLibrary.simpleMessage(
            "Encerrar a tarefa manualmente"),
        "markAsResolved":
            MessageLookupByLibrary.simpleMessage("Marcar como resolvido"),
        "messageCopied":
            MessageLookupByLibrary.simpleMessage("Mensagem Copiada."),
        "multipleChoices":
            MessageLookupByLibrary.simpleMessage("Várias opções"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("Meus Favoritos"),
        "narrate": MessageLookupByLibrary.simpleMessage("Narrar"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Conexão de rede falhou. Por favor, tente novamente"),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("Conexão de rede perdida"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("Rede indisponível"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Nova senha"),
        "newProject": MessageLookupByLibrary.simpleMessage("Novo Projeto"),
        "newTask": MessageLookupByLibrary.simpleMessage("Nova tarefa"),
        "newTitle": MessageLookupByLibrary.simpleMessage("Novo título"),
        "noMoreFiles":
            MessageLookupByLibrary.simpleMessage("Não há mais arquivos"),
        "noMoreMessage":
            MessageLookupByLibrary.simpleMessage("Não há mais mensagens..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Nenhuma tarefa pendente encontrada. Todas as tarefas foram concluídas!"),
        "notAvailableForPurchase":
            MessageLookupByLibrary.simpleMessage("Não disponível para compra"),
        "notConnected": MessageLookupByLibrary.simpleMessage("Não conectado"),
        "notNow": MessageLookupByLibrary.simpleMessage("Agora não"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notificações"),
        "notifyUserViaEmail": MessageLookupByLibrary.simpleMessage(
            "Notificar usuário por e-mail"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "Ok, percebi que você não tem as permissões necessárias. Peça ao proprietário ou a um editor para fazê-lo."),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage(
                "Ok, vou resumir as tarefas de consenso:"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage(
                "Uma vez excluído, o histórico do chat em grupo será apagado."),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("Operação bem-sucedida!"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage(
                "Ou me avise se tiver alguma pergunta"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage(
                "Os outros membros do grupo serão notificados após a alteração do nome do grupo."),
        "outOfDate": MessageLookupByLibrary.simpleMessage("Desatualizado"),
        "owner": MessageLookupByLibrary.simpleMessage("Proprietário"),
        "pages": MessageLookupByLibrary.simpleMessage("Páginas"),
        "passwordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "A senha não pode estar vazia"),
        "pending": MessageLookupByLibrary.simpleMessage("Pendente"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage(
                "Permissão para executar tarefas em segundo plano não concedida"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefone"),
        "phoneNumber":
            MessageLookupByLibrary.simpleMessage("Número de telefone"),
        "phoneNumberCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "O número de telefone não pode estar vazio"),
        "photo": MessageLookupByLibrary.simpleMessage("Foto"),
        "pleaseSelectASlideToEdit": MessageLookupByLibrary.simpleMessage(
            "Selecione um slide para editar."),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "Atualize para a versão mais recente para novos recursos e melhorias"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage(
                "Aguarde a Agnes concluir a tarefa"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Aguarde até que o download seja concluído"),
        "presenter": MessageLookupByLibrary.simpleMessage("Apresentador"),
        "preview": MessageLookupByLibrary.simpleMessage("Pré-visualização"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Política de Privacidade"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage(
                "Forneça feedback adicional sobre esta resposta"),
        "quote": MessageLookupByLibrary.simpleMessage("Citação"),
        "quotedMessageHasExpired":
            MessageLookupByLibrary.simpleMessage("Mensagem citada expirou."),
        "reconnect": MessageLookupByLibrary.simpleMessage("Reconectar"),
        "reject": MessageLookupByLibrary.simpleMessage("Rejeitar"),
        "remove": MessageLookupByLibrary.simpleMessage("Remover"),
        "removedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Removido com sucesso"),
        "rename": MessageLookupByLibrary.simpleMessage("renomear"),
        "reopenTheApp":
            MessageLookupByLibrary.simpleMessage("Reabra o aplicativo"),
        "requestTimedOutPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "O pedido expirou. Por favor, tente novamente"),
        "research": MessageLookupByLibrary.simpleMessage("Pesquisa"),
        "researchForZdSeconds": m1,
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Redefinir senha"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Redefina sua senha"),
        "role": MessageLookupByLibrary.simpleMessage("Função"),
        "save": MessageLookupByLibrary.simpleMessage("Salvar"),
        "saved": MessageLookupByLibrary.simpleMessage("Salvo"),
        "saving": MessageLookupByLibrary.simpleMessage("Salvando…"),
        "search": MessageLookupByLibrary.simpleMessage("Pesquisar"),
        "searchByNameEmailOrPhone": MessageLookupByLibrary.simpleMessage(
            "Pesquisar por nome, e-mail ou telefone"),
        "searchForACountry":
            MessageLookupByLibrary.simpleMessage("Pesquisar país"),
        "seconds": MessageLookupByLibrary.simpleMessage("Segundos"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage(
                "Parece estar desconectado da internet"),
        "selectMembers":
            MessageLookupByLibrary.simpleMessage("Selecionar Membros"),
        "send": MessageLookupByLibrary.simpleMessage("Enviar"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Enviar mensagem"),
        "serverConnectionFailed": MessageLookupByLibrary.simpleMessage(
            "Falha na conexão com o servidor."),
        "serviceIsStillStarting": MessageLookupByLibrary.simpleMessage(
            "Serviço ainda está iniciando."),
        "sessionExpiredPleaseLogInAgain": MessageLookupByLibrary.simpleMessage(
            "A sessão expirou. Por favor, faça login novamente."),
        "setNow": MessageLookupByLibrary.simpleMessage("Definir agora"),
        "settings": MessageLookupByLibrary.simpleMessage("Configurações"),
        "share": MessageLookupByLibrary.simpleMessage("Partilhar"),
        "signIn": MessageLookupByLibrary.simpleMessage("Entrar"),
        "signOut": MessageLookupByLibrary.simpleMessage("Sair"),
        "signUp": MessageLookupByLibrary.simpleMessage("Inscrever-se"),
        "someoneMe":
            MessageLookupByLibrary.simpleMessage("Alguém me @mencionou"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "Desculpe, estamos com capacidade total agora. Por favor, tente novamente amanhã."),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking":
            MessageLookupByLibrary.simpleMessage("Começar a falar"),
        "submit": MessageLookupByLibrary.simpleMessage("Submeter"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry": MessageLookupByLibrary.simpleMessage(
            "Alterne Wi-Fi/dados móveis e tente novamente"),
        "system": MessageLookupByLibrary.simpleMessage("Sistema"),
        "systemBusyPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Sistema ocupado, tente novamente"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("Tirar foto"),
        "taskStoppedManually": MessageLookupByLibrary.simpleMessage(
            "Tarefa interrompida manualmente"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService":
            MessageLookupByLibrary.simpleMessage("Termos de Serviço"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage(
                "2. A contagem de diamantes é atualizada no dia 1 de cada mês."),
        "theGroupIsCurrentlyInACall": MessageLookupByLibrary.simpleMessage(
            "O grupo está atualmente em uma chamada..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage("O idioma usado na interface"),
        "thePhoneNumberIsInvalid": MessageLookupByLibrary.simpleMessage(
            "O número de telefone é inválido"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Este aplicativo precisa de acesso à câmera para que você tire uma foto de perfil."),
        "thisIsntHelpful":
            MessageLookupByLibrary.simpleMessage("Isto não é útil"),
        "thisSlideIsBeingEdited": MessageLookupByLibrary.simpleMessage(
            "Este slide está sendo editado."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "Para permitir que você envie imagens para reconhecimento, interação ou definir seu avatar, permita que Agnes acesse sua câmera"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Para permitir que você envie imagens para reconhecimento, interação ou definir seu avatar, permita que Agnes acesse sua biblioteca de fotos"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Para salvar imagens geradas, permita que Agnes salve imagens na sua biblioteca de fotos"),
        "transcribing": MessageLookupByLibrary.simpleMessage("Transcrevendo…"),
        "transcriptionEnabledLoadingData": MessageLookupByLibrary.simpleMessage(
            "Transcrição ativada. Carregando dados…"),
        "tryOneOfThese":
            MessageLookupByLibrary.simpleMessage("Experimente uma destas:"),
        "twoPasswordsAreInconsistent": MessageLookupByLibrary.simpleMessage(
            "As duas senhas não coincidem"),
        "update": MessageLookupByLibrary.simpleMessage("Atualizar"),
        "updateFailedPleaseTryAgainLater": MessageLookupByLibrary.simpleMessage(
            "Falha na atualização. Por favor, tente novamente mais tarde."),
        "updatePassword":
            MessageLookupByLibrary.simpleMessage("Atualizar senha"),
        "updatePasswordSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Senha atualizada com sucesso"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage(
                "Faça upgrade do seu plano para continuar: Atualizar"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Use 6+ caracteres com letras, números e símbolos"),
        "userAccess": MessageLookupByLibrary.simpleMessage("Acesso de usuário"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("Nome de usuário"),
        "usersWithAccess":
            MessageLookupByLibrary.simpleMessage("Usuários com acesso"),
        "usingTool": MessageLookupByLibrary.simpleMessage("Usando ferramenta"),
        "verificationCode":
            MessageLookupByLibrary.simpleMessage("Código de verificação"),
        "verificationCodeCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "O código de verificação não pode estar vazio"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("Ver"),
        "viewAllComments":
            MessageLookupByLibrary.simpleMessage("Ver todos os comentários"),
        "viewer": MessageLookupByLibrary.simpleMessage("Visualizador"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Falha ao criar chamada de voz, possivelmente devido à instabilidade da rede. Tente novamente."),
        "voiceCallEnded":
            MessageLookupByLibrary.simpleMessage("Chamada de voz encerrada"),
        "waiting": MessageLookupByLibrary.simpleMessage("Aguardando"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Precisamos de acesso para salvar imagens ou vídeos baixados na sua galeria."),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Precisamos acessar seu microfone para interação por voz"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "Precisamos de acesso à sua galeria para que você escolha uma foto de perfil."),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage(
                "Precisamos acessar sua galeria de fotos para selecionar imagens"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Precisamos reconhecer sua fala para convertê-la em texto para interação"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Precisamos salvar imagens na sua galeria de fotos"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Precisamos usar Bluetooth para conectar seu headset para interação por voz"),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage(
                "Precisamos usar sua câmera para tirar fotos"),
        "welcomeToAgnes":
            MessageLookupByLibrary.simpleMessage("Bem-vindo ao Agnes"),
        "writeSomething": MessageLookupByLibrary.simpleMessage("Escreva Algo"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("Você"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage(
                "Você pode executar, modificar ou remover. Como deseja prosseguir?"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "Você pode editar os slides manualmente ou editá-los conversando com a Agnes."),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage(
                "Você pode selecionar conteúdo e compartilhar seu feedback."),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "Você não pode minimizar uma chamada de vídeo, pois o AGNES não está autorizado a usar janelas flutuantes."),
        "youCreatedTheGroup":
            MessageLookupByLibrary.simpleMessage("Você criou o grupo."),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Você foi removido do chat em grupo"),
        "youJoinedTheGroup":
            MessageLookupByLibrary.simpleMessage("Você entrou no grupo."),
        "yourCurrentPlan":
            MessageLookupByLibrary.simpleMessage("Seu plano atual"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "Seu telefone não autorizou Agnes a usar a permissão de janela flutuante. Agnes não poderá continuar executando tarefas em segundo plano para você."),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Seu pedido para o limite de tarefas de pesquisa foi atingido."),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Seu pedido para o limite de tarefas de slides foi atingido."),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Você atingiu o limite desta tarefa."),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd":
            MessageLookupByLibrary.simpleMessage("Você chegou ao fim")
      };
}
