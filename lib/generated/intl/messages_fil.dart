// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fil locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fil';

  static String m0(termsOfService, privacyPolicy) =>
      "Sa pag-sign in, sumasang-ayon ka sa\n${termsOfService} at ${privacyPolicy}.";

  static String m1(value) => "Pananaliksik ng ${value} segundo";

  static String m2(title) =>
      "Pasensya, nabigo ang iyong ${title} na gawain. Pakisubukang muli mamaya.";

  static String m3(value) =>
      "Sinusuportahan ang pag-upload ng hanggang ${value} na larawan.";

  static String m4(remaining_count) =>
      "Ang mga gawain ay natanggal ayon sa iyong hiling. Kasalukuyang bilang ng nakabinbing gawain: ${remaining_count}. Nais mo bang ipakita ko ang lahat ng nakabinbing gawain?";

  static String m5(total) => "Mayroon ngayong ${total} nakabinbing gawain:";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "Ang user ${user_name} ay may hindi sapat na quota, slide natira: ${slides_remaining_count}, kailangang quota ng slide: ${slides_plan_count}, pananaliksik natira: ${research_remaining_count}, kailangang quota ng pananaliksik: ${research_plan_count}. Maaari kang pumili ng mga gawain na may sapat na quota para isagawa.";

  static String m7(xxx) =>
      "Ipinadala ang verification code sa ${xxx}@email.com";

  static String m8(version) => "Bersyon ${version} ay available na";

  static String m9(x) => "${x} miyembro sa voice...";

  static String m10(xxx) => "Binago ni ${xxx} ang pangalan ng grupo";

  static String m11(xxx) =>
      "Inimbitahan ka ni ${xxx} na makipagtulungan sa pag-edit ng \"${xxx}\".";

  static String m12(xxx) =>
      "Inimbitahan ka ni ${xxx} na makipagtulungan sa pag-review ng \"${xxx}\".";

  static String m13(xxx) => "Nagsimula na si ${xxx} ng tawag na boses";

  static String m14(xxx, xyz) =>
      "${xxx} iniimbitahan kang sumali sa tawag ng grupong \"${xyz}\".";

  static String m15(title) =>
      "Matagumpay na natapos ang iyong ${title} na gawain!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond":
            MessageLookupByLibrary.simpleMessage("Tungkol sa Diamond"),
        "accept": MessageLookupByLibrary.simpleMessage("Tanggapin"),
        "account": MessageLookupByLibrary.simpleMessage("Account"),
        "accountDeletionSuccessful": MessageLookupByLibrary.simpleMessage(
            "Matagumpay na na-delete ang account"),
        "accountNotCreatedYet": MessageLookupByLibrary.simpleMessage(
            "Hindi pa nagagawa ang account"),
        "accountSignoutSuccessful": MessageLookupByLibrary.simpleMessage(
            "Matagumpay na naka-sign out ang account"),
        "activeAccount":
            MessageLookupByLibrary.simpleMessage("Aktibong Account"),
        "add": MessageLookupByLibrary.simpleMessage("Idagdag"),
        "addComment":
            MessageLookupByLibrary.simpleMessage("Magdagdag ng Komento"),
        "addPeopleToChat":
            MessageLookupByLibrary.simpleMessage("Idagdag ang mga tao sa Chat"),
        "addToChat": MessageLookupByLibrary.simpleMessage("Idagdag sa Chat"),
        "addUserEmailAndEnterToInvite": MessageLookupByLibrary.simpleMessage(
            "I-type ang email ng user at pindutin ang \"enter\" para imbitahan"),
        "addedToGroupSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Matagumpay na naidagdag sa grupo"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Maaaring magkamali si Agnes. Suriin ang mahalagang impormasyon."),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage(
                "@Agnes para sa buod, ulat, o slides."),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn":
            MessageLookupByLibrary.simpleMessage("Sumang-ayon at Mag-log in"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("Disenyo ng AI"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("AI Slides"),
        "album": MessageLookupByLibrary.simpleMessage("Album"),
        "allMembers": MessageLookupByLibrary.simpleMessage("Lahat ng Miyembro"),
        "answerCall": MessageLookupByLibrary.simpleMessage("Sagutin ang Tawag"),
        "appUpdate": MessageLookupByLibrary.simpleMessage("Pag-update ng App"),
        "archived": MessageLookupByLibrary.simpleMessage("Na-archive"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Sigurado ka bang gusto mong i-delete ang account mo?"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Sigurado ka bang gusto mong mag-sign out sa iyong account?"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Magtanong kay Agnes"),
        "askAgnesToEditSlides": MessageLookupByLibrary.simpleMessage(
            "Hilingin kay Agnes na i-edit ang slides"),
        "assignATaskToBegin": MessageLookupByLibrary.simpleMessage(
            "Magtalaga ng gawain upang magsimula"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Hindi bababa sa anim na character na may mga letra, numero at simbolo"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "Sa kasalukuyan, walang pinagkasunduan tungkol sa mga gawain na kailangang gawin. Huwag mag-atubiling mag-usap pa lalo at magtanong kung mayroon kang tanong!"),
        "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
        "back": MessageLookupByLibrary.simpleMessage("Bumalik"),
        "bookmarkRemoved":
            MessageLookupByLibrary.simpleMessage("Tinanggal ang bookmark"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("Na-bookmark"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined":
            MessageLookupByLibrary.simpleMessage("Tinanggihan ang tawag"),
        "cancel": MessageLookupByLibrary.simpleMessage("Kanselahin"),
        "canvas": MessageLookupByLibrary.simpleMessage("Canvas"),
        "chapter": MessageLookupByLibrary.simpleMessage("Kabanata"),
        "chat": MessageLookupByLibrary.simpleMessage("Chat"),
        "chatSettings":
            MessageLookupByLibrary.simpleMessage("Mga Setting ng Chat"),
        "chooseImage":
            MessageLookupByLibrary.simpleMessage("Pumili ng larawan"),
        "close": MessageLookupByLibrary.simpleMessage("Isara"),
        "code": MessageLookupByLibrary.simpleMessage("Code"),
        "collect": MessageLookupByLibrary.simpleMessage("kolektahin"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("Malapit na"),
        "comment": MessageLookupByLibrary.simpleMessage("Komento"),
        "commenter": MessageLookupByLibrary.simpleMessage("Commenter"),
        "commentsTasks":
            MessageLookupByLibrary.simpleMessage("Gawain sa Komento"),
        "confirm": MessageLookupByLibrary.simpleMessage("Kumpirmahin"),
        "confirmDelete":
            MessageLookupByLibrary.simpleMessage("Kumpirmahin ang Pag-delete"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Kumpirmahin ang Password"),
        "confirmPasswordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Hindi puwedeng walang kumpirmasyon ng password"),
        "confirmSignOut": MessageLookupByLibrary.simpleMessage(
            "Kumpirmahin ang Pag-sign Out"),
        "connect": MessageLookupByLibrary.simpleMessage("Kumonekta"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage(
                "Ikonekta ang AI headset at makipag-chat kay Agnes anumang oras"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage(
                "Kumonekta sa Agnes VibePods at makipag-chat anumang oras."),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Kumonekta sa Mga Kaibigan upang Magsimula ng Group Chat"),
        "connected": MessageLookupByLibrary.simpleMessage("Nakakonekta"),
        "connectionFailed":
            MessageLookupByLibrary.simpleMessage("Nabigo ang koneksyon"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "Nakopya na ang content—i-paste na ito sa paborito mong platform!"),
        "continueWithApple":
            MessageLookupByLibrary.simpleMessage("Magpatuloy sa Apple"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("Magpatuloy sa Google"),
        "continueWithOtherWays": MessageLookupByLibrary.simpleMessage(
            "Magpatuloy gamit ang ibang paraan"),
        "continueWord": MessageLookupByLibrary.simpleMessage("Magpatuloy"),
        "copied": MessageLookupByLibrary.simpleMessage("Nakopya"),
        "copy": MessageLookupByLibrary.simpleMessage("Kopyahin"),
        "cost": MessageLookupByLibrary.simpleMessage("gastos"),
        "cover": MessageLookupByLibrary.simpleMessage("Pabalat"),
        "createANewTaskToGetStarted": MessageLookupByLibrary.simpleMessage(
            "Gumawa ng bagong gawain para magsimula"),
        "createAnAccountToSignUpForAgnes": MessageLookupByLibrary.simpleMessage(
            "Gumawa ng account para mag-sign up sa Agnes"),
        "createGroupChat":
            MessageLookupByLibrary.simpleMessage("Lumikha ng Group Chat"),
        "currentlyInACall":
            MessageLookupByLibrary.simpleMessage("Kasalukuyang nasa tawag..."),
        "deepResearch":
            MessageLookupByLibrary.simpleMessage("Masusing Pananaliksik"),
        "deepresearch":
            MessageLookupByLibrary.simpleMessage("Malalim na Pananaliksik"),
        "delete": MessageLookupByLibrary.simpleMessage("I-delete"),
        "deleteAccount":
            MessageLookupByLibrary.simpleMessage("I-delete ang Account"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("I-delete ang Chat"),
        "doYouWantToExecuteTheseTasks": MessageLookupByLibrary.simpleMessage(
            "Gusto mo bang isagawa ang mga gawaing ito?"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                " ay mukhang hindi Agnes user. I-enter ang kanilang email para magpadala ng invite"),
        "done": MessageLookupByLibrary.simpleMessage("Tapos"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Wala pang account?"),
        "downloading": MessageLookupByLibrary.simpleMessage("Nagda-download"),
        "eachNewTaskConsumesOneDiamond": MessageLookupByLibrary.simpleMessage(
            "1. Bawat bagong gawain ay gumagamit ng isang diamond."),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. Bawat user ay tumatanggap ng 30 diamond kada buwan."),
        "edit": MessageLookupByLibrary.simpleMessage("I-edit"),
        "editGroupName": MessageLookupByLibrary.simpleMessage(
            "I-edit ang pangalan ng grupo"),
        "editor": MessageLookupByLibrary.simpleMessage("Editor"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("Hindi puwedeng walang email"),
        "emailFormatIncorrect":
            MessageLookupByLibrary.simpleMessage("Mali ang format ng email"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage(
                "Palakasin ang produktibidad at pagkamalikhain sa pamamagitan ng pinalawak na access"),
        "enterConfirmPassword": MessageLookupByLibrary.simpleMessage(
            "Ipasok ang Kumpirmasyon ng Password"),
        "enterGroupName": MessageLookupByLibrary.simpleMessage(
            "Ipasok ang pangalan ng grupo"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Ipasok ang Password"),
        "enterVerificationCode": MessageLookupByLibrary.simpleMessage(
            "Ipasok ang Verification Code"),
        "export": MessageLookupByLibrary.simpleMessage("I-export"),
        "file": MessageLookupByLibrary.simpleMessage("File"),
        "floatingWindowNotEnabled": MessageLookupByLibrary.simpleMessage(
            "Hindi naka-enable ang Floating Window"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage(
                "Para sa buong karanasan, mangyaring gamitin ang bersyon ng PC"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Nakalimutan ang Password"),
        "general": MessageLookupByLibrary.simpleMessage("Pangkalahatan"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Kunin ang Agnes AI Plus"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Kunin ang Agnes AI Pro"),
        "giveMeATaskToWorkOn": MessageLookupByLibrary.simpleMessage(
            "Bigyan mo ako ng gawain na gagawin…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("Pumunta sa Tindahan"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage(
                "Naiintindihan! Ginagawa ko ang gawain. Mangyaring maghintay ng sandali."),
        "groupCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Tagumpay na nalikha ang grupo"),
        "groupFiles": MessageLookupByLibrary.simpleMessage("Mga File ng Grupo"),
        "groupName": MessageLookupByLibrary.simpleMessage("Pangalan ng Grupo"),
        "groupNameOptional": MessageLookupByLibrary.simpleMessage(
            "Pangalan ng Grupo (Opsyonal)"),
        "harmful": MessageLookupByLibrary.simpleMessage("Nakakasama"),
        "helpUsImprove":
            MessageLookupByLibrary.simpleMessage("Tulungan kaming mapabuti"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage(
                "Tinutulungan kita sa paghahanap, pagsusulat, at mga sagot"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage(
                "Kung sagutin mo ang papasok na tawag, matatapos ang kasalukuyang tawag mo."),
        "imAgnes": MessageLookupByLibrary.simpleMessage("Ako si Agnes"),
        "inAVoiceCall": MessageLookupByLibrary.simpleMessage("Nasa voice call"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("Hindi Tumpak"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Hindi magagamit ang in-app na pagbili sa kasalukuyan, pakisubukang muli mamaya"),
        "interfaceLanguage":
            MessageLookupByLibrary.simpleMessage("Wika ng Interface"),
        "invite": MessageLookupByLibrary.simpleMessage("Imbitahan"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage(
                "Mag-imbita gamit ang username, email, o numero ng telepono"),
        "inviteFriends":
            MessageLookupByLibrary.simpleMessage("Mag-imbita ng Kaibigan"),
        "inviteOthersToCollaborate": MessageLookupByLibrary.simpleMessage(
            "Imbitahan ang iba na makipagtulungan"),
        "inviteToCollaborate": MessageLookupByLibrary.simpleMessage(
            "Imbitahan upang makipagtulungan"),
        "isEditing": MessageLookupByLibrary.simpleMessage("Nag-e-edit"),
        "join": MessageLookupByLibrary.simpleMessage("Sumali"),
        "knowMore": MessageLookupByLibrary.simpleMessage("Alamin Pa"),
        "language": MessageLookupByLibrary.simpleMessage("Ingles"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Hayaan si Agnes na tulungan kang gumawa ng mas mahusay na format ng pagbabahagi"),
        "letAgnesResolve":
            MessageLookupByLibrary.simpleMessage("Hayaan si Agnes na ayusin"),
        "linkCopied":
            MessageLookupByLibrary.simpleMessage("Nakopya na ang link"),
        "listening": MessageLookupByLibrary.simpleMessage("Nakikinig"),
        "loading": MessageLookupByLibrary.simpleMessage("Naglo-load"),
        "makeMeAShareableImagePost": MessageLookupByLibrary.simpleMessage(
            "Gumawa ng maibabahaging post na larawan para sa akin"),
        "makeMeAnHtmlWebPage": MessageLookupByLibrary.simpleMessage(
            "Gawan mo ako ng HTML web page"),
        "manageMembers":
            MessageLookupByLibrary.simpleMessage("Pamahalaan ang mga Miyembro"),
        "manuallyEndTheTask": MessageLookupByLibrary.simpleMessage(
            "Tapusin ang gawain nang manu-mano"),
        "markAsResolved":
            MessageLookupByLibrary.simpleMessage("Markahan bilang Nalutas"),
        "messageCopied":
            MessageLookupByLibrary.simpleMessage("Mensahe Na-Copy."),
        "multipleChoices":
            MessageLookupByLibrary.simpleMessage("Maramihang pagpipilian"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("Mga Paborito Ko"),
        "narrate": MessageLookupByLibrary.simpleMessage("Ikuwento"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Nabigo ang koneksyon sa network. Subukang muli"),
        "networkConnectionLost": MessageLookupByLibrary.simpleMessage(
            "Nawalan ng koneksyon sa network"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("Hindi magagamit ang network"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Bagong Password"),
        "newProject": MessageLookupByLibrary.simpleMessage("Bagong Proyekto"),
        "newTask": MessageLookupByLibrary.simpleMessage("Bagong Gawain"),
        "newTitle": MessageLookupByLibrary.simpleMessage("Bagong Pamagat"),
        "noMoreFiles":
            MessageLookupByLibrary.simpleMessage("Wala nang Mga File"),
        "noMoreMessage":
            MessageLookupByLibrary.simpleMessage("Wala nang mensahe..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Walang nakitang pending na gawain. Lahat ng gawain ay nakumpleto na!"),
        "notAvailableForPurchase":
            MessageLookupByLibrary.simpleMessage("Hindi magagamit para bilhin"),
        "notConnected":
            MessageLookupByLibrary.simpleMessage("Hindi nakakonekta"),
        "notNow": MessageLookupByLibrary.simpleMessage("Hindi muna"),
        "notifications": MessageLookupByLibrary.simpleMessage("Mga Abiso"),
        "notifyUserViaEmail":
            MessageLookupByLibrary.simpleMessage("Abisuhan ang user sa email"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "Okay, napansin kong wala kang kinakailangang permiso. Mangyaring humingi sa may-ari o editor na gawin ito."),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage(
                "Sige, ibuod ko ang mga napagkasunduang gawain:"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage(
                "Kapag na-delete, mabubura ang kasaysayan ng group chat."),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("Matagumpay!"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage(
                "O ipaalam sa akin kung may tanong ka"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage(
                "Maabisuhan ang ibang miyembro ng grupo pagkatapos baguhin ang pangalan ng grupo."),
        "outOfDate": MessageLookupByLibrary.simpleMessage("Luma na"),
        "owner": MessageLookupByLibrary.simpleMessage("May-ari"),
        "pages": MessageLookupByLibrary.simpleMessage("Mga Pahina"),
        "passwordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Hindi puwedeng walang password"),
        "pending": MessageLookupByLibrary.simpleMessage("Naka-pending"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage(
                "Walang pahintulot na magsagawa ng mga gawain sa background"),
        "phone": MessageLookupByLibrary.simpleMessage("Telepono"),
        "phoneNumber":
            MessageLookupByLibrary.simpleMessage("Numero ng Telepono"),
        "phoneNumberCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Hindi puwedeng walang numero ng telepono"),
        "photo": MessageLookupByLibrary.simpleMessage("Larawan"),
        "pleaseSelectASlideToEdit":
            MessageLookupByLibrary.simpleMessage("Pumili ng slide na i-eedit."),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "Mangyaring i-update sa pinakabagong bersyon para sa mga bagong tampok at pagpapahusay"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage(
                "Mangyaring maghintay para matapos ni Agnes ang task"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Mangyaring maghintay hanggang matapos ang pag-download"),
        "presenter": MessageLookupByLibrary.simpleMessage("Tagapagsalita"),
        "preview": MessageLookupByLibrary.simpleMessage("Prebiyu"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage(
                "Magbigay ng karagdagang puna sa sagot na ito"),
        "quote": MessageLookupByLibrary.simpleMessage("Pagcita"),
        "quotedMessageHasExpired": MessageLookupByLibrary.simpleMessage(
            "Mensaheng na-cita nawalan ng bisa na."),
        "reconnect": MessageLookupByLibrary.simpleMessage("Kumonekta muli"),
        "reject": MessageLookupByLibrary.simpleMessage("Tanggihan"),
        "remove": MessageLookupByLibrary.simpleMessage("Alisin"),
        "removedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Matagumpay na tinanggal"),
        "rename": MessageLookupByLibrary.simpleMessage("palitan ang pangalan"),
        "reopenTheApp":
            MessageLookupByLibrary.simpleMessage("Buksan muli ang app"),
        "requestTimedOutPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Nag-time out ang request. Subukang muli"),
        "research": MessageLookupByLibrary.simpleMessage("Pananaliksik"),
        "researchForZdSeconds": m1,
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("I-reset ang Password"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("I-reset ang iyong Password"),
        "role": MessageLookupByLibrary.simpleMessage("Papel"),
        "save": MessageLookupByLibrary.simpleMessage("I-save"),
        "saved": MessageLookupByLibrary.simpleMessage("Nasave"),
        "saving": MessageLookupByLibrary.simpleMessage("Nagsa-save…"),
        "search": MessageLookupByLibrary.simpleMessage("Maghanap"),
        "searchByNameEmailOrPhone": MessageLookupByLibrary.simpleMessage(
            "Maghanap ayon sa pangalan, email, o telepono"),
        "searchForACountry":
            MessageLookupByLibrary.simpleMessage("Maghanap ng bansa"),
        "seconds": MessageLookupByLibrary.simpleMessage("Segundo"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage(
                "Mukhang nawalan ng koneksyon sa internet"),
        "selectMembers":
            MessageLookupByLibrary.simpleMessage("Piliin ang Mga Miyembro"),
        "send": MessageLookupByLibrary.simpleMessage("Ipadala"),
        "sendMessage":
            MessageLookupByLibrary.simpleMessage("Magpadala ng Mensahe"),
        "serverConnectionFailed": MessageLookupByLibrary.simpleMessage(
            "Nag-fail ang koneksyon sa server."),
        "serviceIsStillStarting": MessageLookupByLibrary.simpleMessage(
            "Nagsisimula pa ang serbisyo."),
        "sessionExpiredPleaseLogInAgain": MessageLookupByLibrary.simpleMessage(
            "Nag-expire ang session. Mangyaring mag-login muli."),
        "setNow": MessageLookupByLibrary.simpleMessage("I-set ngayon"),
        "settings": MessageLookupByLibrary.simpleMessage("Mga Setting"),
        "share": MessageLookupByLibrary.simpleMessage("Ibahagi"),
        "signIn": MessageLookupByLibrary.simpleMessage("Mag-sign In"),
        "signOut": MessageLookupByLibrary.simpleMessage("Mag-sign out"),
        "signUp": MessageLookupByLibrary.simpleMessage("Mag-sign Up"),
        "someoneMe": MessageLookupByLibrary.simpleMessage("May nag-@ sa akin"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "Paumanhin, puno na kami ngayon. Subukang muli bukas."),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking":
            MessageLookupByLibrary.simpleMessage("Magsimulang magsalita"),
        "submit": MessageLookupByLibrary.simpleMessage("Isumite"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry": MessageLookupByLibrary.simpleMessage(
            "Lumipat sa Wi-Fi/mobile data at subukang muli"),
        "system": MessageLookupByLibrary.simpleMessage("Sistema"),
        "systemBusyPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Abala ang sistema, subukang muli"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("Kunan ng larawan"),
        "taskStoppedManually": MessageLookupByLibrary.simpleMessage(
            "Ang task ay pinahinto nang manu-mano"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService":
            MessageLookupByLibrary.simpleMessage("Terms of Service"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage(
                "2. Na-a-update ang bilang ng diamond tuwing ika-1 ng buwan."),
        "theGroupIsCurrentlyInACall": MessageLookupByLibrary.simpleMessage(
            "Ang grupo ay kasalukuyang nasa tawag..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage(
                "Wika na ginagamit sa interface"),
        "thePhoneNumberIsInvalid": MessageLookupByLibrary.simpleMessage(
            "Hindi wasto ang numero ng telepono"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Kailangan ng app na ito ng access sa camera para makakuha ka ng larawan sa iyong profile picture."),
        "thisIsntHelpful":
            MessageLookupByLibrary.simpleMessage("Ito ay hindi nakakatulong"),
        "thisSlideIsBeingEdited": MessageLookupByLibrary.simpleMessage(
            "Ang slide na ito ay ine-edit."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "Upang makapagpadala ka ng mga larawan para sa pagkilala, pakikipag-ugnayan, o pagsasaayos ng avatar, pahintulutan si Agnes na ma-access ang iyong camera"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Upang makapagpadala ka ng mga larawan para sa pagkilala, pakikipag-ugnayan, o pagsasaayos ng avatar, pahintulutan si Agnes na ma-access ang iyong library ng larawan"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Upang mai-save ang mga nilikhang larawan, pahintulutan si Agnes na mai-save ang mga larawan sa iyong library ng larawan"),
        "transcribing": MessageLookupByLibrary.simpleMessage(
            "Nagsusulat ng transkripsyon…"),
        "transcriptionEnabledLoadingData": MessageLookupByLibrary.simpleMessage(
            "Na-enable ang transkripsyon. Naglo-load ng data…"),
        "tryOneOfThese":
            MessageLookupByLibrary.simpleMessage("Subukan ang isa sa mga ito:"),
        "twoPasswordsAreInconsistent": MessageLookupByLibrary.simpleMessage(
            "Magkaiba ang dalawang password"),
        "update": MessageLookupByLibrary.simpleMessage("I-update"),
        "updateFailedPleaseTryAgainLater": MessageLookupByLibrary.simpleMessage(
            "Nabigo ang pag-update. Pakisubukang muli mamaya."),
        "updatePassword":
            MessageLookupByLibrary.simpleMessage("I-update ang Password"),
        "updatePasswordSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Matagumpay na na-update ang password"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage(
                "I-upgrade ang iyong plano para magpatuloy: I-upgrade"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Gumamit ng 6+ character na may mga letra, numero & simbolo"),
        "userAccess": MessageLookupByLibrary.simpleMessage("Access ng User"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("Username"),
        "usersWithAccess":
            MessageLookupByLibrary.simpleMessage("Mga user na may access"),
        "usingTool": MessageLookupByLibrary.simpleMessage("Paggamit ng Tool"),
        "verificationCode":
            MessageLookupByLibrary.simpleMessage("Verification Code"),
        "verificationCodeCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Hindi puwedeng walang verification code"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("Tingnan"),
        "viewAllComments":
            MessageLookupByLibrary.simpleMessage("Tingnan lahat ng komento"),
        "viewer": MessageLookupByLibrary.simpleMessage("Viewer"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Nabigo ang paggawa ng voice call, maaaring dahil sa hindi matatag na network. Pakisubukan muli."),
        "voiceCallEnded":
            MessageLookupByLibrary.simpleMessage("Natapos ang tawag na boses"),
        "waiting": MessageLookupByLibrary.simpleMessage("Naghihintay"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Kailangan namin ng access para ma-save ang mga na-download na larawan o video sa photo library mo."),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Kailangan naming ma-access ang iyong mikropono para sa pakikipag-ugnayan sa boses"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "Kailangan namin ng access sa photo library mo para makapili ka ng larawan sa profile."),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage(
                "Kailangan naming i-access ang photo library mo para pumili ng larawan"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Kailangan naming kilalanin ang iyong boses upang mai-convert ito sa teksto para sa pakikipag-ugnayan"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Kailangan naming i-save ang mga larawan sa photo library mo"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Kailangan naming gamitin ang Bluetooth upang ikonekta ang iyong headset para sa pakikipag-ugnayan sa boses"),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage(
                "Kailangan naming gamitin ang camera mo para kumuha ng larawan"),
        "welcomeToAgnes": MessageLookupByLibrary.simpleMessage(
            "Maligayang pagdating sa Agnes"),
        "writeSomething":
            MessageLookupByLibrary.simpleMessage("Magsulat ng Isang Bagay"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("Ikaw"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage(
                "Maaari mong isagawa, baguhin, o alisin. Paano mo gustong magpatuloy?"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "Maaari mong mano-manong i-edit ang slides o gawin ito sa pamamagitan ng usapan kay Agnes."),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage(
                "Maaari kang pumili ng content at ibahagi ang feedback mo."),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "Hindi mo ma-minimize ang video call dahil walang pahintulot si AGNES na gumamit ng floating window."),
        "youCreatedTheGroup":
            MessageLookupByLibrary.simpleMessage("Ikaw ang lumikha ng grupo."),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage("Tinanggal ka sa group chat"),
        "youJoinedTheGroup":
            MessageLookupByLibrary.simpleMessage("Sumali ka sa grupo."),
        "yourCurrentPlan":
            MessageLookupByLibrary.simpleMessage("Ang kasalukuyan mong plano"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "Hindi pa pinahintulutan ng iyong telepono si Agnes na gamitin ang pahintulot ng floating window. Hindi na maipagpapatuloy ni Agnes ang pagsasagawa ng mga gawain sa background para sa iyo."),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Naabot mo na ang limit ng request para sa research task."),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Naabot mo na ang limit ng request para sa slide task."),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Naabot mo na ang limit para sa gawaing ito."),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd":
            MessageLookupByLibrary.simpleMessage("Naabot mo na ang dulo")
      };
}
