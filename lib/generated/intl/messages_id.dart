// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a id locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'id';

  static String m0(termsOfService, privacyPolicy) =>
      "<PERSON><PERSON>, Anda men<PERSON>\n${termsOfService} dan ${privacyPolicy}.";

  static String m1(value) => "Penelitian selama ${value} detik";

  static String m2(title) =>
      "Maaf, tugas ${title} Anda gagal. Silakan coba lagi nanti.";

  static String m3(value) => "Mendukung unggah hingga ${value} gambar.";

  static String m4(remaining_count) =>
      "Tugas telah dihapus sesuai permintaan. Jumlah tugas tertunda saat ini: ${remaining_count}. Apakah Anda ingin saya tampilkan semua tugas tertunda?";

  static String m5(total) => "Saat ini ada ${total} tugas tertunda:";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "Pengguna ${user_name} memiliki kuota tidak mencukupi, slide tersisa: ${slides_remaining_count}, kuota slide yang dibutuhkan: ${slides_plan_count}, penelitian tersisa: ${research_remaining_count}, kuota penelitian yang dibutuhkan: ${research_plan_count}. Anda dapat memilih tugas dengan kuota cukup untuk dijalankan.";

  static String m7(xxx) => "Kode verifikasi dikirim ke ${xxx}@email.com";

  static String m8(version) => "Versi ${version} sekarang tersedia";

  static String m9(x) => "${x} anggota dalam suara...";

  static String m10(xxx) => "${xxx} mengubah nama grup";

  static String m11(xxx) =>
      "${xxx} mengundang Anda untuk berkolaborasi mengedit \"${xxx}\".";

  static String m12(xxx) =>
      "${xxx} mengundang Anda untuk berkolaborasi meninjau \"${xxx}\".";

  static String m13(xxx) => "${xxx} telah memulai panggilan suara";

  static String m14(xxx, xyz) =>
      "${xxx} mengundang Anda untuk bergabung dalam panggilan grup \"${xyz}\".";

  static String m15(title) =>
      "Tugas ${title} Anda telah berhasil diselesaikan!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond": MessageLookupByLibrary.simpleMessage("Tentang Diamond"),
        "accept": MessageLookupByLibrary.simpleMessage("Terima"),
        "account": MessageLookupByLibrary.simpleMessage("Akun"),
        "accountDeletionSuccessful":
            MessageLookupByLibrary.simpleMessage("Penghapusan akun berhasil"),
        "accountNotCreatedYet":
            MessageLookupByLibrary.simpleMessage("Akun belum dibuat"),
        "accountSignoutSuccessful":
            MessageLookupByLibrary.simpleMessage("Keluar akun berhasil"),
        "activeAccount": MessageLookupByLibrary.simpleMessage("Akun Aktif"),
        "add": MessageLookupByLibrary.simpleMessage("Tambah"),
        "addComment":
            MessageLookupByLibrary.simpleMessage("Tambahkan Komentar"),
        "addPeopleToChat":
            MessageLookupByLibrary.simpleMessage("Tambahkan Orang ke Obrolan"),
        "addToChat":
            MessageLookupByLibrary.simpleMessage("Tambahkan ke Obrolan"),
        "addUserEmailAndEnterToInvite": MessageLookupByLibrary.simpleMessage(
            "Tambahkan email pengguna dan tekan \"enter\" untuk mengundang"),
        "addedToGroupSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Berhasil ditambahkan ke grup"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Agnes bisa saja salah. Periksa informasi penting."),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage(
                "@Agnes untuk ringkasan, laporan, atau slide."),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn":
            MessageLookupByLibrary.simpleMessage("Setuju dan Masuk"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("Desain AI"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("Slide AI"),
        "album": MessageLookupByLibrary.simpleMessage("Album"),
        "allMembers": MessageLookupByLibrary.simpleMessage("Semua Anggota"),
        "answerCall": MessageLookupByLibrary.simpleMessage("Jawab Panggilan"),
        "appUpdate": MessageLookupByLibrary.simpleMessage("Pembaruan Aplikasi"),
        "archived": MessageLookupByLibrary.simpleMessage("Diarsipkan"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Yakin ingin menghapus akun Anda?"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Apakah Anda yakin ingin keluar dari akun Anda?"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Tanya Agnes"),
        "askAgnesToEditSlides":
            MessageLookupByLibrary.simpleMessage("Minta Agnes mengedit slide"),
        "assignATaskToBegin": MessageLookupByLibrary.simpleMessage(
            "Tetapkan tugas untuk memulai"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Minimal enam karakter dengan huruf, angka & simbol"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "Saat ini, belum ada konsensus mengenai tugas yang perlu dilakukan. Silakan diskusikan lebih lanjut dan ajukan pertanyaan jika ada!"),
        "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
        "back": MessageLookupByLibrary.simpleMessage("Kembali"),
        "bookmarkRemoved":
            MessageLookupByLibrary.simpleMessage("Bookmark dihapus"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("Ditandai"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined":
            MessageLookupByLibrary.simpleMessage("Panggilan ditolak"),
        "cancel": MessageLookupByLibrary.simpleMessage("Batal"),
        "canvas": MessageLookupByLibrary.simpleMessage("Kanvas"),
        "chapter": MessageLookupByLibrary.simpleMessage("Bab"),
        "chat": MessageLookupByLibrary.simpleMessage("Obrolan"),
        "chatSettings":
            MessageLookupByLibrary.simpleMessage("Pengaturan Obrolan"),
        "chooseImage": MessageLookupByLibrary.simpleMessage("Pilih Gambar"),
        "close": MessageLookupByLibrary.simpleMessage("Tutup"),
        "code": MessageLookupByLibrary.simpleMessage("Kode"),
        "collect": MessageLookupByLibrary.simpleMessage("kumpulkan"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("Segera Hadir"),
        "comment": MessageLookupByLibrary.simpleMessage("Komentar"),
        "commenter": MessageLookupByLibrary.simpleMessage("Pemberi komentar"),
        "commentsTasks": MessageLookupByLibrary.simpleMessage("Tugas Komentar"),
        "confirm": MessageLookupByLibrary.simpleMessage("Konfirmasi"),
        "confirmDelete":
            MessageLookupByLibrary.simpleMessage("Konfirmasi Hapus"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Konfirmasi Kata Sandi"),
        "confirmPasswordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Konfirmasi kata sandi tidak boleh kosong"),
        "confirmSignOut":
            MessageLookupByLibrary.simpleMessage("Konfirmasi Keluar"),
        "connect": MessageLookupByLibrary.simpleMessage("Sambungkan"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage(
                "Hubungkan headset AI dan ajak bicara dengan Agnes kapan saja"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage(
                "Sambungkan ke Agnes VibePods dan mengobrol kapan saja."),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Terhubung dengan Teman untuk Memulai Obrolan Grup"),
        "connected": MessageLookupByLibrary.simpleMessage("Terhubung"),
        "connectionFailed":
            MessageLookupByLibrary.simpleMessage("Koneksi gagal"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "Konten disalin—silakan tempel di platform favorit Anda!"),
        "continueWithApple":
            MessageLookupByLibrary.simpleMessage("Lanjutkan dengan Apple"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("Lanjutkan dengan Google"),
        "continueWithOtherWays":
            MessageLookupByLibrary.simpleMessage("Lanjutkan dengan cara lain"),
        "continueWord": MessageLookupByLibrary.simpleMessage("Lanjutkan"),
        "copied": MessageLookupByLibrary.simpleMessage("Disalin"),
        "copy": MessageLookupByLibrary.simpleMessage("Salin"),
        "cost": MessageLookupByLibrary.simpleMessage("biaya"),
        "cover": MessageLookupByLibrary.simpleMessage("Sampul"),
        "createANewTaskToGetStarted": MessageLookupByLibrary.simpleMessage(
            "Buat tugas baru untuk memulai"),
        "createAnAccountToSignUpForAgnes": MessageLookupByLibrary.simpleMessage(
            "Buat akun untuk mendaftar Agnes"),
        "createGroupChat":
            MessageLookupByLibrary.simpleMessage("Buat Obrolan Grup"),
        "currentlyInACall":
            MessageLookupByLibrary.simpleMessage("Sedang dalam panggilan..."),
        "deepResearch": MessageLookupByLibrary.simpleMessage("Riset Mendalam"),
        "deepresearch": MessageLookupByLibrary.simpleMessage("Riset Mendalam"),
        "delete": MessageLookupByLibrary.simpleMessage("Hapus"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("Hapus Akun"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Hapus Obrolan"),
        "doYouWantToExecuteTheseTasks": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda ingin menjalankan tugas-tugas ini?"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                " tampaknya bukan pengguna Agnes. Masukkan email mereka untuk mengirim undangan."),
        "done": MessageLookupByLibrary.simpleMessage("Selesai"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Belum punya akun?"),
        "downloading": MessageLookupByLibrary.simpleMessage("Sedang mengunduh"),
        "eachNewTaskConsumesOneDiamond": MessageLookupByLibrary.simpleMessage(
            "1. Setiap tugas baru menghabiskan satu berlian."),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. Setiap pengguna menerima 30 berlian per bulan."),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "editGroupName": MessageLookupByLibrary.simpleMessage("Edit nama grup"),
        "editor": MessageLookupByLibrary.simpleMessage("Editor"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("Email tidak boleh kosong"),
        "emailFormatIncorrect":
            MessageLookupByLibrary.simpleMessage("Format email salah"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage(
                "Tingkatkan produktivitas dan kreativitas melalui akses tambahan"),
        "enterConfirmPassword": MessageLookupByLibrary.simpleMessage(
            "Masukkan Konfirmasi Kata Sandi"),
        "enterGroupName":
            MessageLookupByLibrary.simpleMessage("Masukkan nama grup"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Masukkan Kata Sandi"),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("Masukkan Kode Verifikasi"),
        "export": MessageLookupByLibrary.simpleMessage("Ekspor"),
        "file": MessageLookupByLibrary.simpleMessage("File"),
        "floatingWindowNotEnabled": MessageLookupByLibrary.simpleMessage(
            "Jendela Mengambang Tidak Aktif"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage(
                "Untuk pengalaman lengkap, silakan gunakan versi PC"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Lupa Kata Sandi"),
        "general": MessageLookupByLibrary.simpleMessage("Umum"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Dapatkan Agnes AI Plus"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Dapatkan Agnes AI Pro"),
        "giveMeATaskToWorkOn": MessageLookupByLibrary.simpleMessage(
            "Beri saya tugas untuk dikerjakan…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("Pergi ke Toko"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage(
                "Mengerti! Saya sedang mengerjakan tugas ini. Harap tunggu sebentar."),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Grup berhasil dibuat"),
        "groupFiles": MessageLookupByLibrary.simpleMessage("File Grup"),
        "groupName": MessageLookupByLibrary.simpleMessage("Nama Grup"),
        "groupNameOptional":
            MessageLookupByLibrary.simpleMessage("Nama Grup (Opsional)"),
        "harmful": MessageLookupByLibrary.simpleMessage("Berbahaya"),
        "helpUsImprove":
            MessageLookupByLibrary.simpleMessage("Bantu kami meningkat"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage(
                "Saya membantu pencarian, penulisan, dan jawaban"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage(
                "Jika Anda menjawab panggilan masuk, panggilan Anda saat ini akan berakhir."),
        "imAgnes": MessageLookupByLibrary.simpleMessage("Saya Agnes"),
        "inAVoiceCall":
            MessageLookupByLibrary.simpleMessage("Dalam panggilan suara"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("Tidak Akurat"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Pembelian dalam aplikasi saat ini tidak tersedia, silakan coba lagi nanti"),
        "interfaceLanguage":
            MessageLookupByLibrary.simpleMessage("Bahasa Antarmuka"),
        "invite": MessageLookupByLibrary.simpleMessage("Undang"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage(
                "Undang berdasarkan username, email, atau nomor telepon"),
        "inviteFriends": MessageLookupByLibrary.simpleMessage("Undang Teman"),
        "inviteOthersToCollaborate": MessageLookupByLibrary.simpleMessage(
            "Undang orang lain untuk berkolaborasi"),
        "inviteToCollaborate":
            MessageLookupByLibrary.simpleMessage("Undang untuk berkolaborasi"),
        "isEditing": MessageLookupByLibrary.simpleMessage("Sedang mengedit"),
        "join": MessageLookupByLibrary.simpleMessage("Gabung"),
        "knowMore":
            MessageLookupByLibrary.simpleMessage("Pelajari Lebih Lanjut"),
        "language": MessageLookupByLibrary.simpleMessage("Bahasa Inggris"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Biarkan Agnes membantu Anda membuat format berbagi yang lebih baik"),
        "letAgnesResolve":
            MessageLookupByLibrary.simpleMessage("Biarkan Agnes menyelesaikan"),
        "linkCopied": MessageLookupByLibrary.simpleMessage("Tautan disalin"),
        "listening": MessageLookupByLibrary.simpleMessage("Mendengarkan"),
        "loading": MessageLookupByLibrary.simpleMessage("Memuat"),
        "makeMeAShareableImagePost": MessageLookupByLibrary.simpleMessage(
            "Buatkan saya postingan gambar yang bisa dibagikan"),
        "makeMeAnHtmlWebPage": MessageLookupByLibrary.simpleMessage(
            "Buatkan saya halaman web HTML"),
        "manageMembers": MessageLookupByLibrary.simpleMessage("Kelola Anggota"),
        "manuallyEndTheTask": MessageLookupByLibrary.simpleMessage(
            "Hentikan tugas secara manual"),
        "markAsResolved":
            MessageLookupByLibrary.simpleMessage("Tandai sebagai Selesai"),
        "messageCopied":
            MessageLookupByLibrary.simpleMessage("Pesan Telah Disalin."),
        "multipleChoices":
            MessageLookupByLibrary.simpleMessage("Pilihan ganda"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("Favorit Saya"),
        "narrate": MessageLookupByLibrary.simpleMessage("Narasikan"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Koneksi jaringan gagal. Silakan coba lagi."),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("Koneksi jaringan hilang"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("Jaringan tidak tersedia"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Kata Sandi Baru"),
        "newProject": MessageLookupByLibrary.simpleMessage("Proyek Baru"),
        "newTask": MessageLookupByLibrary.simpleMessage("Tugas Baru"),
        "newTitle": MessageLookupByLibrary.simpleMessage("Judul Baru"),
        "noMoreFiles":
            MessageLookupByLibrary.simpleMessage("Tidak Ada File Lagi"),
        "noMoreMessage":
            MessageLookupByLibrary.simpleMessage("Tidak ada pesan lagi..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Tidak ada tugas tertunda yang ditemukan. Semua tugas telah selesai!"),
        "notAvailableForPurchase":
            MessageLookupByLibrary.simpleMessage("Tidak tersedia untuk dibeli"),
        "notConnected": MessageLookupByLibrary.simpleMessage("Tidak terhubung"),
        "notNow": MessageLookupByLibrary.simpleMessage("Tidak sekarang"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notifikasi"),
        "notifyUserViaEmail": MessageLookupByLibrary.simpleMessage(
            "Beri tahu pengguna melalui email"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "Baik, saya perhatikan Anda tidak memiliki izin yang diperlukan. Silakan minta pemilik atau editor untuk melakukannya."),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage(
                "Baiklah, saya akan merangkum tugas-tugas konsensus:"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage(
                "Setelah dihapus, riwayat obrolan grup akan dihapus."),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("Operasi Berhasil!"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage(
                "Atau beri tahu saya jika ada pertanyaan"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage(
                "Anggota grup lainnya akan diberitahu setelah nama grup diubah."),
        "outOfDate": MessageLookupByLibrary.simpleMessage("Kadaluarsa"),
        "owner": MessageLookupByLibrary.simpleMessage("Pemilik"),
        "pages": MessageLookupByLibrary.simpleMessage("Halaman"),
        "passwordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Kata sandi tidak boleh kosong"),
        "pending": MessageLookupByLibrary.simpleMessage("Tertunda"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage(
                "Izin untuk menjalankan tugas di latar belakang tidak diberikan"),
        "phone": MessageLookupByLibrary.simpleMessage("Telepon"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Nomor Telepon"),
        "phoneNumberCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Nomor telepon tidak boleh kosong"),
        "photo": MessageLookupByLibrary.simpleMessage("Foto"),
        "pleaseSelectASlideToEdit": MessageLookupByLibrary.simpleMessage(
            "Silakan pilih slide untuk diedit."),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "Silakan perbarui ke versi terbaru untuk fitur dan perbaikan baru"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage(
                "Harap tunggu Agnes menyelesaikan tugas"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Harap tunggu hingga unduhan selesai"),
        "presenter": MessageLookupByLibrary.simpleMessage("Penyaji"),
        "preview": MessageLookupByLibrary.simpleMessage("Pratinjau"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Kebijakan Privasi"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage(
                "Berikan masukan tambahan tentang jawaban ini"),
        "quote": MessageLookupByLibrary.simpleMessage("Kutipan"),
        "quotedMessageHasExpired": MessageLookupByLibrary.simpleMessage(
            "Pesan yang dikutip telah kedaluwarsa."),
        "reconnect": MessageLookupByLibrary.simpleMessage("Sambungkan kembali"),
        "reject": MessageLookupByLibrary.simpleMessage("Tolak"),
        "remove": MessageLookupByLibrary.simpleMessage("Hapus"),
        "removedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Berhasil dihapus"),
        "rename": MessageLookupByLibrary.simpleMessage("ganti nama"),
        "reopenTheApp":
            MessageLookupByLibrary.simpleMessage("Buka kembali aplikasi"),
        "requestTimedOutPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Permintaan habis waktu. Silakan coba lagi."),
        "research": MessageLookupByLibrary.simpleMessage("Penelitian"),
        "researchForZdSeconds": m1,
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Atur Ulang Kata Sandi"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Atur Ulang Kata Sandi Anda"),
        "role": MessageLookupByLibrary.simpleMessage("Peran"),
        "save": MessageLookupByLibrary.simpleMessage("Simpan"),
        "saved": MessageLookupByLibrary.simpleMessage("Tersimpan"),
        "saving": MessageLookupByLibrary.simpleMessage("Menyimpan…"),
        "search": MessageLookupByLibrary.simpleMessage("Cari"),
        "searchByNameEmailOrPhone": MessageLookupByLibrary.simpleMessage(
            "Cari berdasarkan nama, email, atau telepon"),
        "searchForACountry":
            MessageLookupByLibrary.simpleMessage("Cari negara"),
        "seconds": MessageLookupByLibrary.simpleMessage("Detik"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage(
                "Sepertinya terputus dari internet"),
        "selectMembers": MessageLookupByLibrary.simpleMessage("Pilih Anggota"),
        "send": MessageLookupByLibrary.simpleMessage("Kirim"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Kirim Pesan"),
        "serverConnectionFailed":
            MessageLookupByLibrary.simpleMessage("Koneksi server gagal."),
        "serviceIsStillStarting":
            MessageLookupByLibrary.simpleMessage("Layanan masih dimulai."),
        "sessionExpiredPleaseLogInAgain": MessageLookupByLibrary.simpleMessage(
            "Sesi berakhir. Silakan masuk lagi."),
        "setNow": MessageLookupByLibrary.simpleMessage("Atur Sekarang"),
        "settings": MessageLookupByLibrary.simpleMessage("Pengaturan"),
        "share": MessageLookupByLibrary.simpleMessage("Bagikan"),
        "signIn": MessageLookupByLibrary.simpleMessage("Masuk"),
        "signOut": MessageLookupByLibrary.simpleMessage("Keluar"),
        "signUp": MessageLookupByLibrary.simpleMessage("Daftar"),
        "someoneMe": MessageLookupByLibrary.simpleMessage("Seseorang @saya"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "Maaf, kapasitas penuh sekarang. Silakan coba lagi besok."),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking":
            MessageLookupByLibrary.simpleMessage("Mulai berbicara"),
        "submit": MessageLookupByLibrary.simpleMessage("Kirim"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry": MessageLookupByLibrary.simpleMessage(
            "Beralih Wi-Fi/data seluler dan coba lagi"),
        "system": MessageLookupByLibrary.simpleMessage("Sistem"),
        "systemBusyPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Sistem sibuk, silakan coba lagi"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("Ambil Foto"),
        "taskStoppedManually": MessageLookupByLibrary.simpleMessage(
            "Tugas dihentikan secara manual"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService":
            MessageLookupByLibrary.simpleMessage("Syarat Layanan"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage(
                "2. Jumlah berlian diperbarui pada tanggal 1 setiap bulan."),
        "theGroupIsCurrentlyInACall": MessageLookupByLibrary.simpleMessage(
            "Grup sedang dalam panggilan..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage(
                "Bahasa yang digunakan di antarmuka"),
        "thePhoneNumberIsInvalid":
            MessageLookupByLibrary.simpleMessage("Nomor telepon tidak valid"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Aplikasi ini memerlukan akses kamera agar Anda bisa mengambil foto untuk foto profil."),
        "thisIsntHelpful":
            MessageLookupByLibrary.simpleMessage("Ini tidak membantu"),
        "thisSlideIsBeingEdited":
            MessageLookupByLibrary.simpleMessage("Slide ini sedang diedit."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "Untuk mengirim gambar agar dapat dikenali, berinteraksi, atau mengatur avatar Anda, izinkan Agnes mengakses kamera Anda"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Untuk mengirim gambar agar dapat dikenali, berinteraksi, atau mengatur avatar Anda, izinkan Agnes mengakses perpustakaan foto Anda"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Untuk menyimpan gambar yang dihasilkan, izinkan Agnes menyimpan gambar ke perpustakaan foto Anda"),
        "transcribing": MessageLookupByLibrary.simpleMessage("Menyalin…"),
        "transcriptionEnabledLoadingData": MessageLookupByLibrary.simpleMessage(
            "Transkripsi diaktifkan. Memuat data…"),
        "tryOneOfThese":
            MessageLookupByLibrary.simpleMessage("Coba salah satu dari ini:"),
        "twoPasswordsAreInconsistent":
            MessageLookupByLibrary.simpleMessage("Dua kata sandi tidak cocok"),
        "update": MessageLookupByLibrary.simpleMessage("Perbarui"),
        "updateFailedPleaseTryAgainLater": MessageLookupByLibrary.simpleMessage(
            "Pembaruan gagal. Silakan coba lagi nanti."),
        "updatePassword":
            MessageLookupByLibrary.simpleMessage("Perbarui Kata Sandi"),
        "updatePasswordSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Kata sandi berhasil diperbarui"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage(
                "Tingkatkan paket Anda untuk melanjutkan: Tingkatkan"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Gunakan 6+ karakter dengan huruf, angka & simbol"),
        "userAccess": MessageLookupByLibrary.simpleMessage("Akses Pengguna"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("Nama Pengguna"),
        "usersWithAccess":
            MessageLookupByLibrary.simpleMessage("Pengguna dengan akses"),
        "usingTool": MessageLookupByLibrary.simpleMessage("Menggunakan Alat"),
        "verificationCode":
            MessageLookupByLibrary.simpleMessage("Kode Verifikasi"),
        "verificationCodeCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Kode verifikasi tidak boleh kosong"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("Lihat"),
        "viewAllComments":
            MessageLookupByLibrary.simpleMessage("Lihat semua komentar"),
        "viewer": MessageLookupByLibrary.simpleMessage("Penampil"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Pembuatan panggilan suara gagal, mungkin karena jaringan tidak stabil. Silakan coba lagi."),
        "voiceCallEnded":
            MessageLookupByLibrary.simpleMessage("Panggilan suara berakhir"),
        "waiting": MessageLookupByLibrary.simpleMessage("Menunggu"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu akses untuk menyimpan gambar atau video yang diunduh ke galeri foto Anda."),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu mengakses mikrofon Anda untuk interaksi suara"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "Kami memerlukan akses ke galeri foto Anda agar Anda bisa memilih foto profil."),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu mengakses galeri foto Anda untuk memilih gambar"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu mengenali ucapan Anda untuk mengubahnya menjadi teks untuk interaksi"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu menyimpan gambar ke galeri foto Anda"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu menggunakan Bluetooth untuk menghubungkan headset Anda untuk interaksi suara"),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage(
                "Kami perlu menggunakan kamera Anda untuk mengambil foto"),
        "welcomeToAgnes":
            MessageLookupByLibrary.simpleMessage("Selamat datang di Agnes"),
        "writeSomething": MessageLookupByLibrary.simpleMessage("Tulis Sesuatu"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("Anda"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage(
                "Anda dapat menjalankan, mengubah, atau menghapus. Bagaimana Anda ingin melanjutkan?"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "Anda dapat mengedit slide secara manual atau melalui percakapan dengan Agnes."),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage(
                "Anda dapat memilih konten dan membagikan masukan Anda."),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "Anda tidak dapat meminimalkan panggilan video karena AGNES tidak diizinkan menggunakan jendela mengambang."),
        "youCreatedTheGroup":
            MessageLookupByLibrary.simpleMessage("Anda membuat grup."),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Anda telah dikeluarkan dari obrolan grup"),
        "youJoinedTheGroup":
            MessageLookupByLibrary.simpleMessage("Anda bergabung dengan grup."),
        "yourCurrentPlan":
            MessageLookupByLibrary.simpleMessage("Paket Anda saat ini"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "Ponsel Anda belum mengizinkan Agnes menggunakan izin jendela mengambang. Agnes tidak akan dapat terus menjalankan tugas di latar belakang untuk Anda."),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Permintaan Anda untuk batas tugas riset telah tercapai."),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Permintaan Anda untuk batas tugas slide telah tercapai."),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Permintaan Anda sudah mencapai batas tugas."),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd":
            MessageLookupByLibrary.simpleMessage("Anda telah mencapai akhir")
      };
}
