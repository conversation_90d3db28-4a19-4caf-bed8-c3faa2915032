// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  static String m0(termsOfService, privacyPolicy) =>
      "Bằng cách đăng nhập, bạn đồng ý với\n${termsOfService} và ${privacyPolicy}.";

  static String m1(value) => "Nghiên cứu trong ${value} giây";

  static String m2(title) =>
      "Xin lỗi, nhiệm vụ ${title} của bạn đã thất bại. Vui lòng thử lại sau.";

  static String m3(value) => "Hỗ trợ tải lên tối đa ${value} hình ảnh.";

  static String m4(remaining_count) =>
      "Nhiệm vụ đã được xóa theo yêu cầu. Số nhiệm vụ đang chờ hiện tại: ${remaining_count}. Bạn có muốn tôi hiển thị tất cả nhiệm vụ đang chờ không?";

  static String m5(total) => "Hiện có ${total} nhiệm vụ đang chờ:";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "Người dùng ${user_name} có hạn ngạch không đủ, slide còn lại: ${slides_remaining_count}, hạn ngạch slide cần thiết: ${slides_plan_count}, nghiên cứu còn lại: ${research_remaining_count}, hạn ngạch nghiên cứu cần thiết: ${research_plan_count}. Bạn có thể chọn các nhiệm vụ có hạn ngạch đủ để thực thi.";

  static String m7(xxx) => "Mã xác minh đã được gửi tới ${xxx}@email.com";

  static String m8(version) => "Phiên bản ${version} hiện đã có";

  static String m9(x) => "${x} thành viên trong cuộc gọi thoại...";

  static String m10(xxx) => "${xxx} đã thay đổi tên nhóm";

  static String m11(xxx) => "${xxx} mời bạn cộng tác chỉnh sửa \"${xxx}\".";

  static String m12(xxx) => "${xxx} mời bạn cộng tác để duyệt \"${xxx}\".";

  static String m13(xxx) => "${xxx} đã bắt đầu cuộc gọi thoại";

  static String m14(xxx, xyz) =>
      "${xxx} mời bạn tham gia cuộc gọi nhóm \"${xyz}\".";

  static String m15(title) =>
      "Nhiệm vụ ${title} của bạn đã hoàn tất thành công!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond": MessageLookupByLibrary.simpleMessage("Về Kim cương"),
        "accept": MessageLookupByLibrary.simpleMessage("Chấp nhận"),
        "account": MessageLookupByLibrary.simpleMessage("Tài khoản"),
        "accountDeletionSuccessful":
            MessageLookupByLibrary.simpleMessage("Xóa tài khoản thành công"),
        "accountNotCreatedYet":
            MessageLookupByLibrary.simpleMessage("Tài khoản chưa được tạo"),
        "accountSignoutSuccessful": MessageLookupByLibrary.simpleMessage(
            "Đăng xuất tài khoản thành công"),
        "activeAccount":
            MessageLookupByLibrary.simpleMessage("Tài khoản đang hoạt động"),
        "add": MessageLookupByLibrary.simpleMessage("Thêm"),
        "addComment": MessageLookupByLibrary.simpleMessage("Thêm bình luận"),
        "addPeopleToChat":
            MessageLookupByLibrary.simpleMessage("Thêm người vào Chat"),
        "addToChat": MessageLookupByLibrary.simpleMessage("Thêm vào Chat"),
        "addUserEmailAndEnterToInvite": MessageLookupByLibrary.simpleMessage(
            "Thêm email người dùng và nhấn \"Enter\" để mời"),
        "addedToGroupSuccessfully":
            MessageLookupByLibrary.simpleMessage("Thêm vào nhóm thành công"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Agnes có thể mắc lỗi. Vui lòng kiểm tra thông tin quan trọng."),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage(
                "@Agnes để có tóm tắt, báo cáo hoặc trang chiếu."),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn":
            MessageLookupByLibrary.simpleMessage("Đồng ý và đăng nhập"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("Thiết kế AI"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("Trang chiếu AI"),
        "album": MessageLookupByLibrary.simpleMessage("Album ảnh"),
        "allMembers": MessageLookupByLibrary.simpleMessage("Tất cả thành viên"),
        "answerCall": MessageLookupByLibrary.simpleMessage("Trả lời cuộc gọi"),
        "appUpdate": MessageLookupByLibrary.simpleMessage("Cập nhật ứng dụng"),
        "archived": MessageLookupByLibrary.simpleMessage("Đã lưu trữ"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Bạn có chắc chắn muốn xóa tài khoản không?"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "Bạn có chắc muốn đăng xuất tài khoản không?"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Hỏi Agnes"),
        "askAgnesToEditSlides": MessageLookupByLibrary.simpleMessage(
            "Nhờ Agnes chỉnh sửa trang trình chiếu"),
        "assignATaskToBegin": MessageLookupByLibrary.simpleMessage(
            "Giao một nhiệm vụ để bắt đầu"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Ít nhất 6 ký tự bao gồm chữ cái, số & ký hiệu"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "Hiện tại, chưa có sự nhất trí về các nhiệm vụ cần thực hiện. Xin tự do thảo luận thêm và đặt câu hỏi nếu có!"),
        "avatar": MessageLookupByLibrary.simpleMessage("Hình đại diện"),
        "back": MessageLookupByLibrary.simpleMessage("Quay lại"),
        "bookmarkRemoved": MessageLookupByLibrary.simpleMessage("Bỏ đánh dấu"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("Đã đánh dấu"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined":
            MessageLookupByLibrary.simpleMessage("Cuộc gọi bị từ chối"),
        "cancel": MessageLookupByLibrary.simpleMessage("Hủy"),
        "canvas": MessageLookupByLibrary.simpleMessage("Bảng vẽ"),
        "chapter": MessageLookupByLibrary.simpleMessage("Chương"),
        "chat": MessageLookupByLibrary.simpleMessage("Trò chuyện"),
        "chatSettings": MessageLookupByLibrary.simpleMessage("Cài đặt Chat"),
        "chooseImage": MessageLookupByLibrary.simpleMessage("Chọn hình ảnh"),
        "close": MessageLookupByLibrary.simpleMessage("Đóng"),
        "code": MessageLookupByLibrary.simpleMessage("Mã"),
        "collect": MessageLookupByLibrary.simpleMessage("thu thập"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("Sắp ra mắt"),
        "comment": MessageLookupByLibrary.simpleMessage("Bình luận"),
        "commenter": MessageLookupByLibrary.simpleMessage("Người bình luận"),
        "commentsTasks":
            MessageLookupByLibrary.simpleMessage("Nhiệm vụ Bình luận"),
        "confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
        "confirmDelete": MessageLookupByLibrary.simpleMessage("Xác nhận xóa"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Xác nhận mật khẩu"),
        "confirmPasswordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Xác nhận mật khẩu không được để trống"),
        "confirmSignOut":
            MessageLookupByLibrary.simpleMessage("Xác nhận đăng xuất"),
        "connect": MessageLookupByLibrary.simpleMessage("Kết nối"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage(
                "Kết nối tai nghe AI và trò chuyện với Agnes bất cứ lúc nào"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage(
                "Kết nối với Agnes VibePods và trò chuyện bất cứ lúc nào."),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Kết nối với bạn bè để bắt đầu trò chuyện nhóm"),
        "connected": MessageLookupByLibrary.simpleMessage("Đã kết nối"),
        "connectionFailed":
            MessageLookupByLibrary.simpleMessage("Kết nối thất bại"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "Nội dung đã được sao chép — hãy dán vào nền tảng yêu thích của bạn!"),
        "continueWithApple":
            MessageLookupByLibrary.simpleMessage("Tiếp tục với Apple"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("Tiếp tục với Google"),
        "continueWithOtherWays":
            MessageLookupByLibrary.simpleMessage("Tiếp tục bằng cách khác"),
        "continueWord": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "copied": MessageLookupByLibrary.simpleMessage("Đã sao chép"),
        "copy": MessageLookupByLibrary.simpleMessage("Sao chép"),
        "cost": MessageLookupByLibrary.simpleMessage("chi phí"),
        "cover": MessageLookupByLibrary.simpleMessage("Bìa"),
        "createANewTaskToGetStarted":
            MessageLookupByLibrary.simpleMessage("Tạo tác vụ mới để bắt đầu"),
        "createAnAccountToSignUpForAgnes": MessageLookupByLibrary.simpleMessage(
            "Tạo tài khoản để đăng ký Agnes"),
        "createGroupChat":
            MessageLookupByLibrary.simpleMessage("Tạo nhóm chat"),
        "currentlyInACall":
            MessageLookupByLibrary.simpleMessage("Đang trong cuộc gọi..."),
        "deepResearch":
            MessageLookupByLibrary.simpleMessage("Nghiên cứu chuyên sâu"),
        "deepresearch": MessageLookupByLibrary.simpleMessage("Nghiên cứu Sâu"),
        "delete": MessageLookupByLibrary.simpleMessage("Xóa"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("Xóa tài khoản"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Xóa Trò chuyện"),
        "doYouWantToExecuteTheseTasks": MessageLookupByLibrary.simpleMessage(
            "Bạn có muốn thực hiện các nhiệm vụ này không?"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                " dường như không phải là người dùng Agnes. Nhập email của họ để gửi lời mời"),
        "done": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Chưa có tài khoản?"),
        "downloading": MessageLookupByLibrary.simpleMessage("Đang tải xuống"),
        "eachNewTaskConsumesOneDiamond": MessageLookupByLibrary.simpleMessage(
            "1. Mỗi nhiệm vụ mới tiêu tốn một viên kim cương."),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. Mỗi người dùng nhận được 30 viên kim cương mỗi tháng."),
        "edit": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "editGroupName":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa tên nhóm"),
        "editor": MessageLookupByLibrary.simpleMessage("Biên tập viên"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("Email không được để trống"),
        "emailFormatIncorrect":
            MessageLookupByLibrary.simpleMessage("Định dạng email không đúng"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage(
                "Nâng cao năng suất và sáng tạo thông qua quyền truy cập mở rộng"),
        "enterConfirmPassword":
            MessageLookupByLibrary.simpleMessage("Nhập mật khẩu xác nhận"),
        "enterGroupName": MessageLookupByLibrary.simpleMessage("Nhập tên nhóm"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Nhập mật khẩu"),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("Nhập mã xác minh"),
        "export": MessageLookupByLibrary.simpleMessage("Xuất"),
        "file": MessageLookupByLibrary.simpleMessage("Tệp"),
        "floatingWindowNotEnabled":
            MessageLookupByLibrary.simpleMessage("Chưa bật cửa sổ nổi"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage(
                "Để trải nghiệm đầy đủ, vui lòng sử dụng phiên bản PC"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage("Quên mật khẩu"),
        "general": MessageLookupByLibrary.simpleMessage("Chung"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Nhận Agnes AI Plus"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Nhận Agnes AI Pro"),
        "giveMeATaskToWorkOn": MessageLookupByLibrary.simpleMessage(
            "Giao cho tôi một nhiệm vụ để làm…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("Đi đến cửa hàng"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage(
                "Hiểu rồi! Tôi đang thực hiện nhiệm vụ. Vui lòng đợi một chút."),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Tạo nhóm thành công"),
        "groupFiles": MessageLookupByLibrary.simpleMessage("Tệp nhóm"),
        "groupName": MessageLookupByLibrary.simpleMessage("Tên nhóm"),
        "groupNameOptional":
            MessageLookupByLibrary.simpleMessage("Tên nhóm (Tùy chọn)"),
        "harmful": MessageLookupByLibrary.simpleMessage("Có hại"),
        "helpUsImprove":
            MessageLookupByLibrary.simpleMessage("Giúp chúng tôi cải thiện"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage(
                "Tôi hỗ trợ tìm kiếm, viết và trả lời"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage(
                "Nếu bạn trả lời cuộc gọi đến, cuộc gọi hiện tại sẽ kết thúc."),
        "imAgnes": MessageLookupByLibrary.simpleMessage("Tôi là Agnes"),
        "inAVoiceCall": MessageLookupByLibrary.simpleMessage("Đang gọi thoại"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("Không chính xác"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Mua hàng trong ứng dụng hiện không khả dụng, vui lòng thử lại sau"),
        "interfaceLanguage":
            MessageLookupByLibrary.simpleMessage("Ngôn ngữ giao diện"),
        "invite": MessageLookupByLibrary.simpleMessage("Mời"),
        "inviteByUsernameEmailOrPhoneNumber":
            MessageLookupByLibrary.simpleMessage(
                "Mời theo tên người dùng, email hoặc số điện thoại"),
        "inviteFriends": MessageLookupByLibrary.simpleMessage("Mời bạn bè"),
        "inviteOthersToCollaborate": MessageLookupByLibrary.simpleMessage(
            "Mời người khác cùng cộng tác"),
        "inviteToCollaborate":
            MessageLookupByLibrary.simpleMessage("Mời để cộng tác"),
        "isEditing": MessageLookupByLibrary.simpleMessage("Đang chỉnh sửa"),
        "join": MessageLookupByLibrary.simpleMessage("Tham gia"),
        "knowMore": MessageLookupByLibrary.simpleMessage("Tìm hiểu thêm"),
        "language": MessageLookupByLibrary.simpleMessage("Tiếng Anh"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Hãy để Agnes giúp bạn tạo định dạng chia sẻ tốt hơn"),
        "letAgnesResolve":
            MessageLookupByLibrary.simpleMessage("Để Agnes xử lý"),
        "linkCopied":
            MessageLookupByLibrary.simpleMessage("Đã sao chép liên kết"),
        "listening": MessageLookupByLibrary.simpleMessage("Đang nghe"),
        "loading": MessageLookupByLibrary.simpleMessage("Đang tải"),
        "makeMeAShareableImagePost": MessageLookupByLibrary.simpleMessage(
            "Tạo cho tôi một bài đăng hình ảnh có thể chia sẻ"),
        "makeMeAnHtmlWebPage": MessageLookupByLibrary.simpleMessage(
            "Tạo cho tôi một trang web HTML"),
        "manageMembers":
            MessageLookupByLibrary.simpleMessage("Quản lý Thành viên"),
        "manuallyEndTheTask":
            MessageLookupByLibrary.simpleMessage("Kết thúc nhiệm vụ thủ công"),
        "markAsResolved":
            MessageLookupByLibrary.simpleMessage("Đánh dấu đã giải quyết"),
        "messageCopied":
            MessageLookupByLibrary.simpleMessage("Thông điệp đã sao chép."),
        "multipleChoices":
            MessageLookupByLibrary.simpleMessage("Nhiều lựa chọn"),
        "myFavorites":
            MessageLookupByLibrary.simpleMessage("Mục ưa thích của tôi"),
        "narrate": MessageLookupByLibrary.simpleMessage("Tường thuật"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Kết nối mạng thất bại. Vui lòng thử lại"),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("Mất kết nối mạng"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("Mạng không khả dụng"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Mật khẩu mới"),
        "newProject": MessageLookupByLibrary.simpleMessage("Dự án mới"),
        "newTask": MessageLookupByLibrary.simpleMessage("Tác vụ mới"),
        "newTitle": MessageLookupByLibrary.simpleMessage("Tiêu đề mới"),
        "noMoreFiles":
            MessageLookupByLibrary.simpleMessage("Không còn tệp nào"),
        "noMoreMessage":
            MessageLookupByLibrary.simpleMessage("Không còn tin nhắn..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Không tìm thấy nhiệm vụ chờ xử lý. Tất cả nhiệm vụ đã hoàn thành!"),
        "notAvailableForPurchase":
            MessageLookupByLibrary.simpleMessage("Không có sẵn để mua"),
        "notConnected": MessageLookupByLibrary.simpleMessage("Chưa kết nối"),
        "notNow": MessageLookupByLibrary.simpleMessage("Không bây giờ"),
        "notifications": MessageLookupByLibrary.simpleMessage("Thông báo"),
        "notifyUserViaEmail": MessageLookupByLibrary.simpleMessage(
            "Thông báo người dùng qua email"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "Được rồi, tôi nhận thấy bạn không có quyền cần thiết. Vui lòng nhờ chủ sở hữu hoặc biên tập viên thực hiện."),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage(
                "Được rồi, tôi sẽ tóm tắt các nhiệm vụ đã thống nhất:"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage(
                "Sau khi xóa, lịch sử trò chuyện nhóm sẽ bị xóa."),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("Thao tác thành công!"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage(
                "Hoặc cho tôi biết nếu bạn có câu hỏi"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage(
                "Các thành viên khác trong nhóm sẽ được thông báo sau khi tên nhóm thay đổi."),
        "outOfDate": MessageLookupByLibrary.simpleMessage("Hết hạn"),
        "owner": MessageLookupByLibrary.simpleMessage("Chủ sở hữu"),
        "pages": MessageLookupByLibrary.simpleMessage("Trang"),
        "passwordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu không được để trống"),
        "pending": MessageLookupByLibrary.simpleMessage("Đang chờ xử lý"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage(
                "Quyền thực hiện nhiệm vụ ở chế độ nền chưa được cấp"),
        "phone": MessageLookupByLibrary.simpleMessage("Điện thoại"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "phoneNumberCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại không được để trống"),
        "photo": MessageLookupByLibrary.simpleMessage("Ảnh"),
        "pleaseSelectASlideToEdit": MessageLookupByLibrary.simpleMessage(
            "Vui lòng chọn một trang trình chiếu để chỉnh sửa."),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng cập nhật lên phiên bản mới nhất để có tính năng và cải tiến mới"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng chờ Agnes hoàn thành nhiệm vụ"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng chờ cho đến khi tải xong"),
        "presenter": MessageLookupByLibrary.simpleMessage("Người thuyết trình"),
        "preview": MessageLookupByLibrary.simpleMessage("Xem trước"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Chính sách Bảo mật"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage(
                "Cung cấp phản hồi bổ sung về câu trả lời này"),
        "quote": MessageLookupByLibrary.simpleMessage("Trích dẫn"),
        "quotedMessageHasExpired": MessageLookupByLibrary.simpleMessage(
            "Thông điệp được trích dẫn đã hết hiệu lực."),
        "reconnect": MessageLookupByLibrary.simpleMessage("Kết nối lại"),
        "reject": MessageLookupByLibrary.simpleMessage("Từ chối"),
        "remove": MessageLookupByLibrary.simpleMessage("Xóa"),
        "removedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Xóa thành công"),
        "rename": MessageLookupByLibrary.simpleMessage("đổi tên"),
        "reopenTheApp": MessageLookupByLibrary.simpleMessage("Mở lại ứng dụng"),
        "requestTimedOutPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Yêu cầu hết thời gian. Vui lòng thử lại"),
        "research": MessageLookupByLibrary.simpleMessage("Nghiên cứu"),
        "researchForZdSeconds": m1,
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Đặt lại mật khẩu"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Đặt lại mật khẩu của bạn"),
        "role": MessageLookupByLibrary.simpleMessage("Vai trò"),
        "save": MessageLookupByLibrary.simpleMessage("Lưu"),
        "saved": MessageLookupByLibrary.simpleMessage("Đã lưu"),
        "saving": MessageLookupByLibrary.simpleMessage("Đang lưu…"),
        "search": MessageLookupByLibrary.simpleMessage("Tìm kiếm"),
        "searchByNameEmailOrPhone": MessageLookupByLibrary.simpleMessage(
            "Tìm theo tên, email hoặc số điện thoại"),
        "searchForACountry":
            MessageLookupByLibrary.simpleMessage("Tìm kiếm quốc gia"),
        "seconds": MessageLookupByLibrary.simpleMessage("Giây"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage(
                "Dường như bị ngắt kết nối internet"),
        "selectMembers":
            MessageLookupByLibrary.simpleMessage("Chọn Thành viên"),
        "send": MessageLookupByLibrary.simpleMessage("Gửi"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Gửi tin nhắn"),
        "serverConnectionFailed":
            MessageLookupByLibrary.simpleMessage("Kết nối máy chủ thất bại."),
        "serviceIsStillStarting":
            MessageLookupByLibrary.simpleMessage("Dịch vụ vẫn đang khởi động."),
        "sessionExpiredPleaseLogInAgain": MessageLookupByLibrary.simpleMessage(
            "Phiên đã hết hạn. Vui lòng đăng nhập lại"),
        "setNow": MessageLookupByLibrary.simpleMessage("Đặt ngay"),
        "settings": MessageLookupByLibrary.simpleMessage("Cài đặt"),
        "share": MessageLookupByLibrary.simpleMessage("Chia sẻ"),
        "signIn": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
        "signOut": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
        "signUp": MessageLookupByLibrary.simpleMessage("Đăng ký"),
        "someoneMe": MessageLookupByLibrary.simpleMessage("Ai đó @tôi"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "Xin lỗi, hiện tại đã đầy. Vui lòng thử lại vào ngày mai."),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking": MessageLookupByLibrary.simpleMessage("Bắt đầu nói"),
        "submit": MessageLookupByLibrary.simpleMessage("Gửi"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry": MessageLookupByLibrary.simpleMessage(
            "Chuyển Wi-Fi/dữ liệu di động và thử lại"),
        "system": MessageLookupByLibrary.simpleMessage("Hệ thống"),
        "systemBusyPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Hệ thống bận, vui lòng thử lại"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("Chụp ảnh"),
        "taskStoppedManually":
            MessageLookupByLibrary.simpleMessage("Nhiệm vụ đã dừng thủ công"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService":
            MessageLookupByLibrary.simpleMessage("Điều khoản Dịch vụ"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage(
                "2. Số kim cương được cập nhật vào ngày 1 hàng tháng."),
        "theGroupIsCurrentlyInACall": MessageLookupByLibrary.simpleMessage(
            "Nhóm hiện đang trong cuộc gọi..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage(
                "Ngôn ngữ được sử dụng trong giao diện"),
        "thePhoneNumberIsInvalid":
            MessageLookupByLibrary.simpleMessage("Số điện thoại không hợp lệ"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Ứng dụng này cần quyền truy cập camera để bạn chụp ảnh làm ảnh đại diện."),
        "thisIsntHelpful":
            MessageLookupByLibrary.simpleMessage("Điều này không hữu ích"),
        "thisSlideIsBeingEdited": MessageLookupByLibrary.simpleMessage(
            "Trang chiếu này đang được chỉnh sửa."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "Để gửi hình ảnh để nhận dạng, tương tác hoặc đặt ảnh đại diện, vui lòng cho phép Agnes truy cập camera của bạn"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Để gửi hình ảnh để nhận dạng, tương tác hoặc đặt ảnh đại diện, vui lòng cho phép Agnes truy cập thư viện ảnh của bạn"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Để lưu hình ảnh được tạo, vui lòng cho phép Agnes lưu hình ảnh vào thư viện ảnh của bạn"),
        "transcribing": MessageLookupByLibrary.simpleMessage("Đang phiên âm…"),
        "transcriptionEnabledLoadingData": MessageLookupByLibrary.simpleMessage(
            "Đã bật phiên âm. Đang tải dữ liệu…"),
        "tryOneOfThese": MessageLookupByLibrary.simpleMessage(
            "Thử một trong những cách sau:"),
        "twoPasswordsAreInconsistent":
            MessageLookupByLibrary.simpleMessage("Hai mật khẩu không khớp"),
        "update": MessageLookupByLibrary.simpleMessage("Cập nhật"),
        "updateFailedPleaseTryAgainLater": MessageLookupByLibrary.simpleMessage(
            "Cập nhật thất bại. Vui lòng thử lại sau."),
        "updatePassword":
            MessageLookupByLibrary.simpleMessage("Cập nhật mật khẩu"),
        "updatePasswordSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Cập nhật mật khẩu thành công"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage(
                "Nâng cấp gói của bạn để tiếp tục: Nâng cấp"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Sử dụng 6+ ký tự với chữ cái, số & ký hiệu"),
        "userAccess":
            MessageLookupByLibrary.simpleMessage("Quyền truy cập người dùng"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("Tên người dùng"),
        "usersWithAccess": MessageLookupByLibrary.simpleMessage(
            "Người dùng có quyền truy cập"),
        "usingTool": MessageLookupByLibrary.simpleMessage("Sử dụng công cụ"),
        "verificationCode": MessageLookupByLibrary.simpleMessage("Mã xác nhận"),
        "verificationCodeCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "Mã xác minh không được để trống"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("Xem"),
        "viewAllComments":
            MessageLookupByLibrary.simpleMessage("Xem tất cả bình luận"),
        "viewer": MessageLookupByLibrary.simpleMessage("Người xem"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Tạo cuộc gọi thoại thất bại, có thể do mạng không ổn định. Vui lòng thử lại."),
        "voiceCallEnded":
            MessageLookupByLibrary.simpleMessage("Cuộc gọi thoại đã kết thúc"),
        "waiting": MessageLookupByLibrary.simpleMessage("Đang chờ"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi cần quyền truy cập để lưu hình ảnh hoặc video tải xuống vào thư viện ảnh."),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi cần truy cập micro của bạn để tương tác bằng giọng nói"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi cần quyền truy cập thư viện ảnh để bạn chọn ảnh đại diện."),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi cần truy cập thư viện ảnh của bạn để chọn hình"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi cần nhận dạng giọng nói của bạn để chuyển đổi thành văn bản phục vụ tương tác"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi cần lưu hình vào thư viện ảnh của bạn"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi cần dùng Bluetooth để kết nối tai nghe của bạn cho tương tác bằng giọng nói"),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi cần dùng máy ảnh của bạn để chụp ảnh"),
        "welcomeToAgnes":
            MessageLookupByLibrary.simpleMessage("Chào mừng đến với Agnes"),
        "writeSomething": MessageLookupByLibrary.simpleMessage("Viết gì đó"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("Bạn"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage(
                "Bạn có thể thực hiện, chỉnh sửa hoặc xóa. Bạn muốn tiếp tục thế nào?"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "Bạn có thể chỉnh sửa thủ công hoặc thông qua trò chuyện với Agnes."),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage(
                "Bạn có thể chọn nội dung và chia sẻ phản hồi."),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "Bạn không thể thu nhỏ cuộc gọi video vì AGNES không được phép dùng cửa sổ nổi."),
        "youCreatedTheGroup":
            MessageLookupByLibrary.simpleMessage("Bạn đã tạo nhóm."),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Bạn đã bị xóa khỏi trò chuyện nhóm"),
        "youJoinedTheGroup":
            MessageLookupByLibrary.simpleMessage("Bạn đã tham gia nhóm."),
        "yourCurrentPlan":
            MessageLookupByLibrary.simpleMessage("Gói hiện tại của bạn"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "Điện thoại của bạn chưa cho phép Agnes sử dụng quyền cửa sổ nổi. Agnes sẽ không thể tiếp tục thực hiện nhiệm vụ ở chế độ nền cho bạn."),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Yêu cầu của bạn về giới hạn nhiệm vụ nghiên cứu đã đạt."),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Yêu cầu của bạn về giới hạn nhiệm vụ slide đã đạt."),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Bạn đã đạt đến giới hạn tác vụ."),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd":
            MessageLookupByLibrary.simpleMessage("Bạn đã đến cuối")
      };
}
