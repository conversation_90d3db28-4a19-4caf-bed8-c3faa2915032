// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a es locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'es';

  static String m0(termsOfService, privacyPolicy) =>
      "Al iniciar sesión, aceptas los\n${termsOfService} y la ${privacyPolicy}.";

  static String m1(value) => "Investigación durante ${value} segundos";

  static String m2(title) =>
      "Lo sentimos, tu tarea ${title} falló. Por favor, inténtalo de nuevo más tarde.";

  static String m3(value) =>
      "Compatible con la carga de hasta ${value} imágenes.";

  static String m4(remaining_count) =>
      "Tareas eliminadas según lo solicitado. Tareas pendientes actuales: ${remaining_count}. ¿Quieres que muestre todas las tareas pendientes?";

  static String m5(total) => "Actualmente hay ${total} tareas pendientes:";

  static String m6(user_name, slides_remaining_count, slides_plan_count,
          research_remaining_count, research_plan_count) =>
      "El usuario ${user_name} no tiene suficiente cuota, diapositivas restantes: ${slides_remaining_count}, cuota requerida para diapositivas: ${slides_plan_count}, investigación restante: ${research_remaining_count}, cuota requerida para investigación: ${research_plan_count}. Puede seleccionar tareas con suficiente cuota para ejecutar.";

  static String m7(xxx) => "Código de verificación enviado a ${xxx}@email.com";

  static String m8(version) => "La versión ${version} ya está disponible";

  static String m9(x) => "${x} miembros en voz...";

  static String m10(xxx) => "${xxx} cambió el nombre del grupo";

  static String m11(xxx) =>
      "${xxx} te ha invitado a colaborar en la edición de \"${xxx}\".";

  static String m12(xxx) =>
      "${xxx} te ha invitado a colaborar en la revisión de \"${xxx}\".";

  static String m13(xxx) => "${xxx} ha iniciado una llamada de voz";

  static String m14(xxx, xyz) =>
      "${xxx} te invita a unirse a la llamada grupal \"${xyz}\".";

  static String m15(title) => "¡Tu tarea ${title} se ha completado con éxito!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "aboutDiamond":
            MessageLookupByLibrary.simpleMessage("Acerca de Diamantes"),
        "accept": MessageLookupByLibrary.simpleMessage("Acceptar"),
        "account": MessageLookupByLibrary.simpleMessage("Cuenta"),
        "accountDeletionSuccessful": MessageLookupByLibrary.simpleMessage(
            "Eliminación de cuenta exitosa"),
        "accountNotCreatedYet":
            MessageLookupByLibrary.simpleMessage("Cuenta no creada todavía"),
        "accountSignoutSuccessful":
            MessageLookupByLibrary.simpleMessage("Cierre de sesión exitoso"),
        "activeAccount": MessageLookupByLibrary.simpleMessage("Cuenta activa"),
        "add": MessageLookupByLibrary.simpleMessage("Agregar"),
        "addComment": MessageLookupByLibrary.simpleMessage("Añadir comentario"),
        "addPeopleToChat":
            MessageLookupByLibrary.simpleMessage("Agregar personas al chat"),
        "addToChat": MessageLookupByLibrary.simpleMessage("Agregar al chat"),
        "addUserEmailAndEnterToInvite": MessageLookupByLibrary.simpleMessage(
            "Añadir correo electrónico del usuario y presiona \"enter\" para invitar"),
        "addedToGroupSuccessfully":
            MessageLookupByLibrary.simpleMessage("Agregado al grupo con éxito"),
        "agnesCanMakeMistakesCheckImportantInfo":
            MessageLookupByLibrary.simpleMessage(
                "Agnes puede cometer errores. Revisa la información importante."),
        "agnesForSummariesReportsOrSlides":
            MessageLookupByLibrary.simpleMessage(
                "@Agnes para resúmenes, informes o diapositivas."),
        "agnesVibepods": MessageLookupByLibrary.simpleMessage("Agnes VibePods"),
        "agreeAndLogIn":
            MessageLookupByLibrary.simpleMessage("Aceptar y acceder"),
        "aiDesign": MessageLookupByLibrary.simpleMessage("Diseño de IA"),
        "aiSlides": MessageLookupByLibrary.simpleMessage("Diapositivas IA"),
        "album": MessageLookupByLibrary.simpleMessage("Álbum"),
        "allMembers":
            MessageLookupByLibrary.simpleMessage("Todos los miembros"),
        "answerCall": MessageLookupByLibrary.simpleMessage("Contestar llamada"),
        "appUpdate":
            MessageLookupByLibrary.simpleMessage("Actualización de la App"),
        "archived": MessageLookupByLibrary.simpleMessage("Archivado"),
        "areYouSureYouWantToDeleteYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "¿Seguro que quieres eliminar tu cuenta?"),
        "areYouSureYouWantToSignOutYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "¿Estás seguro de que deseas cerrar sesión en tu cuenta?"),
        "askAgnes": MessageLookupByLibrary.simpleMessage("Preguntar a Agnes"),
        "askAgnesToEditSlides": MessageLookupByLibrary.simpleMessage(
            "Pídele a Agnes que edite las diapositivas"),
        "assignATaskToBegin": MessageLookupByLibrary.simpleMessage(
            "Asigna una tarea para comenzar"),
        "atLeastSixCharactersLongWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Al menos seis caracteres con letras, números y símbolos"),
        "atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny":
            MessageLookupByLibrary.simpleMessage(
                "Actualmente, no hay consenso sobre las tareas por realizar. No dudes en discutir más a fondo y hacer preguntas si las tienes!"),
        "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
        "back": MessageLookupByLibrary.simpleMessage("Atrás"),
        "bookmarkRemoved":
            MessageLookupByLibrary.simpleMessage("Marcador eliminado"),
        "bookmarked": MessageLookupByLibrary.simpleMessage("Marcado"),
        "bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy": m0,
        "callDeclined":
            MessageLookupByLibrary.simpleMessage("Llamada rechazada"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
        "canvas": MessageLookupByLibrary.simpleMessage("Lienzo"),
        "chapter": MessageLookupByLibrary.simpleMessage("Capítulo"),
        "chat": MessageLookupByLibrary.simpleMessage("Chat"),
        "chatSettings":
            MessageLookupByLibrary.simpleMessage("Configuración del chat"),
        "chooseImage": MessageLookupByLibrary.simpleMessage("Elegir imagen"),
        "close": MessageLookupByLibrary.simpleMessage("Cerrar"),
        "code": MessageLookupByLibrary.simpleMessage("Código"),
        "collect": MessageLookupByLibrary.simpleMessage("recoger"),
        "comingSoon": MessageLookupByLibrary.simpleMessage("Próximamente"),
        "comment": MessageLookupByLibrary.simpleMessage("Comentar"),
        "commenter": MessageLookupByLibrary.simpleMessage("Comentarista"),
        "commentsTasks":
            MessageLookupByLibrary.simpleMessage("Tareas de Comentarios"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirmar"),
        "confirmDelete":
            MessageLookupByLibrary.simpleMessage("Confirmar eliminación"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirmar contraseña"),
        "confirmPasswordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "La confirmación de contraseña no puede estar vacía"),
        "confirmSignOut":
            MessageLookupByLibrary.simpleMessage("Confirmar cierre de sesión"),
        "connect": MessageLookupByLibrary.simpleMessage("Conectar"),
        "connectTheAiHeadsetAndChatWithAgnesAtAnyTime":
            MessageLookupByLibrary.simpleMessage(
                "Conecta el headset de IA y chatea con Agnes en cualquier momento"),
        "connectToAgnesVibepodsAndChatAnytime":
            MessageLookupByLibrary.simpleMessage(
                "Conéctate a Agnes VibePods y chatea en cualquier momento."),
        "connectWithFriendsToStartAGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Conéctate con amigos para iniciar un chat grupal"),
        "connected": MessageLookupByLibrary.simpleMessage("Conectado"),
        "connectionFailed":
            MessageLookupByLibrary.simpleMessage("Conexión fallida"),
        "contentCopiedgoAheadAndPasteItOnYourFavoritePlatform":
            MessageLookupByLibrary.simpleMessage(
                "Contenido copiado: pégalo en tu plataforma favorita."),
        "continueWithApple":
            MessageLookupByLibrary.simpleMessage("Continuar con Apple"),
        "continueWithGoogle":
            MessageLookupByLibrary.simpleMessage("Continuar con Google"),
        "continueWithOtherWays":
            MessageLookupByLibrary.simpleMessage("Continuar con otras formas"),
        "continueWord": MessageLookupByLibrary.simpleMessage("Continuar"),
        "copied": MessageLookupByLibrary.simpleMessage("Copiado"),
        "copy": MessageLookupByLibrary.simpleMessage("Copiar"),
        "cost": MessageLookupByLibrary.simpleMessage("costo"),
        "cover": MessageLookupByLibrary.simpleMessage("Portada"),
        "createANewTaskToGetStarted": MessageLookupByLibrary.simpleMessage(
            "Crear una nueva tarea para comenzar"),
        "createAnAccountToSignUpForAgnes": MessageLookupByLibrary.simpleMessage(
            "Crea una cuenta para registrarte en Agnes"),
        "createGroupChat":
            MessageLookupByLibrary.simpleMessage("Crear chat grupal"),
        "currentlyInACall": MessageLookupByLibrary.simpleMessage(
            "Actualmente en una llamada..."),
        "deepResearch":
            MessageLookupByLibrary.simpleMessage("Investigación profunda"),
        "deepresearch":
            MessageLookupByLibrary.simpleMessage("Investigación Profunda"),
        "delete": MessageLookupByLibrary.simpleMessage("Eliminar"),
        "deleteAccount":
            MessageLookupByLibrary.simpleMessage("Eliminar cuenta"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Eliminar chat"),
        "doYouWantToExecuteTheseTasks": MessageLookupByLibrary.simpleMessage(
            "¿Quieres ejecutar estas tareas?"),
        "doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite":
            MessageLookupByLibrary.simpleMessage(
                " no parece ser un usuario de Agnes. Introduzca su correo electrónico para enviar una invitación"),
        "done": MessageLookupByLibrary.simpleMessage("Hecho"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("¿No tienes cuenta?"),
        "downloading": MessageLookupByLibrary.simpleMessage("Descargando"),
        "eachNewTaskConsumesOneDiamond": MessageLookupByLibrary.simpleMessage(
            "1. Cada nueva tarea consume un diamante."),
        "eachUserReceivesDiamondsPerMonth":
            MessageLookupByLibrary.simpleMessage(
                "3. Cada usuario recibe 30 diamantes por mes."),
        "edit": MessageLookupByLibrary.simpleMessage("Editar"),
        "editGroupName":
            MessageLookupByLibrary.simpleMessage("Editar nombre del grupo"),
        "editor": MessageLookupByLibrary.simpleMessage("Editor"),
        "email": MessageLookupByLibrary.simpleMessage("Correo electrónico"),
        "emailCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "El correo electrónico no puede estar vacío"),
        "emailFormatIncorrect": MessageLookupByLibrary.simpleMessage(
            "El formato del correo es incorrecto"),
        "emaildomaincom":
            MessageLookupByLibrary.simpleMessage("<EMAIL>"),
        "enhanceProductivityAndCreativityThroughExtendedAccess":
            MessageLookupByLibrary.simpleMessage(
                "Mejora la productividad y creatividad mediante acceso extendido"),
        "enterConfirmPassword": MessageLookupByLibrary.simpleMessage(
            "Introducir contraseña de confirmación"),
        "enterGroupName":
            MessageLookupByLibrary.simpleMessage("Ingrese el nombre del grupo"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("Introducir contraseña"),
        "enterVerificationCode": MessageLookupByLibrary.simpleMessage(
            "Introducir código de verificación"),
        "export": MessageLookupByLibrary.simpleMessage("Exportar"),
        "file": MessageLookupByLibrary.simpleMessage("Archivo"),
        "floatingWindowNotEnabled": MessageLookupByLibrary.simpleMessage(
            "Ventana flotante no habilitada"),
        "forTheFullExperiencePleaseUseThePcVersion":
            MessageLookupByLibrary.simpleMessage(
                "Para la experiencia completa, utiliza la versión de PC"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("¿Olvidaste tu contraseña?"),
        "general": MessageLookupByLibrary.simpleMessage("General"),
        "getAgnesAiPlus":
            MessageLookupByLibrary.simpleMessage("Obtener Agnes AI Plus"),
        "getAgnesAiPro":
            MessageLookupByLibrary.simpleMessage("Obtener Agnes AI Pro"),
        "giveMeATaskToWorkOn": MessageLookupByLibrary.simpleMessage(
            "Dame una tarea en la que trabajar…"),
        "goToShop": MessageLookupByLibrary.simpleMessage("Ir a la tienda"),
        "gotItImWorkingOnTheTaskPleaseWaitAMoment":
            MessageLookupByLibrary.simpleMessage(
                "¡Entendido! Estoy trabajando en la tarea. Por favor espera un momento."),
        "groupCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Grupo creado con éxito"),
        "groupFiles":
            MessageLookupByLibrary.simpleMessage("Archivos del grupo"),
        "groupName": MessageLookupByLibrary.simpleMessage("Nombre del grupo"),
        "groupNameOptional":
            MessageLookupByLibrary.simpleMessage("Nombre del grupo (Opcional)"),
        "harmful": MessageLookupByLibrary.simpleMessage("Perjudicial"),
        "helpUsImprove":
            MessageLookupByLibrary.simpleMessage("Ayúdanos a mejorar"),
        "iAssistWithSearchWritingAndAnswers":
            MessageLookupByLibrary.simpleMessage(
                "Te ayudo con búsquedas, redacción y respuestas"),
        "ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded":
            MessageLookupByLibrary.simpleMessage(
                "Si respondes a la llamada entrante, tu llamada actual finalizará."),
        "imAgnes": MessageLookupByLibrary.simpleMessage("Soy Agnes"),
        "inAVoiceCall":
            MessageLookupByLibrary.simpleMessage("En una llamada de voz"),
        "inaccurate": MessageLookupByLibrary.simpleMessage("Inexacto"),
        "inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Las compras dentro de la app no están disponibles actualmente, inténtalo más tarde"),
        "interfaceLanguage":
            MessageLookupByLibrary.simpleMessage("Idioma de la interfaz"),
        "invite": MessageLookupByLibrary.simpleMessage("Invitar"),
        "inviteByUsernameEmailOrPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "Invitar por nombre de usuario, correo electrónico o número de teléfono"),
        "inviteFriends": MessageLookupByLibrary.simpleMessage("Invitar amigos"),
        "inviteOthersToCollaborate":
            MessageLookupByLibrary.simpleMessage("Invitar a otros a colaborar"),
        "inviteToCollaborate":
            MessageLookupByLibrary.simpleMessage("Invitar a colaborar"),
        "isEditing": MessageLookupByLibrary.simpleMessage("está editando"),
        "join": MessageLookupByLibrary.simpleMessage("Unirse"),
        "knowMore": MessageLookupByLibrary.simpleMessage("Saber más"),
        "language": MessageLookupByLibrary.simpleMessage("Inglés"),
        "letAgnesHelpYouCreateABetterSharingFormat":
            MessageLookupByLibrary.simpleMessage(
                "Deja que Agnes te ayude a crear un mejor formato de compartir"),
        "letAgnesResolve":
            MessageLookupByLibrary.simpleMessage("Deja que Agnes lo resuelva"),
        "linkCopied": MessageLookupByLibrary.simpleMessage("Enlace copiado"),
        "listening": MessageLookupByLibrary.simpleMessage("Escuchando"),
        "loading": MessageLookupByLibrary.simpleMessage("Cargando"),
        "makeMeAShareableImagePost": MessageLookupByLibrary.simpleMessage(
            "Hazme una publicación de imagen para compartir"),
        "makeMeAnHtmlWebPage":
            MessageLookupByLibrary.simpleMessage("Hazme una página web HTML"),
        "manageMembers":
            MessageLookupByLibrary.simpleMessage("Administrar miembros"),
        "manuallyEndTheTask": MessageLookupByLibrary.simpleMessage(
            "Finalizar la tarea manualmente"),
        "markAsResolved":
            MessageLookupByLibrary.simpleMessage("Marcar como resuelto"),
        "messageCopied":
            MessageLookupByLibrary.simpleMessage("Mensaje Copiado."),
        "multipleChoices":
            MessageLookupByLibrary.simpleMessage("Múltiples opciones"),
        "myFavorites": MessageLookupByLibrary.simpleMessage("Mis favoritos"),
        "narrate": MessageLookupByLibrary.simpleMessage("Narrar"),
        "networkConnectionFailedPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Falló la conexión de red. Por favor, inténtalo de nuevo"),
        "networkConnectionLost":
            MessageLookupByLibrary.simpleMessage("Conexión de red perdida"),
        "networkUnavailable":
            MessageLookupByLibrary.simpleMessage("Red no disponible"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Nueva contraseña"),
        "newProject": MessageLookupByLibrary.simpleMessage("Nuevo proyecto"),
        "newTask": MessageLookupByLibrary.simpleMessage("Nueva tarea"),
        "newTitle": MessageLookupByLibrary.simpleMessage("Nuevo título"),
        "noMoreFiles":
            MessageLookupByLibrary.simpleMessage("No hay más archivos"),
        "noMoreMessage":
            MessageLookupByLibrary.simpleMessage("No hay más mensajes..."),
        "noPendingTasksFoundAllTasksHaveBeenCompleted":
            MessageLookupByLibrary.simpleMessage(
                "No se encontraron tareas pendientes. ¡Todas las tareas han sido completadas!"),
        "notAvailableForPurchase": MessageLookupByLibrary.simpleMessage(
            "No disponible para la compra"),
        "notConnected": MessageLookupByLibrary.simpleMessage("No conectado"),
        "notNow": MessageLookupByLibrary.simpleMessage("Ahora no"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notificaciones"),
        "notifyUserViaEmail": MessageLookupByLibrary.simpleMessage(
            "Notificar al usuario por correo electrónico"),
        "okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt":
            MessageLookupByLibrary.simpleMessage(
                "Está bien, noté que no tienes los permisos requeridos. Por favor pide al propietario o a un editor que lo haga."),
        "okayIllSummarizeTheConsensusTasks":
            MessageLookupByLibrary.simpleMessage(
                "De acuerdo, resumiré las tareas de consenso:"),
        "onceDeletedGroupChatHistoryWillBeCleared":
            MessageLookupByLibrary.simpleMessage(
                "Una vez eliminado, el historial de chat del grupo se borrará."),
        "operationSuccessful":
            MessageLookupByLibrary.simpleMessage("¡Operación exitosa!"),
        "orLetMeKnowIfYouHaveAnyQuestions":
            MessageLookupByLibrary.simpleMessage(
                "O avísame si tienes alguna pregunta"),
        "otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged":
            MessageLookupByLibrary.simpleMessage(
                "Los demás miembros del grupo serán notificados después de cambiar el nombre del grupo."),
        "outOfDate": MessageLookupByLibrary.simpleMessage("Desactualizado"),
        "owner": MessageLookupByLibrary.simpleMessage("Propietario"),
        "pages": MessageLookupByLibrary.simpleMessage("Páginas"),
        "passwordCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "La contraseña no puede estar vacía"),
        "pending": MessageLookupByLibrary.simpleMessage("Pendiente"),
        "permissionToExecuteTasksInTheBackgroundIsNotGranted":
            MessageLookupByLibrary.simpleMessage(
                "No se ha otorgado permiso para ejecutar tareas en segundo plano"),
        "phone": MessageLookupByLibrary.simpleMessage("Teléfono"),
        "phoneNumber":
            MessageLookupByLibrary.simpleMessage("Número de teléfono"),
        "phoneNumberCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "El número de teléfono no puede estar vacío"),
        "photo": MessageLookupByLibrary.simpleMessage("Foto"),
        "pleaseSelectASlideToEdit": MessageLookupByLibrary.simpleMessage(
            "Selecciona una diapositiva para editar."),
        "pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements":
            MessageLookupByLibrary.simpleMessage(
                "Actualiza a la última versión para nuevas funciones y mejoras"),
        "pleaseWaitForAgnesToCompleteTheTask":
            MessageLookupByLibrary.simpleMessage(
                "Por favor espera a que Agnes complete la tarea"),
        "pleaseWaitUntilTheDownloadIsCompleted":
            MessageLookupByLibrary.simpleMessage(
                "Por favor espera hasta que se complete la descarga"),
        "presenter": MessageLookupByLibrary.simpleMessage("Presentador"),
        "preview": MessageLookupByLibrary.simpleMessage("Vista previa"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Política de Privacidad"),
        "provideAdditionalFeedbackOnThisAnswer":
            MessageLookupByLibrary.simpleMessage(
                "Proporcione comentarios adicionales sobre esta respuesta"),
        "quote": MessageLookupByLibrary.simpleMessage("Cita"),
        "quotedMessageHasExpired":
            MessageLookupByLibrary.simpleMessage("Mensaje citado ha expirado."),
        "reconnect": MessageLookupByLibrary.simpleMessage("Reconectar"),
        "reject": MessageLookupByLibrary.simpleMessage("Rechazar"),
        "remove": MessageLookupByLibrary.simpleMessage("Eliminar"),
        "removedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Eliminado con éxito"),
        "rename": MessageLookupByLibrary.simpleMessage("renombrar"),
        "reopenTheApp": MessageLookupByLibrary.simpleMessage(
            "Vuelve a abrir la aplicación"),
        "requestTimedOutPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "La solicitud expiró. Por favor, inténtalo de nuevo"),
        "research": MessageLookupByLibrary.simpleMessage("Investigación"),
        "researchForZdSeconds": m1,
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Restablecer contraseña"),
        "resetYourPassword":
            MessageLookupByLibrary.simpleMessage("Restablece tu contraseña"),
        "role": MessageLookupByLibrary.simpleMessage("Rol"),
        "save": MessageLookupByLibrary.simpleMessage("Guardar"),
        "saved": MessageLookupByLibrary.simpleMessage("Guardado"),
        "saving": MessageLookupByLibrary.simpleMessage("Guardando…"),
        "search": MessageLookupByLibrary.simpleMessage("Buscar"),
        "searchByNameEmailOrPhone": MessageLookupByLibrary.simpleMessage(
            "Buscar por nombre, correo electrónico o teléfono"),
        "searchForACountry":
            MessageLookupByLibrary.simpleMessage("Buscar un país"),
        "seconds": MessageLookupByLibrary.simpleMessage("Segundos"),
        "seemsToBeDisconnectedFromTheInternet":
            MessageLookupByLibrary.simpleMessage(
                "Parece estar desconectado de internet"),
        "selectMembers":
            MessageLookupByLibrary.simpleMessage("Seleccionar Miembros"),
        "send": MessageLookupByLibrary.simpleMessage("Enviar"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Enviar mensaje"),
        "serverConnectionFailed": MessageLookupByLibrary.simpleMessage(
            "Fallo la conexión al servidor."),
        "serviceIsStillStarting": MessageLookupByLibrary.simpleMessage(
            "El servicio aún se está iniciando."),
        "sessionExpiredPleaseLogInAgain": MessageLookupByLibrary.simpleMessage(
            "La sesión ha expirado. Por favor, inicie sesión de nuevo."),
        "setNow": MessageLookupByLibrary.simpleMessage("Configurar ahora"),
        "settings": MessageLookupByLibrary.simpleMessage("Configuración"),
        "share": MessageLookupByLibrary.simpleMessage("Compartir"),
        "signIn": MessageLookupByLibrary.simpleMessage("Iniciar sesión"),
        "signOut": MessageLookupByLibrary.simpleMessage("Cerrar sesión"),
        "signUp": MessageLookupByLibrary.simpleMessage("Registrarse"),
        "someoneMe":
            MessageLookupByLibrary.simpleMessage("Alguien me @mencionó"),
        "sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow":
            MessageLookupByLibrary.simpleMessage(
                "Lo siento, estamos a plena capacidad ahora. Por favor, inténtelo mañana."),
        "sorryYourTitleTaskFailedPleaseTryAgainLater": m2,
        "startSpeaking":
            MessageLookupByLibrary.simpleMessage("Comenzar a hablar"),
        "submit": MessageLookupByLibrary.simpleMessage("Enviar"),
        "supportsUploadingUpToZdImages": m3,
        "switchWifimobileDataAndRetry": MessageLookupByLibrary.simpleMessage(
            "Cambia Wi-Fi/datos móviles e inténtalo de nuevo"),
        "system": MessageLookupByLibrary.simpleMessage("Sistema"),
        "systemBusyPleaseTryAgain": MessageLookupByLibrary.simpleMessage(
            "Sistema ocupado, inténtelo de nuevo"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("Tomar foto"),
        "taskStoppedManually":
            MessageLookupByLibrary.simpleMessage("Tarea detenida manualmente"),
        "tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks":
            m4,
        "termsOfService":
            MessageLookupByLibrary.simpleMessage("Términos de Servicio"),
        "theDiamondCountUpdatesOnThe1stOfEveryMonth":
            MessageLookupByLibrary.simpleMessage(
                "2. El recuento de diamantes se actualiza el día 1 de cada mes."),
        "theGroupIsCurrentlyInACall": MessageLookupByLibrary.simpleMessage(
            "El grupo está actualmente en una llamada..."),
        "theLanguageUsedInTheUserInterface":
            MessageLookupByLibrary.simpleMessage(
                "El idioma utilizado en la interfaz"),
        "thePhoneNumberIsInvalid": MessageLookupByLibrary.simpleMessage(
            "El número de teléfono no es válido"),
        "thereAreCurrentlyTotalPendingTasksTask": m5,
        "thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Esta aplicación necesita acceso a la cámara para que puedas tomar una foto de perfil."),
        "thisIsntHelpful":
            MessageLookupByLibrary.simpleMessage("Esto no es útil"),
        "thisSlideIsBeingEdited": MessageLookupByLibrary.simpleMessage(
            "Esta diapositiva está siendo editada."),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera":
            MessageLookupByLibrary.simpleMessage(
                "Para permitirte enviar imágenes para reconocimiento, interacción o configurar tu avatar, permite que Agnes acceda a tu cámara"),
        "toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Para permitirte enviar imágenes para reconocimiento, interacción o configurar tu avatar, permite que Agnes acceda a tu biblioteca de fotos"),
        "toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Para guardar las imágenes generadas, permite que Agnes las guarde en tu biblioteca de fotos"),
        "transcribing": MessageLookupByLibrary.simpleMessage("Transcribiendo…"),
        "transcriptionEnabledLoadingData": MessageLookupByLibrary.simpleMessage(
            "Transcripción activada. Cargando datos…"),
        "tryOneOfThese": MessageLookupByLibrary.simpleMessage(
            "Prueba una de estas opciones:"),
        "twoPasswordsAreInconsistent": MessageLookupByLibrary.simpleMessage(
            "Las dos contraseñas no coinciden"),
        "update": MessageLookupByLibrary.simpleMessage("Actualizar"),
        "updateFailedPleaseTryAgainLater": MessageLookupByLibrary.simpleMessage(
            "Actualización fallida. Por favor, inténtelo de nuevo más tarde."),
        "updatePassword":
            MessageLookupByLibrary.simpleMessage("Actualizar contraseña"),
        "updatePasswordSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Contraseña actualizada con éxito"),
        "upgradeYourPlanToContinueUpgrade":
            MessageLookupByLibrary.simpleMessage(
                "Mejora tu plan para continuar: Actualizar"),
        "useCharactersWithLettersNumbersSymbols":
            MessageLookupByLibrary.simpleMessage(
                "Usa 6+ caracteres con letras, números y símbolos"),
        "userAccess": MessageLookupByLibrary.simpleMessage("Acceso de usuario"),
        "userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute":
            m6,
        "username": MessageLookupByLibrary.simpleMessage("Nombre de usuario"),
        "usersWithAccess":
            MessageLookupByLibrary.simpleMessage("Usuarios con acceso"),
        "usingTool": MessageLookupByLibrary.simpleMessage("Usando herramienta"),
        "verificationCode":
            MessageLookupByLibrary.simpleMessage("Código de verificación"),
        "verificationCodeCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
            "El código de verificación no puede estar vacío"),
        "verificationCodeSentToXxxemailcom": m7,
        "versionIsNowAvailable": m8,
        "view": MessageLookupByLibrary.simpleMessage("Ver"),
        "viewAllComments":
            MessageLookupByLibrary.simpleMessage("Ver todos los comentarios"),
        "viewer": MessageLookupByLibrary.simpleMessage("Visualizador"),
        "voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Error al crear la llamada de voz, posiblemente por inestabilidad de la red. Inténtalo de nuevo."),
        "voiceCallEnded":
            MessageLookupByLibrary.simpleMessage("La llamada de voz terminó"),
        "waiting": MessageLookupByLibrary.simpleMessage("Esperando"),
        "weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Necesitamos acceso para guardar imágenes o videos descargados en tu galería."),
        "weNeedAccessToYourMicrophoneForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Necesitamos acceder a tu micrófono para la interacción por voz"),
        "weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile":
            MessageLookupByLibrary.simpleMessage(
                "Necesitamos acceso a tu galería para que selecciones una foto de perfil."),
        "weNeedToAccessYourPhotoLibraryToSelectImages":
            MessageLookupByLibrary.simpleMessage(
                "Necesitamos acceder a tu galería para seleccionar imágenes"),
        "weNeedToRecognizeYourSpeechToConvertItToTextForInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Necesitamos reconocer tu voz para convertirla en texto para la interacción"),
        "weNeedToSaveImagesToYourPhotoLibrary":
            MessageLookupByLibrary.simpleMessage(
                "Necesitamos guardar imágenes en tu galería"),
        "weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction":
            MessageLookupByLibrary.simpleMessage(
                "Necesitamos usar Bluetooth para conectar tus auriculares para la interacción por voz"),
        "weNeedToUseYourCameraToTakePhotos":
            MessageLookupByLibrary.simpleMessage(
                "Necesitamos usar tu cámara para tomar fotos"),
        "welcomeToAgnes":
            MessageLookupByLibrary.simpleMessage("Bienvenido a Agnes"),
        "writeSomething": MessageLookupByLibrary.simpleMessage("Escribir algo"),
        "xMembersInVoice": m9,
        "xxxChangedTheGroupName": m10,
        "xxxHasInvitedYouToCollaborateOnEditingXxx": m11,
        "xxxHasInvitedYouToCollaborateOnReviewingXxx": m12,
        "xxxHasStartedAVoiceCall": m13,
        "xxxInvitesYouToJoinTheXyzGroupCall": m14,
        "you": MessageLookupByLibrary.simpleMessage("Tú"),
        "youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed":
            MessageLookupByLibrary.simpleMessage(
                "Puedes ejecutar, modificar o eliminar. ¿Cómo deseas proceder?"),
        "youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes":
            MessageLookupByLibrary.simpleMessage(
                "Puedes editar manualmente las diapositivas o hacerlo mediante conversación con Agnes."),
        "youCanSelectContentAndShareYourFeedback":
            MessageLookupByLibrary.simpleMessage(
                "Puedes seleccionar contenido y compartir tus comentarios."),
        "youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows":
            MessageLookupByLibrary.simpleMessage(
                "No puedes minimizar una videollamada porque AGNES no está autorizado para usar ventanas flotantes."),
        "youCreatedTheGroup":
            MessageLookupByLibrary.simpleMessage("Creaste el grupo."),
        "youHaveBeenRemovedFromTheGroupChat":
            MessageLookupByLibrary.simpleMessage(
                "Has sido eliminado del chat grupal"),
        "youJoinedTheGroup":
            MessageLookupByLibrary.simpleMessage("Te uniste al grupo."),
        "yourCurrentPlan":
            MessageLookupByLibrary.simpleMessage("Tu plan actual"),
        "yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou":
            MessageLookupByLibrary.simpleMessage(
                "Su teléfono no ha autorizado a Agnes a usar el permiso de ventana flotante. Agnes no podrá continuar realizando tareas en segundo plano para usted."),
        "yourRequestForTheResearchTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Se ha alcanzado el límite de solicitudes para la tarea de investigación."),
        "yourRequestForTheSlideTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Se ha alcanzado el límite de solicitudes para la tarea de diapositivas."),
        "yourRequestForThisTaskLimitHasBeenReached":
            MessageLookupByLibrary.simpleMessage(
                "Has alcanzado el límite de esta tarea."),
        "yourTitleTaskHasBeenSuccessfullyCompleted": m15,
        "youveReachedTheEnd":
            MessageLookupByLibrary.simpleMessage("Has llegado al final")
      };
}
