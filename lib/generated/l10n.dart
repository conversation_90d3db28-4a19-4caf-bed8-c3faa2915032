// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `About Diamond`
  String get aboutDiamond {
    return Intl.message(
      'About Diamond',
      name: 'aboutDiamond',
      desc: '',
      args: [],
    );
  }

  /// `Accept`
  String get accept {
    return Intl.message(
      'Accept',
      name: 'accept',
      desc: '',
      args: [],
    );
  }

  /// `Account`
  String get account {
    return Intl.message(
      'Account',
      name: 'account',
      desc: '',
      args: [],
    );
  }

  /// `Account deletion successful`
  String get accountDeletionSuccessful {
    return Intl.message(
      'Account deletion successful',
      name: 'accountDeletionSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Account not created yet`
  String get accountNotCreatedYet {
    return Intl.message(
      'Account not created yet',
      name: 'accountNotCreatedYet',
      desc: '',
      args: [],
    );
  }

  /// `Account signout successful`
  String get accountSignoutSuccessful {
    return Intl.message(
      'Account signout successful',
      name: 'accountSignoutSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Active Account`
  String get activeAccount {
    return Intl.message(
      'Active Account',
      name: 'activeAccount',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get add {
    return Intl.message(
      'Add',
      name: 'add',
      desc: '',
      args: [],
    );
  }

  /// `Add Comment`
  String get addComment {
    return Intl.message(
      'Add Comment',
      name: 'addComment',
      desc: '',
      args: [],
    );
  }

  /// `Add People To Chat`
  String get addPeopleToChat {
    return Intl.message(
      'Add People To Chat',
      name: 'addPeopleToChat',
      desc: '',
      args: [],
    );
  }

  /// `Add to Chat`
  String get addToChat {
    return Intl.message(
      'Add to Chat',
      name: 'addToChat',
      desc: '',
      args: [],
    );
  }

  /// `Add user email and "enter" to invite`
  String get addUserEmailAndEnterToInvite {
    return Intl.message(
      'Add user email and "enter" to invite',
      name: 'addUserEmailAndEnterToInvite',
      desc: '',
      args: [],
    );
  }

  /// `Added to group successfully`
  String get addedToGroupSuccessfully {
    return Intl.message(
      'Added to group successfully',
      name: 'addedToGroupSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Agnes can make mistakes. Check important info.`
  String get agnesCanMakeMistakesCheckImportantInfo {
    return Intl.message(
      'Agnes can make mistakes. Check important info.',
      name: 'agnesCanMakeMistakesCheckImportantInfo',
      desc: '',
      args: [],
    );
  }

  /// `@Agnes for summaries, reports, or slides.`
  String get agnesForSummariesReportsOrSlides {
    return Intl.message(
      '@Agnes for summaries, reports, or slides.',
      name: 'agnesForSummariesReportsOrSlides',
      desc: '',
      args: [],
    );
  }

  /// `Agnes VibePods`
  String get agnesVibepods {
    return Intl.message(
      'Agnes VibePods',
      name: 'agnesVibepods',
      desc: '',
      args: [],
    );
  }

  /// `Agree and Log In`
  String get agreeAndLogIn {
    return Intl.message(
      'Agree and Log In',
      name: 'agreeAndLogIn',
      desc: '',
      args: [],
    );
  }

  /// `AI Design`
  String get aiDesign {
    return Intl.message(
      'AI Design',
      name: 'aiDesign',
      desc: '',
      args: [],
    );
  }

  /// `AI Slides`
  String get aiSlides {
    return Intl.message(
      'AI Slides',
      name: 'aiSlides',
      desc: '',
      args: [],
    );
  }

  /// `Album`
  String get album {
    return Intl.message(
      'Album',
      name: 'album',
      desc: '',
      args: [],
    );
  }

  /// `All Members`
  String get allMembers {
    return Intl.message(
      'All Members',
      name: 'allMembers',
      desc: '',
      args: [],
    );
  }

  /// `Answer Call`
  String get answerCall {
    return Intl.message(
      'Answer Call',
      name: 'answerCall',
      desc: '',
      args: [],
    );
  }

  /// `App Update`
  String get appUpdate {
    return Intl.message(
      'App Update',
      name: 'appUpdate',
      desc: '',
      args: [],
    );
  }

  /// `Archived`
  String get archived {
    return Intl.message(
      'Archived',
      name: 'archived',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete your account?`
  String get areYouSureYouWantToDeleteYourAccount {
    return Intl.message(
      'Are you sure you want to delete your account?',
      name: 'areYouSureYouWantToDeleteYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to sign out your account?`
  String get areYouSureYouWantToSignOutYourAccount {
    return Intl.message(
      'Are you sure you want to sign out your account?',
      name: 'areYouSureYouWantToSignOutYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `Ask Agnes`
  String get askAgnes {
    return Intl.message(
      'Ask Agnes',
      name: 'askAgnes',
      desc: '',
      args: [],
    );
  }

  /// `Ask Agnes to edit slides`
  String get askAgnesToEditSlides {
    return Intl.message(
      'Ask Agnes to edit slides',
      name: 'askAgnesToEditSlides',
      desc: '',
      args: [],
    );
  }

  /// `Assign a task to begin`
  String get assignATaskToBegin {
    return Intl.message(
      'Assign a task to begin',
      name: 'assignATaskToBegin',
      desc: '',
      args: [],
    );
  }

  /// `At least six characters long with letters, numbers & symbols`
  String get atLeastSixCharactersLongWithLettersNumbersSymbols {
    return Intl.message(
      'At least six characters long with letters, numbers & symbols',
      name: 'atLeastSixCharactersLongWithLettersNumbersSymbols',
      desc: '',
      args: [],
    );
  }

  /// `At present, there is no consensus on the tasks to be done. Feel free to discuss further and ask questions if you have any!`
  String
      get atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny {
    return Intl.message(
      'At present, there is no consensus on the tasks to be done. Feel free to discuss further and ask questions if you have any!',
      name:
          'atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny',
      desc: '',
      args: [],
    );
  }

  /// `Avatar`
  String get avatar {
    return Intl.message(
      'Avatar',
      name: 'avatar',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get back {
    return Intl.message(
      'Back',
      name: 'back',
      desc: '',
      args: [],
    );
  }

  /// `Bookmark removed`
  String get bookmarkRemoved {
    return Intl.message(
      'Bookmark removed',
      name: 'bookmarkRemoved',
      desc: '',
      args: [],
    );
  }

  /// `Bookmarked`
  String get bookmarked {
    return Intl.message(
      'Bookmarked',
      name: 'bookmarked',
      desc: '',
      args: [],
    );
  }

  /// `By signing in, you are agreeing to the\n{termsOfService} and {privacyPolicy}.`
  String bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy(
      Object termsOfService, Object privacyPolicy) {
    return Intl.message(
      'By signing in, you are agreeing to the\n$termsOfService and $privacyPolicy.',
      name: 'bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy',
      desc: '',
      args: [termsOfService, privacyPolicy],
    );
  }

  /// `Call declined`
  String get callDeclined {
    return Intl.message(
      'Call declined',
      name: 'callDeclined',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message(
      'Cancel',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `Canvas`
  String get canvas {
    return Intl.message(
      'Canvas',
      name: 'canvas',
      desc: '',
      args: [],
    );
  }

  /// `Chapter`
  String get chapter {
    return Intl.message(
      'Chapter',
      name: 'chapter',
      desc: '',
      args: [],
    );
  }

  /// `Chat`
  String get chat {
    return Intl.message(
      'Chat',
      name: 'chat',
      desc: '',
      args: [],
    );
  }

  /// `Chat Settings`
  String get chatSettings {
    return Intl.message(
      'Chat Settings',
      name: 'chatSettings',
      desc: '',
      args: [],
    );
  }

  /// `Choose Image`
  String get chooseImage {
    return Intl.message(
      'Choose Image',
      name: 'chooseImage',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message(
      'Close',
      name: 'close',
      desc: '',
      args: [],
    );
  }

  /// `Code`
  String get code {
    return Intl.message(
      'Code',
      name: 'code',
      desc: '',
      args: [],
    );
  }

  /// `Collect`
  String get collect {
    return Intl.message(
      'Collect',
      name: 'collect',
      desc: '',
      args: [],
    );
  }

  /// `Coming Soon`
  String get comingSoon {
    return Intl.message(
      'Coming Soon',
      name: 'comingSoon',
      desc: '',
      args: [],
    );
  }

  /// `Comment`
  String get comment {
    return Intl.message(
      'Comment',
      name: 'comment',
      desc: '',
      args: [],
    );
  }

  /// `Commenter`
  String get commenter {
    return Intl.message(
      'Commenter',
      name: 'commenter',
      desc: '',
      args: [],
    );
  }

  /// `Comments Tasks`
  String get commentsTasks {
    return Intl.message(
      'Comments Tasks',
      name: 'commentsTasks',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirm {
    return Intl.message(
      'Confirm',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Delete`
  String get confirmDelete {
    return Intl.message(
      'Confirm Delete',
      name: 'confirmDelete',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Password`
  String get confirmPassword {
    return Intl.message(
      'Confirm Password',
      name: 'confirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `Confirm password cannot be empty`
  String get confirmPasswordCannotBeEmpty {
    return Intl.message(
      'Confirm password cannot be empty',
      name: 'confirmPasswordCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Sign Out`
  String get confirmSignOut {
    return Intl.message(
      'Confirm Sign Out',
      name: 'confirmSignOut',
      desc: '',
      args: [],
    );
  }

  /// `Connect`
  String get connect {
    return Intl.message(
      'Connect',
      name: 'connect',
      desc: '',
      args: [],
    );
  }

  /// `Connect the AI headset and chat with Agnes at any time`
  String get connectTheAiHeadsetAndChatWithAgnesAtAnyTime {
    return Intl.message(
      'Connect the AI headset and chat with Agnes at any time',
      name: 'connectTheAiHeadsetAndChatWithAgnesAtAnyTime',
      desc: '',
      args: [],
    );
  }

  /// `Connect to Agnes VibePods and chat anytime.`
  String get connectToAgnesVibepodsAndChatAnytime {
    return Intl.message(
      'Connect to Agnes VibePods and chat anytime.',
      name: 'connectToAgnesVibepodsAndChatAnytime',
      desc: '',
      args: [],
    );
  }

  /// `Connect with Friends to Start a Group Chat`
  String get connectWithFriendsToStartAGroupChat {
    return Intl.message(
      'Connect with Friends to Start a Group Chat',
      name: 'connectWithFriendsToStartAGroupChat',
      desc: '',
      args: [],
    );
  }

  /// `Connected`
  String get connected {
    return Intl.message(
      'Connected',
      name: 'connected',
      desc: '',
      args: [],
    );
  }

  /// `Connection failed`
  String get connectionFailed {
    return Intl.message(
      'Connection failed',
      name: 'connectionFailed',
      desc: '',
      args: [],
    );
  }

  /// `Content copied—go ahead and paste it on your favorite platform!`
  String get contentCopiedgoAheadAndPasteItOnYourFavoritePlatform {
    return Intl.message(
      'Content copied—go ahead and paste it on your favorite platform!',
      name: 'contentCopiedgoAheadAndPasteItOnYourFavoritePlatform',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Apple`
  String get continueWithApple {
    return Intl.message(
      'Continue with Apple',
      name: 'continueWithApple',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Google`
  String get continueWithGoogle {
    return Intl.message(
      'Continue with Google',
      name: 'continueWithGoogle',
      desc: '',
      args: [],
    );
  }

  /// `Continue with other ways`
  String get continueWithOtherWays {
    return Intl.message(
      'Continue with other ways',
      name: 'continueWithOtherWays',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueWord {
    return Intl.message(
      'Continue',
      name: 'continueWord',
      desc: '',
      args: [],
    );
  }

  /// `Copied`
  String get copied {
    return Intl.message(
      'Copied',
      name: 'copied',
      desc: '',
      args: [],
    );
  }

  /// `Copy`
  String get copy {
    return Intl.message(
      'Copy',
      name: 'copy',
      desc: '',
      args: [],
    );
  }

  /// `cost`
  String get cost {
    return Intl.message(
      'cost',
      name: 'cost',
      desc: '',
      args: [],
    );
  }

  /// `Cover`
  String get cover {
    return Intl.message(
      'Cover',
      name: 'cover',
      desc: '',
      args: [],
    );
  }

  /// `Create a new task to get started`
  String get createANewTaskToGetStarted {
    return Intl.message(
      'Create a new task to get started',
      name: 'createANewTaskToGetStarted',
      desc: '',
      args: [],
    );
  }

  /// `Create an account to sign up for Agnes`
  String get createAnAccountToSignUpForAgnes {
    return Intl.message(
      'Create an account to sign up for Agnes',
      name: 'createAnAccountToSignUpForAgnes',
      desc: '',
      args: [],
    );
  }

  /// `Create Group Chat`
  String get createGroupChat {
    return Intl.message(
      'Create Group Chat',
      name: 'createGroupChat',
      desc: '',
      args: [],
    );
  }

  /// `Currently in a call...`
  String get currentlyInACall {
    return Intl.message(
      'Currently in a call...',
      name: 'currentlyInACall',
      desc: '',
      args: [],
    );
  }

  /// `Deep Research`
  String get deepResearch {
    return Intl.message(
      'Deep Research',
      name: 'deepResearch',
      desc: '',
      args: [],
    );
  }

  /// `DeepResearch`
  String get deepresearch {
    return Intl.message(
      'DeepResearch',
      name: 'deepresearch',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get delete {
    return Intl.message(
      'Delete',
      name: 'delete',
      desc: '',
      args: [],
    );
  }

  /// `Delete Account`
  String get deleteAccount {
    return Intl.message(
      'Delete Account',
      name: 'deleteAccount',
      desc: '',
      args: [],
    );
  }

  /// `Delete Chat`
  String get deleteChat {
    return Intl.message(
      'Delete Chat',
      name: 'deleteChat',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to execute these tasks?`
  String get doYouWantToExecuteTheseTasks {
    return Intl.message(
      'Do you want to execute these tasks?',
      name: 'doYouWantToExecuteTheseTasks',
      desc: '',
      args: [],
    );
  }

  /// ` doesn’t seem to be an Agnes user. Enter their email to send an invite.`
  String get doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite {
    return Intl.message(
      ' doesn’t seem to be an Agnes user. Enter their email to send an invite.',
      name: 'doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite',
      desc: '',
      args: [],
    );
  }

  /// `done`
  String get done {
    return Intl.message(
      'done',
      name: 'done',
      desc: '',
      args: [],
    );
  }

  /// `Don’t have an account?`
  String get dontHaveAnAccount {
    return Intl.message(
      'Don’t have an account?',
      name: 'dontHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `Downloading`
  String get downloading {
    return Intl.message(
      'Downloading',
      name: 'downloading',
      desc: '',
      args: [],
    );
  }

  /// `1. Each new task consumes one diamond.`
  String get eachNewTaskConsumesOneDiamond {
    return Intl.message(
      '1. Each new task consumes one diamond.',
      name: 'eachNewTaskConsumesOneDiamond',
      desc: '',
      args: [],
    );
  }

  /// `3. Each user receives 30 diamonds per month.`
  String get eachUserReceivesDiamondsPerMonth {
    return Intl.message(
      '3. Each user receives 30 diamonds per month.',
      name: 'eachUserReceivesDiamondsPerMonth',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get edit {
    return Intl.message(
      'Edit',
      name: 'edit',
      desc: '',
      args: [],
    );
  }

  /// `Edit group name`
  String get editGroupName {
    return Intl.message(
      'Edit group name',
      name: 'editGroupName',
      desc: '',
      args: [],
    );
  }

  /// `Editor`
  String get editor {
    return Intl.message(
      'Editor',
      name: 'editor',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get email {
    return Intl.message(
      'Email',
      name: 'email',
      desc: '',
      args: [],
    );
  }

  /// `Email cannot be empty`
  String get emailCannotBeEmpty {
    return Intl.message(
      'Email cannot be empty',
      name: 'emailCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Email format incorrect`
  String get emailFormatIncorrect {
    return Intl.message(
      'Email format incorrect',
      name: 'emailFormatIncorrect',
      desc: '',
      args: [],
    );
  }

  /// `<EMAIL>`
  String get emaildomaincom {
    return Intl.message(
      '<EMAIL>',
      name: 'emaildomaincom',
      desc: '',
      args: [],
    );
  }

  /// `Enhance productivity and creativity through extended access`
  String get enhanceProductivityAndCreativityThroughExtendedAccess {
    return Intl.message(
      'Enhance productivity and creativity through extended access',
      name: 'enhanceProductivityAndCreativityThroughExtendedAccess',
      desc: '',
      args: [],
    );
  }

  /// `Enter Confirm Password`
  String get enterConfirmPassword {
    return Intl.message(
      'Enter Confirm Password',
      name: 'enterConfirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `Enter group name`
  String get enterGroupName {
    return Intl.message(
      'Enter group name',
      name: 'enterGroupName',
      desc: '',
      args: [],
    );
  }

  /// `Enter Password`
  String get enterPassword {
    return Intl.message(
      'Enter Password',
      name: 'enterPassword',
      desc: '',
      args: [],
    );
  }

  /// `Enter Verification Code`
  String get enterVerificationCode {
    return Intl.message(
      'Enter Verification Code',
      name: 'enterVerificationCode',
      desc: '',
      args: [],
    );
  }

  /// `Export`
  String get export {
    return Intl.message(
      'Export',
      name: 'export',
      desc: '',
      args: [],
    );
  }

  /// `File`
  String get file {
    return Intl.message(
      'File',
      name: 'file',
      desc: '',
      args: [],
    );
  }

  /// `Floating Window Not Enabled`
  String get floatingWindowNotEnabled {
    return Intl.message(
      'Floating Window Not Enabled',
      name: 'floatingWindowNotEnabled',
      desc: '',
      args: [],
    );
  }

  /// `For the full experience, please use the PC version`
  String get forTheFullExperiencePleaseUseThePcVersion {
    return Intl.message(
      'For the full experience, please use the PC version',
      name: 'forTheFullExperiencePleaseUseThePcVersion',
      desc: '',
      args: [],
    );
  }

  /// `Forgot Password`
  String get forgotPassword {
    return Intl.message(
      'Forgot Password',
      name: 'forgotPassword',
      desc: '',
      args: [],
    );
  }

  /// `General`
  String get general {
    return Intl.message(
      'General',
      name: 'general',
      desc: '',
      args: [],
    );
  }

  /// `Get Agnes AI Plus`
  String get getAgnesAiPlus {
    return Intl.message(
      'Get Agnes AI Plus',
      name: 'getAgnesAiPlus',
      desc: '',
      args: [],
    );
  }

  /// `Get Agnes AI Pro`
  String get getAgnesAiPro {
    return Intl.message(
      'Get Agnes AI Pro',
      name: 'getAgnesAiPro',
      desc: '',
      args: [],
    );
  }

  /// `Give me a task to work on…`
  String get giveMeATaskToWorkOn {
    return Intl.message(
      'Give me a task to work on…',
      name: 'giveMeATaskToWorkOn',
      desc: '',
      args: [],
    );
  }

  /// `Go To Shop`
  String get goToShop {
    return Intl.message(
      'Go To Shop',
      name: 'goToShop',
      desc: '',
      args: [],
    );
  }

  /// `Got it! I'm working on the task. Please wait a moment.`
  String get gotItImWorkingOnTheTaskPleaseWaitAMoment {
    return Intl.message(
      'Got it! I\'m working on the task. Please wait a moment.',
      name: 'gotItImWorkingOnTheTaskPleaseWaitAMoment',
      desc: '',
      args: [],
    );
  }

  /// `Group created successfully`
  String get groupCreatedSuccessfully {
    return Intl.message(
      'Group created successfully',
      name: 'groupCreatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Group Files`
  String get groupFiles {
    return Intl.message(
      'Group Files',
      name: 'groupFiles',
      desc: '',
      args: [],
    );
  }

  /// `Group name`
  String get groupName {
    return Intl.message(
      'Group name',
      name: 'groupName',
      desc: '',
      args: [],
    );
  }

  /// `Group Name (Optional)`
  String get groupNameOptional {
    return Intl.message(
      'Group Name (Optional)',
      name: 'groupNameOptional',
      desc: '',
      args: [],
    );
  }

  /// `Harmful`
  String get harmful {
    return Intl.message(
      'Harmful',
      name: 'harmful',
      desc: '',
      args: [],
    );
  }

  /// `Help us improve`
  String get helpUsImprove {
    return Intl.message(
      'Help us improve',
      name: 'helpUsImprove',
      desc: '',
      args: [],
    );
  }

  /// `I assist with search, writing, and answers`
  String get iAssistWithSearchWritingAndAnswers {
    return Intl.message(
      'I assist with search, writing, and answers',
      name: 'iAssistWithSearchWritingAndAnswers',
      desc: '',
      args: [],
    );
  }

  /// `If you answer the incoming call yourcurrent call will be ended.`
  String get ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded {
    return Intl.message(
      'If you answer the incoming call yourcurrent call will be ended.',
      name: 'ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded',
      desc: '',
      args: [],
    );
  }

  /// `I'm Agnes`
  String get imAgnes {
    return Intl.message(
      'I\'m Agnes',
      name: 'imAgnes',
      desc: '',
      args: [],
    );
  }

  /// `In a voice call`
  String get inAVoiceCall {
    return Intl.message(
      'In a voice call',
      name: 'inAVoiceCall',
      desc: '',
      args: [],
    );
  }

  /// `Inaccurate`
  String get inaccurate {
    return Intl.message(
      'Inaccurate',
      name: 'inaccurate',
      desc: '',
      args: [],
    );
  }

  /// `In-app purchases are currently unavailable, Please try again later`
  String get inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater {
    return Intl.message(
      'In-app purchases are currently unavailable, Please try again later',
      name: 'inappPurchasesAreCurrentlyUnavailablePleaseTryAgainLater',
      desc: '',
      args: [],
    );
  }

  /// `Interface Language`
  String get interfaceLanguage {
    return Intl.message(
      'Interface Language',
      name: 'interfaceLanguage',
      desc: '',
      args: [],
    );
  }

  /// `Invite`
  String get invite {
    return Intl.message(
      'Invite',
      name: 'invite',
      desc: '',
      args: [],
    );
  }

  /// `Invite by username, email, or phone number`
  String get inviteByUsernameEmailOrPhoneNumber {
    return Intl.message(
      'Invite by username, email, or phone number',
      name: 'inviteByUsernameEmailOrPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Invite Friends`
  String get inviteFriends {
    return Intl.message(
      'Invite Friends',
      name: 'inviteFriends',
      desc: '',
      args: [],
    );
  }

  /// `Invite others to collaborate`
  String get inviteOthersToCollaborate {
    return Intl.message(
      'Invite others to collaborate',
      name: 'inviteOthersToCollaborate',
      desc: '',
      args: [],
    );
  }

  /// `Invite to collaborate`
  String get inviteToCollaborate {
    return Intl.message(
      'Invite to collaborate',
      name: 'inviteToCollaborate',
      desc: '',
      args: [],
    );
  }

  /// `is editing`
  String get isEditing {
    return Intl.message(
      'is editing',
      name: 'isEditing',
      desc: '',
      args: [],
    );
  }

  /// `Join`
  String get join {
    return Intl.message(
      'Join',
      name: 'join',
      desc: '',
      args: [],
    );
  }

  /// `Know More`
  String get knowMore {
    return Intl.message(
      'Know More',
      name: 'knowMore',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get language {
    return Intl.message(
      'English',
      name: 'language',
      desc: '',
      args: [],
    );
  }

  /// `Let Agnes help you create a better sharing format`
  String get letAgnesHelpYouCreateABetterSharingFormat {
    return Intl.message(
      'Let Agnes help you create a better sharing format',
      name: 'letAgnesHelpYouCreateABetterSharingFormat',
      desc: '',
      args: [],
    );
  }

  /// `Let Agnes resolve`
  String get letAgnesResolve {
    return Intl.message(
      'Let Agnes resolve',
      name: 'letAgnesResolve',
      desc: '',
      args: [],
    );
  }

  /// `Link copied`
  String get linkCopied {
    return Intl.message(
      'Link copied',
      name: 'linkCopied',
      desc: '',
      args: [],
    );
  }

  /// `Listening`
  String get listening {
    return Intl.message(
      'Listening',
      name: 'listening',
      desc: '',
      args: [],
    );
  }

  /// `Loading`
  String get loading {
    return Intl.message(
      'Loading',
      name: 'loading',
      desc: '',
      args: [],
    );
  }

  /// `Make me a shareable image post`
  String get makeMeAShareableImagePost {
    return Intl.message(
      'Make me a shareable image post',
      name: 'makeMeAShareableImagePost',
      desc: '',
      args: [],
    );
  }

  /// `Make me an HTML web page`
  String get makeMeAnHtmlWebPage {
    return Intl.message(
      'Make me an HTML web page',
      name: 'makeMeAnHtmlWebPage',
      desc: '',
      args: [],
    );
  }

  /// `Manage Members`
  String get manageMembers {
    return Intl.message(
      'Manage Members',
      name: 'manageMembers',
      desc: '',
      args: [],
    );
  }

  /// `Manually end the task.`
  String get manuallyEndTheTask {
    return Intl.message(
      'Manually end the task.',
      name: 'manuallyEndTheTask',
      desc: '',
      args: [],
    );
  }

  /// `Mark as Resolved`
  String get markAsResolved {
    return Intl.message(
      'Mark as Resolved',
      name: 'markAsResolved',
      desc: '',
      args: [],
    );
  }

  /// `Message Copied.`
  String get messageCopied {
    return Intl.message(
      'Message Copied.',
      name: 'messageCopied',
      desc: '',
      args: [],
    );
  }

  /// `Multiple choices`
  String get multipleChoices {
    return Intl.message(
      'Multiple choices',
      name: 'multipleChoices',
      desc: '',
      args: [],
    );
  }

  /// `My Favorites`
  String get myFavorites {
    return Intl.message(
      'My Favorites',
      name: 'myFavorites',
      desc: '',
      args: [],
    );
  }

  /// `Narrate`
  String get narrate {
    return Intl.message(
      'Narrate',
      name: 'narrate',
      desc: '',
      args: [],
    );
  }

  /// `Network connection failed. Please try again.`
  String get networkConnectionFailedPleaseTryAgain {
    return Intl.message(
      'Network connection failed. Please try again.',
      name: 'networkConnectionFailedPleaseTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Network connection lost`
  String get networkConnectionLost {
    return Intl.message(
      'Network connection lost',
      name: 'networkConnectionLost',
      desc: '',
      args: [],
    );
  }

  /// `Network unavailable`
  String get networkUnavailable {
    return Intl.message(
      'Network unavailable',
      name: 'networkUnavailable',
      desc: '',
      args: [],
    );
  }

  /// `New Password`
  String get newPassword {
    return Intl.message(
      'New Password',
      name: 'newPassword',
      desc: '',
      args: [],
    );
  }

  /// `New Project`
  String get newProject {
    return Intl.message(
      'New Project',
      name: 'newProject',
      desc: '',
      args: [],
    );
  }

  /// `New Task`
  String get newTask {
    return Intl.message(
      'New Task',
      name: 'newTask',
      desc: '',
      args: [],
    );
  }

  /// `New Title`
  String get newTitle {
    return Intl.message(
      'New Title',
      name: 'newTitle',
      desc: '',
      args: [],
    );
  }

  /// `No More Files`
  String get noMoreFiles {
    return Intl.message(
      'No More Files',
      name: 'noMoreFiles',
      desc: '',
      args: [],
    );
  }

  /// `no more message...`
  String get noMoreMessage {
    return Intl.message(
      'no more message...',
      name: 'noMoreMessage',
      desc: '',
      args: [],
    );
  }

  /// `No pending tasks found. All tasks have been completed!`
  String get noPendingTasksFoundAllTasksHaveBeenCompleted {
    return Intl.message(
      'No pending tasks found. All tasks have been completed!',
      name: 'noPendingTasksFoundAllTasksHaveBeenCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Not Available for Purchase`
  String get notAvailableForPurchase {
    return Intl.message(
      'Not Available for Purchase',
      name: 'notAvailableForPurchase',
      desc: '',
      args: [],
    );
  }

  /// `Not connected`
  String get notConnected {
    return Intl.message(
      'Not connected',
      name: 'notConnected',
      desc: '',
      args: [],
    );
  }

  /// `Not now`
  String get notNow {
    return Intl.message(
      'Not now',
      name: 'notNow',
      desc: '',
      args: [],
    );
  }

  /// `Notifications`
  String get notifications {
    return Intl.message(
      'Notifications',
      name: 'notifications',
      desc: '',
      args: [],
    );
  }

  /// `Notify user via email`
  String get notifyUserViaEmail {
    return Intl.message(
      'Notify user via email',
      name: 'notifyUserViaEmail',
      desc: '',
      args: [],
    );
  }

  /// `Okay,  I noticed you don’t have the required permissions. Please ask the owner or an editor to do it.`
  String
      get okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt {
    return Intl.message(
      'Okay,  I noticed you don’t have the required permissions. Please ask the owner or an editor to do it.',
      name:
          'okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt',
      desc: '',
      args: [],
    );
  }

  /// `Okay, I'll summarize the consensus tasks:`
  String get okayIllSummarizeTheConsensusTasks {
    return Intl.message(
      'Okay, I\'ll summarize the consensus tasks:',
      name: 'okayIllSummarizeTheConsensusTasks',
      desc: '',
      args: [],
    );
  }

  /// `Once deleted, group chat history will be cleared.`
  String get onceDeletedGroupChatHistoryWillBeCleared {
    return Intl.message(
      'Once deleted, group chat history will be cleared.',
      name: 'onceDeletedGroupChatHistoryWillBeCleared',
      desc: '',
      args: [],
    );
  }

  /// `Operation Successful!`
  String get operationSuccessful {
    return Intl.message(
      'Operation Successful!',
      name: 'operationSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Or let me know if you have any questions.`
  String get orLetMeKnowIfYouHaveAnyQuestions {
    return Intl.message(
      'Or let me know if you have any questions.',
      name: 'orLetMeKnowIfYouHaveAnyQuestions',
      desc: '',
      args: [],
    );
  }

  /// `Other group members will be notified after the group name is changed.`
  String get otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged {
    return Intl.message(
      'Other group members will be notified after the group name is changed.',
      name: 'otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged',
      desc: '',
      args: [],
    );
  }

  /// `Out of date`
  String get outOfDate {
    return Intl.message(
      'Out of date',
      name: 'outOfDate',
      desc: '',
      args: [],
    );
  }

  /// `Owner`
  String get owner {
    return Intl.message(
      'Owner',
      name: 'owner',
      desc: '',
      args: [],
    );
  }

  /// `Pages`
  String get pages {
    return Intl.message(
      'Pages',
      name: 'pages',
      desc: '',
      args: [],
    );
  }

  /// `Password cannot be empty`
  String get passwordCannotBeEmpty {
    return Intl.message(
      'Password cannot be empty',
      name: 'passwordCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Pending`
  String get pending {
    return Intl.message(
      'Pending',
      name: 'pending',
      desc: '',
      args: [],
    );
  }

  /// `Permission to execute tasks in the background is not granted`
  String get permissionToExecuteTasksInTheBackgroundIsNotGranted {
    return Intl.message(
      'Permission to execute tasks in the background is not granted',
      name: 'permissionToExecuteTasksInTheBackgroundIsNotGranted',
      desc: '',
      args: [],
    );
  }

  /// `Phone`
  String get phone {
    return Intl.message(
      'Phone',
      name: 'phone',
      desc: '',
      args: [],
    );
  }

  /// `Phone Number`
  String get phoneNumber {
    return Intl.message(
      'Phone Number',
      name: 'phoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Phone number cannot be empty`
  String get phoneNumberCannotBeEmpty {
    return Intl.message(
      'Phone number cannot be empty',
      name: 'phoneNumberCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Photo`
  String get photo {
    return Intl.message(
      'Photo',
      name: 'photo',
      desc: '',
      args: [],
    );
  }

  /// `Please select a slide to edit.`
  String get pleaseSelectASlideToEdit {
    return Intl.message(
      'Please select a slide to edit.',
      name: 'pleaseSelectASlideToEdit',
      desc: '',
      args: [],
    );
  }

  /// `Please update to the latest version for new features and improvements.`
  String get pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements {
    return Intl.message(
      'Please update to the latest version for new features and improvements.',
      name: 'pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements',
      desc: '',
      args: [],
    );
  }

  /// `Please wait for Agnes to complete the task`
  String get pleaseWaitForAgnesToCompleteTheTask {
    return Intl.message(
      'Please wait for Agnes to complete the task',
      name: 'pleaseWaitForAgnesToCompleteTheTask',
      desc: '',
      args: [],
    );
  }

  /// `Please wait until the download is completed`
  String get pleaseWaitUntilTheDownloadIsCompleted {
    return Intl.message(
      'Please wait until the download is completed',
      name: 'pleaseWaitUntilTheDownloadIsCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Presenter`
  String get presenter {
    return Intl.message(
      'Presenter',
      name: 'presenter',
      desc: '',
      args: [],
    );
  }

  /// `Preview`
  String get preview {
    return Intl.message(
      'Preview',
      name: 'preview',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Provide additional feedback on this answer`
  String get provideAdditionalFeedbackOnThisAnswer {
    return Intl.message(
      'Provide additional feedback on this answer',
      name: 'provideAdditionalFeedbackOnThisAnswer',
      desc: '',
      args: [],
    );
  }

  /// `Quote`
  String get quote {
    return Intl.message(
      'Quote',
      name: 'quote',
      desc: '',
      args: [],
    );
  }

  /// `Quoted message has expired.`
  String get quotedMessageHasExpired {
    return Intl.message(
      'Quoted message has expired.',
      name: 'quotedMessageHasExpired',
      desc: '',
      args: [],
    );
  }

  /// `Reconnect`
  String get reconnect {
    return Intl.message(
      'Reconnect',
      name: 'reconnect',
      desc: '',
      args: [],
    );
  }

  /// `Reject`
  String get reject {
    return Intl.message(
      'Reject',
      name: 'reject',
      desc: '',
      args: [],
    );
  }

  /// `Remove`
  String get remove {
    return Intl.message(
      'Remove',
      name: 'remove',
      desc: '',
      args: [],
    );
  }

  /// `Removed successfully`
  String get removedSuccessfully {
    return Intl.message(
      'Removed successfully',
      name: 'removedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Rename`
  String get rename {
    return Intl.message(
      'Rename',
      name: 'rename',
      desc: '',
      args: [],
    );
  }

  /// `Reopen the app`
  String get reopenTheApp {
    return Intl.message(
      'Reopen the app',
      name: 'reopenTheApp',
      desc: '',
      args: [],
    );
  }

  /// `Request timed out. Please try again.`
  String get requestTimedOutPleaseTryAgain {
    return Intl.message(
      'Request timed out. Please try again.',
      name: 'requestTimedOutPleaseTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Research`
  String get research {
    return Intl.message(
      'Research',
      name: 'research',
      desc: '',
      args: [],
    );
  }

  /// `Research for {value} seconds`
  String researchForZdSeconds(Object value) {
    return Intl.message(
      'Research for $value seconds',
      name: 'researchForZdSeconds',
      desc: '',
      args: [value],
    );
  }

  /// `Reset Password`
  String get resetPassword {
    return Intl.message(
      'Reset Password',
      name: 'resetPassword',
      desc: '',
      args: [],
    );
  }

  /// `Reset Your Password`
  String get resetYourPassword {
    return Intl.message(
      'Reset Your Password',
      name: 'resetYourPassword',
      desc: '',
      args: [],
    );
  }

  /// `Role`
  String get role {
    return Intl.message(
      'Role',
      name: 'role',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get save {
    return Intl.message(
      'Save',
      name: 'save',
      desc: '',
      args: [],
    );
  }

  /// `Saved`
  String get saved {
    return Intl.message(
      'Saved',
      name: 'saved',
      desc: '',
      args: [],
    );
  }

  /// `Saving…`
  String get saving {
    return Intl.message(
      'Saving…',
      name: 'saving',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get search {
    return Intl.message(
      'Search',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `Search by name, email or phone`
  String get searchByNameEmailOrPhone {
    return Intl.message(
      'Search by name, email or phone',
      name: 'searchByNameEmailOrPhone',
      desc: '',
      args: [],
    );
  }

  /// `Search for a country`
  String get searchForACountry {
    return Intl.message(
      'Search for a country',
      name: 'searchForACountry',
      desc: '',
      args: [],
    );
  }

  /// `Seconds`
  String get seconds {
    return Intl.message(
      'Seconds',
      name: 'seconds',
      desc: '',
      args: [],
    );
  }

  /// `Seems to be disconnected from the internet`
  String get seemsToBeDisconnectedFromTheInternet {
    return Intl.message(
      'Seems to be disconnected from the internet',
      name: 'seemsToBeDisconnectedFromTheInternet',
      desc: '',
      args: [],
    );
  }

  /// `Select Members`
  String get selectMembers {
    return Intl.message(
      'Select Members',
      name: 'selectMembers',
      desc: '',
      args: [],
    );
  }

  /// `Send`
  String get send {
    return Intl.message(
      'Send',
      name: 'send',
      desc: '',
      args: [],
    );
  }

  /// `Send Message`
  String get sendMessage {
    return Intl.message(
      'Send Message',
      name: 'sendMessage',
      desc: '',
      args: [],
    );
  }

  /// `Server connection failed.`
  String get serverConnectionFailed {
    return Intl.message(
      'Server connection failed.',
      name: 'serverConnectionFailed',
      desc: '',
      args: [],
    );
  }

  /// `Service is still starting.`
  String get serviceIsStillStarting {
    return Intl.message(
      'Service is still starting.',
      name: 'serviceIsStillStarting',
      desc: '',
      args: [],
    );
  }

  /// `Session expired. Please log in again.`
  String get sessionExpiredPleaseLogInAgain {
    return Intl.message(
      'Session expired. Please log in again.',
      name: 'sessionExpiredPleaseLogInAgain',
      desc: '',
      args: [],
    );
  }

  /// `Set Now`
  String get setNow {
    return Intl.message(
      'Set Now',
      name: 'setNow',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get settings {
    return Intl.message(
      'Settings',
      name: 'settings',
      desc: '',
      args: [],
    );
  }

  /// `Share`
  String get share {
    return Intl.message(
      'Share',
      name: 'share',
      desc: '',
      args: [],
    );
  }

  /// `Sign In`
  String get signIn {
    return Intl.message(
      'Sign In',
      name: 'signIn',
      desc: '',
      args: [],
    );
  }

  /// `Sign Out`
  String get signOut {
    return Intl.message(
      'Sign Out',
      name: 'signOut',
      desc: '',
      args: [],
    );
  }

  /// `Sign Up`
  String get signUp {
    return Intl.message(
      'Sign Up',
      name: 'signUp',
      desc: '',
      args: [],
    );
  }

  /// `Someone @me`
  String get someoneMe {
    return Intl.message(
      'Someone @me',
      name: 'someoneMe',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, we’re at full capacity right now. Please try again tomorrow.`
  String get sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow {
    return Intl.message(
      'Sorry, we’re at full capacity right now. Please try again tomorrow.',
      name: 'sorryWereAtFullCapacityRightNowPleaseTryAgainTomorrow',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, your {title} task failed. Please try again later.`
  String sorryYourTitleTaskFailedPleaseTryAgainLater(Object title) {
    return Intl.message(
      'Sorry, your $title task failed. Please try again later.',
      name: 'sorryYourTitleTaskFailedPleaseTryAgainLater',
      desc: '',
      args: [title],
    );
  }

  /// `Start speaking`
  String get startSpeaking {
    return Intl.message(
      'Start speaking',
      name: 'startSpeaking',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get submit {
    return Intl.message(
      'Submit',
      name: 'submit',
      desc: '',
      args: [],
    );
  }

  /// `Supports uploading up to {value} images.`
  String supportsUploadingUpToZdImages(Object value) {
    return Intl.message(
      'Supports uploading up to $value images.',
      name: 'supportsUploadingUpToZdImages',
      desc: '',
      args: [value],
    );
  }

  /// `Switch Wi-Fi/mobile data and retry`
  String get switchWifimobileDataAndRetry {
    return Intl.message(
      'Switch Wi-Fi/mobile data and retry',
      name: 'switchWifimobileDataAndRetry',
      desc: '',
      args: [],
    );
  }

  /// `System`
  String get system {
    return Intl.message(
      'System',
      name: 'system',
      desc: '',
      args: [],
    );
  }

  /// `System busy, please try again.`
  String get systemBusyPleaseTryAgain {
    return Intl.message(
      'System busy, please try again.',
      name: 'systemBusyPleaseTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Take Photo`
  String get takePhoto {
    return Intl.message(
      'Take Photo',
      name: 'takePhoto',
      desc: '',
      args: [],
    );
  }

  /// `Task stopped manually.`
  String get taskStoppedManually {
    return Intl.message(
      'Task stopped manually.',
      name: 'taskStoppedManually',
      desc: '',
      args: [],
    );
  }

  /// `Tasks deleted as requested. Current pending tasks: {remaining_count}. Would you like me to show all pending tasks?`
  String
      tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks(
          Object remaining_count) {
    return Intl.message(
      'Tasks deleted as requested. Current pending tasks: $remaining_count. Would you like me to show all pending tasks?',
      name:
          'tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks',
      desc: '',
      args: [remaining_count],
    );
  }

  /// `Terms of Service`
  String get termsOfService {
    return Intl.message(
      'Terms of Service',
      name: 'termsOfService',
      desc: '',
      args: [],
    );
  }

  /// `2. The diamond count updates on the 1st of every month.`
  String get theDiamondCountUpdatesOnThe1stOfEveryMonth {
    return Intl.message(
      '2. The diamond count updates on the 1st of every month.',
      name: 'theDiamondCountUpdatesOnThe1stOfEveryMonth',
      desc: '',
      args: [],
    );
  }

  /// `The group is currently in a call...`
  String get theGroupIsCurrentlyInACall {
    return Intl.message(
      'The group is currently in a call...',
      name: 'theGroupIsCurrentlyInACall',
      desc: '',
      args: [],
    );
  }

  /// `The language used in the user interface`
  String get theLanguageUsedInTheUserInterface {
    return Intl.message(
      'The language used in the user interface',
      name: 'theLanguageUsedInTheUserInterface',
      desc: '',
      args: [],
    );
  }

  /// `The phone number is invalid`
  String get thePhoneNumberIsInvalid {
    return Intl.message(
      'The phone number is invalid',
      name: 'thePhoneNumberIsInvalid',
      desc: '',
      args: [],
    );
  }

  /// `There are currently {total} pending tasks:`
  String thereAreCurrentlyTotalPendingTasksTask(Object total) {
    return Intl.message(
      'There are currently $total pending tasks:',
      name: 'thereAreCurrentlyTotalPendingTasksTask',
      desc: '',
      args: [total],
    );
  }

  /// `This app requires camera access to let you take a photo for your profile picture.`
  String
      get thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture {
    return Intl.message(
      'This app requires camera access to let you take a photo for your profile picture.',
      name:
          'thisAppRequiresCameraAccessToLetYouTakeAPhotoForYourProfilePicture',
      desc: '',
      args: [],
    );
  }

  /// `This isn't helpful`
  String get thisIsntHelpful {
    return Intl.message(
      'This isn\'t helpful',
      name: 'thisIsntHelpful',
      desc: '',
      args: [],
    );
  }

  /// `This slide is being edited.`
  String get thisSlideIsBeingEdited {
    return Intl.message(
      'This slide is being edited.',
      name: 'thisSlideIsBeingEdited',
      desc: '',
      args: [],
    );
  }

  /// `To allow you to send images for recognition, interaction, or setting your avatar, please allow Agnes to access your camera.`
  String
      get toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera {
    return Intl.message(
      'To allow you to send images for recognition, interaction, or setting your avatar, please allow Agnes to access your camera.',
      name:
          'toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourCamera',
      desc: '',
      args: [],
    );
  }

  /// `To allow you to send images for recognition, interaction, or setting your avatar, please allow Agnes to access your photo library.`
  String
      get toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary {
    return Intl.message(
      'To allow you to send images for recognition, interaction, or setting your avatar, please allow Agnes to access your photo library.',
      name:
          'toAllowYouToSendImagesForRecognitionInteractionOrSettingYourAvatarPleaseAllowAgnesToAccessYourPhotoLibrary',
      desc: '',
      args: [],
    );
  }

  /// `To save generated images, please allow Agnes to save images to your photo library.`
  String
      get toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary {
    return Intl.message(
      'To save generated images, please allow Agnes to save images to your photo library.',
      name:
          'toSaveGeneratedImagesPleaseAllowAgnesToSaveImagesToYourPhotoLibrary',
      desc: '',
      args: [],
    );
  }

  /// `Transcribing...`
  String get transcribing {
    return Intl.message(
      'Transcribing...',
      name: 'transcribing',
      desc: '',
      args: [],
    );
  }

  /// `Transcription enabled. Loading data…`
  String get transcriptionEnabledLoadingData {
    return Intl.message(
      'Transcription enabled. Loading data…',
      name: 'transcriptionEnabledLoadingData',
      desc: '',
      args: [],
    );
  }

  /// `Try one of these:`
  String get tryOneOfThese {
    return Intl.message(
      'Try one of these:',
      name: 'tryOneOfThese',
      desc: '',
      args: [],
    );
  }

  /// `Two passwords are inconsistent`
  String get twoPasswordsAreInconsistent {
    return Intl.message(
      'Two passwords are inconsistent',
      name: 'twoPasswordsAreInconsistent',
      desc: '',
      args: [],
    );
  }

  /// `Update`
  String get update {
    return Intl.message(
      'Update',
      name: 'update',
      desc: '',
      args: [],
    );
  }

  /// `Update failed. Please try again later.`
  String get updateFailedPleaseTryAgainLater {
    return Intl.message(
      'Update failed. Please try again later.',
      name: 'updateFailedPleaseTryAgainLater',
      desc: '',
      args: [],
    );
  }

  /// `Update Password`
  String get updatePassword {
    return Intl.message(
      'Update Password',
      name: 'updatePassword',
      desc: '',
      args: [],
    );
  }

  /// `Update password successfully`
  String get updatePasswordSuccessfully {
    return Intl.message(
      'Update password successfully',
      name: 'updatePasswordSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade your plan to continue: Upgrade`
  String get upgradeYourPlanToContinueUpgrade {
    return Intl.message(
      'Upgrade your plan to continue: Upgrade',
      name: 'upgradeYourPlanToContinueUpgrade',
      desc: '',
      args: [],
    );
  }

  /// `Use 6+ characters with letters, numbers & symbols`
  String get useCharactersWithLettersNumbersSymbols {
    return Intl.message(
      'Use 6+ characters with letters, numbers & symbols',
      name: 'useCharactersWithLettersNumbersSymbols',
      desc: '',
      args: [],
    );
  }

  /// `User Access`
  String get userAccess {
    return Intl.message(
      'User Access',
      name: 'userAccess',
      desc: '',
      args: [],
    );
  }

  /// `User {user_name} has insufficient quota, slides remaining: {slides_remaining_count}, slides required quota: {slides_plan_count}, research remaining: {research_remaining_count}, research required quota: {research_plan_count}. You can choose tasks with enough quota to execute.`
  String
      userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute(
          Object user_name,
          Object slides_remaining_count,
          Object slides_plan_count,
          Object research_remaining_count,
          Object research_plan_count) {
    return Intl.message(
      'User $user_name has insufficient quota, slides remaining: $slides_remaining_count, slides required quota: $slides_plan_count, research remaining: $research_remaining_count, research required quota: $research_plan_count. You can choose tasks with enough quota to execute.',
      name:
          'userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute',
      desc: '',
      args: [
        user_name,
        slides_remaining_count,
        slides_plan_count,
        research_remaining_count,
        research_plan_count
      ],
    );
  }

  /// `Username`
  String get username {
    return Intl.message(
      'Username',
      name: 'username',
      desc: '',
      args: [],
    );
  }

  /// `Users with access`
  String get usersWithAccess {
    return Intl.message(
      'Users with access',
      name: 'usersWithAccess',
      desc: '',
      args: [],
    );
  }

  /// `Using Tool`
  String get usingTool {
    return Intl.message(
      'Using Tool',
      name: 'usingTool',
      desc: '',
      args: [],
    );
  }

  /// `Verification Code`
  String get verificationCode {
    return Intl.message(
      'Verification Code',
      name: 'verificationCode',
      desc: '',
      args: [],
    );
  }

  /// `Verification code cannot be empty`
  String get verificationCodeCannotBeEmpty {
    return Intl.message(
      'Verification code cannot be empty',
      name: 'verificationCodeCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Verification code sent to {xxx}@email.com`
  String verificationCodeSentToXxxemailcom(Object xxx) {
    return Intl.message(
      'Verification code sent to $<EMAIL>',
      name: 'verificationCodeSentToXxxemailcom',
      desc: '',
      args: [xxx],
    );
  }

  /// `Version {version} is now available.`
  String versionIsNowAvailable(Object version) {
    return Intl.message(
      'Version $version is now available.',
      name: 'versionIsNowAvailable',
      desc: '',
      args: [version],
    );
  }

  /// `View`
  String get view {
    return Intl.message(
      'View',
      name: 'view',
      desc: '',
      args: [],
    );
  }

  /// `View all comments`
  String get viewAllComments {
    return Intl.message(
      'View all comments',
      name: 'viewAllComments',
      desc: '',
      args: [],
    );
  }

  /// `Viewer`
  String get viewer {
    return Intl.message(
      'Viewer',
      name: 'viewer',
      desc: '',
      args: [],
    );
  }

  /// `Voice call creation failed, possibly due to network instability. Please try again.`
  String
      get voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain {
    return Intl.message(
      'Voice call creation failed, possibly due to network instability. Please try again.',
      name:
          'voiceCallCreationFailedPossiblyDueToNetworkInstabilityPleaseTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Voice call ended`
  String get voiceCallEnded {
    return Intl.message(
      'Voice call ended',
      name: 'voiceCallEnded',
      desc: '',
      args: [],
    );
  }

  /// `Waiting`
  String get waiting {
    return Intl.message(
      'Waiting',
      name: 'waiting',
      desc: '',
      args: [],
    );
  }

  /// `We need access to save downloaded images or videos to your photo library.`
  String get weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary {
    return Intl.message(
      'We need access to save downloaded images or videos to your photo library.',
      name: 'weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary',
      desc: '',
      args: [],
    );
  }

  /// `We need access to your microphone for voice interaction.`
  String get weNeedAccessToYourMicrophoneForVoiceInteraction {
    return Intl.message(
      'We need access to your microphone for voice interaction.',
      name: 'weNeedAccessToYourMicrophoneForVoiceInteraction',
      desc: '',
      args: [],
    );
  }

  /// `We need access to your photo library so you can select a picture for your profile.`
  String
      get weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile {
    return Intl.message(
      'We need access to your photo library so you can select a picture for your profile.',
      name:
          'weNeedAccessToYourPhotoLibrarySoYouCanSelectAPictureForYourProfile',
      desc: '',
      args: [],
    );
  }

  /// `We need to access your photo library to select images`
  String get weNeedToAccessYourPhotoLibraryToSelectImages {
    return Intl.message(
      'We need to access your photo library to select images',
      name: 'weNeedToAccessYourPhotoLibraryToSelectImages',
      desc: '',
      args: [],
    );
  }

  /// `We need to recognize your speech to convert it to text for interaction.`
  String get weNeedToRecognizeYourSpeechToConvertItToTextForInteraction {
    return Intl.message(
      'We need to recognize your speech to convert it to text for interaction.',
      name: 'weNeedToRecognizeYourSpeechToConvertItToTextForInteraction',
      desc: '',
      args: [],
    );
  }

  /// `We need to save images to your photo library`
  String get weNeedToSaveImagesToYourPhotoLibrary {
    return Intl.message(
      'We need to save images to your photo library',
      name: 'weNeedToSaveImagesToYourPhotoLibrary',
      desc: '',
      args: [],
    );
  }

  /// `We need to use Bluetooth to connect to your headset for voice interaction.`
  String get weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction {
    return Intl.message(
      'We need to use Bluetooth to connect to your headset for voice interaction.',
      name: 'weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction',
      desc: '',
      args: [],
    );
  }

  /// `We need to use your camera to take photos`
  String get weNeedToUseYourCameraToTakePhotos {
    return Intl.message(
      'We need to use your camera to take photos',
      name: 'weNeedToUseYourCameraToTakePhotos',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to Agnes`
  String get welcomeToAgnes {
    return Intl.message(
      'Welcome to Agnes',
      name: 'welcomeToAgnes',
      desc: '',
      args: [],
    );
  }

  /// `Write Something`
  String get writeSomething {
    return Intl.message(
      'Write Something',
      name: 'writeSomething',
      desc: '',
      args: [],
    );
  }

  /// `{x} members in voice...`
  String xMembersInVoice(Object x) {
    return Intl.message(
      '$x members in voice...',
      name: 'xMembersInVoice',
      desc: '',
      args: [x],
    );
  }

  /// `{xxx} changed the group name`
  String xxxChangedTheGroupName(Object xxx) {
    return Intl.message(
      '$xxx changed the group name',
      name: 'xxxChangedTheGroupName',
      desc: '',
      args: [xxx],
    );
  }

  /// `{xxx} has invited you to collaborate on editing "{xxx}".`
  String xxxHasInvitedYouToCollaborateOnEditingXxx(Object xxx) {
    return Intl.message(
      '$xxx has invited you to collaborate on editing "$xxx".',
      name: 'xxxHasInvitedYouToCollaborateOnEditingXxx',
      desc: '',
      args: [xxx],
    );
  }

  /// `{xxx} has invited you to collaborate on reviewing "{xxx}".`
  String xxxHasInvitedYouToCollaborateOnReviewingXxx(Object xxx) {
    return Intl.message(
      '$xxx has invited you to collaborate on reviewing "$xxx".',
      name: 'xxxHasInvitedYouToCollaborateOnReviewingXxx',
      desc: '',
      args: [xxx],
    );
  }

  /// `{xxx} has started a voice call`
  String xxxHasStartedAVoiceCall(Object xxx) {
    return Intl.message(
      '$xxx has started a voice call',
      name: 'xxxHasStartedAVoiceCall',
      desc: '',
      args: [xxx],
    );
  }

  /// `{xxx} invites you to join the "{xyz}" group call.`
  String xxxInvitesYouToJoinTheXyzGroupCall(Object xxx, Object xyz) {
    return Intl.message(
      '$xxx invites you to join the "$xyz" group call.',
      name: 'xxxInvitesYouToJoinTheXyzGroupCall',
      desc: '',
      args: [xxx, xyz],
    );
  }

  /// `You`
  String get you {
    return Intl.message(
      'You',
      name: 'you',
      desc: '',
      args: [],
    );
  }

  /// `You can execute, modify, or remove, how would you like to proceed?`
  String get youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed {
    return Intl.message(
      'You can execute, modify, or remove, how would you like to proceed?',
      name: 'youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed',
      desc: '',
      args: [],
    );
  }

  /// `You can manually edit the slides or edit through conversation with Agnes.`
  String get youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes {
    return Intl.message(
      'You can manually edit the slides or edit through conversation with Agnes.',
      name: 'youCanManuallyEditTheSlidesOrEditThroughConversationWithAgnes',
      desc: '',
      args: [],
    );
  }

  /// `You can select content and share your feedback.`
  String get youCanSelectContentAndShareYourFeedback {
    return Intl.message(
      'You can select content and share your feedback.',
      name: 'youCanSelectContentAndShareYourFeedback',
      desc: '',
      args: [],
    );
  }

  /// `You can't minimize a video call as AGNES isn't authorized to use floating windows.`
  String
      get youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows {
    return Intl.message(
      'You can\'t minimize a video call as AGNES isn\'t authorized to use floating windows.',
      name:
          'youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows',
      desc: '',
      args: [],
    );
  }

  /// `You created the group.`
  String get youCreatedTheGroup {
    return Intl.message(
      'You created the group.',
      name: 'youCreatedTheGroup',
      desc: '',
      args: [],
    );
  }

  /// `You have been removed from the group chat`
  String get youHaveBeenRemovedFromTheGroupChat {
    return Intl.message(
      'You have been removed from the group chat',
      name: 'youHaveBeenRemovedFromTheGroupChat',
      desc: '',
      args: [],
    );
  }

  /// `You joined the group.`
  String get youJoinedTheGroup {
    return Intl.message(
      'You joined the group.',
      name: 'youJoinedTheGroup',
      desc: '',
      args: [],
    );
  }

  /// `Your current plan`
  String get yourCurrentPlan {
    return Intl.message(
      'Your current plan',
      name: 'yourCurrentPlan',
      desc: '',
      args: [],
    );
  }

  /// `Your phone has not authorized Agnes to use the floating window permission. Agnes will not be able to continue performing tasks in the background for you.`
  String
      get yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou {
    return Intl.message(
      'Your phone has not authorized Agnes to use the floating window permission. Agnes will not be able to continue performing tasks in the background for you.',
      name:
          'yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou',
      desc: '',
      args: [],
    );
  }

  /// `Your request for the research task limit has been reached.`
  String get yourRequestForTheResearchTaskLimitHasBeenReached {
    return Intl.message(
      'Your request for the research task limit has been reached.',
      name: 'yourRequestForTheResearchTaskLimitHasBeenReached',
      desc: '',
      args: [],
    );
  }

  /// `Your request for the slide task limit has been reached.`
  String get yourRequestForTheSlideTaskLimitHasBeenReached {
    return Intl.message(
      'Your request for the slide task limit has been reached.',
      name: 'yourRequestForTheSlideTaskLimitHasBeenReached',
      desc: '',
      args: [],
    );
  }

  /// `Your request for this task limit has been reached.`
  String get yourRequestForThisTaskLimitHasBeenReached {
    return Intl.message(
      'Your request for this task limit has been reached.',
      name: 'yourRequestForThisTaskLimitHasBeenReached',
      desc: '',
      args: [],
    );
  }

  /// `Your {title} task has been successfully completed!`
  String yourTitleTaskHasBeenSuccessfullyCompleted(Object title) {
    return Intl.message(
      'Your $title task has been successfully completed!',
      name: 'yourTitleTaskHasBeenSuccessfullyCompleted',
      desc: '',
      args: [title],
    );
  }

  /// `You’ve reached the end`
  String get youveReachedTheEnd {
    return Intl.message(
      'You’ve reached the end',
      name: 'youveReachedTheEnd',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'es'),
      Locale.fromSubtags(languageCode: 'fil'),
      Locale.fromSubtags(languageCode: 'fr'),
      Locale.fromSubtags(languageCode: 'id'),
      Locale.fromSubtags(languageCode: 'ja'),
      Locale.fromSubtags(languageCode: 'ko'),
      Locale.fromSubtags(languageCode: 'ms'),
      Locale.fromSubtags(languageCode: 'pt'),
      Locale.fromSubtags(languageCode: 'th'),
      Locale.fromSubtags(languageCode: 'vi'),
      Locale.fromSubtags(languageCode: 'zh'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
