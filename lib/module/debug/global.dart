import 'package:get/get.dart';
import 'package:new_agnes/utils/environment_config.dart';

/// 全局配置
/// 动态获取baseUrl，支持环境切换
/// 如果EnvironmentConfig还未注册，使用默认正式环境
/// 不要修改，如果需要修改，请不要提交！！！
class Global {
  static bool get isDebug => true;

  static String get globalBaseUrl {
    // if (isDebug && Get.isRegistered<EnvironmentConfig>()) {
    //   return Get.find<EnvironmentConfig>().baseUrl;
    // }
    // return "https://app.agnes-ai.com";
    // return "https://agnes-dev.kiwiar.com";
    return "http://10.0.0.79:8093";
    // return "https://agnes-test-gcp.kiwiar.com";
  }
}
