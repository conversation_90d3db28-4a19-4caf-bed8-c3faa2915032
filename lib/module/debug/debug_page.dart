import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/Api.dart';
import 'package:new_agnes/api/ApiProvider.dart';
import 'package:new_agnes/api/StorageService.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/utils/environment_config.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/widget/ComAppbar.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:io';

import '../chat/group/group_chat/agora_logic.dart';
import '../mine/mine_info/logic.dart';
import '../tools/iap/iap_demo_page.dart';

class DebugPage extends StatefulWidget {
  const DebugPage({super.key});

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> {
  final StorageService _storageService = Get.find<StorageService>();
  late EnvironmentConfig _environmentConfig;
  PackageInfo? _packageInfo;
  bool _showNetworkLogs = false;
  final TextEditingController _customUrlController = TextEditingController();
  final TextEditingController _urlAppendTextController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _environmentConfig = Get.find<EnvironmentConfig>();
    _initializeDebugInfo();
  }

  Future<void> _initializeDebugInfo() async {
    _packageInfo = await PackageInfo.fromPlatform();
    _loadNetworkLogsSettings();
    _loadCustomUrl();
    _loadUrlAppendText();
    setState(() {});
  }
  
  void _loadCustomUrl() {
    final customUrl = _environmentConfig.customUrl;
    if (customUrl != null && customUrl.isNotEmpty) {
      _customUrlController.text = customUrl;
    }
  }

  void _loadNetworkLogsSettings() {
    _showNetworkLogs = _storageService.getStorage().read('debug_network_logs') ?? false;
  }
  
  void _loadUrlAppendText() {
    final appendText = _storageService.getStorage().read('debug_url_append_text') ?? '';
    if (appendText.isNotEmpty) {
      _urlAppendTextController.text = appendText;
    }
  }
  
  @override
  void dispose() {
    _customUrlController.dispose();
    _urlAppendTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ComAppBar(
        context,
        'Debug 调试页面',
        showLeading: true,
        textColor: Colors.black,
        backgroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAppInfoSection(),
            const SizedBox(height: 20),
            _buildEnvironmentSection(),
            const SizedBox(height: 20),
            _buildUserInfoSection(),
            const SizedBox(height: 20),
            _buildNetworkSection(),
            const SizedBox(height: 20),
            _buildCacheSection(),
            const SizedBox(height: 20),
            _buildDeveloperToolsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({required String title, required List<Widget> children}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildAppInfoSection() {
    return _buildSectionCard(
      title: '应用信息',
      children: [
        _buildInfoRow('应用名称', _packageInfo?.appName ?? '加载中...'),
        _buildInfoRow('包名', _packageInfo?.packageName ?? '加载中...'),
        _buildInfoRow('版本号', _packageInfo?.version ?? '加载中...'),
        _buildInfoRow('构建号', _packageInfo?.buildNumber ?? '加载中...'),
        _buildInfoRow('平台', Platform.isAndroid ? 'Android' : 'iOS'),
      ],
    );
  }

  Widget _buildEnvironmentSection() {
    return _buildSectionCard(
      title: '环境配置',
      children: [
        _buildInfoRow('当前环境', _environmentConfig.environmentName),
        _buildInfoRow('API地址', Api.baseUrl),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _environmentConfig.isProduction 
                    ? null 
                    : () => _switchEnvironment(Environment.production),
                icon: const Icon(Icons.cloud),
                label: const Text('正式环境'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _environmentConfig.isDevelopment 
                    ? null 
                    : () => _switchEnvironment(Environment.development),
                icon: const Icon(Icons.bug_report),
                label: const Text('测试环境'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _environmentConfig.isGcp 
                    ? null 
                    : () => _switchEnvironment(Environment.gcp),
                icon: const Icon(Icons.cloud_queue),
                label: const Text('GCP环境'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Container(), // Empty container to maintain layout
            ),
          ],
        ),
        const SizedBox(height: 16),
        const Text(
          '自定义地址:',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _customUrlController,
          decoration: InputDecoration(
            hintText: '请输入API地址 (例: https://api.example.com)',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            prefixIcon: const Icon(Icons.link),
          ),
          keyboardType: TextInputType.url,
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _applyCustomUrl,
            icon: const Icon(Icons.settings),
            label: const Text('应用自定义地址'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  void _openIapDemo() {
    Get.to(() => const IapDemoPage());
  }

  Widget _buildUserInfoSection() {
    final token = _storageService.getToken();
    final userInfo = _storageService.getUserInfoData();
    
    return _buildSectionCard(
      title: '用户信息',
      children: [
        _buildInfoRow('登录状态', token != null && token.isNotEmpty ? '已登录' : '未登录'),
        if (token != null && token.isNotEmpty) ...[
          _buildInfoRow('Token', '${token.substring(0, 20)}...'),
          GestureDetector(
            onTap: () => _copyToClipboard('Token', token),
            child: const Text(
              '点击复制完整Token',
              style: TextStyle(color: Colors.blue, decoration: TextDecoration.underline),
            ),
          ),
        ],
        if (userInfo != null) ...[
          const SizedBox(height: 8),
          _buildInfoRow('用户ID', userInfo.id?.toString() ?? 'N/A'),
          _buildInfoRow('用户名', userInfo.username != null ?userInfo.username!():'N/A'),
          _buildInfoRow('邮箱', userInfo.email ?? 'N/A'),
        ],
      ],
    );
  }

  Widget _buildNetworkSection() {
    return _buildSectionCard(
      title: '网络调试',
      children: [
        SwitchListTile(
          title: const Text('显示网络请求日志'),
          subtitle: const Text('在控制台显示详细的网络请求和响应'),
          value: _showNetworkLogs,
          onChanged: (value) {
            setState(() {
              _showNetworkLogs = value;
            });
            // 这里可以设置全局的网络日志开关
            _storageService.getStorage().write('debug_network_logs', value);
            showSuccessToast(_showNetworkLogs ? '已开启网络日志' : '已关闭网络日志');
          },
        ),
        const SizedBox(height: 16),
        const Text(
          'URL拼接文本:',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _urlAppendTextController,
          decoration: InputDecoration(
            hintText: '请输入要拼接到URL前面的文本',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            prefixIcon: const Icon(Icons.text_fields),
          ),
          keyboardType: TextInputType.text,
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _applyUrlAppendText,
            icon: const Icon(Icons.add_link),
            label: const Text('应用URL拼接文本'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _testNetworkConnection,
            icon: const Icon(Icons.network_check),
            label: const Text('测试网络连接'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCacheSection() {
    return _buildSectionCard(
      title: '缓存管理',
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _showStorageInfo,
            icon: const Icon(Icons.storage),
            label: const Text('查看存储信息'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _clearAllCache,
            icon: const Icon(Icons.clear_all),
            label: const Text('清除所有缓存'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _clearUserData,
            icon: const Icon(Icons.person_remove),
            label: const Text('清除用户数据'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDeveloperToolsSection() {
    return _buildSectionCard(
      title: '日志工具',
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _openLogViewer,
            icon: const Icon(Icons.article),
            label: const Text('查看应用日志'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _openIapDemo,
            icon: const Icon(Icons.shopping_cart_checkout),
            label: const Text('打开 IAP Demo 页面'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        // const SizedBox(height: 8),
        // SizedBox(
        //   width: double.infinity,
        //   child: ElevatedButton.icon(
        //     onPressed: _exportLogs,
        //     icon: const Icon(Icons.file_download),
        //     label: const Text('导出应用日志'),
        //     style: ElevatedButton.styleFrom(
        //       backgroundColor: Colors.indigo,
        //       foregroundColor: Colors.white,
        //     ),
        //   ),
        // ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }

  String _getEnvironmentDisplayName(Environment environment) {
    switch (environment) {
      case Environment.production:
        return '正式';
      case Environment.development:
        return '测试';
      case Environment.gcp:
        return 'GCP';
      case Environment.custom:
        return '自定义';
    }
  }

  void _switchEnvironment(Environment environment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('切换环境'),
        content: Text('确定要切换到${_getEnvironmentDisplayName(environment)}环境吗？\n\n切换后需要重启应用才能生效。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performEnvironmentSwitch(environment);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _performEnvironmentSwitch(Environment environment) {
    // 使用环境配置服务切换环境
    _environmentConfig.switchEnvironment(environment);
    Get.find<ApiProvider>().clearToken();
    releaseCache();
    setState(() {});
    
    showSuccessToast('环境已切换，请重启应用生效');
  }

  Future<void> releaseCache() async {
    try {
      Get.find<StorageService>().removeAppKey();
      Get.find<StorageService>().removeAgoraInfo();
      Get.find<StorageService>().removeThirdSDKData();
      await ChatClient.getInstance.chatManager
          .deleteAllMessageAndConversation();
      Get.find<StorageService>()
          .removeHasSync(Get.find<AgoraLogic>().agoraUserId.value);
      if (Get.find<AgoraLogic>().isJoined.value) {
        await ChatClient.getInstance.logout(true);
      }
      await Get.delete<AgoraLogic>();
    } catch (e) {
      logError("未登录,无需退出登陆${e.toString()}");
    }
  }

  Future<void> _testNetworkConnection() async {
    try {
      showInfoToast('测试网络连接中...');
      
      // 这里可以调用一个简单的API来测试连接
      // 比如获取用户信息或者版本检查
      final response = await Get.find<ApiProvider>().get(Api.checkApkVersion);
      
      if (response.isOk) {
        showSuccessToast('网络连接正常');
      } else {
        showFailToast('网络连接异常: ${response.statusCode}');
      }
    } catch (e) {
      showFailToast('网络连接失败: $e');
    }
  }

  void _clearAllCache() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除缓存'),
        content: const Text('确定要清除所有缓存数据吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // 清除缓存逻辑
              _storageService.getStorage().erase();
              showSuccessToast('缓存已清除');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _clearUserData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除用户数据'),
        content: const Text('确定要清除用户登录信息吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // 清除用户数据
              Get.find<ApiProvider>().clearToken();
              showSuccessToast('用户数据已清除');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showStorageInfo() {
    final storage = _storageService.getStorage();
    final keys = storage.getKeys();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('存储信息'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: keys.length,
            itemBuilder: (context, index) {
              final key = keys.elementAt(index);
              final value = storage.read(key);
              return ListTile(
                title: Text(key),
                subtitle: Text(value?.toString() ?? 'null'),
                dense: true,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _openLogViewer() {
    Get.to(() => LogViewerPage());
  }

  void _exportLogs() {
    // 这里可以实现导出日志的功能
    showSuccessToast('日志导出功能开发中...');
  }

  void _copyToClipboard(String label, String value) {
    Clipboard.setData(ClipboardData(text: value));
    showSuccessToast('$label 已复制到剪贴板');
  }
  
  void _applyCustomUrl() {
    final url = _customUrlController.text.trim();
    
    if (url.isEmpty) {
      showFailToast('请输入自定义地址');
      return;
    }
    
    if (!_isValidUrl(url)) {
      showFailToast('请输入有效的URL地址');
      return;
    }
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('应用自定义地址'),
        content: Text('确定要应用自定义地址吗？\n\n新地址: $url\n\n应用后需要重启应用才能生效。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performCustomUrlSwitch(url);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
  
  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https') && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }
  
  void _performCustomUrlSwitch(String url) {
    try {
      _environmentConfig.setCustomUrl(url);
      setState(() {});
      showSuccessToast('自定义地址已应用，请重启应用生效');
    } catch (e) {
      showFailToast('应用自定义地址失败: $e');
    }
  }
  
  void _applyUrlAppendText() {
    final appendText = _urlAppendTextController.text.trim();
    
    try {
      _storageService.getStorage().write('debug_url_append_text', appendText);
      showSuccessToast(appendText.isEmpty ? 'URL拼接文本已清空' : 'URL拼接文本已应用: $appendText');
    } catch (e) {
      showFailToast('应用URL拼接文本失败: $e');
    }
  }
}
