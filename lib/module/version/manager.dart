import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_api_availability/google_api_availability.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';
import 'package:open_file/open_file.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path_provider/path_provider.dart';
import 'package:get/get.dart' as getx;

import '../../api/Api.dart';
import '../../api/ApiProvider.dart';
import '../../generated/l10n.dart';

class AppUpdateManager {
  static final AppUpdateManager _instance = AppUpdateManager._internal();

  factory AppUpdateManager() => _instance;

  AppUpdateManager._internal();

  bool _isUpdateEnabled = false;
  String _currentChannel = 'googleplay'; // 默认渠道

  // 初始化配置
  Future<void> init({
    bool enableUpdate = false,
    String channel = 'googleplay',
  }) async {
    _isUpdateEnabled = enableUpdate;
    _currentChannel = channel;
  }

  // 检查更新
  Future<void> checkForUpdate(BuildContext context) async {
    if (!_isUpdateEnabled) {
      print('Update feature is disabled for this channel');
      return;
    }
    await _checkCustomUpdate(context);
  }

  // Google Play 应用内更新
  Future<void> _checkPlayStoreUpdate(
      String packageName, BuildContext context) async {
    try {
      final availability = await GoogleApiAvailability.instance.checkGooglePlayServicesAvailability(true);
      if (availability != GooglePlayServicesAvailability.success) {
        print('Google Play Services not available');
        Navigator.pop(context);
        await _launchStore(packageName);
        return;
      }
      // 检查更新可用性
      final updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        if (updateInfo.immediateUpdateAllowed) {
          // 立即更新
          var result = await InAppUpdate.performImmediateUpdate();
          if (result == AppUpdateResult.success) {
            // 更新成功
          } else if (result == AppUpdateResult.userDeniedUpdate) {
            // 用户拒绝了更新
          } else if (result == AppUpdateResult.inAppUpdateFailed) {
            // 更新失败
          }
        } else if (updateInfo.flexibleUpdateAllowed) {
          // 灵活更新
          await InAppUpdate.startFlexibleUpdate();
          await InAppUpdate.completeFlexibleUpdate();
        }
      }
    } catch (e) {
      print('Play Store update failed: $e');
      Navigator.pop(context);
      // 可以回退到跳转到Play Store
      await _launchStore(packageName);
    }
  }

  // 自定义更新逻辑（非Google Play渠道）
  Future<void> _checkCustomUpdate(BuildContext context) async {
    final packageInfo = await PackageInfo.fromPlatform();
    var localVersion = packageInfo.version;
    try {
      getx.Response res =
          await Get.find<ApiProvider>().get(Api.checkApkVersion,
          query: {"current_version": localVersion});
      if (res.statusCode == 200) {
        if (res.body == null || res.body.isEmpty) {
          return;
        }
        Map<String, dynamic> jsonMap = res.body;
        if (Platform.isAndroid) {
          // {name: 1.0.16, code: 116, url: http://test.agnes.com, mandatory: true}
          var versionName = jsonMap["name"];
          // int remoteCode = jsonMap["code"] is int
          //     ? jsonMap["code"]
          //     : int.tryParse(jsonMap["code"].toString()) ?? 0;
          var apkUrl = jsonMap["url"];

          String packageName = packageInfo.packageName;
          if (!jsonMap.isEmpty) {
            print("准备弹出提示");
            showUpdateInfoDialog(context,
                versionName: versionName,
                mandatory: jsonMap["mandatory"] ?? false,
                updateDescription: S.of(context).pleaseUpdateToTheLatestVersionForNewFeaturesAndImprovements,
                onUpdate: () {
              if (_currentChannel == 'googleplay') {
                // Google Play 渠道使用官方应用内更新API
                _checkPlayStoreUpdate(packageName, context);
              } else {
                Navigator.pop(context);
                print("准备下载");
                downloadAndInstallApk(
                    context, apkUrl, versionName, packageName);
              }
            });
          }
        }
      }
    } catch (e) {
      print('Custom update check failed: $e');
    }
  }

  // 跳转到应用商店
  Future<void> _launchStore(String appId) async {
    final url = _currentChannel == 'googleplay'
        ? 'https://play.google.com/store/apps/details?id=$appId'
        : 'your_other_store_url';

    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url),mode: LaunchMode.externalApplication,);
    }
  }

  Future<void> downloadAndInstallApk(BuildContext context, String apkUrl,
      String versionName, String packageName) async {
    bool isDenied = await requestStoragePermission();
    if (isDenied) {
      print("存储权限未授予");
      requestPermissionDialog(context);
      return;
    } else {
      final tempDir = await getApplicationDocumentsDirectory();
      final savePath = "${tempDir.path}/update_v$versionName.apk";
      final dio = Dio();

      double progress = 0.0;
      late void Function(void Function()) dialogSetState;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState) {
              dialogSetState = setState;
              return Scaffold(
                backgroundColor: Color(0xCD201034),
                body: Stack(
                  children: [
                    BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 0, sigmaY: 0),
                      child: Container(
                        decoration:
                            BoxDecoration(color: Colors.black.withOpacity(0)),
                      ),
                    ),
                    GestureDetector(
                        child: Container(color: Colors.transparent),
                        onTap: () {
                          //Navigator.pop(context);
                        }),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 40.w),
                        child: GradientBorderContainer(
                          borderRadius: BorderRadius.all(Radius.circular(21.r)),
                          strokeWidth: 2.w,
                          gradients: [
                            LinearGradient(
                              colors: [
                                Color(0xFFFF3BDF),
                                Color(0x08FF3BDF),
                                Color(0xFF00FFFF),
                                Color(0xFF543C86),
                              ],
                              stops: [0, 0.34, 0.76, 1],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                          ],
                          child: Container(
                            width: 1.sw,
                            height: 200.h,
                            decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20)),
                                image: DecorationImage(
                                    image: AssetImage(
                                        'assets/images/bg_message.png'),
                                    fit: BoxFit.fill)),
                            child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20.w),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    S.of(context)
                                        .pleaseWaitUntilTheDownloadIsCompleted,
                                    style: TextStyle(
                                      color: Color(0xFF988B9A),
                                      fontSize: 14.sp,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 16.h,
                                  ),
                                  _createProgressBar(progress),
                                  SizedBox(height: 12.h),
                                  Text(
                                      "${(progress * 100).toStringAsFixed(0)}%",
                                      style: TextStyle(
                                        fontSize: 16.sp,
                                        color: Color(0xFF988B9A),
                                      )),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      );

      // 外部控制下载，不会多次触发
      try {
        await dio.download(
          apkUrl,
          savePath,
          onReceiveProgress: (received, total) {
            if (total != -1) {
              progress = received / total;
              dialogSetState(() {}); // 只更新 UI，不重建逻辑
            }
          },
        );
        Navigator.pop(context); // 关闭进度框

        if (!await Permission.requestInstallPackages.isGranted) {
          final status = await Permission.requestInstallPackages.request();
          if (!status.isGranted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("permission refuse")),
            );
            return;
          }
        }

        final result = await OpenFile.open(savePath);
        print(result.message);

        if (result.type != ResultType.done) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('install fail: ${result.message}')),
          );
          return;
        }
      } catch (e) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("download fail")),
        );
      }
    }
  }

  _createProgressBar(double progress) {
    return LinearProgressIndicator(
      backgroundColor: Color(0x78733D7D),
      minHeight: 8.w,
      value: progress,
      borderRadius: BorderRadius.circular(10),
      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00FFFF)),
    );
  }

  Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      int sdkInt = androidInfo.version.sdkInt;
      print("requestStoragePermission--operatingSystemVersion=${sdkInt}");
      if (sdkInt >= 33) {
        final photosStatus = await Permission.photos.status;
        final videosStatus = await Permission.videos.status;
        final audioStatus = await Permission.audio.status;
        final requestInstallPackageStatus =
            await Permission.requestInstallPackages.status;
        // Android 13+，需要请求媒体权限
        // 如果有任何一个权限未授权，则请求
        if (photosStatus.isDenied ||
            videosStatus.isDenied ||
            audioStatus.isDenied ||
            requestInstallPackageStatus.isDenied) {
          final statuses = await [
            Permission.photos,
            Permission.videos,
            Permission.audio,
            Permission.requestInstallPackages,
          ].request();

          return statuses.values.any((status) => status.isDenied);
        }
        return false;
      } else {
        final storageStatus = await Permission.storage.status;
        final requestInstallPackageStatus =
            await Permission.requestInstallPackages.status;
        if (storageStatus.isDenied || requestInstallPackageStatus.isDenied) {
          final finalStatus = await [
            Permission.storage,
            Permission.requestInstallPackages,
          ].request();

          print("--requestStoragePermission--${finalStatus}--");
          return finalStatus.values.any((status) => status.isDenied);
        }
        return false;
      }
    }
    return true;
  }

  void requestPermissionDialog(BuildContext ctx) {
    showDialog(
      context: ctx,
      builder: (context) => AlertDialog(
        title: const Text("Requests for permissions"),
        content: const Text(
            "Please grant storage permission to continue using the relevant features"),
        actions: [
          TextButton(
            onPressed: () {
              safePopDialog(context);
            },
            child: const Text("cancle"),
          ),
          TextButton(
            onPressed: () {
              safePopDialog(ctx);
              openAppSettings();
            },
            child: const Text("set up"),
          ),
        ],
      ),
    );
  }

  void safePopDialog(BuildContext context) {
    final navigator = Navigator.of(context, rootNavigator: true);
    if (navigator.canPop()) {
      navigator.pop();
    }
  }

  void showUpdateInfoDialog(
    BuildContext context, {
    required String versionName,
    required String updateDescription,
    required bool mandatory,
    required VoidCallback onUpdate,
    VoidCallback? onSkip,
  }) {

    final fullText = S.of(context).versionIsNowAvailable(versionName);
    final versionStartIndex = fullText.indexOf(versionName);
    final versionEndIndex = versionStartIndex + versionName.length;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: "update Info",
      pageBuilder: (context, animation, secondaryAnimation) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 0, sigmaY: 0),
          child: Center(
            child: Material(
              color: Color(0xCD201034),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 180.h,
                    ),
                    Stack(
                      children: [
                        Center(
                          child: Padding(
                            padding: EdgeInsets.only(top: 81.h),
                            child: Container(
                              width: 297.w,
                              height: 286.h,
                              decoration: const BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(
                                      'assets/images/bg_update.png'),
                                  fit: BoxFit.fill,
                                ),
                              ),
                              child: Padding(
                                padding: EdgeInsets.only(left: 20.w, top: 20.h),
                                child: Text(
                                  S.of(context).appUpdate,
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 24.sp,
                                      fontWeight: FontWeight.w700),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.topRight,
                          child: Image.asset(
                            "assets/images/icon_update.png",
                            width: 179.w,
                            height: 204.h,
                            fit: BoxFit.fill,
                          ),
                        ),
                        Padding(
                            padding: EdgeInsets.only(top: 150.h),
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 24.w),
                              width: 345.w,
                              height: 226.h,
                              decoration: const BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(
                                      'assets/images/bg_update_info.png'),
                                  fit: BoxFit.fill,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 32.h,
                                  ),
                                  RichText(
                                    text: TextSpan(
                                      style: TextStyle(color: Colors.white, fontSize: 16.sp),
                                      children: [
                                        TextSpan(text: fullText.substring(0, versionStartIndex)),
                                        TextSpan(
                                          text: versionName,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                            fontSize: 20.sp,
                                          ),
                                        ),
                                        TextSpan(text: fullText.substring(versionEndIndex)),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 16.h),
                                  Text(
                                    updateDescription,
                                    style: TextStyle(
                                        fontSize: 14.sp, color: Colors.white),
                                  ),
                                  Expanded(child: SizedBox()),
                                  Row(
                                    children: [
                                      if (!mandatory) ...{
                                        Expanded(
                                          child: Container(
                                            height: 44.h,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              color: Colors.transparent,
                                              border: Border.all(
                                                color: Color(0xFFFF3BDF),
                                                width: 1,
                                              ),
                                            ),
                                            child: TextButton(
                                              style: TextButton.styleFrom(
                                                  padding: EdgeInsets.zero,
                                                  backgroundColor:
                                                      Colors.transparent),
                                              onPressed: () {
                                                Navigator.pop(context);
                                              },
                                              child: Center(
                                                child: Text(
                                                  S.of(Get.context!).notNow,
                                                  style: TextStyle(
                                                    fontSize: 16.sp,
                                                    color: Color((0xFFFF3BDF)),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 16.w),
                                      },
                                      Expanded(
                                        child: Container(
                                          height: 44.h,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            color: Colors.white,
                                            gradient: LinearGradient(
                                              colors: [
                                                Color(0xFFFF91EE),
                                                Color(0xFFFF3BDF),
                                              ],
                                              begin: Alignment.topCenter,
                                              end: Alignment.bottomCenter,
                                            ),
                                          ),
                                          child: TextButton(
                                            style: TextButton.styleFrom(
                                              padding: EdgeInsets.zero,
                                              foregroundColor: Colors.white,
                                            ),
                                            onPressed: () {
                                              onUpdate();
                                            },
                                            child: Center(
                                              child: Text(
                                                S.of(Get.context!).update,
                                                style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: Color((0xFF0D0D0D)),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 32.h),
                                ],
                              ),
                            ))
                      ],
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                    if (!mandatory)
                      GestureDetector(
                        onTap: () => Navigator.of(context).pop(),
                        child: Image.asset(
                          "assets/images/icon_dialog_close.webp",
                          width: 24.w,
                          height: 24.h,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutBack,
            ),
            child: child,
          ),
        );
      },
    );
  }
}
