import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/Api.dart';
import 'package:new_agnes/api/ApiProvider.dart';
import 'package:new_agnes/module/account/login/page.dart';
import 'package:new_agnes/module/account/password/forget/input_widgets/forget_email_input.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../../../../generated/l10n.dart';
import 'input_widgets/forget_update_input.dart';

class ForgetLogic extends GetxController {
  var isUpdate = false.obs;
  var sent = false.obs;

  final emailLogic = Get.put(EmailInputForgetC());
  final forgetUpdateLogic updateLogic = Get.put(forgetUpdateLogic());

  bool get canSendCode => emailLogic.email.value.isNotEmpty;

  Future<void> sendCode() async {
    Response res = await Get.find<ApiProvider>().post(
      Api.sendVerifyCode,
      {
        'email': emailLogic.email.value,
        'is_registry': false,
      },
    );

    if (res.statusCode == 200) {
      // 解析 JSON 响应
      final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
      // {"success":true,"message":"Validation code sent successfully"}
      final String message = responseBody['message'] ?? '';
      showSuccessToast(message);
      sent.value = true;
    } else {}
  }

  Future<void> updatePassword() async {
    debugPrint(
        "code:${updateLogic.code.value}--password:${updateLogic.password.value}---passwordConfirm:"
        "${updateLogic.passwordConfirm.value}---canResetPassword:${updateLogic.canResetPassword}");

    if (!updateLogic.canResetPassword) {
      showFailToast(S.of(Get.context!).writeSomething);
      return;
    }

    if (!updateLogic.isSame) {
      showFailToast("两次密码不相同！");
      return;
    }

    if (updateLogic.isWeakPassword) {
      showFailToast(S.of(Get.context!).useCharactersWithLettersNumbersSymbols);
      return;
    }

    if (updateLogic.isConfirmWeakPassword) {
      showFailToast(S.of(Get.context!).useCharactersWithLettersNumbersSymbols);
      return;
    }

    Response res = await Get.find<ApiProvider>().post(
      Api.resetPwd,
      {
        'new_password': updateLogic.password.value,
        'email': emailLogic.email.value,
        'verification_code': updateLogic.code.value,
      },
    );
    // 解析 JSON 响应
    final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
    if (res.statusCode == 200) {
      // {"success":true,"message":"Validation code sent successfully"}
      final String message = responseBody['message'] ?? '';
      showSuccessToast(message);
      Get.off(LoginPage());
    }
  }
}
