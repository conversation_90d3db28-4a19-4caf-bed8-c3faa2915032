import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/generated/l10n.dart';
import 'package:new_agnes/utils/bgCover.dart';
import 'forget_logic.dart';
import 'input_widgets/forget_email_input.dart';
import 'input_widgets/forget_update_input.dart';

class ForgetPage extends StatelessWidget {
  ForgetPage({super.key});

  final ForgetLogic logic = Get.put(ForgetLogic());

  @override
  Widget build(BuildContext context) {
    return buildLoginBg(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: Colors.transparent,
        appBar: null,
        body: SingleChildScrollView(
          child: Column(
            children: [
              Center(
                child: Column(
                  children: [
                    SizedBox(height: 112),
                    Image.asset(
                      'assets/images/agnes_logan.webp',
                      width: 134,
                      height: 71,
                      fit: BoxFit.contain,
                    ),
                    SizedBox(height: 10),
                    Text(
                      S.of(Get.context!).welcomeToAgnes,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 29,
                      ),
                    ),
                  ],
                ),
              ),
              Obx(
                () {
                  return logic.isUpdate.value
                      ? _buildPasswordInput()
                      : _buildEmailInput();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmailInput() {
    return Column(
      children: [
        SizedBox(height: 80),
        Text(
          S.of(Get.context!).resetYourPassword,
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            children: [
              SizedBox(height: 32),
              ForgetEmailInput(),
              SizedBox(height: 48),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 49,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.transparent,
                        border: Border.all(
                          color: Color(0xFFFF3BDF),
                          width: 1,
                        ),
                      ),
                      child: TextButton(
                        style: TextButton.styleFrom(
                            padding: EdgeInsets.zero,
                            backgroundColor: Colors.transparent),
                        onPressed: () {
                          logic.emailLogic.clearCache();
                          logic.updateLogic.clearCache();
                          Get.back();
                        },
                        child: Center(
                          child: Text(
                            S.of(Get.context!).back,
                            style: TextStyle(
                              fontSize: 16,
                              color: Color((0xFFFF3BDF)),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 24),
                  Expanded(
                    child: Container(
                      height: 49,
                      decoration: logic.emailLogic.isValidEmail
                          ? BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: Colors.white,
                              gradient: LinearGradient(
                                colors: [
                                  Color(0xFFFF91EE),
                                  Color(0xFFFF3BDF),
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                            )
                          : BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: Color(0x78733D7D),
                            ),
                      child: TextButton(
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () {
                          if (logic.emailLogic.isValidEmail) {
                            logic.isUpdate.value = true;
                            // logic.sendCode();
                          }
                        },
                        child: Center(
                          child: Text(
                            S.of(Get.context!).resetPassword,
                            style: TextStyle(
                              fontSize: 16,
                              color: logic.emailLogic.isValidEmail
                                  ? Color(0xFF0D0D0D)
                                  : Color(0xFF988B9A),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordInput() {
    return Column(
      children: [
        SizedBox(height: 80),
        Center(
          child: Text(
            textAlign: TextAlign.center,
            S.of(Get.context!).verificationCodeSentToXxxemailcom(
                logic.emailLogic.email.value),
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
            ),
          ),
        ),
        SizedBox(height: 32),
        ForgetUpdateInput(),
        SizedBox(height: 48),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 49,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.transparent,
                    border: Border.all(
                      color: Color(0xFFFF3BDF),
                      width: 1,
                    ),
                  ),
                  child: TextButton(
                    style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        backgroundColor: Colors.transparent),
                    // onPressed: () => Get.back(),
                    onPressed: () {
                      logic.updateLogic.clearCache();
                      logic.isUpdate.value = false;
                    },
                    child: Center(
                      child: Text(
                        S.of(Get.context!).back,
                        style: TextStyle(
                          fontSize: 16,
                          color: Color((0xFFFF3BDF)),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 24),
              Expanded(
                child: Obx(() => Container(
                      height: 49,
                      decoration: logic.updateLogic.canResetPassword
                          ? BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: Colors.white,
                              gradient: LinearGradient(
                                colors: [
                                  Color(0xFFFF91EE),
                                  Color(0xFFFF3BDF),
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                            )
                          : BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: Color(0x78733D7D),
                            ),
                      child: TextButton(
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () {
                          logic.updateLogic.canResetPassword
                              ? logic.updatePassword()
                              : null;
                        },
                        // onPressed:
                        //     logic.canSendCode ? () => logic.sendCode() : null,
                        child: Center(
                          child: Text(
                            S.of(Get.context!).updatePassword,
                            style: TextStyle(
                              fontSize: 16,
                              color: logic.updateLogic.canResetPassword
                                  ? Color(0xFF0D0D0D)
                                  : Color(0xFF988B9A),
                            ),
                          ),
                        ),
                      ),
                    )),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
