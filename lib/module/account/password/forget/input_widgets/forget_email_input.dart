import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/cmUtils.dart';

import '../../../../../generated/l10n.dart';

class ForgetEmailInput extends StatefulWidget {
  /// 邮箱输入框
  const ForgetEmailInput({super.key});

  @override
  State<ForgetEmailInput> createState() => _ForgetEmailInputState();
}

class _ForgetEmailInputState extends State<ForgetEmailInput> {
  final logic = Get.find<EmailInputForgetC>();
  final TextEditingController emailController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    emailController.addListener(() {
      logic.email.value = emailController.text;
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(() => Container(
              height: 49,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                    color: !logic.isValidEmailUiShow
                        ? Color(0xFFFE4D4D)
                        : Color(0xFFCADEE3)),
              ),
              child: Row(
                children: [
                  // 左侧图标
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 12),
                    child: Image.asset(
                      'assets/images/icon_mail.webp',
                      width: 20,
                      height: 20,
                      color: Colors.white,
                    ),
                  ),
                  // 邮箱输入框
                  Expanded(
                    child: TextField(
                      controller: emailController,
                      keyboardType: TextInputType.emailAddress,
                      textAlignVertical: TextAlignVertical.center,
                      style: TextStyle(fontSize: 16, color: Colors.white),
                      decoration: InputDecoration(
                        hintText: S.of(Get.context!).emaildomaincom,
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(vertical: 14),
                        hintStyle:
                            TextStyle(fontSize: 16, color: Color(0xFF757E88)),
                      ),
                    ),
                  ),
                ],
              ),
            )),
        Obx(() => Visibility(
            visible: !logic.isValidEmailUiShow,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12),
                Container(
                  margin: EdgeInsets.only(left: 12),
                  child: Text(
                    S.of(context).emailFormatIncorrect,
                    style: TextStyle(fontSize: 12.sp, color: Color(0xFFFE4D4D)),
                  ),
                ),
              ],
            ))),
      ],
    );
  }

  @override
  void dispose() {
    emailController.dispose();
    super.dispose();
  }
}

class EmailInputForgetC extends GetxController {
  var email = "".obs;

  get emailValid => email.trim().isNotEmpty;

  bool get isValidEmail =>
      CmUtils.isValidEmail(email.value) && email.value.isNotEmpty;

  bool get isValidEmailUiShow =>
      CmUtils.isValidEmail(email.value) || email.value.isEmpty;

  void clearCache() {
    email = ''.obs;
  }
}
