import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../generated/l10n.dart';
import '../../../../../utils/cmUtils.dart';
import '../forget_logic.dart';
import 'forget_email_input.dart';

class ForgetUpdateInput extends StatefulWidget {
  const ForgetUpdateInput({super.key});

  @override
  State<ForgetUpdateInput> createState() => _ForgetUpdateInputState();
}

class _ForgetUpdateInputState extends State<ForgetUpdateInput> {
  final updateLogic = Get.find<forgetUpdateLogic>();
  final emailInputLogic = Get.find<EmailInputForgetC>();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    updateLogic.send.value = S.of(context).send;
    updateLogic.startCountdown(context);
    codeController.addListener(() {
      updateLogic.code.value = codeController.text;
    });

    newPasswordController.addListener(() {
      updateLogic.password.value = newPasswordController.text;
    });

    confirmPasswordController.addListener(() {
      updateLogic.passwordConfirm.value = confirmPasswordController.text;
    });

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
        height: 49,
        margin: EdgeInsets.symmetric(horizontal: 24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Color(0xFFCADEE3)),
        ),
        child: Row(
          children: [
            SizedBox(width: 12),
            // 验证码输入框
            Expanded(
              child: TextField(
                controller: codeController,
                textAlignVertical: TextAlignVertical.center,
                style: TextStyle(fontSize: 16, color: Colors.white),
                decoration: InputDecoration(
                  hintText: S.of(Get.context!).enterVerificationCode,
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(vertical: 14),
                  hintStyle: TextStyle(fontSize: 16, color: Color(0xFF757E88)),
                ),
              ),
            ),
            SizedBox(width: 12),
            GestureDetector(
              onTap: () {
                if (emailInputLogic.emailValid && !updateLogic.isCounting) {
                  updateLogic.startCountdown(context);
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16),
                alignment: Alignment.center,
                child: Obx(() => Text(
                      updateLogic.send.value,
                      style: TextStyle(
                        fontSize: 16,
                        color: !emailInputLogic.emailValid ||
                                updateLogic.isCounting
                            ? Colors.grey
                            : Colors.white,
                      ),
                    )),
              ),
            ),
          ],
        ),
      ),
      SizedBox(height: 12),
      Padding(
        padding: EdgeInsets.only(left: 36),
        child: Text(
          textAlign: TextAlign.left,
          S.of(Get.context!).newPassword,
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
      ),
      SizedBox(height: 12),
      Obx(
        () => Container(
          height: 49,
          margin: EdgeInsets.symmetric(horizontal: 24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
                color: updateLogic.isWeakPassword
                    ? Color(0xFFFE4D4D)
                    : Color(0xFFCADEE3)),
          ),
          child: Row(
            children: [
              SizedBox(width: 12),
              Expanded(
                child: Obx(() => TextField(
                      controller: newPasswordController,
                      obscureText: updateLogic.isPasswordObscure.value,
                      textAlignVertical: TextAlignVertical.center,
                      style: TextStyle(fontSize: 16, color: Colors.white),
                      decoration: InputDecoration(
                        suffixIcon: IconButton(
                            onPressed: () {
                              updateLogic.isPasswordObscure.value =
                                  !updateLogic.isPasswordObscure.value;
                            },
                            icon: updateLogic.isPasswordObscure.value
                                ? Image.asset(
                                    'assets/images/icon_eye_close.webp',
                                    width: 24)
                                : Image.asset(
                                    'assets/images/icon_eye_open.webp',
                                    width: 24)),
                        hintText: S.of(Get.context!).enterPassword,
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(vertical: 14),
                        hintStyle:
                            TextStyle(fontSize: 16, color: Color(0xFF757E88)),
                      ),
                    )),
              ),
            ],
          ),
        ),
      ),
      SizedBox(height: 12),
      Obx(() => Visibility(
          visible: updateLogic.isWeakPassword,
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.only(left: 24),
                child: Text(
                  S
                      .of(context)
                      .atLeastSixCharactersLongWithLettersNumbersSymbols,
                  style: TextStyle(fontSize: 12.sp, color: Color(0xFFFE4D4D)),
                ),
              ),
              SizedBox(height: 12),
            ],
          ))),
      Padding(
        padding: EdgeInsets.only(left: 36),
        child: Text(
          textAlign: TextAlign.left,
          S.of(Get.context!).confirmPassword,
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
      ),
      SizedBox(height: 12),
      Obx(
        () => Container(
          height: 49,
          margin: EdgeInsets.symmetric(horizontal: 24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
                color: updateLogic.isConfirmWeakPassword
                    ? Color(0xFFFE4D4D)
                    : Color(0xFFCADEE3)),
          ),
          child: Row(
            children: [
              SizedBox(width: 12),
              Expanded(
                child: Obx(() => TextField(
                      controller: confirmPasswordController,
                      obscureText: updateLogic.isConfirmPasswordObscure.value,
                      textAlignVertical: TextAlignVertical.center,
                      style: TextStyle(fontSize: 16, color: Colors.white),
                      decoration: InputDecoration(
                        suffixIcon: IconButton(
                            onPressed: () {
                              updateLogic.isConfirmPasswordObscure.value =
                                  !updateLogic.isConfirmPasswordObscure.value;
                            },
                            icon: updateLogic.isConfirmPasswordObscure.value
                                ? Image.asset(
                                    'assets/images/icon_eye_close.webp',
                                    width: 24)
                                : Image.asset(
                                    'assets/images/icon_eye_open.webp',
                                    width: 24)),
                        hintText: S.of(Get.context!).confirmPassword,
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(vertical: 14),
                        hintStyle:
                            TextStyle(fontSize: 16, color: Color(0xFF757E88)),
                      ),
                    )),
              ),
            ],
          ),
        ),
      ),
      Obx(() => Visibility(
          visible: updateLogic.isConfirmWeakPassword,
          child: Column(
            children: [
              SizedBox(height: 12),
              Container(
                margin: EdgeInsets.only(left: 24),
                child: Text(
                  S
                      .of(context)
                      .atLeastSixCharactersLongWithLettersNumbersSymbols,
                  style: TextStyle(fontSize: 12.sp, color: Color(0xFFFE4D4D)),
                ),
              ),
            ],
          ))),
    ]);
  }

  @override
  void dispose() {
    codeController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }
}

class forgetUpdateLogic extends GetxController {
  var code = ''.obs;
  var password = ''.obs;
  var passwordConfirm = ''.obs;
  var isPasswordObscure = true.obs;
  var isConfirmPasswordObscure = true.obs;
  var isCounting = false;
  var send = ''.obs;

  bool get isWeakPassword =>
      password.isNotEmpty && CmUtils.isWeakPassword(password.value);

  bool get isConfirmWeakPassword =>
      passwordConfirm.isNotEmpty &&
      CmUtils.isWeakPassword(passwordConfirm.value);

  bool get isSame => password.value == passwordConfirm.value;

  bool get canResetPassword =>
      code.trim().isNotEmpty &&
      password.trim().isNotEmpty &&
      passwordConfirm.trim().isNotEmpty &&
      !CmUtils.isWeakPassword(password.value) &&
      !CmUtils.isWeakPassword(passwordConfirm.value);

  void startCountdown(BuildContext context) {
    Get.find<ForgetLogic>().sendCode();

    if (isCounting) return;
    int seconds = 60;
    send.value = '$seconds s';
    isCounting = true;

    Timer.periodic(Duration(seconds: 1), (timer) {
      seconds--;
      if (seconds > 0) {
        send.value = '$seconds s';
      } else {
        timer.cancel();
        send.value = S.of(context).send;
        isCounting = false;
      }
    });
  }

  void clearCache() {
    code = ''.obs;
    password = ''.obs;
    passwordConfirm = ''.obs;
    isPasswordObscure = true.obs;
    isConfirmPasswordObscure = true.obs;
    isCounting = false;
  }
}
