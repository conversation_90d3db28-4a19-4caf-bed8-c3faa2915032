import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/analytics/AnalyticsService.dart';
import 'package:new_agnes/module/account/register/input_widgets/register_input.dart';

import '../../../generated/l10n.dart';
import '../../../utils/bgCover.dart';
import 'register_logic.dart';

class RegisterWidget extends StatefulWidget {
  RegisterWidget({super.key});

  final RegisterLogic logic = Get.put(RegisterLogic());

  @override
  State<StatefulWidget> createState() {
    return RegisterPageState();
  }
}

class RegisterPageState extends State<RegisterWidget> {
  @override
  Widget build(BuildContext context) {
    return buildLoginBg(
      child: Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Colors.transparent,
      appBar: null,
      body: SingleChildScrollView(
          child: Column(
            children: [
              Center(
                child: Column(
                  children: [
                    SizedBox(height: 112),
                    Image.asset(
                      'assets/images/agnes_logan.webp',
                      width: 134,
                      height: 71,
                      fit: BoxFit.contain,
                    ),
                    SizedBox(height: 10),
                    Text(
                      S.of(Get.context!).welcomeToAgnes,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 29,
                      ),
                    ),
                    SizedBox(height: 56),
                    Center(
                      child: Text(
                        textAlign: TextAlign.center,
                        S.of(Get.context!).createAnAccountToSignUpForAgnes,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              _buildPasswordInput(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RegisterInput(),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 49,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.transparent,
                    border: Border.all(
                      color: Color(0xFFFF3BDF),
                      width: 1,
                    ),
                  ),
                  child: TextButton(
                    style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        backgroundColor: Colors.transparent),
                    onPressed: () {
                      widget.logic.registerInputLogic.clearCache();
                      Get.back();
                      Get.find<AnalyticsManager>().eventTo(
                          [AnalyticsProvider.singular],
                          AnalyticEvent.registerBack);
                    },
                    child: Center(
                      child: Text(
                        S.of(Get.context!).back,
                        style: TextStyle(
                          fontSize: 16,
                          color: Color((0xFFFF3BDF)),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 24),
              Expanded(
                child: Obx(()=>Container(
                  height: 49,
                  decoration: widget.logic.registerInputLogic.canRegister
                      ? BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.white,
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFFFF91EE),
                        Color(0xFFFF3BDF),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  )
                      : BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Color(0x78733D7D),
                  ),
                  child: TextButton(
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () {
                      widget.logic.registerInputLogic.canRegister
                          ? widget.logic.register()
                          : null;
                    },
                    child: Center(
                      child: Text(
                        S.of(Get.context!).agreeAndLogIn,
                        style: TextStyle(
                          fontSize: 16,
                          color: widget.logic.registerInputLogic.canRegister
                              ? Color(0xFF0D0D0D)
                              : Color(0xFF988B9A),
                        ),
                      ),
                    ),
                  ),
                )),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
