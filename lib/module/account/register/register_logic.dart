import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:new_agnes/analytics/AnalyticsService.dart';
import 'package:new_agnes/utils/loading_util.dart';

import '../../../api/Api.dart';
import '../../../api/ApiProvider.dart';
import '../../../api/StorageService.dart';
import '../../../generated/l10n.dart';
import '../../../main_tab_page.dart';
import '../../../utils/toastUtil.dart';
import '../login/page.dart';
import 'input_widgets/register_input.dart';
import 'input_widgets/register_phone_input.dart';

class RegisterLogic extends GetxController {
  final registerInputLogic = Get.put(RegisterInputLogic());
  final registerPhoneInputLogic = Get.put(RegisterPhoneInputLogic());

  Future<void> sendCode() async {
    if (!registerInputLogic.canSendCode) {
      return;
    }

    Response res = await Get.find<ApiProvider>().post(
      Api.sendVerifyCode,
      {
        'email': registerInputLogic.email.value,
        'is_registry': true,
      },
    );

    // 解析 JSON 响应
    final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
    if (res.statusCode == 200) {
      // {"success":true,"message":"Validation code sent successfully"}
      final String message = responseBody['message'] ?? '';
      showSuccessToast(message);
    }
  }

  Future<void> register() async {
    if (!registerInputLogic.canRegister) {
      showFailToast(S.of(Get.context!).writeSomething);
      return;
    }

    if (registerInputLogic.isWeakPassword) {
      showFailToast(S.of(Get.context!).atLeastSixCharactersLongWithLettersNumbersSymbols);
      return;
    }

    LoadingUtil.show();

    Get.find<AnalyticsManager>().eventTo(
        [AnalyticsProvider.singular], AnalyticEvent.registerAgreeToLogin);
    Response res = await Get.find<ApiProvider>().post(
      Api.register,
      {
        "username": "",
        'password': registerInputLogic.password.value,
        'email': registerInputLogic.email.value,
        'verification_code': registerInputLogic.code.value
      },
    );

    LoadingUtil.dismiss();

    // 解析 JSON 响应
    final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
    if (res.statusCode == 201) {
      Get.find<StorageService>().setLoginData(jsonEncode(responseBody));
      Get.find<AnalyticsManager>().event(AnalyticEvent.mailAccountRegistration);
      String? token =
          Get.find<StorageService>().getLoginData().accessToken ?? '';
      Get.find<ApiProvider>().setToken('${token}');
      Get.offAll(() => MainTabPage());
    } else {
      showFailToast(responseBody['detail'] ?? '${res.statusCode}');
    }
  }
}
