import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/analytics/AnalyticsService.dart';
import '../../../../generated/l10n.dart';
import '../../../../utils/cmUtils.dart';
import '../register_logic.dart';

class RegisterInput extends StatelessWidget {
  RegisterInput({super.key});

  @override
  Widget build(BuildContext context) {
    final registerInputLogic = Get.find<RegisterInputLogic>();
    final registerLogic = Get.find<RegisterLogic>();

    registerInputLogic.send.value = S.of(context).send;

    final TextEditingController emailController = TextEditingController();

    emailController.addListener(() {
      registerInputLogic.email.value = emailController.text;
    });

    final TextEditingController passwordController = TextEditingController();
    passwordController.addListener(() {
      registerInputLogic.password.value = passwordController.text;
    });

    final TextEditingController codeController = TextEditingController();
    codeController.addListener(() {
      registerInputLogic.code.value = codeController.text;
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 32),
        Obx(
          () => Container(
            height: 49,
            margin: EdgeInsets.symmetric(horizontal: 24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                  color: !registerInputLogic.isValidEmailUiShow
                      ? Color(0xFFFE4D4D)
                      : Color(0xFFCADEE3)),
            ),
            child: Row(
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  child: Image.asset(
                    'assets/images/icon_mail.webp',
                    width: 20,
                    height: 20,
                    color: Colors.white,
                  ),
                ),
                Expanded(
                  child: TextField(
                    controller: emailController,
                    keyboardType: TextInputType.emailAddress,
                    textAlignVertical: TextAlignVertical.center,
                    style: TextStyle(fontSize: 16, color: Colors.white),
                    decoration: InputDecoration(
                      hintText: S.of(Get.context!).emaildomaincom,
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 14),
                      hintStyle:
                          TextStyle(fontSize: 16, color: Color(0xFF757E88)),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Obx(() => Visibility(
            visible: !registerInputLogic.isValidEmailUiShow,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12),
                Container(
                  margin: EdgeInsets.only(left: 24),
                  child: Text(
                    S.of(context).emailFormatIncorrect,
                    style: TextStyle(fontSize: 12.sp, color: Color(0xFFFE4D4D)),
                  ),
                ),
              ],
            ))),
        SizedBox(height: 16),
        Obx(
          () => Container(
            height: 49,
            margin: EdgeInsets.symmetric(horizontal: 24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                  color: registerInputLogic.isWeakPassword
                      ? Color(0xFFFE4D4D)
                      : Color(0xFFCADEE3)),
            ),
            child: Row(
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  child: Image.asset(
                    'assets/images/icon_password.webp',
                    width: 20,
                    height: 20,
                    color: Colors.white,
                  ),
                ),
                Expanded(
                  child: Obx(() => TextField(
                        controller: passwordController,
                        keyboardType: TextInputType.visiblePassword,
                        obscureText: registerInputLogic.isPasswordObscure.value,
                        textAlignVertical: TextAlignVertical.center,
                        style: TextStyle(fontSize: 16, color: Colors.white),
                        decoration: InputDecoration(
                          suffixIcon: IconButton(
                              onPressed: () {
                                registerInputLogic.isPasswordObscure.value =
                                    !registerInputLogic.isPasswordObscure.value;
                              },
                              icon: registerInputLogic.isPasswordObscure.value
                                  ? Image.asset(
                                      'assets/images/icon_eye_close.webp',
                                      width: 24)
                                  : Image.asset(
                                      'assets/images/icon_eye_open.webp',
                                      width: 24)),
                          hintText: S.of(Get.context!).enterPassword,
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(vertical: 14),
                          hintStyle:
                              TextStyle(fontSize: 16, color: Color(0xFF757E88)),
                        ),
                      )),
                ),
              ],
            ),
          ),
        ),
        Obx(() => Visibility(
            visible: registerInputLogic.isWeakPassword,
            child: Column(
              children: [
                SizedBox(height: 12),
                Container(
                  margin: EdgeInsets.only(left: 24),
                  child: Text(
                    S
                        .of(context)
                        .atLeastSixCharactersLongWithLettersNumbersSymbols,
                    style: TextStyle(fontSize: 12.sp, color: Color(0xFFFE4D4D)),
                  ),
                ),
              ],
            ))),
        SizedBox(height: 16),
        Container(
          height: 49,
          margin: EdgeInsets.symmetric(horizontal: 24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Color(0xFFCADEE3)),
          ),
          child: Row(
            children: [
              SizedBox(width: 12),
              Expanded(
                child: TextField(
                  controller: codeController,
                  textAlignVertical: TextAlignVertical.center,
                  style: TextStyle(fontSize: 16, color: Colors.white),
                  decoration: InputDecoration(
                    hintText: S.of(Get.context!).enterVerificationCode,
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 14),
                    hintStyle:
                        TextStyle(fontSize: 16, color: Color(0xFF757E88)),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (registerInputLogic.canSendCode &&
                      !registerInputLogic.isCounting) {
                    registerInputLogic.startCountdown(context);
                    Get.find<AnalyticsManager>().eventTo(
                        [AnalyticsProvider.singular],
                        AnalyticEvent.registerSend);
                  }
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  alignment: Alignment.center,
                  child: Obx(
                    () => Text(
                      "${registerInputLogic.send}",
                      style: TextStyle(
                        fontSize: 16,
                        color: !registerInputLogic.canSendCode ||
                                registerInputLogic.isCounting
                            ? Colors.grey
                            : Colors.white,
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
        SizedBox(height: 48),
      ],
    );
  }
}

class RegisterInputLogic extends GetxController {
  /// 注册方式 0:手机号 1:邮箱
  var loginType = 1.obs;
  var email = ''.obs;
  var password = ''.obs;
  var code = ''.obs;
  var isPasswordObscure = true.obs;
  var isCounting = false;
  var send = ''.obs;

  bool get isWeakPassword =>
      password.isNotEmpty && CmUtils.isWeakPassword(password.value);

  bool get isValidEmail =>
      CmUtils.isValidEmail(email.value) && email.trim().isNotEmpty;

  bool get isValidEmailUiShow =>
      CmUtils.isValidEmail(email.value) || email.value.isEmpty;

  bool get canSendCode =>
      email.trim().isNotEmpty && CmUtils.isValidEmail(email.value);

  bool get canRegister =>
      email.trim().isNotEmpty &&
      password.trim().isNotEmpty &&
      code.trim().isNotEmpty &&
      CmUtils.isValidEmail(email.value) &&
      !CmUtils.isWeakPassword(password.value);

  void clearCache() {
    email = ''.obs;
    password = ''.obs;
    code = ''.obs;
    isPasswordObscure = true.obs;
  }

  void startCountdown(BuildContext context) {
    Get.find<RegisterLogic>().sendCode();

    if (isCounting) return;
    int seconds = 60;
    send.value = '$seconds s';
    isCounting = true;

    Timer.periodic(Duration(seconds: 1), (timer) {
      seconds--;
      if (seconds > 0) {
        send.value = '$seconds s';
      } else {
        timer.cancel();
        send.value = S.of(context).send;
        isCounting = false;
      }
    });
  }
}
