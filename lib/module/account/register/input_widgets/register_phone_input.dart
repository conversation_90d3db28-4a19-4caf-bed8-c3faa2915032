import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/account/login/logic.dart';

import '../../../../data/countries.dart';
import '../../../../generated/l10n.dart';
import '../../../../widget/CountryPickerBottomSheet.dart';

class RegisterPhoneInput extends StatelessWidget {
  /// 手机号输入框
  const RegisterPhoneInput({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<RegisterPhoneInputLogic>();
    final TextEditingController phoneController = TextEditingController();
    phoneController.addListener(() {
      logic.phone.value = phoneController.text;
    });

    return Container(
      height: 49,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10), // 圆角边框
        border: Border.all(color: Color(0xFFCADEE3)), // 外层统一边框
      ),
      child: Row(
        children: [
          SizedBox(width: 16),
          // 国家编码部分
          GestureDetector(
            onTap: () {
              // 选择国家编码
              CountryPickerDialogX.show(
                context,
                countryList: countryList,
                selectedCountry: countryList.firstWhere(
                    (item) => item.number == logic.countryCode.value),
                onCountrySelected: (country) =>
                    logic.countryCode.value = country.number,
                searchText: S.of(context).searchForACountry,
                style: CountryPickerStyle(
                  backgroundColor: const Color(0xFF000A19),
                  countryNameStyle: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                  countryCodeStyle: const TextStyle(
                    color: Color(0xFF988B9A),
                    fontSize: 14,
                  ),
                  searchFieldInputDecoration: InputDecoration(
                    hintText: S.of(context).searchForACountry,
                    hintStyle: const TextStyle(color: Colors.grey),
                    suffixIcon: const Icon(Icons.search),
                  ),
                ),
              );
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 0, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius:
                    BorderRadius.horizontal(left: Radius.circular(10)),
              ),
              child: Obx(() {
                return Text(
                  "+${logic.countryCode}",
                  style: TextStyle(fontSize: 16, color: Colors.white),
                );
              }),
            ),
          ),
          SizedBox(width: 12),
          // 竖线分隔符
          Container(
            width: 1,
            height: 27,
            color: Color(0xFFCADEE3),
          ),

          SizedBox(width: 12),
          // 手机号输入框（占主要空间）
          Expanded(
            child: TextField(
              controller: phoneController,
              style: TextStyle(fontSize: 16, color: Colors.white),
              keyboardType: TextInputType.phone,
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                hintText: S.of(context).phoneNumber,
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                isDense: true,
                hintStyle: TextStyle(fontSize: 16, color: Color(0xFF757E88)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 手机号输入逻辑
class RegisterPhoneInputLogic extends GetxController {
  var phone = ''.obs;
  var countryCode = '65'.obs;

  get phoneNumberValid => phone.trim().isNotEmpty;
}
