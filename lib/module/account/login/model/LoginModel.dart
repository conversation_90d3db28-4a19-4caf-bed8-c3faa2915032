///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class LoginModel {
/*
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************.L3lcQVhFXHwXx34U1hd50GR3DyyW8fpBxRgZyvmVcZ0",
  "token_type": "bearer",
  "new_account": false
}

{
  "access_token" : "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************.6AmQb3UFXIgHLdM04ekZlBJoEM_nulYiHu5_54_RhU0",
  "token_type" : "bearer",
  "sign_type" : "sign_up"
}
*/

  String? accessToken;
  String? tokenType;
  bool? newAccount;
  String? signType;

  LoginModel({
    this.accessToken,
    this.tokenType,
    this.newAccount,
    this.signType,
  });
  LoginModel.fromJson(Map<String, dynamic> json) {
    accessToken = json['access_token']?.toString();
    tokenType = json['token_type']?.toString();
    newAccount = json['new_account'];
    signType = json['sign_type']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['access_token'] = accessToken;
    data['token_type'] = tokenType;
    data['new_account'] = newAccount;
    data['sign_type'] = signType;
    return data;
  }
}
