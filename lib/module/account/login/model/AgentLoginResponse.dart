class AgentLoginResponse {
  final String? accessToken;
  final String? tokenType;
  final String? detail;
  final String? signType;

  AgentLoginResponse(
      {this.accessToken, this.tokenType, this.detail, this.signType});

  factory AgentLoginResponse.fromJson(Map<String, dynamic> json) {
    return AgentLoginResponse(
      accessToken: json['access_token'] as String?,
      tokenType: json['token_type'] as String?,
      detail: json['detail'] as String?,
      signType: json['sign_type'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (accessToken != null) 'access_token': accessToken,
      if (tokenType != null) 'token_type': tokenType,
      if (detail != null) 'detail': detail,
      if (detail != null) 'sign_type': signType,
    };
  }

  bool hasDetail() {
    return detail?.isNotEmpty == true;
  }

  bool hasAccessToken() {
    return accessToken?.isNotEmpty == true;
  }
}
