import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/analytics/AnalyticsService.dart';
import 'package:new_agnes/generated/l10n.dart';
import 'package:new_agnes/module/account/register/page.dart';
import 'package:new_agnes/module/debug/global.dart';
import 'package:new_agnes/utils/bgCover.dart';
import 'package:new_agnes/widget/GlobalFloatingWrapper.dart';

import '../../../api/Api.dart';
import '../../../utils/cmUtils.dart';
import '../../../widget/RichTextWithLinks.dart';
import '../../web/single_page_web/page.dart';
import '../password/forget/page.dart';
import 'input_widgets/login_email_input.dart';
import 'input_widgets/login_phone_input.dart';
import 'logic.dart';

class LoginPage extends StatelessWidget {
  LoginPage({super.key});

  final LoginLogic logic = Get.put(LoginLogic());
  double bottomBarHeight = 0;

  @override
  Widget build(BuildContext context) {
    if (bottomBarHeight == 0) {
      bottomBarHeight = CmUtils.getBottomBarHeight(Get.context!);
    }
    return buildLoginBg(
      child: Scaffold(
        appBar: null,
        extendBody: true,
        backgroundColor: Colors.transparent,
        resizeToAvoidBottomInset: true,
        body: GlobalFloatingWrapper(
          showDebugFloating: Global.isDebug,
          child: Obx(() {
            return SingleChildScrollView(
                child: Container(
                    constraints: BoxConstraints(maxHeight: 1.sh),
                    child: Column(
                      children: [
                        SizedBox(height: 30,),
                        Expanded(
                          child: Column(
                            children: logic.loginType.value != 2
                                ? accountLogin()
                                : thirdPartyLogin(),
                          ),
                        ),
                        Container(
                          height: bottomBarHeight,
                        )
                      ],
                    )));
          }),
        ),
      ),
    );
  }

  /// 账号登录
  List<Widget> accountLogin() {
    return [
      Center(
        child: Column(
          children: [
            SizedBox(height: 112),
            Image.asset(
              'assets/images/agnes_logan.webp',
              width: 134,
              height: 71,
              fit: BoxFit.contain,
            ),
            SizedBox(height: 10),
            Text(
              S.of(Get.context!).welcomeToAgnes,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: 30,
              ),
            ),
          ],
        ),
      ),
      Expanded(child: SizedBox()),
      buildLoginButton(),

      /// 注册及底部协议
      Expanded(
        child: SizedBox(),
      ),
      buildTermsOfService()
    ];
  }

  /// 第三方登录
  List<Widget> thirdPartyLogin() {
    return [
      Expanded(
        child: SizedBox(),
      ),
      Center(
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Image.asset(
                  'assets/images/icon_h.webp',
                  width: 53,
                  height: 57,
                ),
                const Text(
                  "ello",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    height: 1,
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
            Text(
              S.of(Get.context!).welcomeToAgnes,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: 30,
              ),
            ),
          ],
        ),
      ),
      Expanded(
        child: SizedBox(),
      ),
      Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (GetPlatform.isAndroid) ...[
            buildGoogleLoginButton(),
          ],
          if (GetPlatform.isIOS) ...[
            SizedBox(height: 16),
            buildAppleLoginButton(),
          ],
          SizedBox(height: 16),
          buildOtherLoginButton(),
          SizedBox(height: 28),

          /// 注册及底部协议
          buildTermsOfService(),
        ],
      )
    ];
  }

  /// 中部登录框
  Widget buildLoginButton() {
    return DefaultTabController(
      // ✅ 添加 DefaultTabController
      length: 2, // 手机号登录 + 邮箱登录两个 Tab
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Tab 标签栏
            SizedBox(
              height: 31,
              child: TabBar(
                onTap: (index) {
                  logic.loginType.value = index;
                  if (index == 0) {
                    logic.emailLogic.reset();
                  } else {
                    logic.phoneLogic.reset();
                  }
                  Get.find<AnalyticsManager>().eventTo(
                      [AnalyticsProvider.singular],
                      index == 0
                          ? AnalyticEvent.loginPhoneTab
                          : AnalyticEvent.loginEmailTab);
                },
                overlayColor: WidgetStateProperty.all(Colors.transparent),
                padding: EdgeInsets.symmetric(horizontal: 16),
                // 已选中 Tab 字体样式
                labelStyle: TextStyle(
                    fontSize: 15,
                    color: Colors.white,
                    fontWeight: FontWeight.normal),
                // 未选中 Tab 字体样式
                unselectedLabelStyle: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF988B9A),
                    fontWeight: FontWeight.normal),
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(color: Color(0xFFFF3BDF), width: 1),
                  insets: EdgeInsets.symmetric(horizontal: 62),
                ),
                tabs: [
                  Tab(text: S.of(Get.context!).phone),
                  Tab(text: S.of(Get.context!).email),
                ],
              ),
            ),
            SizedBox(height: 24),
            // Tab 内容区域
            SizedBox(
              height: 194,
              child: TabBarView(
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // 手机号登录页面
                  Column(
                    children: [
                      LoginPhoneInput(),
                      SizedBox(height: 16),
                      LoginCodeInput(),
                    ],
                  ),

                  // 邮箱登录页面
                  Column(
                    children: [
                      LoginEmailInput(),
                      SizedBox(height: 16),
                      LoginPasswordInput(),
                      Row(
                        children: [
                          Spacer(),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 12.0),
                            child: GestureDetector(
                              onTap: () {
                                Get.to(
                                  ForgetPage(),
                                  transition: Transition.noTransition,
                                );
                                Get.find<AnalyticsManager>().eventTo(
                                    [AnalyticsProvider.singular],
                                    AnalyticEvent.loginEmailForgetPassword);
                              },
                              child: Text(
                                S.of(Get.context!).forgotPassword,
                                style: TextStyle(
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ],
              ),
            ),
            // SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 49,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.transparent,
                      border: Border.all(
                        color: Color(0xFFFF3BDF),
                        width: 1,
                      ),
                    ),
                    child: TextButton(
                      style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          backgroundColor: Colors.transparent),
                      onPressed: () {
                        Get.find<AnalyticsManager>().eventTo(
                            [AnalyticsProvider.singular],
                            logic.loginType.value == 0
                                ? AnalyticEvent.loginPhoneBack
                                : AnalyticEvent.loginEmailBack);
                        logic.loginType.value = 2;
                      },
                      child: Center(
                        child: Text(
                          S.of(Get.context!).back,
                          style: TextStyle(
                            fontSize: 16,
                            color: Color((0xFFFF3BDF)),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 24),
                Expanded(
                  child: Obx(() => Container(
                        height: 49,
                        decoration: logic.signInBtnValid
                            ? BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: Colors.white,
                                gradient: LinearGradient(
                                  colors: [
                                    Color(0xFFFF91EE),
                                    Color(0xFFFF3BDF),
                                  ],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                ),
                              )
                            : BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: Color(0x78733D7D),
                              ),
                        child: TextButton(
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.zero,
                            foregroundColor: Colors.white,
                          ),
                          onPressed: () {
                            logic.signInBtnValid ? logic.login() : null;
                            Get.find<AnalyticsManager>().eventTo(
                                [AnalyticsProvider.singular],
                                logic.loginType.value == 0
                                    ? AnalyticEvent.loginPhoneSignIn
                                    : AnalyticEvent.loginEmailSignIn);
                          },
                          child: Center(
                            child: Text(
                              S.of(Get.context!).signIn,
                              style: TextStyle(
                                fontSize: 16,
                                color: logic.signInBtnValid
                                    ? Color(0xFF0D0D0D)
                                    : Color(0xFF988B9A),
                              ),
                            ),
                          ),
                        ),
                      )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 底部协议
  Widget buildTermsOfService() {
    return Padding(
      padding: EdgeInsets.only(bottom: 30, top: 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (logic.loginType.value != 2)
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  S.of(Get.context!).dontHaveAnAccount,
                  style: TextStyle(color: Color(0xFF988B9A), fontSize: 14),
                ),
                GestureDetector(
                  onTap: () {
                    Get.to(RegisterWidget(),
                        transition: Transition.noTransition);
                    Get.find<AnalyticsManager>().eventTo(
                        [AnalyticsProvider.singular], AnalyticEvent.register);
                  },
                  child: Text(S.of(Get.context!).signUp,
                      style: TextStyle(color: Colors.white, fontSize: 14)),
                ),
              ],
            ),
          SizedBox(height: 28),
          Text.rich(
            RichTextWithLinks(
              text: S
                  .of(Get.context!)
                  .bySigningInYouAreAgreeingToTheTermsOfServiceAndPrivacyPolicy(
                    S.of(Get.context!).termsOfService,
                    S.of(Get.context!).privacyPolicy,
                  ),
              links: {
                S.of(Get.context!).termsOfService: () =>
                    Get.to(SingleWidgetWebWidget(
                      Api.termsUrl,
                      title: S.of(Get.context!).termsOfService,
                    )),
                S.of(Get.context!).privacyPolicy: () =>
                    Get.to(SingleWidgetWebWidget(
                      Api.privacyUrl,
                      title: S.of(Get.context!).privacyPolicy,
                    )),
              },
              style: const TextStyle(color: Color(0xFF988B9A), fontSize: 12),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// google 服务登录
  Widget buildGoogleLoginButton() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24),
      child: ElevatedButton(
        onPressed: () {
          // 登录逻辑
          logic.onGoogleLogin(Get.context!);
          Get.find<AnalyticsManager>().eventTo(
              [AnalyticsProvider.singular], AnalyticEvent.loginWithGoogle);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          minimumSize: Size(double.infinity, 49),
          shape: RoundedRectangleBorder(
              side: BorderSide(color: Color(0xFFCADEE3), width: 1),
              borderRadius: BorderRadius.circular(10)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/icon_google.webp',
              width: 20,
              height: 20,
            ),
            SizedBox(width: 8),
            Text(
              S.of(Get.context!).continueWithGoogle,
              style: TextStyle(
                color: Colors.white,
                fontSize: 15,
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// apple 服务登录
  Widget buildAppleLoginButton() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24),
      child: ElevatedButton(
        onPressed: () {
          // 登录逻辑
          debugPrint("apple 登录成功");
          Get.find<AnalyticsManager>().eventTo(
              [AnalyticsProvider.singular], AnalyticEvent.loginWithApple);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          shadowColor: Colors.transparent,
          minimumSize: Size(double.infinity, 49),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/icon_apple.webp',
              width: 20,
              height: 20,
              color: Colors.black,
            ),
            SizedBox(width: 12),
            Text(
              S.of(Get.context!).continueWithApple,
              style: TextStyle(
                color: Colors.black,
                fontSize: 15,
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 账号邮箱登录
  Widget buildOtherLoginButton() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24),
      child: ElevatedButton(
        onPressed: () {
          // 登录逻辑
          logic.loginType.value = 0;
          Get.find<AnalyticsManager>().eventTo(
              [AnalyticsProvider.singular], AnalyticEvent.loginWithOther);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(0x78733D7D),
          shadowColor: Colors.transparent,
          minimumSize: Size(double.infinity, 49),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
        child: Text(
          S.of(Get.context!).continueWithOtherWays,
          style: TextStyle(
            color: Colors.white,
            fontSize: 15,
            fontWeight: FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
