import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:google_api_availability/google_api_availability.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:new_agnes/analytics/AnalyticsService.dart';
import 'package:new_agnes/analytics/singular/SingularService.dart';
import 'package:new_agnes/api/Api.dart';
import 'package:new_agnes/api/ApiProvider.dart';
import 'package:new_agnes/module/account/login/model/LoginModel.dart';
import 'package:new_agnes/module/home/<USER>/page.dart';
import 'package:new_agnes/utils/loading_util.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../../../api/StorageService.dart';
import '../../../generated/l10n.dart';
import '../../../main_tab_page.dart';
import '../../chat/group/group_chat/agora_logic.dart';
import 'input_widgets/login_email_input.dart';
import 'input_widgets/login_phone_input.dart';
import 'model/AgentLoginResponse.dart';

class LoginLogic extends GetxController {
  /// 登录方式 0:手机号 1:邮箱,2:google or Apple
  var loginType = 2.obs;

  final phoneLogic = Get.put(PhoneInputLogic());
  final emailLogic = Get.put(EmailInputLogic());

  get signInBtnValid => (loginType.value < 2 && loginType.value == 0)
      ? phoneLogic.phoneNumberValid && phoneLogic.codeValid && phoneLogic.isValidPhoneNumber.value
      : emailLogic.emailValid && emailLogic.passwordValid && emailLogic.isValidEmail;

  static void cleanLoginInfo(){
    bool isPhoneRegistered = Get.isRegistered<PhoneInputLogic>();
    if (isPhoneRegistered) {
      PhoneInputLogic phoneInputLogic = Get.find<PhoneInputLogic>();
      phoneInputLogic.reset();
    }
    bool isEmailRegistered = Get.isRegistered<EmailInputLogic>();
    if (isEmailRegistered) {
      EmailInputLogic emailInputLogic = Get.find<EmailInputLogic>();
      emailInputLogic.reset();
    }

  }

  Future<void> getCode(BuildContext context,{onResult}) async {
    Response res = await Get.find<ApiProvider>().post(
      Api.sendVerifyCode,
      {
        'user_phone': '+${phoneLogic.countryCode} ${phoneLogic.phone.trim()}',
        'is_registry': true,
      },
    );

    if (res.statusCode == 200) {
      // 解析 JSON 响应
      final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
      // {"success":true,"message":"Validation code sent successfully"}
      final String message = responseBody['message'] ?? '';
      showSuccessToast(message);
      if(onResult!=null){
        onResult();
      }
    } else {
      showFailToast(res.statusText ?? S.of(context).networkConnectionLost);
    }
  }

  // 登录接口
  Future<void> login() async {
    late Response res;
    LoadingUtil.show();
    res = await Get.find<ApiProvider>().post(
      loginType.value == 0 ? Api.phoneLogin : Api.emailLogin,
      loginType.value == 0
          ? {
              'userphone':
                  '+${phoneLogic.countryCode} ${phoneLogic.phone.value}',
              'validation_code': phoneLogic.code.value,
            }
          : {
              'email': emailLogic.email.value,
              'password': emailLogic.password.value,
            },
    );
    LoadingUtil.dismiss();

    if (res.statusCode == 200) {
      final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
      Get.find<StorageService>().setLoginData(jsonEncode(responseBody));

      var signType = Get.find<StorageService>().getLoginData().signType ?? "";

      if (loginType.value == 0) {
        Get.find<AnalyticsManager>().event(signType == "sign_up"
            ? AnalyticEvent.phoneRegistration
            : AnalyticEvent.phoneLogin);
      } else {
        Get.find<AnalyticsManager>().event(AnalyticEvent.mailAccountLogin);
      }
      String? token =
          Get.find<StorageService>().getLoginData().accessToken ?? '';
      Get.find<StorageService>().removeAppKey();
      Get.find<StorageService>().removeAgoraInfo();
      Get.find<ApiProvider>().setToken('${token}');
      Get.offAll(() => MainTabPage());
    } else {
      String? errorMsg;
      try{
        Map bodyString = jsonDecode(res.bodyString??"{}");
        if(bodyString.containsKey("message")){
          errorMsg = bodyString['message']?.toString();
        }else {
          errorMsg = bodyString['detail']?.toString();
        }
      }catch(e){

      }
      showFailToast(errorMsg ?? S.of(Get.context!).networkConnectionLost);
    }
  }

  static const String showGooglePlayMsg =
      "Google Play services are unavailable, please check your device or use another login method";
  String showGooglePlayMsg2 =
      "Detected that your device does not have Google Play services installed, unable to login in using Google";
  String showTimeOutMsg = "Request timeout or other exceptions";
  static const String loginToastMsg = "Login failed, exception";

  static final List<String> scopes = <String>[
    'email',
    'profile',
  ];

  Future<void> onGoogleLogin(BuildContext context) async {
    bool resultCheck = await checkGooglePlayServices();
    print('google--loginWithGoogle被调用--resultCheck="${resultCheck}"');
    if (!resultCheck) {
      showDialog(
        context: context,
        builder: (_) => AlertDialog(
          title: Text('Reminder information'),
          content: Text(showGooglePlayMsg2),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Confirm'),
            ),
          ],
        ),
      );
      return;
    }
    LoadingUtil.show();
    Future.delayed(Duration(seconds: 10), () {
      LoadingUtil.dismiss();
    });
    try {
      await loginWithGoogle((bool issucc) {
        LoadingUtil.dismiss();
        print('Google 登录后调用的函数--' + issucc.toString());
        if (issucc) {
          Get.offAll(() => MainTabPage());
        } else {
          showFailToast(showTimeOutMsg);
        }
      });
    } catch (e) {
      print(e);
      LoadingUtil.dismiss();
      showFailToast(showTimeOutMsg);
    }
  }

  static Future<void> loginWithGoogle(var loginBack) async {
    print('google--loginWithGoogle被调用');
    try {
      final _googleSignIn = GoogleSignIn(
        scopes: scopes,
        // clientId: "*************-ad6kr2nhcqpnaoh63hbft5lg2eg3qhvr.apps.googleusercontent.com",
        serverClientId:
            '*************-m9a2cusss3pncoj88e42kgurs28fs458.apps.googleusercontent.com',
      );
      bool isSignedIn = await _googleSignIn.isSignedIn();
      if (isSignedIn) {
        await _googleSignIn.signOut();
      }
      final account = await _googleSignIn.signIn();
      final auth = await account?.authentication;
      final idToken = auth?.accessToken;
      print('google--授权返回，idToken: $idToken');
      if (idToken != null) {
        print('google--授权返回，idToken length:${idToken.length} ');
        Get.find<StorageService>().setIdToken(idToken);
        Get.find<StorageService>().removeAppKey();
        Get.find<StorageService>().removeAgoraInfo();
        Response response = await Get.find<ApiProvider>().post(
          Api.googleLogin,
          {"token": idToken},
        );

        if (response.statusCode == 200) {
          if (response.body == null) {
            loginBack(false);
          }
          Get.find<StorageService>().setLoginData(jsonEncode(response.body));
          var loginUserInfo = LoginModel.fromJson(response.body);
          var newAccount =
              Get.find<StorageService>().getLoginData().newAccount ?? false;
          Get.find<AnalyticsManager>().event(newAccount
              ? AnalyticEvent.googleAccountRegistration
              : AnalyticEvent.googleAccountLogin);
          if (loginUserInfo.accessToken != null &&
              loginUserInfo.accessToken!.isNotEmpty) {
            Get.find<ApiProvider>().setToken(loginUserInfo.accessToken!);
            loginBack(true);
          } else {
            loginBack(false);
          }
        } else {
          loginBack(false);
        }
      } else {
        LoadingUtil.dismiss();
      }
    } catch (e, stackTrace) {
      print('google--异常类型: ${e.runtimeType}');
      print('google--异常信息: $e');
      print('google--堆栈信息: $stackTrace');
      if (e is PlatformException) {
        print(
            'google--堆栈信息: code=${e.code},message=${e.message},details=${e.details}');
      }
      // 捕获异常并判断是否是 Google Play 服务不可用
      if (e is PlatformException &&
          (e.code == 'sign_in_failed' ||
              e.code.contains('google_play_services'))) {
        print('Google Play 服务不可用，请检查您的设备或使用其他登录方式');
        showFailToast(showGooglePlayMsg);
      } else {
        print('其他错误: $e');
        showFailToast(loginToastMsg);
      }
      loginBack(false);
    }
  }

  Future<bool> checkGooglePlayServices() async {
    if (Platform.isIOS) {
      return true;
    } else if (Platform.isAndroid) {
      final availability = await GoogleApiAvailability.instance
          .checkGooglePlayServicesAvailability();
      return availability == GooglePlayServicesAvailability.success;
    } else {
      return false;
    }
  }
}
