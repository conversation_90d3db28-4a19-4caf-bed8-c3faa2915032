import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/cmUtils.dart';

import '../../../../generated/l10n.dart';

class LoginEmailInput extends StatelessWidget {
  /// 邮箱输入框
  const LoginEmailInput({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.isRegistered<EmailInputLogic>()
        ? Get.find<EmailInputLogic>()
        : Get.put(EmailInputLogic());
    final TextEditingController emailController = TextEditingController();
    emailController.addListener(() {
      logic.email.value = emailController.text;
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () => Container(
            height: 49,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                  color: !logic.isValidEmailShow
                      ? Color(0xFFFE4D4D)
                      : Color(0xFFCADEE3)),
            ),
            child: Row(
              children: [
                // 左侧图标
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  child: Image.asset(
                    'assets/images/icon_mail.webp',
                    width: 20,
                    height: 20,
                    color: Colors.white,
                  ),
                ),
                // 邮箱输入框
                Expanded(
                  child: TextField(
                    controller: emailController,
                    keyboardType: TextInputType.emailAddress,
                    textAlignVertical: TextAlignVertical.center,
                    style: TextStyle(fontSize: 16, color: Colors.white),
                    decoration: InputDecoration(
                      hintText: S.of(Get.context!).emaildomaincom,
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 14),
                      hintStyle:
                          TextStyle(fontSize: 16, color: Color(0xFF757E88)),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Obx(() => Visibility(
            visible: !logic.isValidEmailShow,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12),
                Container(
                  margin: EdgeInsets.only(left: 12),
                  child: Text(
                    S.of(context).emailFormatIncorrect,
                    style: TextStyle(fontSize: 12.sp, color: Color(0xFFFE4D4D)),
                  ),
                ),
              ],
            ))),
      ],
    );
  }
}

class LoginPasswordInput extends StatelessWidget {
  /// 密码输入框
  const LoginPasswordInput({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.isRegistered<EmailInputLogic>()
        ? Get.find<EmailInputLogic>()
        : Get.put(EmailInputLogic());
    final TextEditingController passwordController = TextEditingController();
    passwordController.addListener(() {
      logic.password.value = passwordController.text;
    });
    return Container(
      height: 49,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Color(0xFFCADEE3)),
      ),
      child: Obx(() {
        return TextField(
          controller: passwordController,
          obscureText: logic.isPasswordObscure.value,
          textAlignVertical: TextAlignVertical.center,
          style: TextStyle(fontSize: 16, color: Colors.white),
          decoration: InputDecoration(
            hintText: S.of(Get.context!).enterPassword,
            border: InputBorder.none,
            suffixIcon: IconButton(
              onPressed: () {
                logic.isPasswordObscure.value = !logic.isPasswordObscure.value;
              },
              icon: logic.isPasswordObscure.value
                  ? Image.asset('assets/images/icon_eye_close.webp', width: 24)
                  : Image.asset('assets/images/icon_eye_open.webp', width: 24),
            ),
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            isDense: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 14),
            hintStyle: TextStyle(fontSize: 16, color: Color(0xFF757E88)),
          ),
        );
      }),
    );
  }
}

class EmailInputLogic extends GetxController {
  var email = "".obs;
  var password = "".obs;

  var isPasswordObscure = true.obs;

  get emailValid => email.trim().isNotEmpty;

  bool get isValidEmail => CmUtils.isValidEmail(email.value);

  bool get isValidEmailShow => CmUtils.isValidEmail(email.value) || email.value.isEmpty;

  get passwordValid => password.trim().isNotEmpty;

  void reset() {
    email.value = '';
    password.value = '';
    isPasswordObscure.value = true;
  }
}
