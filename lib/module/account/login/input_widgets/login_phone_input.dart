import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/analytics/AnalyticsService.dart';
import 'package:new_agnes/api/StorageService.dart';
import 'package:new_agnes/module/account/login/logic.dart';

import '../../../../data/countries.dart';
import '../../../../generated/l10n.dart';
import '../../../../widget/CountryPickerBottomSheet.dart';

class LoginPhoneInput extends StatelessWidget {
  /// 手机号输入框
  const LoginPhoneInput({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.isRegistered<PhoneInputLogic>()
        ? Get.find<PhoneInputLogic>()
        : Get.put(PhoneInputLogic());
    final TextEditingController phoneController = TextEditingController();
    phoneController.addListener(() {
      logic.phone.value = phoneController.text;
      if (phoneController.text.trim().isNotEmpty) {
        logic.verifyPhone();
      } else {
        logic.isValidPhoneNumber.value = true;
      }
    });

    // 初始化发送按钮文字
    logic.send.value = S.of(context).send;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () => Container(
            height: 49,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10), // 圆角边框
              // border: Border.all(color: Color(0xFFCADEE3)),
              border: Border.all(
                  color: !logic.isValidPhoneNumber.value
                      ? Color(0xFFFE4D4D)
                      : Color(0xFFCADEE3)), // 外层统一边框
            ),
            child: Row(
              children: [
                SizedBox(width: 16),
                // 国家编码部分
                GestureDetector(
                  onTap: () {
                    // 选择国家编码
                    CountryPickerDialogX.show(
                      context,
                      countryList: countryList,
                      selectedCountry: countryList.firstWhere(
                          (item) => item.number == logic.countryCode.value),
                      onCountrySelected: (country) {
                        logic.countryCode.value = country.number;
                        logic.verifyPhone();
                        Get.find<StorageService>()
                            .setUserCountryCode(country.number);
                      },
                      searchText: S.of(context).searchForACountry,
                      style: CountryPickerStyle(
                        backgroundColor: const Color(0xFF000A19),
                        countryNameStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                        countryCodeStyle: const TextStyle(
                          color: Color(0xFF988B9A),
                          fontSize: 14,
                        ),
                        searchFieldInputDecoration: InputDecoration(
                          hintText: S.of(context).searchForACountry,
                          hintStyle: const TextStyle(color: Colors.grey),
                          suffixIcon: const Icon(Icons.search),
                        ),
                      ),
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 0, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius:
                          BorderRadius.horizontal(left: Radius.circular(10)),
                    ),
                    child: Obx(() {
                      return Text(
                        "+${logic.countryCode}",
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      );
                    }),
                  ),
                ),
                SizedBox(width: 12),
                // 竖线分隔符
                Container(
                  width: 1,
                  height: 27,
                  color: Color(0xFFCADEE3),
                ),

                SizedBox(width: 12),
                // 手机号输入框（占主要空间）
                Expanded(
                  child: TextField(
                    controller: phoneController,
                    style: TextStyle(fontSize: 16, color: Colors.white),
                    keyboardType: TextInputType.phone,
                    textAlignVertical: TextAlignVertical.center,
                    decoration: InputDecoration(
                      hintText: S.of(context).phoneNumber,
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      isDense: true,
                      hintStyle:
                          TextStyle(fontSize: 16, color: Color(0xFF757E88)),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                // 倒计时按钮
                GestureDetector(
                  onTap: () {
                    if (logic.phoneNumberValid &&
                        logic.isValidPhoneNumber.value &&
                        !logic.isCounting) {
                      logic.startCountdown(context);
                      Get.find<AnalyticsManager>().eventTo(
                          [AnalyticsProvider.singular],
                          AnalyticEvent.loginPhoneSend);
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    alignment: Alignment.center,
                    child: Obx(() => Text(
                          "${logic.send}",
                          style: TextStyle(
                            fontSize: 16,
                            color: !logic.phoneNumberValid ||
                                    logic.isCounting ||
                                    !logic.isValidPhoneNumber.value
                                ? Colors.grey
                                : Colors.white,
                          ),
                        )),
                  ),
                ),
              ],
            ),
          ),
        ),
        Obx(() => Visibility(
            visible: !logic.isValidPhoneNumber.value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12),
                Container(
                  margin: EdgeInsets.only(left: 12),
                  child: Text(
                    S.of(context).thePhoneNumberIsInvalid,
                    style: TextStyle(fontSize: 12.sp, color: Color(0xFFFE4D4D)),
                  ),
                ),
              ],
            ))),
      ],
    );
  }
}

class LoginCodeInput extends StatelessWidget {
  /// 验证码输入框
  const LoginCodeInput({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<PhoneInputLogic>();
    final TextEditingController codeController = TextEditingController();
    codeController.addListener(() {
      logic.code.value = codeController.text;
    });

    return Container(
      height: 49,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10), // 圆角边框
        border: Border.all(color: Color(0xFFCADEE3)), // 外层统一边框
      ),
      child: TextField(
        controller: codeController,
        keyboardType: TextInputType.number,
        textAlignVertical: TextAlignVertical.center,
        style: TextStyle(fontSize: 16, color: Colors.white),
        decoration: InputDecoration(
          hintText: S.of(context).verificationCode,
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          isDense: true,
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 14),
          hintStyle: TextStyle(fontSize: 16, color: Color(0xFF757E88)),
        ),
      ),
    );
  }
}

/// 手机号输入逻辑
class PhoneInputLogic extends GetxController {
  var send = ''.obs;
  var phone = ''.obs;
  var code = ''.obs;
  var countryCode = '65'.obs;

  var isCounting = false;

  get phoneNumberValid => phone.trim().isNotEmpty;

  get codeValid => code.trim().isNotEmpty;

  var isValidPhoneNumber = true.obs;

  @override
  void onInit() {
    super.onInit();
    String? code = Get.find<StorageService>().getUserCountryCode();
    debugPrint('user chosen countryCode: $code');
    if (code == null || code.isEmpty) {
      code = countryList.getLocalCountryCode();
      debugPrint('user local countryCode: $code');
    }
    countryCode.value = code ?? "65";
  }

  Future<void> verifyPhone() async {
    try {
      final res = await parse(
        "+${countryCode.value}" + phone.value,
        region: " ",
      );
      isValidPhoneNumber.value = true;
    } catch (e) {
      print(e);
      isValidPhoneNumber.value = false;
    }
  }

  Future<void> startCountdown(BuildContext context) async {
    try {
      final res = await parse(
        "+${countryCode.value}" + phone.value,
        region: " ",
      );
      isValidPhoneNumber.value = true;
    } catch (e) {
      print(e);
      isValidPhoneNumber.value = false;
      return;
    }
    Get.put(LoginLogic()).getCode(context);
    debugPrint('phone: $phone, code: $code');
    if (isCounting) return;
    int seconds = 60;
    send.value = '$seconds s';
    isCounting = true;

    Timer.periodic(Duration(seconds: 1), (timer) {
      seconds--;
      if (seconds > 0) {
        send.value = '$seconds s';
      } else {
        timer.cancel();
        send.value = S.of(context).send;
        isCounting = false;
      }
    });
  }

  void reset() {
    send.value = '';
    phone.value = '';
    code.value = '';
    countryCode.value = '65';
    isValidPhoneNumber.value = true;
    isCounting = false;
  }
}
