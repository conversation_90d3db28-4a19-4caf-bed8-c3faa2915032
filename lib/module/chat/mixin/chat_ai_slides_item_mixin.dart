import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:markdown_widget/config/all.dart';
import 'package:markdown_widget/config/markdown_generator.dart';
import 'package:markdown_widget/widget/all.dart';
import 'package:new_agnes/api/stream/stream_message_type_enum.dart';
import 'package:new_agnes/module/chat/widget/rotating_loading_image.dart';

import '../widget/chat_ai_slides_planner_widget.dart';
import '../widget/ppt_lay.dart';

mixin ChatAiSlidesItemMixin {
  bool _isExpanded = false;

  List<Color> getBackgroundColor(StreamMessageTypeEnum type, String agentName) {
    switch (type) {
      case StreamMessageTypeEnum.plan_schedule:
        return [Color(0xFF77CDFF), Color(0xFF77CDFF).withValues(alpha: 0.07)];
      case StreamMessageTypeEnum.slide_design:
        return [Color(0xFFDFCBA3), Color(0xFFDFCBA3).withValues(alpha: 0.07)];
      case StreamMessageTypeEnum.search_web:
        return [Color(0xFFB7E992), Color(0xFFD0FFAE).withValues(alpha: 0.07)];
      case StreamMessageTypeEnum.search_image:
        return [Color(0xFFFF84F4), Color(0xFFFF84F4).withValues(alpha: 0.07)];
      case StreamMessageTypeEnum.design_mentor:
        return [Color(0xFFFF84F4), Color(0xFFFF84F4).withValues(alpha: 0.07)];
      case StreamMessageTypeEnum.visual_principles:
        return [Color(0xFFD8AEFF), Color(0xFFD8AEFF).withValues(alpha: 0.07)];
      case StreamMessageTypeEnum.prompt_enhance:
        return [Color(0xFFFF6530), Color(0xFFFF6530).withValues(alpha: 0.07)];
      default:
        return [Color(0xFFFEB7BD), Color(0xFFFEB7BD).withValues(alpha: 0.07)];
    }
  }

  Color getBorderColor(StreamMessageTypeEnum type, String agentName) {
    switch (type) {
      case StreamMessageTypeEnum.plan_schedule:
        return Color(0xFF77CDFF);
      case StreamMessageTypeEnum.slide_design:
        return Color(0xFFDFCBA3);
      case StreamMessageTypeEnum.search_web:
        return Color(0xFFD0FFAE);
      case StreamMessageTypeEnum.search_image:
        return Color(0xFFFF84F4);
      case StreamMessageTypeEnum.design_mentor:
        return Color(0xFFFF84F4);
      case StreamMessageTypeEnum.visual_principles:
        return Color(0xFFD8AEFF);
      case StreamMessageTypeEnum.prompt_enhance:
        return Color(0xFFFF6530);
      default:
        return Color(0xFFFEB7BD);
    }
  }

  ScrollPhysics get physics {
    if (_isExpanded) {
      return const AlwaysScrollableScrollPhysics();
    }
    return const NeverScrollableScrollPhysics();
  }

  void changeExpandState(VoidCallback setState) {
    _isExpanded = !_isExpanded;
    setState();
  }

  /// Wrap an inner scrollable to hand off remaining drag to the outer scroll
  /// when the inner hits its top/bottom edge.
  Widget wrapWithEdgeHandoff(
      Widget scrollable, ScrollController? outerScrollController) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        final outer = outerScrollController;
        if (outer == null || !outer.hasClients) return false;

        if (notification is OverscrollNotification) {
          final position = outer.position;
          final double target = (position.pixels + notification.overscroll)
              .clamp(position.minScrollExtent, position.maxScrollExtent);
          if (target != position.pixels) {
            position.jumpTo(target);
            return true;
          }
        }
        if (notification is ScrollUpdateNotification) {
          final delta = notification.scrollDelta;
          if (delta == null) return false;
          final metrics = notification.metrics;
          final bool atTop = metrics.pixels <= metrics.minScrollExtent;
          final bool atBottom = metrics.pixels >= metrics.maxScrollExtent;
          final bool draggingUpwards = delta < 0;
          final bool draggingDownwards = delta > 0;

          if ((atTop && draggingUpwards) || (atBottom && draggingDownwards)) {
            final position = outer.position;
            final double target = (position.pixels + delta)
                .clamp(position.minScrollExtent, position.maxScrollExtent);
            if (target != position.pixels) {
              position.jumpTo(target);
              return true;
            }
          }
        }
        return false;
      },
      child: scrollable,
    );
  }

  Widget appendExpandButton(Widget child, VoidCallback iconClick,{bool isExpand = true,double minHeight = 114}) {
    if (!isExpand) {
      return Container(
        constraints: _isExpanded
            ? BoxConstraints(
          minHeight: minHeight,
          maxHeight: 508.h,
        )
            : BoxConstraints(
          minHeight: minHeight,
          maxHeight: 114.h,
        ),
        child: child,
      );
    }
    return Container(
      constraints: _isExpanded
          ? BoxConstraints(
              minHeight: 114.h,
              maxHeight: 508.h,
            )
          : BoxConstraints(
              minHeight: 114.h,
              maxHeight: 114.h,
            ),
      child: Stack(
        children: [
          // Let child dictate natural height within constraints
          child,
          Positioned(
            left: 8,
            bottom: 0,
            child: GestureDetector(
              onTap: () {
                iconClick();
              },
              child: Image.asset(
                _isExpanded
                    ? "assets/images/ic_expend_close.png"
                    : "assets/images/ic_expend_open.png",
                width: 20,
                height: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget appendMaskToContainer(Widget child, EdgeInsets insets,{bool isMask = true}) {
    if (_isExpanded || !isMask) {
      return Padding(
        padding: insets,
        child: child,
      );
    }
    return Stack(
      fit: StackFit.expand,
      children: [
        Padding(
          padding: insets,
          child: child,
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 40.h,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.07),
                ],
              ),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildMarkdownWidget(String content, String agentName,ScrollController? outerScrollController) {
    return Container(
      // margin: EdgeInsets.only(top: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: MarkdownGenerator(
          linesMargin: EdgeInsets.symmetric(vertical: 8.h),
        ).buildWidgets(content, config: _getMarkConfig(agentName,outerScrollController)),
      ),
    );
  }

  MarkdownConfig _getMarkConfig(String agentName,ScrollController? outerScrollController) {
    return MarkdownConfig(configs: [
      HrConfig.darkConfig,
      H1Config.darkConfig,
      H2Config.darkConfig,
      H3Config.darkConfig,
      H4Config.darkConfig,
      H5Config.darkConfig,
      H6Config.darkConfig,
      PreConfig.darkConfig,
      PConfig(
          textStyle: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              height: 1.4,
              backgroundColor: Colors.transparent)),
      PreConfig(
        language: 'json',
        builder: (String? code, String? language) {
          if (language != "json") {
            return Text(
              code ?? "",
              style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                  height: 1.4,
                  backgroundColor: Colors.transparent),
            );
          }
          try {
            dynamic value = jsonDecode(code ?? "{}");
            if (agentName == "planner") {
              //计划
              return ChatAiSlidesPlannerWidget(
                map: value,
              );
            } else {
              return JsonPPtLay(Get.context!,value,outerScrollController: outerScrollController);
            }
          } catch (e) {
            return Container(
                alignment: Alignment.centerRight,
                child: RotatingLoadingImage(
                    imagePath: "assets/images/icon_load.webp",
                    width: 20.w,
                    height: 20.h));
          }
        },
      ),
      CodeConfig(
          style: TextStyle(
              backgroundColor: Colors.transparent, color: Colors.white)),
      BlockquoteConfig.darkConfig,
      ListConfig(
          marginLeft: 16.w,
          marginBottom: 4.h,
          marker: (a, b, c) {
            return Padding(
              padding: EdgeInsets.only(top: 10.h),
              child: Container(
                alignment: Alignment.center,
                width: 4.w,
                height: 4.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
              ),
            );
          })
    ]);
  }
}
