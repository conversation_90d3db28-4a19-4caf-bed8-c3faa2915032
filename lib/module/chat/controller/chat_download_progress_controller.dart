import 'dart:async';

import 'package:get/get.dart';
import 'package:new_agnes/api/Api.dart';
import 'package:new_agnes/api/ApiProvider.dart';
import 'package:new_agnes/utils/chat_slides_download_utils.dart';

class ChatDownloadProgressController extends GetxController {
  final progress = 0.0.obs;
  final time = 0.obs;
  final isDownloading = false.obs;
  Timer? _timer;

  void startDownload(
    String fileUrl,
    String format,
    String fileName,
    String downloadId,
    Function(String filePath, String format)? onSuccess,
    Function(String error, String format)? onError,
  ) async {
    _timer?.cancel();
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      time.value = time.value + 1;
    });
    ChatDownloadFileUtils.downloadFileWithCallbacks(
        fileUrl: fileUrl,
        fileName: fileName,
        downloadId: downloadId,
        onProgress: (progress) {
          this.progress.value = progress.overallProgress;
        },
        onSuccess: (filePath) {
          onSuccess?.call(filePath, format);
          cancelTime();
        },
        onError: (error) {
          onError?.call(error, format);
          cancelTime();
        },
        onCancelled: () {
          cancelTime();
        },
    );
  }

  void cancelTime(){
    _timer?.cancel();
    _timer = null;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    cancelTime();
    super.dispose();
  }
}
