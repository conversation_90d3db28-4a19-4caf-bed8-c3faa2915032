import 'dart:async';
import 'dart:isolate';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/widget/TapHigh.dart';

import '../../../../generated/l10n.dart';
import '../../../../utils/logger_utils.dart';

final String _kOverlayNameHome = 'overlay_port_home';

final String _kPortNameHome = 'call_button_widget_port';

class CallFloatingButtonWidget extends StatefulWidget {
  const CallFloatingButtonWidget({Key? key}) : super(key: key);

  @override
  State<CallFloatingButtonWidget> createState() =>
      _CallFloatingButtonWidgetState();
}

class _CallFloatingButtonWidgetState extends State<CallFloatingButtonWidget> {
  CallButtonWidgetStatus status = CallButtonWidgetStatus.waiting;

  int totalSeconds = 0;

  bool isVisible = true;

  String statusText = "";

  // 格式化时间（MM:SS）
  String _formatTime() {
    final hours = (totalSeconds ~/ 3600).toString().padLeft(2, '0');
    final minutes = ((totalSeconds % 3600) ~/ 60).toString().padLeft(2, '0');
    final seconds = (totalSeconds % 60).toString().padLeft(2, '0');
    if (totalSeconds ~/ 3600 > 0) {
      return '$hours:$minutes:$seconds';
    }
    return '$minutes:$seconds';
  }

  Function? onButtonTapCallBack;

  ReceivePort? _receivePort;

  bool isShowCeiling = false;
  bool isLeft = true;

  OverlayPosition? overlayPosition;
  Timer? shrinkTimer; //缩小倒计时

  @override
  void initState() {
    super.initState();
    IsolateNameServer.removePortNameMapping(_kPortNameHome);
    _receivePort = ReceivePort();
    final res = IsolateNameServer.registerPortWithName(
      _receivePort!.sendPort,
      _kPortNameHome,
    );
    LoggerUtils.d("$res: OVERLAY");
    print('悬浮框初始化');
    _receivePort?.listen((event) {
      // 处理状态更新事件
      if (event["event"] == CallEvent.callStatus.name) {
        setState(() {
          status = event["arg"]["callStatus"];
        });
      }
      if (event["event"] == CallEvent.refresh.name) {
        setState(() {
          totalSeconds = event["arg"]["totalSeconds"];
          status = CallButtonWidgetStatus.values[event["arg"]["status"]];
          statusText = event['arg']["waiting"];
        });
      }

      if (event["event"] == CallEvent.showVisibility.name) {
        setState(() {
          isVisible = event["arg"]["isVisible"];
        });
      }

      if (event["event"] == CallEvent.hideVisibility.name) {
        setState(() {
          isVisible = event["arg"]["isVisible"];
          isShowCeiling = false;
        });
      }
    });

    FlutterOverlayWindow.overlayListener.listen((data) async {
      if (data["type"] == "init") {
        //初始化数据
        setState(() {
          isShowCeiling = false;
          // initTimer();
          // startTimer();
        });
      }
      if (data["type"] == "moveFinish") {
        var moveX = data['x'];
        var width = data['width'];
        var layWidth = data['layWidth'];
        var rightWidth = width - layWidth;
        if (moveX < 0) {
          setState(() {
            isLeft = false;
            isShowCeiling = true;
          });
          //吸附了
          // startTimer();
        } else if (moveX > rightWidth) {
          setState(() {
            isLeft = true;
            isShowCeiling = true;
            print('刷新2');
          });
        } else {
          setState(() {
            isShowCeiling = false;
          });
        }
      }
      if (data["type"] == "moveStart") {
        var moveX = data['x'];
        var width = data['width'];
        var layWidth = data['layWidth'];
        var rightWidth = width - layWidth;
        if (isShowCeiling) {
          if (moveX > 0 && moveX < rightWidth) {
            setState(() {
              isShowCeiling = false;
            });
          }
        }
        // initTimer();
      }
    });
  }

  // void startTimer(){
  //   if (shrinkTimer == null) {
  //     shrinkTimer = Timer(Duration(seconds: 3), () async {
  //       await FlutterOverlayWindow.resizeOverlay(32, 66, true);
  //       setState(() {
  //         isShowCeiling = true;
  //         initTimer();
  //       });
  //     });
  //   }
  // }
  //
  // void initTimer(){
  //   if (shrinkTimer != null) {
  //     shrinkTimer!.cancel();
  //     shrinkTimer = null;
  //   }
  // }

  @override
  void dispose() {
    IsolateNameServer.removePortNameMapping(_kPortNameHome);
    _receivePort?.close();
    _receivePort = null;
    isShowCeiling = false;
    if (shrinkTimer != null) {
      shrinkTimer!.cancel();
      shrinkTimer = null;
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return buildDraggableBtn();
  }

  // 构建可拖动的悬浮按钮（带计时器显示）
  Widget buildDraggableBtn() {
    return GestureDetector(
      onTap: () async {
        LoggerUtils.d("CallFloatingButtonWidget 被点击");
        // 先尝试调用本地回调
        // 如果是在 overlay 中，通过 FlutterOverlayWindow 发送数据到主应用
        try {
          IsolateNameServer.lookupPortByName(_kOverlayNameHome)?.send({
            "action": "floating_button_clicked",
          });
          LoggerUtils.d("通过 IsolateNameServer 发送点击事件到主应用");
        } catch (e) {
          LoggerUtils.e("发送 overlay 数据失败: $e");
        }
      },
      child: AnimatedOpacity(
        opacity: isVisible ? 1.0 : 0.0, // 淡入淡出动画
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: AnimatedScale(
            scale: isVisible ? 1.0 : 0.5, // 缩放动画
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: Container(
              child: !isShowCeiling
                  ? Container(
                      width: CallFloatingButtonWidgetManager().buttonSize.w,
                      height: CallFloatingButtonWidgetManager().buttonSize.w,
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      decoration: BoxDecoration(
                        color: Color(0xE3FFFFFF),
                        borderRadius: BorderRadius.circular(14.r),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            "assets/icon/phone_call.png",
                            width: 24.w,
                            height: 24.w,
                            fit: BoxFit.cover,
                          ),
                          SizedBox(
                            height: 5.w,
                          ),
                          Text(
                            status == CallButtonWidgetStatus.contacting
                                ? _formatTime()
                                : statusText,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 13.sp,
                              height: 1,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Container(
                      child: Row(
                        children: [
                          isLeft ? SizedBox() : Expanded(child: SizedBox()),
                          TapHigh(
                              onTap: () async {
                                setState(() {
                                  isShowCeiling = false;
                                });
                                // startTimer();
                              },
                              child: Container(
                                  height: 66.w,
                                  width: 32.w,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      color: Color(0x78733D7D),
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(
                                            isLeft ? 0.r : 6.r),
                                        topRight: Radius.circular(
                                            isLeft ? 6.r : 0.r),
                                        bottomLeft: Radius.circular(
                                            isLeft ? 0.r : 6.r),
                                        bottomRight: Radius.circular(
                                            isLeft ? 6.r : 0.r),
                                      )),
                                  child: RotationTransition(
                                    turns: AlwaysStoppedAnimation(
                                        isLeft ? 0.5 : 0),
                                    // 0.5 = 180度
                                    child: Image.asset(
                                      'assets/icon/icon_arrow_white.png',
                                      width: 20.w,
                                      height: 20.w,
                                    ),
                                  ))),
                          isLeft ? Expanded(child: SizedBox()) : SizedBox(),
                        ],
                      ),
                    ),
            )),
      ),
    );
  }
}

enum CallButtonWidgetStatus {
  waiting,
  idle,
  // ending,
  contacting,
}

class CallFloatingButtonWidgetManager {
  CallFloatingButtonWidgetManager._internal();

  static final CallFloatingButtonWidgetManager _instance =
      CallFloatingButtonWidgetManager._internal();

  factory CallFloatingButtonWidgetManager() {
    return _instance;
  }

  double btnX = 300; // 初始X坐标（右下角）
  double btnY = 500; // 初始Y坐标
  double buttonSize = 70;

  /// 记录当前路由地址
  String currentRouteName = "";

  /// 悬浮框按钮是否创建
  bool isCreate = false;

  /// 当前应用是否处于前台
  bool isFront = true;

  /// 记录当前弹窗的groupId
  String currentGroupId = "";

  // 1. 定时器(callFloatingAlert 消失时的剩余时长，开启定时器)
  Timer? _timer;

  // 默认显示为等待接听
  CallButtonWidgetStatus _status = CallButtonWidgetStatus.idle;

  CallButtonWidgetStatus get status => _status;

  Function? onButtonTapCallBack;

  ReceivePort? _receivePort;

  // 端口注册和监听已经在 rtc_logic.dart 的 showPhoneCallAlertWidget() 中处理
  // 这个方法不再需要，因为会与 rtc_logic.dart 中的端口注册冲突
  void initCallFloatingButtonWidgetManager() {
    IsolateNameServer.removePortNameMapping(_kOverlayNameHome);
    _receivePort = ReceivePort();
    final res = IsolateNameServer.registerPortWithName(
      _receivePort!.sendPort,
      _kOverlayNameHome,
    );
    LoggerUtils.d("$res: OVERLAY");
    _receivePort?.listen((message) {
      print("message from OVERLAY: $message");
      if (message["action"] == "floating_button_clicked") {
        if (CallFloatingButtonWidgetManager().isFront) {
          onButtonTapCallBack?.call();
        } else {
          FlutterOverlayWindow.moveToFront();
        }
      }
    });
  }

  setCallStatus(CallButtonWidgetStatus status) {
    _status = status;
    // IsolateNameServer.lookupPortByName(_kPortNameHome)?.send({"event":CallEvent.callStatus.name,"arg":{
    //   "callStatus":status,
    // }});
  }

  void setIsCreate(bool value) {
    isCreate = value;
  }

  void setOnButtonTapCallBack({Function? callback}) {
    onButtonTapCallBack = callback;
  }

  // 刷新文字信息
  void refreshText(CallButtonWidgetStatus sta, int totalSeconds,
      {String? waiting}) {
    IsolateNameServer.lookupPortByName(_kPortNameHome)?.send({
      "event": CallEvent.refresh.name,
      "arg": {
        "status": sta.index,
        "totalSeconds": totalSeconds,
        "waiting": waiting ?? ''
      }
    });
  }

  // 销毁按钮（页面退出时调用）
  void dispose() {
    IsolateNameServer.removePortNameMapping(_kOverlayNameHome);
    _receivePort?.close();
    _receivePort = null;
    _timer?.cancel();
    currentGroupId = "";
    setCallStatus(CallButtonWidgetStatus.idle);
  }

  /// 关闭overlay
  void closeOverlay() {
    FlutterOverlayWindow.closeOverlay();
  }

  /// 初始化overlay显示数据
  void initOverlay({required CallButtonWidgetStatus status}) {
    FlutterOverlayWindow.shareData({
      "type": "startShow",
    });
  }

  // 隐藏按钮（仅隐藏，不销毁Overlay，避免重建）
  void hideBtn() {
    IsolateNameServer.lookupPortByName(_kPortNameHome)?.send({
      "event": CallEvent.hideVisibility.name,
      "arg": {
        "isVisible": false,
      }
    });
  }

  // 显示按钮（从隐藏状态恢复）
  void showBtnAgain() {
    IsolateNameServer.lookupPortByName(_kPortNameHome)?.send({
      "event": CallEvent.showVisibility.name,
      "arg": {
        "isVisible": true,
      }
    });
  }

  // 创建定时器
  void startTimer(int? seconds, {Function? timerCallback}) {
    if (seconds == null || seconds <= 0) return;
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      seconds = seconds! - 1;
      LoggerUtils.d("悬浮按钮定时器倒计时----$seconds");
      if (seconds == 0) {
        _timer!.cancel();
        if (status == CallButtonWidgetStatus.waiting) {
          closeOverlay();
        }
      }
      timerCallback?.call(seconds);
    });
  }

  /// 销毁定时器
  void destroyTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }
  }
}

enum CallEvent {
  callStatus,
  refresh,
  hideVisibility,
  showVisibility,
  onTap, // 添加点击事件
}
