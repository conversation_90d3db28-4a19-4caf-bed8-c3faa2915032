import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_group_user_info.dart';
import 'package:new_agnes/module/chat/widget/deep_research_lay.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:new_agnes/widget/ImageUrl.dart';

class UserItemAvatar extends StatelessWidget {
  Member? user;

  UserItemAvatar(this.user);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        alignment: Alignment.center,
        child: Stack(
          children: [
            user!.isSpeak!.value?Lottie.asset("assets/json/rtc_user_bg.json",
                width: 94.w,
                height: 94.w,
                fit: BoxFit.fill):Si<PERSON><PERSON><PERSON>(),
            Container(
              padding: EdgeInsets.all(10.w),
              child: UrlImage(
                user!.avatarUrl,
                width: 74.w,
                height: 74.w,
                fit: BoxFit.cover,
                radius: 180,
                errorWidget:user!.userId=='1'? Image.asset(
                  "assets/icon/icon_agens_chat.png",
                  width: 74.w,
                  height: 74.w,
                  fit: BoxFit.cover,
                ):DefaultUserAvatar(),
              ),
            ),
            // Container(
            //   child: Text('${user!.username}',style: TextStyle(color: Colors.red),),
            // )
          ],
        ),
      );
    });
  }
}
