import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_logic.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_utils.dart';
import 'package:new_agnes/module/chat/group/group_chat_room/CallFloatingButton.dart';
import 'package:new_agnes/module/chat/group/group_chat_room/UserItemAvatar.dart';
import 'package:new_agnes/module/chat/group/group_chat_room/rtc_call_timer.dart';
import 'package:new_agnes/module/chat/widget/deep_research_lay.dart';
import 'package:new_agnes/module/chat/widget/rotating_loading_image.dart';
import 'package:new_agnes/utils/bgCover.dart';
import 'package:new_agnes/utils/event_bus.dart';
import 'package:new_agnes/widget/ComAppbar.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import 'package:new_agnes/widget/ImageUrl.dart';
import 'package:new_agnes/widget/TapHigh.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../dialog/message_dialog.dart';
import '../../../../generated/l10n.dart';
import '../../../../utils/logger_utils.dart';
import '../../../../widget/PrinterText.dart';
import '../../../../widget/animated_page_indicator.dart';
import '../../../../widget/message_long_tap_tools.dart';
import '../group_chat/rtc_chat/rtc_speech_send_message_utils.dart';
import '../message_chat/logic/group_chat_message_controller.dart';
import 'CallFloatingButtonWidget.dart';

class GroupChatRoomPage extends StatefulWidget {
  int type; //0 群聊页面  1 悬浮框
  String groupName;
  String groupId;
  String thirdId;
  List<String>? selectIds;

  GroupChatRoomPage(
      {Key? key,
      this.type = 1,
      required this.groupName,
      required this.groupId,
      this.thirdId = '',
      this.selectIds})
      : super(key: key);

  @override
  State<GroupChatRoomPage> createState() => _GroupChatRoomPageState();
}

class _GroupChatRoomPageState extends State<GroupChatRoomPage> {
  late Worker worker;
  RtcLogic rtcLogic = Get.find<RtcLogic>();
  final controller = Get.isRegistered<GroupChatMessageController>()
      ? Get.find<GroupChatMessageController>()
      : Get.put(GroupChatMessageController());
  ScrollController _scrollController = ScrollController();
  RxInt _currentInedex = 0.obs;

  @override
  void dispose() {
    // TODO: implement dispose
    _scrollController.dispose();
    eventBus.fire(RtcEvent(type: 1));
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    MessageLongTapTools.hideCustomOverlay();
    rtcLogic.thirdGroupId = widget.thirdId;
    rtcLogic.currentGroupId = widget.groupId;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (rtcLogic.userList.length > 0) {
        for (int a = 0; a < rtcLogic.userList.length; a++) {
          rtcLogic.userList[a].isSpeak!.value = false;
        }
      }
      rtcLogic.createGroupAudioChat(widget.type, widget.groupId, callback: () {
        if (widget.type == 0) {
          rtcLogic.sendRtcMessage('', widget.groupName, widget.groupId,
              widget.thirdId, '', ChatType.GroupChat, widget.selectIds!);
        }
      });
    });
    worker = ever(rtcLogic.robotJoin, (value) {
      if (rtcLogic.robotJoin() && RtcUtils.instance.isAudio()) {
        //云转录机器人加入成功
        RtcUtils.instance.muteLocalAudioStream(false);
        worker.dispose();
      }
    });

    eventBus.on<String>().listen((data) {
      if (data == "showFloatButton") {
        _showCallFloatingButton();
      }
      if (data == "hideFloatButton") {
        CallFloatingButtonWidgetManager().closeOverlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return PopScope(
      canPop: false,
      child: buildMineBg(
          child: Scaffold(
        appBar: ComAppBar(
          context,
          "${widget.groupName}",
          showLeading: false,
          iSCenterTitle: true,
          titleFontWeight: FontWeight.w400,
          leading: Padding(
            padding: const EdgeInsets.all(17.0),
            child: GestureDetector(
                onTap: () {
                  if (rtcLogic.isJoin) {
                    rtcLogic.showSystemAlertWindowPermissionDialog(() {
                      _showCallFloatingButton();
                    });
                  }
                },
                child: Image.asset("assets/images/icon_scale.png",
                    width: 22.w, height: 22.w, fit: BoxFit.contain)),
          ),
          actions: [
            SizedBox.shrink(),
            Obx(() {
              return TapHigh(
                  onTap: () {
                    rtcLogic.isShowDanum.value = !rtcLogic.isShowDanum.value;
                    rtcLogic.getPageViewUserList();
                  },
                  child: Padding(
                    padding: EdgeInsets.all(15.w),
                    child: Image.asset(
                      rtcLogic.isShowDanum.value
                          ? 'assets/images/ic_danum_select.png'
                          : 'assets/images/ic_danum_unselect.png',
                      width: 24.w,
                      height: 24.w,
                    ),
                  ));
            })
            // Padding(
            //   padding: const EdgeInsets.all(15.0),
            //   child: GestureDetector(
            //       onTap: () {},
            //       child: Image.asset("assets/images/icon_more.png",
            //           width: 24.w, height: 24.w, fit: BoxFit.contain)),
            // )
          ],
        ),
        body: SafeArea(
            child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 14.w),
          child: Column(
            children: [
              Expanded(child: _pageView()),
              _danumLay(),
              Padding(
                padding: EdgeInsets.only(bottom: 30.w, left: 66.w, right: 66.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 64.w,
                      height: 64.h,
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          GestureDetector(
                            onTap: () {
                              // if (RtcUtils.instance.isAudio() &&
                              //     !rtcLogic.robotJoin()) {
                              //   //闭麦的情况下开启麦克风，如果云转录机器人没有进入房间，则开启云转录
                              //   rtcLogic.startAgoraRtcTranscription();
                              //   return;
                              // }
                              RtcUtils.instance.isAudio.value =
                                  !RtcUtils.instance.isAudio.value;
                              if (!RtcUtils.instance.isAudio.value) {
                                RtcSpeechSendMessageUtils.instance
                                    .joinRoom(rtcLogic.thirdGroupId); //开启语音识别
                              }
                              RtcUtils.instance.muteLocalAudioStream(
                                  RtcUtils.instance.isAudio.value);
                            },
                            child: Obx(() {
                              return Image.asset(
                                RtcUtils.instance.isAudio.value
                                    ? "assets/images/Icon_mic_off.png"
                                    : "assets/icon/icon_mic_on.png",
                                width: 64.w,
                                height: 64.w,
                              );
                            }),
                          ),
                          // Obx(() {
                          //   if (!rtcLogic.robotJoin.value && widget.type == 0) {
                          //     return ClipRRect(
                          //       borderRadius: BorderRadius.circular(32.w),
                          //       child: Container(
                          //         width: 64.w,
                          //         height: 64.h,
                          //         color: Colors.grey.withValues(alpha: 0.5),
                          //         child: Center(
                          //           child: RotatingLoadingImage(
                          //               imagePath:
                          //                   "assets/images/icon_load.webp",
                          //               width: 20.w,
                          //               height: 20.h),
                          //         ),
                          //       ),
                          //     );
                          //   } else {
                          //     return SizedBox.shrink();
                          //   }
                          // }),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        rtcLogic.getFeatureAccess();
                        RtcSpeechSendMessageUtils.instance
                            .closeMicrophone(); //关闭语音识别
                        rtcLogic.leaveChannel();
                        rtcLogic.isShowDanum.value = false;
                        rtcLogic.subtitleList.clear();
                        rtcLogic.lastSubtitle = Subtitle("".obs, "", "", 0).obs;
                        Get.back();
                      },
                      child: Image.asset(
                        "assets/images/icon_exit.png",
                        width: 64.w,
                        height: 64.w,
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        )),
      )),
    );
  }

  Widget _pageView() {
    return Obx(() {
      return rtcLogic.isShowLoading.value
          ? Center(
              child: Lottie.asset(
                  "assets/json/single_voice_connecting_animation.json",
                  width: 200.w,
                  height: 200.w,
                  fit: BoxFit.fill),
            )
          : Container(
              child: Column(
                children: [
                  Expanded(
                      child: Container(
                    width: 1.sw,
                    margin: EdgeInsets.only(top: 40.w, bottom: 20.w),
                    child: PageView.builder(
                      itemCount: rtcLogic.userPageList.length,
                      itemBuilder: (buildcontext, index) {
                        return Container(
                          padding: EdgeInsets.only(left: 15.w, right: 15.w),
                          child: GridView.builder(
                              itemCount:
                                  rtcLogic.userPageList.value[index].length,
                              physics: NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 3,
                                      childAspectRatio: 1.0,
                                      mainAxisSpacing: 5,
                                      crossAxisSpacing: 4),
                              itemBuilder: (context, gridIndex) {
                                return UserItemAvatar(rtcLogic
                                    .userPageList.value[index][gridIndex]);
                              }),
                        );
                      },
                      onPageChanged: (index) {
                        _currentInedex.value = index;
                      },
                    ),
                  )),
                  rtcLogic.userPageList.length > 1
                      ? Container(
                          margin: EdgeInsets.only(
                              bottom: rtcLogic.isShowDanum.value ? 0.w : 80.w),
                          child: AnimatedPageIndicator(
                            currentPage: _currentInedex.value,
                            pageCount: rtcLogic.userPageList.length,
                            activeColor: Color(0xFFFF3BDF),
                            inactiveColor: Color(0xFF733D7D),
                            activeSize: 5,
                            inactiveSize: 5,
                          ),
                        )
                      : SizedBox()
                ],
              ),
            );
    });
  }

  Widget _danumLay() {
    return Obx(() {
      return rtcLogic.isShowDanum.value
          ? Container(
              margin: EdgeInsets.only(
                  left: 16.w, right: 16.w, bottom: 30.w, top: 30.w),
              constraints: BoxConstraints(maxHeight: 130.h),
              child: ContainerBox(
                boxColor: Color(0xFF37254D),
                borderColor: Color(0xFF37254D),
                radius: 14,
                padding: EdgeInsets.all(12.w),
                child: rtcLogic.subtitleList.length > 0
                    ? ListView.builder(
                        shrinkWrap: true,
                        controller: _scrollController,
                        itemCount: rtcLogic.subtitleList.length,
                        physics: NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          Subtitle subtitle = rtcLogic.subtitleList[index];
                          return Container(
                            margin:
                                EdgeInsets.only(bottom: index == 0 ? 0 : 8.w),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                UrlImage('${subtitle.avatarUrl}',
                                    width: 24.w,
                                    height: 24.w,
                                    radius: 180,
                                    errorWidget: DefaultUserAvatar()),
                                Expanded(
                                    child: Container(
                                  margin: EdgeInsets.only(left: 8.w),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        child: Text('${subtitle.name}',
                                            style: TextStyle(
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF988B9A),
                                            )),
                                      ),
                                      Obx(() {
                                        scrollGoToBottom();
                                        return Container(
                                          margin: EdgeInsets.only(top: 4.w),
                                          child: index ==
                                                  rtcLogic.subtitleList.length -
                                                      1
                                              ? PrinterText(
                                                  text:
                                                      '${subtitle.content.value}',
                                                  charDuration: Duration(
                                                      milliseconds: 50),
                                                  textAlign: TextAlign.left,
                                                  style: TextStyle(
                                                    fontSize: 14.sp,
                                                    fontWeight: FontWeight.w400,
                                                    color: Colors.white,
                                                  ),
                                                  onPrinted: () {
                                                    // print("打印机效果完成");
                                                  },
                                                )
                                              : Text(
                                                  '${subtitle.content.value}',
                                                  style: TextStyle(
                                                    fontSize: 14.sp,
                                                    fontWeight: FontWeight.w400,
                                                    color: Colors.white,
                                                  )),
                                        );
                                        return Container(
                                          margin: EdgeInsets.only(top: 4.w),
                                          child:
                                              Text('${subtitle.content.value}',
                                                  style: TextStyle(
                                                    fontSize: 14.sp,
                                                    fontWeight: FontWeight.w400,
                                                    color: Colors.white,
                                                  )),
                                        );
                                      }),
                                    ],
                                  ),
                                ))
                              ],
                            ),
                          );
                        })
                    : Container(
                        width: 1.sw,
                        child: Text(
                          S.of(context).transcriptionEnabledLoadingData,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: Color(0xFF988B9A),
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
              ),
            )
          : SizedBox();
    });
  }

  void scrollGoToBottom() {
    if (_scrollController.hasClients) {
      Future.delayed(Duration(milliseconds: 200), () {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  /// 显示悬浮通话按钮
  void _showCallFloatingButton() async {
    if (!mounted) return; // 确保组件还在
    CallFloatingButtonWidgetManager()
        .setCallStatus(CallButtonWidgetStatus.contacting);
    rtcLogic.showFloatingButtonWidget(
        groupId: widget.groupId,
        groupName: widget.groupName,
        thirdId: widget.thirdId,
        type: widget.type,
        callback: () {
          Get.back();
        });
  }
}
