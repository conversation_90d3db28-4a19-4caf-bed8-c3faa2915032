import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:new_agnes/utils/logger_utils.dart';

class RtcCallTimer {
  factory RtcCallTimer() => _instance;
  RtcCallTimer._internal();
  static final RtcCallTimer _instance = RtcCallTimer._internal();
  static Timer? _timer;
  static int _totalSeconds = 0;
  static bool isTimerRunning = false;
  static int get totalSeconds => _totalSeconds;

  static Function? timmeCallBack;

  // 开始计时器
  static void startTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }
    isTimerRunning = true;
    _totalSeconds = 0;
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      _totalSeconds++;
      timmeCallBack?.call(_totalSeconds);
      // LoggerUtils.d("定时器已启动$_totalSeconds");
    });
    // LoggerUtils.d("定时器已启动");
  }

  // 停止计时器
  static void stopTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }
    _totalSeconds = 0;
    isTimerRunning = false;
  }
}