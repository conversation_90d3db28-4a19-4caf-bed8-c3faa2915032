import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/widget/deep_research_lay.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import 'package:new_agnes/widget/EditText.dart';
import 'package:new_agnes/widget/ImageUrl.dart';
import 'package:new_agnes/widget/TapHigh.dart';

import '../../../../generated/l10n.dart';
import '../../../../widget/ComAppbar.dart';
import '../group_setting/components/member_list_item.dart';
import 'group_chat_room_view.dart';
import 'group_rtc_select_people_logic.dart';

class GroupRtcSelectPeoplePage extends StatefulWidget {
  String? groupId;
  String? groupName;
  String? thirdId;

  GroupRtcSelectPeoplePage({this.groupId, this.groupName, this.thirdId});

  @override
  State<GroupRtcSelectPeoplePage> createState() =>
      _GroupRtcSelectPeoplePageState();
}

class _GroupRtcSelectPeoplePageState extends State<GroupRtcSelectPeoplePage> {
  GroupRtcSelectPeopleLogic logic = Get.put(GroupRtcSelectPeopleLogic());
  TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    // TODO: implement initState
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (mounted) {
        logic.getGroupMembers(widget!.thirdId!);
      }
    });
    searchController.addListener(() {
      final searchText = searchController.text.trim();
      if (searchText.trim().isEmpty) {
        logic.isShowSearch.value = false;
      } else {
        logic.getSearchMembers(searchText);
        logic.isShowSearch.value = true;
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    Get.delete<GroupRtcSelectPeopleLogic>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Color(0xFF1A031E),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: ComAppBar(
          context,
          '${S.of(Get.context!).selectMembers}',
          actions: [
            Obx(() => TapHigh(
                onTap: () {
                  if (logic.selectGroupMembers.length > 0) {
                    Get.back();
                    List<String> ids = [];
                    logic.selectGroupMembers.value.forEach((member) {
                      ids.add(member.id!);
                    });
                    Get.to(GroupChatRoomPage(
                      groupId: widget.groupId!,
                      groupName: widget.groupName!,
                      thirdId: widget.thirdId!,
                      type: 0,
                      selectIds: ids,
                    ));
                  }
                },
                child: Container(
                  margin: EdgeInsets.only(right: 16.w),
                  color: Colors.transparent,
                  alignment: Alignment.center,
                  child: Text(
                    logic.selectGroupMembers.length > 0
                        ? '${logic.doneText.value}'
                        : '${S.of(Get.context!).done}',
                    style: TextStyle(
                        color: logic.selectGroupMembers.length > 0
                            ? Colors.white
                            : Color(0xFF988B9A),
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400),
                  ),
                )))
          ],
        ),
        resizeToAvoidBottomInset: false, // 规避底部布局被软键盘顶起
        body: Container(
          width: 1.sw,
          height: 1.sh,
          child: Column(
            children: [
              SizedBox(
                height: 10.w,
              ),
              _hListLay(),
              _searchLay(),
              Expanded(child: _listLay()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _hListLay() {
    return Obx(() => Visibility(
        visible: logic.selectGroupMembers.value.length > 0,
        child: Container(
            height: 50.w,
            alignment: Alignment.center,
            child: ListView.builder(
                shrinkWrap: true,
                itemCount: logic.selectGroupMembers.value.length,
                scrollDirection: Axis.horizontal,
                itemBuilder: (buildContext, index) {
                  final member = logic.selectGroupMembers[index];
                  return Container(
                    margin: EdgeInsets.only(
                        right: 12.w, left: index == 0 ? 16.w : 0.w),
                    child: UrlImage('${member.avatarUrl}',
                        width: 48.w, height: 48.w, radius: 180),
                  );
                }))));
  }

  Widget _searchLay() {
    return ContainerBox(
      margin: EdgeInsets.only(top: 16.w, left: 16.w, right: 16.w),
      radius: 12,
      height: 46.w,
      width: 1.sw,
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      boxColor: Colors.transparent,
      borderColor: Color(0xFF8F8F8F),
      child: Row(
        children: [
          Image.asset(
            'assets/groupChat/search.png',
            width: 20,
            height: 20,
          ),
          SizedBox(
            width: 8.w,
          ),
          Expanded(
              child: EditText(
            searchController,
            textColor: Colors.white,
            maxLine: 1,
            size: 14,
            hint: S.of(Get.context!).search,
            hintColor: Color(0xFF988B9A),
          ))
        ],
      ),
    );
  }

  Widget _listLay() {
    return Obx(() => Container(
          child: ListView.builder(
              shrinkWrap: true,
              itemCount: logic.isShowSearch.value
                  ? logic.searchGroupMembers.length
                  : logic.groupMembers.value.length,
              itemBuilder: (buildContext, index) {
                final member = logic.isShowSearch.value
                    ? logic.searchGroupMembers[index]
                    : logic.groupMembers[index];
                return Obx(() => TapHigh(
                    onTap: () {
                      FocusScope.of(context).requestFocus(FocusNode());
                      member.isSelected!.value = !member.isSelected!.value;
                      if (member.isSelected!.value!) {
                        logic.selectGroupMembers.add(member);
                      } else {
                        logic.selectGroupMembers.remove(member);
                      }
                      logic.doneText.value =
                          '${S.of(Get.context!).done} (${logic.selectGroupMembers.length})';
                    },
                    child: Container(
                      width: 1.sw,
                      margin: EdgeInsets.only(top: 24.w),
                      padding: EdgeInsets.only(left: 16.w, right: 16.w),
                      child: Row(
                        children: [
                          UrlImage('${member.avatarUrl!}',
                              width: 40.w,
                              height: 40.w,
                              radius: 180,
                              errorWidget: DefaultUserAvatar()),
                          SizedBox(
                            width: 12.w,
                          ),
                          Expanded(
                              child: Container(
                            child: Text(
                              member.username!,
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400),
                            ),
                          )),
                          SizedBox(
                            width: 12.w,
                          ),
                          Image.asset(
                            member.isSelected!.value!
                                ? 'assets/groupChat/selected.png'
                                : 'assets/groupChat/unselected.png',
                            width: 20.w,
                            height: 20.w,
                          )
                        ],
                      ),
                    )));
              }),
        ));
  }
}
