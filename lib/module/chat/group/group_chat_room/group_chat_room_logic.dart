// import 'dart:convert';
//
// import 'package:get/get.dart';
// import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_event_model.dart';
// import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_logic.dart';
// import 'package:new_agnes/utils/logger_utils.dart';
//
// import '../../../../api/Api.dart';
// import '../../../../api/ApiProvider.dart';
// import '../group_chat/rtc_chat/rtc_utils.dart';
// import 'UserItemAvatar.dart';
//
// class GroupChatRoomLogic extends GetxController {
//
//   final rtcLogic = Get.find<RtcLogic>();
//   RxList userList = [
//     {
//       "name": "张三",
//       "avatar": "assets/icon/icon_agens_chat.png",
//     },
//     {
//       "name": "杨二",
//       "avatar": "https://picsum.photos/200/300?random=1",
//     },
//     {
//       "name": "徐柳",
//       "avatar": "https://picsum.photos/200/300?random=2",
//     },
//     {
//       "name": "李四",
//       "avatar": "https://picsum.photos/200/300?random=2",
//     },
//     {
//       "name": "王五",
//       "avatar": "https://picsum.photos/200/300?random=2",
//     }
//
//    ].obs;
//
//
//   @override
//   void onInit() {
//     super.onInit();
//     rtcLogic.initRtc();
//   }
//
//   // 加入房间
//   void onJoinChanner(RtcEventModel model){}
//
//   // 用户加入
//   void onUserJoin(RtcEventModel model){}
//
//   // 用户离开
//   void onUserOffline(RtcEventModel model){}
//
//   // 当前说话用户
//   void onAudioVolume(RtcEventModel model){}
//
//
// }
