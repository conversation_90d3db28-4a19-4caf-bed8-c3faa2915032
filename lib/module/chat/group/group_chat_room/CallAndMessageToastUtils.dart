import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:new_agnes/widget/ImageUrl.dart';

import '../../../../generated/l10n.dart';
import '../../../../utils/kwFloatToast.dart';
import '../../../../widget/gradient_text.dart';

class CallAndMessageToastUtils {
  factory CallAndMessageToastUtils() => _instance;
  static kwFloatToast _fluttertoast = kwFloatToast();

  CallAndMessageToastUtils._internal();

  static final CallAndMessageToastUtils _instance =
      CallAndMessageToastUtils._internal();

  static init(BuildContext context) {
    _fluttertoast.init(context);
  }

  // 通话邀请
  static callFloatingAlert(
      {String? name,
      String? image,
      String? groupName,
        Duration? duration,// 显示的时长
      Function? acceptCallBack, // 接听回调
      Function? endingCallBack,// 通话结束回调
      Function? draggingCallBack,// 拖拽回调
      Function? autoDismissCallBack,// 倒计时结束回调
      Function? timerCallBack, // 倒计时回调
        ValueChanged<bool>? floatIsShowingCallBack,// 悬浮窗显示状态回调
      }) {
    if (_fluttertoast.context == null) {
      LoggerUtils.d("请先调用init(context)方法");
      return;
    }
    _fluttertoast.showToast(
      child: Dismissible(
        key: UniqueKey(),
        direction: DismissDirection.horizontal,
        onDismissed: (direction) {
          _fluttertoast.removeCustomToast();
          draggingCallBack?.call();
        },
        child: Dismissible(
          key: UniqueKey(),
          direction: DismissDirection.up,
          onDismissed: (direction) {
            _fluttertoast.removeCustomToast();
            draggingCallBack?.call();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 24.w),
            decoration: BoxDecoration(
              color: Color(0xE3FFFFFF),
              borderRadius: BorderRadius.circular(14.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 5,
                  offset: Offset(2, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                UrlImage(
                  '${image}',
                  width: 40.w,
                  height: 40.w,
                  fit: BoxFit.fill,
                  radius: 180,
                  errorWidget: Image.asset(
                    "assets/icon/icon_agens_chat.png",
                    width: 40.w,
                    height: 40.w,
                    fit: BoxFit.fill,
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: 10.w, right: 20.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GradientText(
                          name??'',
                          style: TextStyle(
                            fontSize: 18,
                          ),
                          gradient: LinearGradient(
                            colors: [
                              Color(0xFFFF3BDF),
                              Color(0xFFFF91EE),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                        Text(
                          S.of(Get.context!).xxxInvitesYouToJoinTheXyzGroupCall("", groupName??''),
                          maxLines: 2,
                          style: TextStyle(
                              fontSize: 14.sp, color: Color(0xFF0D0D0D)),
                          overflow: TextOverflow.ellipsis,
                        )
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    _fluttertoast.removeCustomToast();
                    endingCallBack?.call();
                  },
                  child: Image.asset(
                    'assets/icon/icon_phone_end.png',
                    width: 35.w,
                    height: 35.w,
                  ),
                ),
                SizedBox(
                  width: 15.w,
                ),
                InkWell(
                    onTap: () {
                      acceptCallBack?.call();
                      _fluttertoast.removeCustomToast();
                    },
                    child: Image.asset(
                      "assets/icon/icon_iphone_accept.png",
                      width: 35.w,
                      height: 35.w,
                    ))
              ],
            ),
          ),
        ),
      ),
      fadeDuration: const Duration(microseconds: 250),
      toastDuration: duration ?? const Duration(seconds: 30),
      timerCallback: (time){
        timerCallBack?.call(time);
      },
      autoDismissCallback: (){
        autoDismissCallBack?.call();
      },
      floatIsShowingCallBack: (isShowing) {
        floatIsShowingCallBack?.call(isShowing);
      },
      gravity: ToastGravity.TOP,
    );
  }

  /// 拒绝通话邀请
  static callFloatingCancel({Function? sendMessageCallBack}) {
    if (_fluttertoast.context == null) {
      LoggerUtils.d("请先调用init(context)方法");
      return;
    }
    _fluttertoast.showToast(
      child: Dismissible(
        direction: DismissDirection.horizontal,
        key: UniqueKey(),
        onDismissed: (direction) {
          _fluttertoast.removeCustomToast();
        },
        child: Dismissible(
          key: UniqueKey(),
          direction: DismissDirection.up,
          onDismissed: (direction) {
            _fluttertoast.removeCustomToast();
          },
          child: Container(
            decoration: BoxDecoration(
              color: Color(0xFFFFFFFFE3),
              borderRadius: BorderRadius.circular(14.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 5,
                  offset: Offset(2, 2),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.w),
              child: Row(
                children: [
                  Image.asset(
                    "assets/icon/icon_phone_cancel.png",
                    width: 32.w,
                    height: 32.w,
                    fit: BoxFit.fill,
                  ),
                  Expanded(
                    flex: 1,
                    child: Padding(
                      padding: EdgeInsets.only(left: 10.w, right: 10.w),
                      child: Text(
                        S.of(Get.context!).callDeclined,
                        maxLines: 2,
                        style: TextStyle(
                            fontSize: 15.sp, color: Color(0xFFD64240)),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      onTap: () {
                        _fluttertoast.removeCustomToast();
                        sendMessageCallBack?.call();
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Expanded(
                            child: Text(
                              S.of(Get.context!).sendMessage,
                              maxLines: 2,
                              textAlign: TextAlign.end,
                              style:
                                  TextStyle(fontSize: 14.sp, color: Colors.black),
                            ),
                          ),
                          SizedBox(
                            width: 5.w,
                          ),
                          Image.asset(
                            'assets/icon/icon_arrow_right.png',
                            width: 20.w,
                            height: 20.w,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      fadeDuration: const Duration(microseconds: 250),
      toastDuration: const Duration(seconds: 4),
      gravity: ToastGravity.TOP,
    );
  }

  /// 邀请用户加入群聊
  static inviteUserAlert({Function? acceptCallBack}) {
    if (_fluttertoast.context == null) {
      LoggerUtils.d("请先调用init(context)方法");
      return;
    }
    _fluttertoast.showToast(
      child: Dismissible(
        direction: DismissDirection.horizontal,
        key: UniqueKey(),
        onDismissed: (direction) {
          _fluttertoast.removeCustomToast();
        },
        child: Dismissible(
          key: UniqueKey(),
          direction: DismissDirection.up,
          onDismissed: (direction) {
            _fluttertoast.removeCustomToast();
          },
          child: Container(
            decoration: BoxDecoration(
              color: Color(0xFFFFFFFFE3),
              borderRadius: BorderRadius.circular(14.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 5,
                  offset: Offset(2, 2),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.w),
              child: Row(
                children: [
                  Column(
                    children: [
                      Image.asset(
                        "assets/icon/icon_message.png",
                        width: 44.w,
                        height: 44.w,
                        fit: BoxFit.fill,
                      ),
                      const Spacer(),
                    ],
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(left: 10.w, right: 20.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ShaderMask(
                            // 创建渐变着色器
                            shaderCallback: (bounds) => LinearGradient(
                                    colors: [
                                  Color(0xFFFF3BDF),
                                  Color(0xFFFF91EE),
                                ],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter)
                                .createShader(
                              Rect.fromLTWH(0, 0, bounds.width, bounds.height),
                            ),
                            // 文字颜色必须设置为白色或透明才能显示渐变效果
                            child: Text("Invite",
                                style: TextStyle(
                                    fontSize: 18.sp, color: Colors.white)),
                          ),
                          Text(
                            "Yang,would like you to join Group chats？",
                            maxLines: 2,
                            style: TextStyle(
                                fontSize: 14.sp, color: Color(0xFF0D0D0D)),
                            overflow: TextOverflow.ellipsis,
                          )
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 20.w,
                  ),
                  InkWell(
                    onTap: () {
                      acceptCallBack?.call();
                    },
                    child: Column(
                      children: [
                        const Spacer(),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 14.w, vertical: 6.w),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            gradient: LinearGradient(
                              colors: [
                                Color(0xFFFF3BDF),
                                Color(0xFFFF91EE),
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                          ),
                          child: Text(
                            "Join",
                            style: TextStyle(
                                fontSize: 14.sp, color: Color(0xFF0D0D0D)),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      fadeDuration: const Duration(microseconds: 250),
      toastDuration: const Duration(seconds: 10),
      gravity: ToastGravity.TOP,
    );
  }

  /// 取消弹窗
  static cancelPresentToast() {
    if (_fluttertoast.context == null) {
      return;
    }
    _fluttertoast.removeCustomToast();
  }

  /// 取消所有弹窗
  static cancelAllToast() {
    if (_fluttertoast.context == null) {
      return;
    }
    _fluttertoast.removeQueuedCustomToasts();
  }
}

