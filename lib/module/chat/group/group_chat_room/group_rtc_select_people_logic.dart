import 'package:get/get.dart';

import '../../../../api/Api.dart';
import '../../../../api/ApiProvider.dart';
import '../../../../api/StorageService.dart';
import '../../../../utils/loading_util.dart';
import '../../../mine/model/UserInfoModel.dart';
import '../../model/grouplist/GroupUserBean.dart';

class GroupRtcSelectPeopleLogic extends GetxController {
  //获取群成员列表
  var groupMembers = <GroupUserBean>[].obs;
  var selectGroupMembers = <GroupUserBean>[].obs;
  var searchGroupMembers = <GroupUserBean>[].obs;
  RxString doneText=''.obs;
  RxBool isShowSearch=false.obs;
  Future<void> getGroupMembers(String groupId) async {
    LoadingUtil.show();
    Response res = await Get.find<ApiProvider>()
        .get("${Api.groupUserList}?group_id=$groupId");
    LoadingUtil.dismiss();
    if (res.statusCode == 200) {
      groupMembers.clear();
      UserInfoModel userInfoModel =
      Get.find<StorageService>().getUserInfoData();

      if(res.body['users']!=null){
        groupMembers.value = (res.body['users'] as List)
            .map((e) => GroupUserBean.fromJson(e))
            .where((user) => user.roleId != '0'&&user.id!='${userInfoModel.id}')
            .toList();
      }
    }
  }

  //根据内容，获取搜索到的成员
  void getSearchMembers(String searchText) {
    if (searchText.isEmpty) {
      // 如果搜索文本为空，则显示所有成员
      searchGroupMembers.value = groupMembers;
    } else {
      // 过滤包含搜索文本的成员
      searchGroupMembers.value = groupMembers
          .where((member) =>
      member.username != null &&
          member.username!.toLowerCase().contains(searchText.toLowerCase()))
          .toList();
    }
  }
}
