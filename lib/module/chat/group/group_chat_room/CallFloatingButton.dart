import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';

class CallFloatingButton {
  // 1. 基础状态（位置、显隐、拖动）
  static OverlayEntry? _overlayEntry;
  static double _btnX = 300; // 初始X坐标（右下角）
  static double _btnY = 500; // 初始Y坐标
  static bool _isDragging = false;
  static bool isVisible = true; // 按钮显隐状态
  static String statusText = "Waiting";
  static bool isCreate = false;
  static final Set<String> _hiddenRoutes = {'/GroupChatRoomPage'}; // 需要隐藏按钮的路由

  // 2. 计时器状态
  static int _totalSeconds = 0; // 总秒数
  static double buttonSize = 70.w;

  // 1. 定时器(callFloatingAlert 消失时的剩余时长，开启定时器)
  static Timer? _timer;
  // 默认显示为等待接听
  static CallStatus status = CallStatus.waiting;

  static Function? _timerCallback;

  // 格式化时间（MM:SS）
  static String _formatTime() {
    final minutes = (_totalSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_totalSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  // 创建定时器
  static void startTimer(int? seconds) {
    if(seconds == null || seconds <= 0) return;
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      seconds = seconds! - 1;
      LoggerUtils.d("悬浮按钮定时器倒计时----$seconds");
      if (seconds == 0) {
        _timer!.cancel();
      }
      _timerCallback?.call(seconds);
    });
  }

  // -------------------------- 按钮显隐与渲染 --------------------------
  // 显示全局按钮（初始化Overlay）
  static void show(
      {required BuildContext context,
      required CallStatus localCallStatus,
        Function? timerCallBack,
        Function? onTapCallBack,
      Duration? duration}) {
    status = localCallStatus;
    if (_overlayEntry != null) return;
    final screenSize = MediaQuery.of(context).size; // 屏幕尺寸（限制拖动范围）
    isCreate = true;
    if (status == CallStatus.waiting) {
      _timerCallback = timerCallBack;
      startTimer(duration?.inSeconds);
    }
    _overlayEntry = OverlayEntry(
      builder: (context) => StatefulBuilder(
        // 局部状态更新（用于拖动和显隐动画）
        builder: (context, setState) => Positioned(
          left: _btnX,
          top: _btnY,
          child: AnimatedOpacity(
            opacity: isVisible ? 1.0 : 0.0, // 淡入淡出动画
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: AnimatedScale(
              scale: isVisible ? 1.0 : 0.5, // 缩放动画
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: _buildDraggableBtn(
                  context, screenSize, setState, onTapCallBack),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  // 隐藏按钮（仅隐藏，不销毁Overlay，避免重建）
  static void hideBtn() {
    isVisible = false;
    _updateBtnUI();
  }

  // 显示按钮（从隐藏状态恢复）
  static void showBtnAgain() {
    isVisible = true;
    if (_overlayEntry != null) {
      _updateBtnUI();
    }
  }


  // 构建可拖动的悬浮按钮（带计时器显示）
  static Widget _buildDraggableBtn(
    BuildContext context,
    Size screenSize,
    StateSetter setState,
    Function? onTapCallBack,
  ) {
    const btnSize = 80.0; // 按钮大小

    return GestureDetector(
        // 拖动开始
        onPanStart: (details) => _isDragging = true,
        // 拖动更新（限制在屏幕内）
        onPanUpdate: (details) {
          if (!_isDragging) return;
          _btnX =
              (_btnX + details.delta.dx).clamp(0, screenSize.width - btnSize);
          _btnY =
              (_btnY + details.delta.dy).clamp(0, screenSize.height - btnSize);
          setState(() {}); // 实时更新按钮位置
        },
        // 拖动结束
        onPanEnd: (details) => _isDragging = false,
        // 点击按钮：切换计时器（开始/暂停）
        onTap: !_isDragging
            ? () {
                LoggerUtils.d("是否点击了悬浮按钮");
                onTapCallBack?.call();
              }
            : null,
        child: Container(
          width: buttonSize,
          height: buttonSize,
          decoration: BoxDecoration(
            color: Color(0xFFFFFFE3),
            borderRadius: BorderRadius.circular(14.r),
          ),
          child: Column(
            children: [
              const Spacer(),
              Image.asset(
                "assets/icon/phone_call.png",
                width: 24.w,
                height: 24.w,
                fit: BoxFit.cover,
              ),
              SizedBox(
                height: 5.w,
              ),
              Text(
                status == CallStatus.contacting ? _formatTime() : statusText,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
            ],
          ),
        ));
  }

  // 刷新文字信息
  static void refreshText(CallStatus sta, int totalSeconds) {
    if (_overlayEntry == null) return;
    _totalSeconds = totalSeconds;
    status = sta;
    statusText = status.name;
    _updateBtnUI();
  }

  // 根据路由更新按钮可见性
  static void updateVisibility(String? currentRoute) {
    if (currentRoute == null) return;
    isVisible = !_hiddenRoutes.contains(currentRoute);
    _updateBtnUI();
  }

  // 刷新按钮UI（更新时间或显隐状态）
  static void _updateBtnUI() {
    _overlayEntry?.markNeedsBuild();
  }

  // 销毁按钮（页面退出时调用）
  static void dispose() {
    isCreate = false;
    _totalSeconds = 0;
    _overlayEntry?.remove();
    _overlayEntry = null;
    _timer?.cancel();
  }
}

enum CallStatus {
  waiting,
  // ending,
  contacting,
}
