import 'dart:convert';
/// status : "online"
/// message : "频道在线"
/// timestamp : "2025-08-22T10:49:26.347428"
/// members : [{"user_id":"ec741755-d3bf-4162-8153-a492c4043935","username":"15755061719","avatar_url":"https://agnes-sg.s3.ap-southeast-1.amazonaws.com/avatars/default/default-avatar-0605.svg","rtc_user_id":"1627738014"}]
/// is_member : 1
/// member_count : 1

ChannelStatusModel channelStatusModelFromJson(String str) => ChannelStatusModel.fromJson(json.decode(str));
String channelStatusModelToJson(ChannelStatusModel data) => json.encode(data.toJson());
class ChannelStatusModel {
  ChannelStatusModel({
      String? status, 
      String? message, 
      String? timestamp, 
      List<Members>? members, 
      num? isMember, 
      num? memberCount,}){
    _status = status;
    _message = message;
    _timestamp = timestamp;
    _members = members;
    _isMember = isMember;
    _memberCount = memberCount;
}

  ChannelStatusModel.fromJson(dynamic json) {
    _status = json['status'];
    _message = json['message'];
    _timestamp = json['timestamp'];
    if (json['members'] != null) {
      _members = [];
      json['members'].forEach((v) {
        _members?.add(Members.fromJson(v));
      });
    }
    _isMember = json['is_member'];
    _memberCount = json['member_count'];
  }
  String? _status;
  String? _message;
  String? _timestamp;
  List<Members>? _members;
  num? _isMember;
  num? _memberCount;
ChannelStatusModel copyWith({  String? status,
  String? message,
  String? timestamp,
  List<Members>? members,
  num? isMember,
  num? memberCount,
}) => ChannelStatusModel(  status: status ?? _status,
  message: message ?? _message,
  timestamp: timestamp ?? _timestamp,
  members: members ?? _members,
  isMember: isMember ?? _isMember,
  memberCount: memberCount ?? _memberCount,
);
  String? get status => _status;
  String? get message => _message;
  String? get timestamp => _timestamp;
  List<Members>? get members => _members;
  num? get isMember => _isMember;
  num? get memberCount => _memberCount;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['status'] = _status;
    map['message'] = _message;
    map['timestamp'] = _timestamp;
    if (_members != null) {
      map['members'] = _members?.map((v) => v.toJson()).toList();
    }
    map['is_member'] = _isMember;
    map['member_count'] = _memberCount;
    return map;
  }

}

/// user_id : "ec741755-d3bf-4162-8153-a492c4043935"
/// username : "15755061719"
/// avatar_url : "https://agnes-sg.s3.ap-southeast-1.amazonaws.com/avatars/default/default-avatar-0605.svg"
/// rtc_user_id : "1627738014"

Members membersFromJson(String str) => Members.fromJson(json.decode(str));
String membersToJson(Members data) => json.encode(data.toJson());
class Members {
  Members({
      String? userId, 
      String? username, 
      String? avatarUrl, 
      String? rtcUserId,}){
    _userId = userId;
    _username = username;
    _avatarUrl = avatarUrl;
    _rtcUserId = rtcUserId;
}

  Members.fromJson(dynamic json) {
    _userId = json['user_id'];
    _username = json['username'];
    _avatarUrl = json['avatar_url'];
    _rtcUserId = json['rtc_user_id'];
  }
  String? _userId;
  String? _username;
  String? _avatarUrl;
  String? _rtcUserId;
Members copyWith({  String? userId,
  String? username,
  String? avatarUrl,
  String? rtcUserId,
}) => Members(  userId: userId ?? _userId,
  username: username ?? _username,
  avatarUrl: avatarUrl ?? _avatarUrl,
  rtcUserId: rtcUserId ?? _rtcUserId,
);
  String? get userId => _userId;
  String? get username => _username;
  String? get avatarUrl => _avatarUrl;
  String? get rtcUserId => _rtcUserId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['user_id'] = _userId;
    map['username'] = _username;
    map['avatar_url'] = _avatarUrl;
    map['rtc_user_id'] = _rtcUserId;
    return map;
  }

}