import 'dart:convert';
import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:contacts_service_plus/contacts_service_plus.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:new_agnes/module/chat/group/group_chat/agora_logic.dart';
import 'package:new_agnes/module/chat/model/grouplist/AppUserAddressBook.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../api/Api.dart';
import '../../../../api/ApiProvider.dart';
import '../../../../api/StorageService.dart';
import '../../../../utils/event_bus.dart';
import '../../../../utils/loading_util.dart';
import '../../model/grouplist/SearchUserBean.dart';

import '../../model/grouplist/role_info_bean.dart';
import '../group_setting/utils/group_manage_util.dart';

class InviteMemberController extends GetxController {
  // 记录当前最新的搜索内容，防止接口返回时内容已变导致脏数据
  String latestQuery = '';
  final TextEditingController textController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();
  final TextEditingController groupNameController = TextEditingController();
  final FocusNode groupFocusNode = FocusNode();
  var searchText = ''.obs;
  var groupName = ''.obs;
  var searchResults = <String>[].obs;
  var selectedMembers = <SearchUserBean>[].obs;
  var searchGroupMembers = <SearchUserBean>[].obs;
  var role_id = ''.obs;
  bool get hasPermission => _hasPermission.value;
  final RxBool _hasPermission = false.obs;
  List<SearchUserBean> _allContacts = <SearchUserBean>[];
  RxList<SearchUserBean> localSearchResults = <SearchUserBean>[].obs;
  List<SearchUserBean> _filteredUsers = <SearchUserBean>[];
  Rx<bool> isLoadingAnimation = false.obs;

  @override
  void onInit() async {
    super.onInit();
  }

  /// 获取过滤后的角色列表（可选是否排除群主）
  List<RoleInfoBean> getFilteredRoles({bool filterGroupOwner = false}) {
    final roles = Get.find<StorageService>().getRoleInfoData();
    return GroupManageUtils.getFilteredRoles(roles,
        filterGroupOwner: filterGroupOwner);
  }

  /// 获取默认角色（优先 group_viewer，没有则取第一个）
  RoleInfoBean? getDefaultRole({bool filterGroupOwner = false}) {
    final roles = Get.find<StorageService>().getRoleInfoData();
    return GroupManageUtils.getDefaultRole(roles,
        filterGroupOwner: filterGroupOwner);
  }

  /// 设置默认角色ID
  void setDefaultRoleId({bool filterGroupOwner = false}) {
    final defaultRole = getDefaultRole(filterGroupOwner: filterGroupOwner);
    if (defaultRole != null) {
      role_id.value = defaultRole.role_id ?? '';
    }
  }

  /// 检查并请求通讯录权限
  Future<bool> checkAndRequestPermission() async {
    try {
      final status = await Permission.contacts.status;

      if (status.isGranted) {
        _hasPermission.value = true;
        return true;
      }

      if (status.isDenied) {
        final result = await Permission.contacts.request();
        _hasPermission.value = result.isGranted;

        if (result.isDenied) {
          // showFailToast(S.of(Get.context!).weNeedAccessToYourContactsToInviteFriends);
          return false;
        }

        return result.isGranted;
      }

      if (status.isPermanentlyDenied) {
        await showPermissionDialog();
        return false;
      }

      return false;
    } catch (e) {
      logError('检查通讯录权限失败: $e');
      showFailToast('权限检查失败，请重试');
      return false;
    }
  }

  /// 显示权限设置对话框
  Future<void> showPermissionDialog() async {
    return Get.dialog(
      AlertDialog(
        backgroundColor: Color(0xFF1A1A2E),
        title: Text(
          '需要通讯录权限',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          '为了邀请您的朋友加入群聊，我们需要访问您的通讯录。请在设置中开启通讯录权限。',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('取消', style: TextStyle(color: Colors.grey)),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('去设置', style: TextStyle(color: Color(0xFF00FFFF))),
          ),
        ],
      ),
    );
  }

  /// 加载通讯录联系人
  Future<void> loadContacts() async {
    if (!_hasPermission.value) {
      logError('没有通讯录权限');
      return;
    }

    try {
      // 获取系统联系人
      final contacts = await ContactsService.getContacts(
        withThumbnails: false, // 暂时不加载头像，提高性能
        photoHighResolution: false,
      );

      logError('获取到 ${contacts.length} 个联系人');

      // 转换为自定义数据模型
      final addressBookContacts = <SearchUserBean>[];

      List<String> usersPhoneNumbers = <String>[];
      for (final contact in contacts) {
        try {
          final addressBookContact = AppUserAddressBook.fromContact(contact);
          if (addressBookContact.phoneNumbers.isNotEmpty) {
            usersPhoneNumbers.add(addressBookContact.phoneNumbers[0]);
          }
          SearchUserBean searchUserBean = SearchUserBean(
            user_name: addressBookContact.phoneNumbers.isNotEmpty
                ? addressBookContact.phoneNumbers[0]
                : "",
            un_create: true,
            registration_credential: addressBookContact.emails.isNotEmpty
                ? addressBookContact.emails[0]
                : "",
            avatar_url: addressBookContact.appUserAvatar ??
                addressBookContact.firstLetter,
          );
          // 过滤掉没有姓名的联系人
          addressBookContacts.add(searchUserBean);
        } catch (e) {
          logError('转换联系人失败: $e');
        }
      }
      _allContacts = addressBookContacts;
      submitUserInfo(usersPhoneNumbers);
      logError('成功加载 ${_allContacts.length} 个有效联系人');
    } catch (e) {
      logError('加载通讯录失败: $e');
      showFailToast('加载通讯录失败，请重试');
    }
  }

  /// 提交获取的用户手机信息
  void submitUserInfo(List<String> userPhoneNumbers) async {
    try {
      Response response =
          await Get.find<ApiProvider>().post(Api.checkAddressBook, {
        "address_book": userPhoneNumbers,
      });
      if (response.statusCode == 200) {
        if (response.bodyString != null) {
          List<Map<String,String>> tempResult = jsonDecode(response.bodyString!) as List<Map<String,String>>;
          List<SearchUserBean> users =  tempResult.map<SearchUserBean>((element) {
            SearchUserBean bean = SearchUserBean.fromJson(element);
            _allContacts.forEach((value) {
              if (value.user_name == bean.user_name) {
                if (bean.user_id != null) {
                  value.is_registered = true;
                }
              }
            });
            return bean;
          }).toList();
        }
        localSearchResults.value = _allContacts;
      }
    } catch (e) {
      logError('提交用户信息失败: $e');
      showFailToast('提交用户信息失败，请重试');
    }
  }

  /// 搜索联系人
  void searchContacts(String query, String group_id) {
    if (query.isEmpty) {
      return;
    }
    _filteredUsers.clear();
    final searchText = query.toLowerCase().trim();
    _allContacts.forEach((contact) {
      // 匹配电话号码
      final phoneMatch = contact.user_name!.contains(searchText);

      // 匹配邮箱
      final emailMatch =
          contact.registration_credential!.toLowerCase().contains(searchText);

      // // 匹配拼音
      // bool pinyinMatch = false;
      // bool pinyinShortMatch = false;
      //
      // if (!RegExp(r'^[0-9]+$').hasMatch(contact.name)) {
      //   try {
      //     pinyinMatch = contact.pinyin.toLowerCase().contains(searchText);
      //     pinyinShortMatch = contact.pinyinShort.toLowerCase().contains(searchText);
      //   } catch (e) {
      //     // 忽略拼音匹配错误
      //   }
      // }
      if (phoneMatch || emailMatch) {
        _filteredUsers.add(contact);
      }
    });
    get_addable_users(query, group_id);
  }

  /// 群组名称变更
  void onGroupNameTextChanged(String value) {
    groupName.value = value;
  }

  /// 邮箱校验
  bool isEmail(String input) {
    return GroupManageUtils.isEmail(input);
  }

  /// 成员名称
  void onTextChanged(String value, String group_id) {
    // 过滤空格
    String input = value.replaceAll(' ', '');
    searchText.value = input;
    latestQuery = input;
    if (input.isEmpty) {
      searchGroupMembers.value = [];
      searchResults.value = [];
      return;
    }
    // 判断数字/非数字
    final isDigits = RegExp(r'^\d+$').hasMatch(input);
    if (isDigits) {
      if (input.length < 3) {
        searchGroupMembers.value = [];
        searchResults.value = [];
        _filteredUsers = [];
        localSearchResults.value = [];
        return;
      }
    } else {
      if (input.length < 2) {
        searchGroupMembers.value = [];
        searchResults.value = [];
        _filteredUsers = [];
        localSearchResults.value = [];
        return;
      }
    }
    searchContacts(input, group_id);
  }

  // 选中成员
  void selectItem(String userName) {
    final member =
        searchGroupMembers.firstWhereOrNull((e) => e.user_name == userName);
    if (member != null &&
        !selectedMembers.any((e) => e.user_id == member.user_id)) {
      selectedMembers.add(member);
    }
    searchResults.clear();
    update();
  }

  // 切换成员选中状态
  void toggleMemberSelected(SearchUserBean member) {
    final existIndex =
        selectedMembers.indexWhere((e) => e.user_id == member.user_id);
    if (existIndex != -1) {
      selectedMembers.removeAt(existIndex);
    } else {
      selectedMembers.add(member);
    }
    update();
  }

  // 移除成员
  void removeSelectedMember(SearchUserBean member) {
    // selectedMembers.remove(member);
    selectedMembers.removeWhere((e) => e.user_id == member.user_id);
    update();
  }

  // 获取成员列表
  Future<void> get_addable_users(String user_info, String group_id) async {
    late Response res;
    res = await Get.find<ApiProvider>().get(Api.get_addable_users +
        '?user_info=' +
        user_info +
        '&group_id=' +
        group_id);
    // 接口返回时校验当前输入内容，防止脏数据
    if (user_info != latestQuery) {
      return;
    }
    if (res.statusCode == 200) {
      final List<dynamic> list = json.decode(res.bodyString!);
      List<SearchUserBean> tempList = list
          .map((e) => SearchUserBean.fromJson(e as Map<String, dynamic>))
          .toList();
      // 如果没搜到数据且输入为邮箱，插入虚拟用户
      if (tempList.isEmpty && isEmail(user_info)) {
        tempList.add(
          SearchUserBean(
            user_id: 'virtual_${user_info}', // 避免和真实用户冲突
            user_name: user_info,
            registration_credential: user_info,
            avatar_url: '',
            auth_type: 'email',
            is_member: false,
            un_create: true,
          ),
        );
      }
      if (tempList.isNotEmpty) {
        for (final member in tempList) {
          int index = _filteredUsers.indexWhere((element) =>
              element.user_name == member.user_name ||
              element.registration_credential ==
                  member.registration_credential);
          if (index != -1) {
            _filteredUsers[index].is_registered = true;
          } else {
            _filteredUsers[index].is_registered = false;
          }
        }
      }
      localSearchResults.value = _filteredUsers;
      searchGroupMembers.value = tempList;
      refresh();
    }
  }

  // 创建群聊
  Future<void> create_group() async {
    LoadingUtil.show();

    late Response res;
    final Map<String, dynamic> data = {
      'source': 2,
      'is_pre_existing_artifact': false,
      'cardinality': 1,
    };
    if (groupName.value.trim().isNotEmpty) {
      data['group_name'] = groupName.value;
    }
    res = await Get.find<ApiProvider>().post(
      Api.create_group,
      data,
    );
    final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
    if (res.statusCode == 200) {
      add_members(responseBody['cooperation_group_id'],
          responseBody['third_chat_group_id'],
          create: true);
      return;
    }
    LoadingUtil.dismiss();
  }

  // 获取群聊roles
  Future<void> get_groupChat_roles() async {
    late Response res;
    res = await Get.find<ApiProvider>().get(
      Api.getRoles,
    );

    if (res.statusCode == 200) {
      var data = jsonEncode(res.body["group_chat"]);
      Get.find<StorageService>().setRoleData(data);
    }
  }

  // 添加成员入参处理
  List<Map<String, dynamic>> add_member_info() {
    List<Map<String, dynamic>> user_infos = [];
    if (role_id.value == '') {
      setDefaultRoleId();
    }
    for (SearchUserBean user in selectedMembers) {
      Map<String, dynamic> info = {};
      info['registration_credential'] = user.registration_credential;
      info['auth_type'] = user.auth_type;
      info['role_id'] = role_id.value;
      user_infos.add(info);
    }
    return user_infos;
  }

  // 添加成员
  Future<void> add_members(String group_id, String third_chat_group_id,
      {bool? create}) async {
    late Response res;
    LoadingUtil.show();
    res = await Get.find<ApiProvider>().post(
      Api.add_members,
      {"group_id": group_id, "user_infos": add_member_info()},
    );
    LoadingUtil.dismiss();

    if (res.statusCode == 200) {
      eventBus.fire(GroupInfoEvent(
        addMember: true,
      ));
      int create_join_type = 1002;
      if (create == true) {
        create_join_type = 1001;
      }

      // var cusMsg = Get.find<AgoraLogic>()
      //     .getCustomMessageBody("", third_chat_group_id, create_join_type);
      // ChatClient.getInstance.chatManager.importMessages([cusMsg]).then((ss) {
      //   ChatClient.getInstance.chatManager.updateMessage(cusMsg).then((value) {
      //
      //   });
      // });
      Future.delayed(Duration(milliseconds: 500)).then((value) {
        try {
          ChatClient.getInstance.groupManager
              .getGroupWithId(third_chat_group_id)
              .then((newGroup) {
            Get.back(result: newGroup);
          });
        } catch (e) {
          print("创建群聊${e.toString()}");
        }
      });
    }
  }

  //清除数据
  void clearAll() {
    textController.clear();
    groupNameController.clear();
    searchResults.clear();
    selectedMembers.clear();
    searchGroupMembers.clear();
    searchText.value = '';
    update();
  }
}
