import 'dart:convert';
import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:new_agnes/module/chat/group/group_chat/agora_logic.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import '../../../../api/Api.dart';
import '../../../../api/ApiProvider.dart';
import '../../../../api/StorageService.dart';
import '../../../../utils/event_bus.dart';
import '../../../../utils/loading_util.dart';
import '../../model/grouplist/SearchUserBean.dart';

import '../../model/grouplist/role_info_bean.dart';
import '../group_setting/utils/group_manage_util.dart';

class InviteMemberController extends GetxController {
  // 记录当前最新的搜索内容，防止接口返回时内容已变导致脏数据
  String latestQuery = '';
  final TextEditingController textController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();
  final TextEditingController groupNameController = TextEditingController();
  final FocusNode groupFocusNode = FocusNode();
  var searchText = ''.obs;
  var groupName = ''.obs;
  var searchResults = <String>[].obs;
  var selectedMembers = <SearchUserBean>[].obs;
  var searchGroupMembers = <SearchUserBean>[].obs;
  var role_id = ''.obs;
  Rx<bool> isLoadingAnimation = false.obs;

  /// 获取过滤后的角色列表（可选是否排除群主）
  List<RoleInfoBean> getFilteredRoles({bool filterGroupOwner = false}) {
    final roles = Get.find<StorageService>().getRoleInfoData();
    return GroupManageUtils.getFilteredRoles(roles,
        filterGroupOwner: filterGroupOwner);
  }

  /// 获取默认角色（优先 group_viewer，没有则取第一个）
  RoleInfoBean? getDefaultRole({bool filterGroupOwner = false}) {
    final roles = Get.find<StorageService>().getRoleInfoData();
    return GroupManageUtils.getDefaultRole(roles,
        filterGroupOwner: filterGroupOwner);
  }

  /// 设置默认角色ID
  void setDefaultRoleId({bool filterGroupOwner = false}) {
    final defaultRole = getDefaultRole(filterGroupOwner: filterGroupOwner);
    if (defaultRole != null) {
      role_id.value = defaultRole.role_id ?? '';
    }
  }

  /// 群组名称变更
  void onGroupNameTextChanged(String value) {
    groupName.value = value;
  }

  /// 邮箱校验
  bool isEmail(String input) {
    return GroupManageUtils.isEmail(input);
  }

  /// 成员名称
  void onTextChanged(String value, String group_id) {
    // 过滤空格
    String input = value.replaceAll(' ', '');
    searchText.value = input;
    latestQuery = input;
    if (input.isEmpty) {
      searchGroupMembers.value = [];
      searchResults.value = [];
      return;
    }
    // 判断数字/非数字
    final isDigits = RegExp(r'^\d+$').hasMatch(input);
    if (isDigits) {
      if (input.length < 3) {
        searchGroupMembers.value = [];
        searchResults.value = [];
        return;
      }
    } else {
      if (input.length < 2) {
        searchGroupMembers.value = [];
        searchResults.value = [];
        return;
      }
    }
    get_addable_users(input, group_id);
  }

  // 选中成员
  void selectItem(String userName) {
    final member =
        searchGroupMembers.firstWhereOrNull((e) => e.user_name == userName);
    if (member != null &&
        !selectedMembers.any((e) => e.user_id == member.user_id)) {
      selectedMembers.add(member);
    }
    searchResults.clear();
    update();
  }

  // 切换成员选中状态
  void toggleMemberSelected(SearchUserBean member) {
    final existIndex =
        selectedMembers.indexWhere((e) => e.user_id == member.user_id);
    if (existIndex != -1) {
      selectedMembers.removeAt(existIndex);
    } else {
      selectedMembers.add(member);
    }
    update();
  }

  // 移除成员
  void removeSelectedMember(SearchUserBean member) {
    // selectedMembers.remove(member);
    selectedMembers.removeWhere((e) => e.user_id == member.user_id);
    update();
  }

  // 获取成员列表
  Future<void> get_addable_users(String user_info, String group_id) async {
    late Response res;
    res = await Get.find<ApiProvider>().get(Api.get_addable_users +
        '?user_info=' +
        user_info +
        '&group_id=' +
        group_id);
    // 接口返回时校验当前输入内容，防止脏数据
    if (user_info != latestQuery) {
      return;
    }
    if (res.statusCode == 200) {
      final List<dynamic> list = json.decode(res.bodyString!);
      searchGroupMembers.value = list
          .map((e) => SearchUserBean.fromJson(e as Map<String, dynamic>))
          .toList();
      // 如果没搜到数据且输入为邮箱，插入虚拟用户
      if (searchGroupMembers.isEmpty && isEmail(user_info)) {
        searchGroupMembers.add(
          SearchUserBean(
            user_id: 'virtual_${user_info}', // 避免和真实用户冲突
            user_name: user_info,
            registration_credential: user_info,
            avatar_url: '',
            auth_type: 'email',
            is_member: false,
            un_create: true,
          ),
        );
      }
    }
  }

  // 创建群聊
  Future<void> create_group() async {
    LoadingUtil.show();

    late Response res;
    final Map<String, dynamic> data = {
      'source': 2,
      'is_pre_existing_artifact': false,
      'cardinality': 1,
    };
    if (groupName.value.trim().isNotEmpty) {
      data['group_name'] = groupName.value;
    }
    res = await Get.find<ApiProvider>().post(
      Api.create_group,
      data,
    );
    final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
    if (res.statusCode == 200) {
      add_members(responseBody['cooperation_group_id'],
          responseBody['third_chat_group_id'],
          create: true);
      return;
    }
    LoadingUtil.dismiss();
  }

  // 获取群聊roles
  Future<void> get_groupChat_roles() async {
    late Response res;
    res = await Get.find<ApiProvider>().get(
      Api.getRoles,
    );

    if (res.statusCode == 200) {
      var data = jsonEncode(res.body["group_chat"]);
      Get.find<StorageService>().setRoleData(data);
    }
  }

  // 添加成员入参处理
  List<Map<String, dynamic>> add_member_info() {
    List<Map<String, dynamic>> user_infos = [];
    if (role_id.value == '') {
      setDefaultRoleId();
    }
    for (SearchUserBean user in selectedMembers) {
      Map<String, dynamic> info = {};
      info['registration_credential'] = user.registration_credential;
      info['auth_type'] = user.auth_type;
      info['role_id'] = role_id.value;
      user_infos.add(info);
    }
    return user_infos;
  }

  // 添加成员
  Future<void> add_members(String group_id, String third_chat_group_id,
      {bool? create}) async {
    late Response res;
    LoadingUtil.show();
    res = await Get.find<ApiProvider>().post(
      Api.add_members,
      {"group_id": group_id, "user_infos": add_member_info()},
    );
    LoadingUtil.dismiss();

    if (res.statusCode == 200) {
      eventBus.fire(GroupInfoEvent(
        addMember: true,
      ));
      int create_join_type = 1002;
      if (create == true) {
        create_join_type = 1001;
      }

      // var cusMsg = Get.find<AgoraLogic>()
      //     .getCustomMessageBody("", third_chat_group_id, create_join_type);
      // ChatClient.getInstance.chatManager.importMessages([cusMsg]).then((ss) {
      //   ChatClient.getInstance.chatManager.updateMessage(cusMsg).then((value) {
      //
      //   });
      // });
      Future.delayed(Duration(milliseconds: 500)).then((value){
        ChatClient.getInstance.groupManager.getGroupWithId(third_chat_group_id).then((newGroup){
          Get.back(result: newGroup);
        });
      });
    }
  }

  //清除数据
  void clearAll() {
    textController.clear();
    groupNameController.clear();
    searchResults.clear();
    selectedMembers.clear();
    searchGroupMembers.clear();
    searchText.value = '';
    update();
  }
}
