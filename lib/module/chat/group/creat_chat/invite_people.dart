import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:new_agnes/module/chat/group/creat_chat/invite_logic.dart';
import 'package:new_agnes/widget/ComAppbar.dart';

import '../../../../api/StorageService.dart';
import '../../../../generated/l10n.dart';
import '../../../../utils/bgCover.dart';
import '../../../../utils/ocolor.dart';
import '../../model/grouplist/GroupUserBean.dart';
import '../group_setting/manage_member_page.dart';
import '../group_setting/utils/group_manage_util.dart';
import 'components/add_chat_button.dart';
import 'components/group_name_field.dart';
import 'components/member_item.dart';
import 'components/search_widget.dart';
import 'components/select_manager_widget.dart';
import 'components/user_not_regitered_view.dart';

/// 群聊邀请页面，支持搜索、选择、邀请成员
class InvitePeople extends StatefulWidget {
  final String? group_id;
  final String? agoraGroupId;
  final GroupUserBean? currentUser;
  const InvitePeople(
      {super.key, this.group_id, this.agoraGroupId, this.currentUser});

  @override
  State<InvitePeople> createState() => _InvitePeopleState();
}

class _InvitePeopleState extends State<InvitePeople> {
  /// 控制器，负责业务逻辑和状态
  final InviteMemberController controller = Get.put(InviteMemberController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.clearAll();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return buildMineBg(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: ComAppBar(
          context,
          widget.group_id == null || widget.group_id!.isEmpty
              ? S.of(context).createGroupChat
              : S.of(context).addPeopleToChat,
          actions: [
            widget.group_id == null || widget.group_id!.isEmpty
                ? Container()
                : GestureDetector(
                    onTap: () {
                      Get.to(() => ManageMemberPage(
                            currentUser: widget.currentUser!,
                            group_id: widget.group_id!,
                            third_group_id: widget.agoraGroupId!,
                          ));
                    },
                    child: Padding(
                      padding: EdgeInsets.only(right: 16),
                      child: Image.asset(
                        'assets/groupChat/member_manage_icon.png',
                        width: 24.w,
                        height: 24.w,
                      ),
                    )),
          ],
        ),
        body: Obx(() {
          final results = controller.searchGroupMembers;
          return Column(
            children: [
              // // 新建群聊时显示群名输入框（暂时隐藏）
              // (widget.group_id == null || widget.group_id!.isEmpty)
              //     ? GroupTextField()
              //     : Container(),
              // 搜索框
              SearchBox(
                groupId: widget.group_id ?? '',
              ),
              // 已选成员横向列表
              SelectedMembersBar(),
              // 搜索结果/未注册提示/成员列表
              Expanded(
                child: results.isEmpty &&
                        controller.textController.text.isNotEmpty &&
                        GroupManageUtils.isValidSearchInput(
                            controller.textController.text)
                    ? UserNotRegisteredEmptyView(
                        message: controller.textController.text,
                      )
                    : ListView.builder(
                        padding: EdgeInsets.symmetric(vertical: 8),
                        itemCount: results.length,
                        itemBuilder: (context, index) {
                          final member = results[index];
                          return GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: member.is_member
                                ? null
                                : () {
                                    FocusScope.of(context).unfocus();
                                    controller.toggleMemberSelected(member);
                                  },
                            child: MemberItem(
                              member: member,
                              isSelected: controller.selectedMembers
                                  .any((e) => e.user_id == member.user_id),
                              isDisabled: member.is_member,
                            ),
                          );
                        },
                      ),
              ),
              controller.selectedMembers.isNotEmpty
                  ? AddChatButton(
                      onTap: () {
                        if (widget.group_id == null ||
                            widget.group_id!.isEmpty) {
                          controller.create_group();
                          return;
                        }
                        controller.add_members(
                            widget.group_id!, widget.agoraGroupId!,
                            create: false);
                      },
                    )
                  : SizedBox.shrink(),
              SizedBox(height: 20),
            ],
          );
        }),
      ),
    );
  }
}
