import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:new_agnes/module/chat/group/creat_chat/components/local_member_item.dart';
import 'package:new_agnes/module/chat/group/creat_chat/invite_logic.dart';
import 'package:new_agnes/module/chat/model/grouplist/SearchUserBean.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:new_agnes/widget/ComAppbar.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../api/StorageService.dart';
import '../../../../generated/l10n.dart';
import '../../../../utils/bgCover.dart';
import '../../../../utils/ocolor.dart';
import '../../model/grouplist/GroupUserBean.dart';
import '../group_setting/manage_member_page.dart';
import '../group_setting/utils/group_manage_util.dart';
import 'components/add_chat_button.dart';
import 'components/group_name_field.dart';
import 'components/member_item.dart';
import 'components/search_widget.dart';
import 'components/select_manager_widget.dart';
import 'components/user_not_regitered_view.dart';

/// 群聊邀请页面，支持搜索、选择、邀请成员
class InvitePeople extends StatefulWidget {
  final String? group_id;
  final String? agoraGroupId;
  final GroupUserBean? currentUser;

  const InvitePeople(
      {super.key, this.group_id, this.agoraGroupId, this.currentUser});

  @override
  State<InvitePeople> createState() => _InvitePeopleState();
}

class _InvitePeopleState extends State<InvitePeople> with WidgetsBindingObserver {
  /// 控制器，负责业务逻辑和状态
  final InviteMemberController controller = Get.put(InviteMemberController());

  @override
  void initState(){
    super.initState();
    WidgetsBinding.instance.addObserver(this);
   WidgetsBinding.instance.addPostFrameCallback((timeStamp)async {
     if (await controller.checkAndRequestPermission()) {
     await controller.loadContacts();
     } else {
     await controller.showPermissionDialog();
     }
  });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    controller.clearAll();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return buildMineBg(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: ComAppBar(
          context,
          widget.group_id == null || widget.group_id!.isEmpty
              ? S
              .of(context)
              .createGroupChat
              : S
              .of(context)
              .addPeopleToChat,
          actions: [
            widget.group_id == null || widget.group_id!.isEmpty
                ? Container()
                : GestureDetector(
                onTap: () {
                  Get.to(() =>
                      ManageMemberPage(
                        currentUser: widget.currentUser!,
                        group_id: widget.group_id!,
                        third_group_id: widget.agoraGroupId!,
                      ));
                },
                child: Padding(
                  padding: EdgeInsets.only(right: 16),
                  child: Image.asset(
                    'assets/groupChat/member_manage_icon.png',
                    width: 24.w,
                    height: 24.w,
                  ),
                )),
          ],
        ),
        body: Column(
          children: [
            // // 新建群聊时显示群名输入框（暂时隐藏）
            // (widget.group_id == null || widget.group_id!.isEmpty)
            //     ? GroupTextField()
            //     : Container(),
            // 搜索框
            SearchBox(
              groupId: widget.group_id ?? '',
            ),
            // 已选成员横向列表
            SelectedMembersBar(),
            // 搜索结果/未注册提示/成员列表
            Expanded(child: showSearchUserInfo()),
            Obx(() {
              return controller.selectedMembers.isNotEmpty
                  ? AddChatButton(
                onTap: () {
                  if (widget.group_id == null ||
                      widget.group_id!.isEmpty) {
                    controller.create_group();
                    return;
                  }
                  controller.add_members(
                      widget.group_id!, widget.agoraGroupId!,
                      create: false);
                },
              )
                  : SizedBox.shrink();
            }),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
  
  //  未授权界面
  Widget showNoPermissionView() {
    return Container(
      padding: EdgeInsets.only(left: 36.w,right: 36.w,top: 24.w),
      decoration: BoxDecoration(
        border:Border(
          top: BorderSide(
            color: Color(0xFF441E4A),
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          Text("Agnes needs permission for full contact access to ensure you can create group chats directly from your contacts.",style: TextStyle(fontSize: 14.sp,color: Color(0xFF988B9A)),textAlign: TextAlign.center,),
          GestureDetector(
            onTap: (){
              openAppSettings();
            },
            child: Row(
              children: [
                const Spacer(),
                Container(
                  margin: EdgeInsets.only(top: 40.w),
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 10.w),
                  decoration: ShapeDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.50, 1.00),
                      end: Alignment(0.50, 0.00),
                      colors: [const Color(0xFFFF3ADF), const Color(0xFFFF91EE)],
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                    child: Text("Open Settings",style: TextStyle(fontSize: 14.sp,color: Colors.black,fontWeight: FontWeight.bold),),

                ),
                const Spacer(),
              ],
            ),
          )
        ],
      ),
    );
  }

  // 搜索用户信息展示
  Widget showSearchUserInfo() {
    return Obx(() {
      bool showLocalUsers = controller.localSearchResults.isNotEmpty;
      bool showRemoteUsers = controller.searchGroupMembers.isNotEmpty ||
          (controller.textController.text.isNotEmpty &&
              GroupManageUtils.isValidSearchInput(controller.textController.text));

      if (controller.hasPermission) {
        if (showLocalUsers && showRemoteUsers) {
          // 两个列表都显示时，各占一半空间
          return Column(
            children: [
              Expanded(
                flex: 1,
                child: localUserList(),
              ),
              Expanded(
                flex: 1,
                child: remoteUserList(),
              ),
            ],
          );
        } else if (showLocalUsers) {
          // 只显示本地用户列表
          return localUserList();
        } else if (showRemoteUsers) {
          // 只显示远程用户列表
          return remoteUserList();
        } else {
          // 都不显示时返回空容器
          return UserNotRegisteredEmptyView(
            message: controller.textController.text,
          );
        }
      } else {
        return showNoPermissionView();
      }
    });
  }

  //  远程用户列表
  Widget remoteUserList() {
    return Obx(() {
      bool isShow = controller.searchGroupMembers.isEmpty &&
          controller.textController.text.isNotEmpty &&
          GroupManageUtils.isValidSearchInput(
              controller.textController.text);

      if (isShow) {
        // 显示未注册用户提示
        return UserNotRegisteredEmptyView(
          message: controller.textController.text,
        );
      }

      // 显示远程用户列表
      return Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 24.w, bottom: 16.w, left: 16.w),
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Color(0xFF441E4A),
                  width: 1,
                ),
              ),
            ),
            child: Text(
              "User",
              style: TextStyle(fontSize: 14.sp, color: Color(0xFF988B9A)),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(vertical: 8),
              shrinkWrap: true,
              physics: ClampingScrollPhysics(),
              itemCount: controller.searchGroupMembers.length,
              itemBuilder: (context, index) {
                final member = controller.searchGroupMembers[index];
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: member.is_member
                      ? null
                      : () {
                    FocusScope.of(context).unfocus();
                    controller.toggleMemberSelected(member);
                  },
                  child: MemberItem(
                    member: member,
                    isSelected: controller.selectedMembers
                        .any((e) => e.user_id == member.user_id),
                    isDisabled: member.is_member,
                  ),
                );
              },
            ),
          )
        ],
      );
    });
  }

  // 本地通讯录用户列表
  Widget localUserList() {
    return Obx(() {
      if (controller.localSearchResults.isEmpty) {
        return SizedBox.shrink();
      }
      return Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 24.w, bottom: 16.w, left: 16.w),
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Color(0xFF441E4A),
                  width: 1,
                ),
              ),
            ),
            child: Text(
              "Contact",
              style: TextStyle(fontSize: 14.sp, color: Color(0xFF988B9A)),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(vertical: 8),
              itemCount: controller.localSearchResults.length,
              shrinkWrap: true,
              physics: ClampingScrollPhysics(),
              itemBuilder: (context, index) {
                final member = controller.localSearchResults[index];
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: member.is_member
                      ? null
                      : () {
                    FocusScope.of(context).unfocus();
                    controller.toggleContactMemberSelected(member);
                  },
                  child: LocalMemberItem(
                    member: member,
                    isSelected: controller.selectedMembers
                        .any((e) => e.user_id == member.appUserId),
                    isDisabled: member.is_member,
                  ),
                );
              },
            ),
          ),
        ],
      );
    });
  }
}
