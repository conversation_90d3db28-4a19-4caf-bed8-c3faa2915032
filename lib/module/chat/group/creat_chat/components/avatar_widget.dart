import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// 通用头像组件，支持本地、网络、SVG、默认头像
class AvatarWidget extends StatelessWidget {
  final String? url;
  final double size;
  final String defaultAsset;

  const AvatarWidget({
    Key? key,
    this.url,
    this.size = 40,
    this.defaultAsset = 'assets/groupChat/default_avatar.png',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (url == null || url!.isEmpty) {
      return CircleAvatar(
        radius: size / 2,
        backgroundImage: AssetImage(defaultAsset),
      );
    }
    if (url!.toLowerCase().endsWith('.svg')) {
      return ClipOval(
        child: SvgPicture.network(
          url!,
          width: size,
          height: size,
          fit: BoxFit.cover,
          placeholderBuilder: (context) => CircleAvatar(
            radius: size / 2,
            backgroundColor: Colors.grey,
            child: Icon(Icons.person, color: Colors.white),
          ),
        ),
      );
    }
    return CircleAvatar(
      radius: size / 2,
      backgroundImage: NetworkImage(url!),
      backgroundColor: Colors.grey,
    );
  }
}
