import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../generated/l10n.dart';
import '../invite_logic.dart';

class SearchBox extends StatelessWidget {
  final InviteMemberController controller = Get.find<InviteMemberController>();
  final String groupId;

  SearchBox({Key? key, this.groupId = ''}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 48,
            margin: EdgeInsets.only(left: 16, right: 8, top: 16, bottom: 16),
            decoration: BoxDecoration(
              border: Border.all(
                color: Color.fromRGBO(143, 143, 143, 1),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: Image.asset(
                    'assets/groupChat/search.png',
                    width: 20,
                    height: 20,
                    fit: BoxFit.cover,
                  ),
                ),
                Expanded(
                  child: ValueListenableBuilder<TextEditingValue>(
                    valueListenable: controller.textController,
                    builder: (context, value, child) {
                      return Stack(
                        alignment: Alignment.centerRight,
                        children: [
                          TextField(
                            focusNode: controller.searchFocusNode,
                            style: TextStyle(color: Colors.white, fontSize: 14),
                            controller: controller.textController,
                            decoration: InputDecoration(
                              hintStyle: TextStyle(
                                  color: Color.fromRGBO(152, 139, 154, 1),
                                  fontSize: 14),
                              hintText:
                              S.of(context).inviteByUsernameEmailOrPhoneNumber,
                              border: InputBorder.none,
                              contentPadding:
                              EdgeInsets.only(left: 8, right: 36,bottom: 4),
                            ),
                            maxLines: 1,
                            onChanged: (text) {
                              controller.onTextChanged(text, groupId);
                            },
                          ),
                          if (value.text.isNotEmpty)
                            Positioned(
                                right: 0,
                                child: GestureDetector(
                                  onTap: () {
                                    controller.textController.clear();
                                    controller.onTextChanged('',groupId);
                                  },
                                  child: Container(
                                    child: Image.asset(
                                      'assets/groupChat/closed.png',
                                      width: 24,
                                      height: 24,
                                      fit: BoxFit.cover,
                                    ),
                                    padding: EdgeInsets.only(right: 16),
                                  ),
                                )

                              // child: IconButton(
                              //   icon: Icon(Icons.clear, color: Colors.grey, size: 20),
                              //   onPressed: () {
                              //     controller.textController.clear();
                              //     controller.onTextChanged('');
                              //   },
                              // ),
                            ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
