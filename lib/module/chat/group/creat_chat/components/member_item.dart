import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../generated/l10n.dart';
import '../../../../../utils/ocolor.dart';
import '../../../model/grouplist/SearchUserBean.dart';
import 'avatar_widget.dart';

/// 群成员条目组件，展示成员头像、名称、选中状态
class MemberItem extends StatelessWidget {
  final SearchUserBean member;
  final bool isLocal = false;
  final VoidCallback? onClick;

  const MemberItem({
    Key? key,
    required this.member,
    this.onClick,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: Row(
        children: [
          Visibility(
            visible: member.avatar_url != null &&
                member.avatar_url!.isNotEmpty &&
                member.avatar_url!.contains("http"),
            child: AvatarWidget(url: member.avatar_url, size: 40),
            replacement: Container(
              width: 40,
              height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.grey,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                member.avatar_url ?? '',
                style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  member.user_name ?? '',
                  style: getStyle(fontSize: 16, color: Colors.white),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  isLocal
                      ? (member.registration_credential ?? '')
                      : member.un_create
                      ? S
                      .of(context)
                      .accountNotCreatedYet
                      : member.registration_credential ?? '',
                  style: getStyle(fontSize: 12, color: Colors.white),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          if (isLocal)
            Obx(() {
              return Visibility(
                  visible: member.isRegister.value,
                  replacement: GestureDetector(
                    onTap: () {
                      onClick?.call();
                    },
                    child: Text(
                      "Invite",
                      style:
                      getStyle(fontSize: 16.sp, color: Color(0xFFFF3ADF)),
                    ),
                  ),
                  child: Image.asset(
                    member.is_member.value
                        ? 'assets/groupChat/disabled.png'
                        : member.isSelected.value
                        ? 'assets/groupChat/selected.png'
                        : 'assets/groupChat/unselected.png',
                    width: 20,
                    height: 20,
                  ));
            })
          else
            Obx(() {
              return Image.asset(
                member.is_member.value
                    ? 'assets/groupChat/disabled.png'
                    : member.isSelected.value
                    ? 'assets/groupChat/selected.png'
                    : 'assets/groupChat/unselected.png',
                width: 20,
                height: 20,
              );
            }),
        ],
      ),
    );
  }
}
