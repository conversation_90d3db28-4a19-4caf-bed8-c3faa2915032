import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../generated/l10n.dart';
import '../../../../../utils/ocolor.dart';
import '../../../model/grouplist/SearchUserBean.dart';
import 'avatar_widget.dart';

/// 群成员条目组件，展示成员头像、名称、选中状态
class MemberItem extends StatelessWidget {
  final SearchUserBean member;
  final bool isSelected;
  final bool isDisabled;

  const MemberItem({
    Key? key,
    required this.member,
    required this.isSelected,
    this.isDisabled = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: Row(
        children: [
          Visibility(
            visible: member.avatar_url != null && member.avatar_url!.isNotEmpty && member.avatar_url!.contains("http"),
              child: AvatarWidget(url: member.avatar_url, size: 40),
             replacement: Container(
               width: 40,
               height: 40,
               decoration: BoxDecoration(
                 color: Colors.grey,
                 borderRadius: BorderRadius.circular(20),
               ),
               child: Text(member.avatar_url??'',style: TextStyle(fontSize: 16.sp,fontWeight: FontWeight.bold,color: Colors.white),),
             ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  member.user_name ?? '',
                  style: getStyle(fontSize: 16, color: Colors.white),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  member.un_create
                      ? S.of(context).accountNotCreatedYet
                      : member.registration_credential ?? '',
                  style: getStyle(fontSize: 12, color: Colors.white),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          Image.asset(
            isDisabled
                ? 'assets/groupChat/disabled.png'
                : isSelected
                    ? 'assets/groupChat/selected.png'
                    : 'assets/groupChat/unselected.png',
            width: 20,
            height: 20,
          ),
        ],
      ),
    );
  }
}
