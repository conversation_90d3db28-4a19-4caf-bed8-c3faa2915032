import 'package:flutter/material.dart';

import '../../../../../generated/l10n.dart';

class UserNotRegisteredEmptyView extends StatelessWidget {
  final String message;

  const UserNotRegisteredEmptyView({
    Key? key,
    this.message = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 36),
      child: Text(
        '"$message"${S.of(context).doesntSeemToBeAnAgnesUserEnterTheirEmailToSendAnInvite}',
        style: TextStyle(fontSize: 14, color: Color.fromRGBO(152, 139, 154, 1)),
      ),
    );
  }
}
