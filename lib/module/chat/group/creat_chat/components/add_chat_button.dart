import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../generated/l10n.dart';
import '../../../../../widget/gradient_popover_selector.dart';
import '../../group_setting/utils/group_manage_util.dart';
import '../invite_logic.dart';


class AddChatButton extends StatefulWidget {
  final VoidCallback onTap;
  final bool filterGroupOwner; // 新增参数

  const AddChatButton({
    Key? key,
    required this.onTap,
    this.filterGroupOwner = true, // 默认过滤
  }) : super(key: key);

  @override
  State<AddChatButton> createState() => _AddChatButtonState();
}

class _AddChatButtonState extends State<AddChatButton> {
  String? selectedRole;
  String? selectedRoleName;
  final InviteMemberController controller = Get.find<InviteMemberController>();

  double bo = 20.0;

  @override
  void initState() {
    super.initState();
    final defaultRole = controller.getDefaultRole(filterGroupOwner: widget.filterGroupOwner);
    if (defaultRole != null) {
      selectedRole = defaultRole.role_code;
      selectedRoleName = defaultRole.role_name;
      controller.role_id.value = defaultRole.role_id ?? '';
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    bo = MediaQuery.of(context).padding.bottom;
  }

  void _onRoleSelected(int index) {
    final roles = controller.getFilteredRoles(filterGroupOwner: widget.filterGroupOwner);
    final role = roles[index];
    setState(() {
      selectedRole = role.role_code;
      selectedRoleName = role.role_name;
    });
    controller.role_id.value = role.role_id ?? '';
    controller.searchFocusNode.unfocus();
    controller.groupFocusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final roles = controller.getFilteredRoles(filterGroupOwner: widget.filterGroupOwner);
    final items = roles.map((role) {
      return {
        'icon': role.role_code == "group_viewer"
            ? 'assets/groupChat/viewer.png'
            : 'assets/groupChat/editor.png',
        'text': GroupManageUtils.getRoleIntlKey(role.role_code, context),
      };
    }).toList();

    final selectedIndex = roles.indexWhere((role) => role.role_code == selectedRole);

    return Column(
      children: [
        // Container(
        //   padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //     children: [
        //       Text(
        //         S.of(context).userAccess,
        //         style: TextStyle(fontSize: 16, color: Colors.white),
        //       ),
        //       GradientPopoverSelector(
        //         items: items,
        //         selectedIndex: selectedIndex < 0 ? 0 : selectedIndex,
        //         onSelected: _onRoleSelected,
        //         showDeleteItem: false,
        //         child: Row(
        //           children: [
        //             Text(
        //               GroupManageUtils.getRoleIntlKey(selectedRole, context),
        //               style: const TextStyle(fontSize: 16, color: Colors.white),
        //             ),
        //             Image.asset(
        //               'assets/groupChat/arrow.png',
        //               width: 20,
        //               height: 20,
        //               fit: BoxFit.cover,
        //             )
        //           ],
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
        Padding(
          padding: const EdgeInsets.only(left: 16,right: 16,top: 16),
          child: GestureDetector(
            onTap: widget.onTap,
            child: Container(
              height: 44,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                gradient: const LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Color(0xFFFF3BDF),
                    Color(0xFFFF91EE),
                  ],
                  stops: [0.3173, 1.0],
                ),
              ),
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      'assets/groupChat/add_chat_icon.png',
                      width: 20,
                      height: 20,
                    ),
                    const SizedBox(width: 8),
                     Text(
                      S.of(context).addToChat,
                      style: TextStyle(
                        color: Color.fromRGBO(13, 13, 13, 1),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: bo,)
      ],
    );
  }
}