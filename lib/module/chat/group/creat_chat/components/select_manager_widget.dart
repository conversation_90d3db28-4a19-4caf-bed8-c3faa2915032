import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../invite_logic.dart';
import 'avatar_widget.dart';

class SelectedMembersBar extends StatelessWidget {
  final InviteMemberController controller = Get.find<InviteMemberController>();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final selected = controller.selectedMembers;
      if (selected.isEmpty) return SizedBox.shrink();
      return Container(
        height: 88,
        margin: EdgeInsets.only(top: 24),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: selected.length,
          itemBuilder: (context, index) {
            final member = selected[index];
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Column(
                    children: [
                      AvatarWidget(url: member.avatar_url, size: 40),
                      SizedBox(height: 4),
                      Container(
                        width: 60,
                        alignment: Alignment.center,
                        child: Text(
                          member.user_name ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(color: Colors.white, fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                  Positioned(
                    right: 0,
                    top: 0,
                    child: GestureDetector(
                      onTap: () {
                        controller.removeSelectedMember(member);
                      },
                      child: Container(
                          padding:
                              EdgeInsets.only(left: 12, right: 4, bottom: 12),
                          child: SvgPicture.asset(
                            'assets/groupChat/close.svg',
                            width: 16,
                            height: 16,
                            fit: BoxFit.cover,
                          )),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      );
    });
  }
}
