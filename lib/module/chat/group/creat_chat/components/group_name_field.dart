import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../generated/l10n.dart';
import '../../../../../widget/GradientBorderContainer.dart';
import '../invite_logic.dart';

class GroupTextField extends StatelessWidget {
  final InviteMemberController controller = Get.find<InviteMemberController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      margin: EdgeInsets.only(left: 16, right: 16, top: 16),

      child: GradientBorderContainer.single(
        strokeWidth: 1,
        gradient: LinearGradient(
          colors: [
            Color(0xFF7253FA),
            Color(0xFFFF3BDF),
            Color(0xFF5E57FE),
          ],
          stops: const [0.0, 0.32, 1.0],
          begin: Alignment.bottomLeft,
          end: Alignment.topRight,
          tileMode: TileMode.decal,
        ),
        borderRadius: BorderRadius.circular(10),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                focusNode: controller.groupFocusNode,
                style: TextStyle(color: Colors.white, fontSize: 16), // 输入内容字体白色
                controller: controller.groupNameController,
                onChanged: controller.onGroupNameTextChanged,
                decoration: InputDecoration(
                  hintText: S.of(context).groupNameOptional,
                  hintStyle: TextStyle(
                      color: Color.fromRGBO(152, 139, 154, 1),
                      fontSize: 16), // 占位字符串颜色灰色

                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 12),
                ),
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );

  }
}
