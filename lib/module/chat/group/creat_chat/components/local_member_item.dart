import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/module/chat/model/grouplist/AppUserAddressBook.dart';

import '../../../../../generated/l10n.dart';
import '../../../../../utils/ocolor.dart';
import '../../../model/grouplist/SearchUserBean.dart';
import 'avatar_widget.dart';

/// 群成员条目组件，展示成员头像、名称、选中状态
class LocalMemberItem extends StatelessWidget {
  final AppUserAddressBook member;
  final bool isSelected;
  final bool isDisabled;
  final Function? onClick;

  const LocalMemberItem({
    Key? key,
    required this.member,
    required this.isSelected,
    this.isDisabled = false,
    this.onClick,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
              child: member.avatar == null ? Container(
                width: 40,
                height: 40,
                color: Colors.grey,
                alignment: Alignment.center,
                child: Text(member.firstLetter,style: TextStyle(
                    fontSize: 16.sp,fontWeight: FontWeight.bold,
                    color: Colors.white
                ),),
              ) : Image.memory(member.avatar!,width: 40,height: 40,)),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  member.name ?? '',
                  style: getStyle(fontSize: 16, color: Colors.white),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  member.phoneNumbers.isNotEmpty ? member.phoneNumbers[0] : '',
                  style: getStyle(fontSize: 12, color: Colors.white),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          Visibility(
            visible: member.isRegistered,
            replacement: GestureDetector(
              onTap: (){
                onClick?.call();
              },
              child: Text(
                "Invite",
                style: getStyle(fontSize: 16.sp, color: Color(0xFFFF3ADF)),
              ),
            ),
            child: Image.asset(
              isDisabled
                  ? 'assets/groupChat/disabled.png'
                  : isSelected
                      ? 'assets/groupChat/selected.png'
                      : 'assets/groupChat/unselected.png',
              width: 20,
              height: 20,
            ),
          ),
        ],
      ),
    );
  }
}
