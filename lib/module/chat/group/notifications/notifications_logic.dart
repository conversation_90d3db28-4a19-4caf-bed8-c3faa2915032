import 'dart:convert';

import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/group_chat/agora_logic.dart';
import 'package:new_agnes/module/chat/group/notifications/NotificationModel.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../api/Api.dart';
import '../../../../api/ApiProvider.dart';

class NotificationsLogic extends GetxController {

  RefreshController refreshController = RefreshController(initialRefresh: false);

  final notifications = <NotificationModel>[].obs;

  final int currentIndex = 1;
  final int pageSize = 10;

  final isLoading = true.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    getNotifications();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }

  // 获取通知列表
  void getNotifications() async{
    try {
      isLoading.value = true;
      notifications.clear();
      final response = await Get.find<ApiProvider>().get(Api.getSystemMessages);
      if(response.statusCode == 200){
        if (response.bodyString != null) {
          Map<String,dynamic> map = json.decode(response.bodyString!);
          List<NotificationModel> data = [];
          if (map["messages"] != null) {
            data = map["messages"].map<NotificationModel>((e)=>NotificationModel.fromJson(e)).toList();
          }
         notifications.value = data;
        }
      }
      isLoading.value = false;
      Get.find<AgoraLogic>().hasJoinedTips.value = false;
    }catch (e){
      isLoading.value = false;
      LoggerUtils.d("获取系统消息报错----$e");
    }
  }

}
