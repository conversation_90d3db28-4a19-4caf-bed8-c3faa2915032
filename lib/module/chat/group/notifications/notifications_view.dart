import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/notifications/NotificationModel.dart';
import 'package:new_agnes/widget/ComAppbar.dart';
import 'package:path/path.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../generated/l10n.dart';
import '../../../../utils/bgCover.dart';
import 'notifications_logic.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  final NotificationsLogic logic = Get.put(NotificationsLogic());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    logic.getNotifications();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: buildMineBg(
        child: Column(
          children: [
            ComAppBar(context, S.of(context).notifications),
            Expanded(child: _buildList(context)),
          ],
        ),
      ),
    );
  }

  Widget _buildList(BuildContext context) {
    return AnimationLimiter(
        child: MediaQuery.removePadding(
      context: context,
      removeTop: true,
      child: Obx(() {
        if (logic.isLoading.value) {
          return SizedBox.shrink();
        } else {
          return logic.notifications.isNotEmpty
              ? Obx(() {
                return ListView.builder(
                    itemCount: logic.notifications.length,
                    itemBuilder: (context, index) {
                      return AnimationConfiguration.staggeredList(
                        position: index,
                        duration: const Duration(milliseconds: 200),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: _buildItem(logic.notifications[index]),
                          ),
                        ),
                      );
                    });
              })
              : Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  "assets/icon/icon_empty.png",
                  width: 150.w,
                  height: 150.w,
                ),
                Padding(
                  padding: EdgeInsets.only(top: 10.w),
                  child: Text(
                    S.of(context).noMoreMessage,
                    style: TextStyle(fontSize: 16.sp, color: Colors.white),
                  ),
                )
              ],
            ),
          );
        }
      }),
    ));
  }

  Widget _buildItem(NotificationModel model) {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        color: Colors.white.withAlpha(10),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            children: [
              Image.asset(
                "assets/icon/icon_message.png",
                width: 32.w,
                height: 32.w,
              ),
              // CachedNetworkImage(imageUrl: "imageUrl",width: 32.w,height: 32.w,
              //   errorWidget: (context,url,error)=>Image.asset("assets/images/default_avatar.png",width: 32.w,height: 32.w),
              //   progressIndicatorBuilder: (context,url,progress)=>CupertinoActivityIndicator(color: Colors.white,radius: 20.w,),),
              SizedBox(
                width: 10.w,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                            child: Text(
                          model.title ?? '',
                          style:
                              TextStyle(fontSize: 16.sp, color: Colors.white),
                          overflow: TextOverflow.ellipsis,
                        )),
                        SizedBox(
                          width: 20.w,
                        ),
                        Text(
                          model.createdAt ?? '',
                          style: TextStyle(
                              fontSize: 12.sp, color: Color(0xFF988B9A)),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 5.w,
                    ),
                    Text(
                      model.content ?? '',
                      style:
                          TextStyle(fontSize: 14.sp, color: Color(0xFF988B9A)),
                    ),
                  ],
                ),
              )
            ],
          ),
          Offstage(
            offstage: true,
            child: Padding(
              padding: EdgeInsets.only(top: 16.w),
              child: Row(
                children: [
                  const Spacer(),
                  GestureDetector(
                      onTap: () {},
                      child: Container(
                          width: 53.w,
                          height: 36.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            border: Border.all(color: Color(0xFFFF3BDF)),
                          ),
                          child: Center(
                              child: Text(
                            "Reject",
                            style: TextStyle(
                                fontSize: 14.sp, color: Color(0xFFFF3BDF)),
                          )))),
                  Padding(
                    padding: EdgeInsets.only(left: 16.w),
                    child: TextButton(
                        onPressed: () {},
                        style: TextButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16.r),
                          ),
                        ),
                        child: Container(
                            width: 53.w,
                            height: 36.h,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.r),
                              gradient: LinearGradient(
                                colors: [
                                  Color(0xFFFF3BDF),
                                  Color(0xFFFF91EE),
                                ],
                              ),
                            ),
                            child: Text(
                              "Join",
                              style: TextStyle(
                                  fontSize: 14.sp, color: Colors.black),
                            ))),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
