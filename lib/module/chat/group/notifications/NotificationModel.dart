import 'dart:convert';
/// id : "msg_123"
/// title : "Welcome to the platform"
/// content : "Thank you for joining our platform..."
/// message_type : 1
/// target_type : 1
/// target_user_id : "user_123"
/// sender_user_id : "user_456"
/// is_read : false
/// created_at : "2025-01-20T10:30:00"
/// updated_at : "2025-01-20T10:30:00"

NotificationModel notificationModelFromJson(String str) => NotificationModel.fromJson(json.decode(str));
String notificationModelToJson(NotificationModel data) => json.encode(data.toJson());
class NotificationModel {
  NotificationModel({
      String? id, 
      String? title, 
      String? content, 
      num? messageType, 
      num? targetType, 
      String? targetUserId, 
      String? senderUserId, 
      bool? isRead, 
      String? createdAt, 
      String? updatedAt,}){
    _id = id;
    _title = title;
    _content = content;
    _messageType = messageType;
    _targetType = targetType;
    _targetUserId = targetUserId;
    _senderUserId = senderUserId;
    _isRead = isRead;
    _createdAt = createdAt;
    _updatedAt = updatedAt;
}

  NotificationModel.fromJson(dynamic json) {
    _id = json['id'];
    _title = json['title'];
    _content = json['content'];
    _messageType = json['message_type'];
    _targetType = json['target_type'];
    _targetUserId = json['target_user_id'];
    _senderUserId = json['sender_user_id'];
    _isRead = json['is_read'];
    _createdAt = json['created_at'] != null && json['created_at'].isNotEmpty ? (json['created_at'].contains("T") ? json['created_at'].replaceFirst("T", " "):json['created_at']):json['created_at'];
    _updatedAt = json['updated_at'];
  }
  String? _id;
  String? _title;
  String? _content;
  num? _messageType;
  num? _targetType;
  String? _targetUserId;
  String? _senderUserId;
  bool? _isRead;
  String? _createdAt;
  String? _updatedAt;
NotificationModel copyWith({  String? id,
  String? title,
  String? content,
  num? messageType,
  num? targetType,
  String? targetUserId,
  String? senderUserId,
  bool? isRead,
  String? createdAt,
  String? updatedAt,
}) => NotificationModel(  id: id ?? _id,
  title: title ?? _title,
  content: content ?? _content,
  messageType: messageType ?? _messageType,
  targetType: targetType ?? _targetType,
  targetUserId: targetUserId ?? _targetUserId,
  senderUserId: senderUserId ?? _senderUserId,
  isRead: isRead ?? _isRead,
  createdAt: createdAt ?? _createdAt,
  updatedAt: updatedAt ?? _updatedAt,
);
  String? get id => _id;
  String? get title => _title;
  String? get content => _content;
  num? get messageType => _messageType;
  num? get targetType => _targetType;
  String? get targetUserId => _targetUserId;
  String? get senderUserId => _senderUserId;
  bool? get isRead => _isRead;
  String? get createdAt => _createdAt;
  String? get updatedAt => _updatedAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['title'] = _title;
    map['content'] = _content;
    map['message_type'] = _messageType;
    map['target_type'] = _targetType;
    map['target_user_id'] = _targetUserId;
    map['sender_user_id'] = _senderUserId;
    map['is_read'] = _isRead;
    map['created_at'] = _createdAt;
    map['updated_at'] = _updatedAt;
    return map;
  }

}