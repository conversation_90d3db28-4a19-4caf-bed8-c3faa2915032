import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/data/caseAndQuestion.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_base_item.dart';
import 'package:new_agnes/module/chat/group/message_chat/mixin/group_chat_message_ui_mixin.dart';

import '../../../../../api/StorageService.dart';
import '../../../../../utils/cmUtils.dart';
import '../../../../../utils/event_bus.dart';
import '../../../../../widget/message_long_tap_tools.dart';
import '../enum/group_chat_message_enum.dart';
import '../logic/group_chat_message_controller.dart';
import '../model/group_chat_message_model.dart';
import '../widgets/group_chat_agens_result_widget.dart';
import 'group_chat_base_user_info_item.dart';

class GroupChatTextAgensResultItem extends GroupChatBaseItem {
  GroupChatTextAgensResultItem({required super.model, required super.userId});
  final groupChatController = Get.isRegistered<GroupChatMessageController>()
      ? Get.find<GroupChatMessageController>()
      : Get.put(GroupChatMessageController());

  @override
  Widget buildNoUserInfoItem() {
    Timer? _longPressTimer;
    double _scale = 1.0;
    Color lightColor = Colors.transparent;
    try{
      GroupCreateMessageModel createMessageModel =
      GroupCreateMessageModel.fromJson(
          jsonDecode(model.messageContent ?? "{}"));
      return StatefulBuilder(
        builder: (context,state) {
          return GestureDetector(
            onLongPressStart: (details) {
              if (context.mounted) {
                state(() {
                  _scale = 1.05;
                });
              }

              _longPressTimer = Timer(const Duration(milliseconds: 250), () {
                eventBus.fire<String>("showCustomOverlay");
                MessageLongTapTools.showCustomOverlay(
                    context,
                        (MessageItemType type) {
                      switch (type) {
                        case MessageItemType.quote:
                          groupChatController.quoteMessage.value = model;
                          break;
                        case MessageItemType.copy:
                          CmUtils.fuZhi(model.content);
                          break;
                      }
                    },
                    false,
                    contentType: GroupChatMessageContentType.values[model.content_type!-1],
                    onDismiss: () {
                      eventBus.fire<String>("hideCustomOverlay");
                    });
              });
            },
            onLongPressEnd: (details) {
              if (context.mounted) {
                state(() {
                  _scale = 1.0;
                });
              }
              _longPressTimer?.cancel();
            },
            child: AnimatedScale(
              duration: Duration(milliseconds: 200),
              scale: _scale,
              curve: Curves.easeOut,
              child: AnimatedContainer(
                  duration: Duration(milliseconds: 200),
                  margin: EdgeInsets.only(left: 42.w,bottom: 8.w),
                  alignment: Alignment.centerLeft,
                  color: lightColor,
                  curve: Curves.easeIn,
                child: GroupChatAgensResultWidget(
                itemMargin: itemMargin,
                model:model,
                createMessageModel: createMessageModel,
              ),
            ),
          ));
        }
      );
    }catch(e){

    }
    return SizedBox.shrink();
  }
}
