import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/module/chat/group/message_chat/mixin/group_chat_message_ui_mixin.dart';

import 'group_chat_base_user_info_item.dart';

class GroupChatTextAudioItem extends GroupChatBaseUserInfoItem
    with GroupChatMessageUIMixin {
  GroupChatTextAudioItem({required super.model, required super.userId});

  @override
  Widget buildContentText() {
    return buildCommonTextItem();
  }

  @override
  Widget buildAudioIcon() {
    return Padding(
      padding: EdgeInsets.only(top: 6),
      child: Image.asset(
        "assets/groupChat/ic_group_message_audio.png",
        width: 16.w,
        height: 16.h,
      ),
    );
  }
}
