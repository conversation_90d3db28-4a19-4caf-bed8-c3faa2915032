import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/module/chat/group/message_chat/model/group_chat_message_model.dart';
///不带头像的基类
/// 2.不带头像跟名字
///   1️⃣加入群组，创建群组的消息
///   2️⃣Agens生成物的过程
class GroupChatBaseItem extends StatelessWidget {
  final GroupChatMessageModel model;
  final String userId;
  double get itemMargin => 42.w;
  const GroupChatBaseItem(
      {super.key, required this.model, required this.userId});

  @override
  Widget build(BuildContext context) {
    return buildNoUserInfoItem();
  }

  Widget buildNoUserInfoItem() {
    return SizedBox.shrink();
  }
}
