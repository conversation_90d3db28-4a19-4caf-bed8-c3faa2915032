import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/message_chat/mixin/group_chat_message_ui_mixin.dart';
import 'package:markdown_widget/widget/inlines/img.dart';

import '../enum/group_chat_message_enum.dart';
import '../model/group_chat_message_model.dart';
import 'group_chat_base_user_info_item.dart';

class GroupChatTextFileItem extends GroupChatBaseUserInfoItem
    with GroupChatMessageUIMixin {
  GroupChatTextFileItem({required super.model, required super.userId, super.agnes = false});

  // 用于跟踪图片重试状态的Map
  final Map<String, int> _retryCount = {};

  @override
  Widget buildContentText() {
    if (model.content.isEmpty) {
      return SizedBox.shrink();
    }
    return buildCommonTextItem();
  }

  @override
  Widget buildAttachmentsGrid() {
    List<GroupChatAttachmentsModel> attachments = model.attachments ?? [];
    if (attachments.isEmpty) {
      return SizedBox.shrink();
    }

    // 按类型分类附件
    Map<AttachmentType, List<GroupChatAttachmentsModel>> classifiedAttachments =
        _classifyAttachments(attachments);

    List<Widget> attachmentWidgets = [];

    // 按顺序显示不同类型的附件
    if (classifiedAttachments[AttachmentType.image]?.isNotEmpty ?? false) {
      attachmentWidgets.add(_buildImageGrid(
          classifiedAttachments[AttachmentType.image]!, crossAxisAlignment));
    }

    if (classifiedAttachments[AttachmentType.file]?.isNotEmpty ?? false) {
      attachmentWidgets
          .add(_buildFileList(classifiedAttachments[AttachmentType.file]!));
    }

    if (classifiedAttachments[AttachmentType.unknown]?.isNotEmpty ?? false) {
      attachmentWidgets.add(
          _buildUnknownList(classifiedAttachments[AttachmentType.unknown]!));
    }

    return Column(
      children: attachmentWidgets
          .map((widget) => Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: widget,
              ))
          .toList(),
    );
  }

  /// 附件分类方法
  Map<AttachmentType, List<GroupChatAttachmentsModel>> _classifyAttachments(
      List<GroupChatAttachmentsModel> attachments) {
    Map<AttachmentType, List<GroupChatAttachmentsModel>> classified = {
      AttachmentType.image: [],
      AttachmentType.file: [],
      AttachmentType.unknown: [],
    };

    for (var attachment in attachments) {
      classified[attachment.fileType]?.add(attachment);
    }

    return classified;
  }

  /// 构建图片网格布局（每行3张）
  Widget _buildImageGrid(List<GroupChatAttachmentsModel> imageAttachments,
      CrossAxisAlignment crossAxisAlignment) {
    
    // 根据图片数量决定布局方式
    if (imageAttachments.length <= 2) {
      // 两张图以下使用固定尺寸 115x153
      return Wrap(
        alignment: crossAxisAlignment == CrossAxisAlignment.end
            ? WrapAlignment.end
            : WrapAlignment.start,
        spacing: 8.w, // 水平间距
        runSpacing: 8.h, // 垂直间距
        children: imageAttachments
            .map(
              (attachment) => SizedBox(
                width: 115.w, // 固定宽度
                height: 153.h, // 固定高度
                child: _buildSingleAttachmentImage(attachment),
              ),
            )
            .toList(),
      );
    } else {
      // 三张及以上图片使用原有的动态计算宽度
      double imageWidth = (MediaQuery.of(Get.context!).size.width - (itemMargin * 2 + 48.w)) / 3;
      
      return Wrap(
        alignment: crossAxisAlignment == CrossAxisAlignment.end
            ? WrapAlignment.end
            : WrapAlignment.start,
        spacing: 8.w, // 水平间距
        runSpacing: 8.h, // 垂直间距
        children: imageAttachments
            .map(
              (attachment) => SizedBox(
                width: imageWidth,
                child: AspectRatio(
                  aspectRatio: 115 / 153, // 维持原有宽高比
                  child: _buildSingleAttachmentImage(attachment),
                ),
              ),
            )
            .toList(),
      );
    }
  }

  /// 构建单个附件图片
  Widget _buildSingleAttachmentImage(
      GroupChatAttachmentsModel attachmentsModel) {
    return StatefulBuilder(
      builder: (context, setState) {
        return GestureDetector(
          onTap: () => _onImageTap(attachmentsModel),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10.r),
            child: CachedNetworkImage(
              imageUrl: attachmentsModel.attachment_url ?? "",
              fit: BoxFit.cover, // 改为 cover，避免拉伸，占满容器
              placeholder: (context, url) => Container(
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2.0,
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFCB53BA)),
                  ),
                ),
              ),
              errorWidget: (context, url, error) => StatefulBuilder(
                builder: (context, errorSetState) {
                  return GestureDetector(
                    onTap: () => _retryImageLoad(attachmentsModel, setState),
                    onTapDown: (_) => errorSetState(() {}),
                    child: AnimatedContainer(
                      duration: Duration(milliseconds: 150),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(10.r),
                        border: Border.all(
                          color: Color(0xFFCB53BA).withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.refresh,
                            color: Color(0xFFCB53BA),
                            size: 32.sp,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建音频附件列表
  Widget _buildAudioList(List<GroupChatAttachmentsModel> audioAttachments) {
    return Column(
      children:
          audioAttachments.map((audio) => _buildAudioItem(audio)).toList(),
    );
  }

  /// 构建单个音频附件
  Widget _buildAudioItem(GroupChatAttachmentsModel audioModel) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.audiotrack,
            color: Color(0xFFCB53BA),
            size: 24.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getFileName(audioModel.attachment_url ?? ""),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.h),
                Text(
                  "音频文件",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.play_circle_outline,
            color: Color(0xFFCB53BA),
            size: 32.sp,
          ),
        ],
      ),
    );
  }

  /// 构建文件附件列表
  Widget _buildFileList(List<GroupChatAttachmentsModel> fileAttachments) {
    return Column(
      children: fileAttachments.map((file) => _buildFileItem(file)).toList(),
    );
  }

  /// 构建单个文件附件
  Widget _buildFileItem(GroupChatAttachmentsModel fileModel) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            _getFileIcon(fileModel.attachment_url ?? ""),
            color: Color(0xFFCB53BA),
            size: 24.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getFileName(fileModel.attachment_url ?? ""),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.h),
                Text(
                  _getFileType(fileModel.attachment_url ?? ""),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.download,
            color: Color(0xFFCB53BA),
            size: 24.sp,
          ),
        ],
      ),
    );
  }

  /// 构建未知类型附件列表
  Widget _buildUnknownList(List<GroupChatAttachmentsModel> unknownAttachments) {
    return Column(
      children: unknownAttachments
          .map((unknown) => _buildUnknownItem(unknown))
          .toList(),
    );
  }

  /// 构建单个未知类型附件
  Widget _buildUnknownItem(GroupChatAttachmentsModel unknownModel) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.insert_drive_file,
            color: Colors.grey[600],
            size: 24.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getFileName(unknownModel.attachment_url ?? ""),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.h),
                Text(
                  "未知类型",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.help_outline,
            color: Colors.grey[600],
            size: 24.sp,
          ),
        ],
      ),
    );
  }

  /// 从URL中提取文件名
  String _getFileName(String url) {
    if (url.isEmpty) return "未知文件";
    try {
      Uri uri = Uri.parse(url);
      String fileName = uri.pathSegments.last;
      return fileName.isNotEmpty ? fileName : "未知文件";
    } catch (e) {
      return "未知文件";
    }
  }

  /// 根据文件扩展名获取对应图标
  IconData _getFileIcon(String url) {
    String extension = _getFileExtension(url).toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.archive;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// 根据文件扩展名获取文件类型描述
  String _getFileType(String url) {
    String extension = _getFileExtension(url).toLowerCase();
    switch (extension) {
      case 'pdf':
        return "PDF文档";
      case 'doc':
      case 'docx':
        return "Word文档";
      case 'xls':
      case 'xlsx':
        return "Excel表格";
      case 'ppt':
      case 'pptx':
        return "PowerPoint演示文稿";
      case 'txt':
        return "文本文件";
      case 'zip':
      case 'rar':
      case '7z':
        return "压缩文件";
      default:
        return "文件";
    }
  }

  /// 从URL中提取文件扩展名
  String _getFileExtension(String url) {
    if (url.isEmpty) return "";
    try {
      String fileName = _getFileName(url);
      int dotIndex = fileName.lastIndexOf('.');
      return dotIndex != -1 ? fileName.substring(dotIndex + 1) : "";
    } catch (e) {
      return "";
    }
  }

  /// 重试图片加载
  void _retryImageLoad(GroupChatAttachmentsModel attachmentsModel, StateSetter setState) {
    String imageUrl = attachmentsModel.attachment_url ?? "";
    if (imageUrl.isEmpty) return;
    
    // 增加重试次数
    _retryCount[imageUrl] = (_retryCount[imageUrl] ?? 0) + 1;
    
    // 限制重试次数，避免无限重试
    if (_retryCount[imageUrl]! > 3) {
      return;
    }
    
    // 清空缓存，强制重新加载
    CachedNetworkImage.evictFromCache(imageUrl);
    
    // 通过 setState 强制重新构建组件
    setState(() {
      // 这里不需要做任何操作，只是触发重新构建
    });
  }

  /// 处理图片点击事件，打开图片查看器
  void _onImageTap(GroupChatAttachmentsModel attachmentsModel) {
    if (attachmentsModel.attachment_url == null || 
        attachmentsModel.attachment_url!.isEmpty) {
      return;
    }

    // 创建图片组件
    final imageWidget = CachedNetworkImage(
      imageUrl: attachmentsModel.attachment_url!,
      fit: BoxFit.contain, // 查看器中使用 contain 以完整显示图片
      placeholder: (context, url) => Container(
        width: 200.w,
        height: 200.w,
        child: Center(
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFCB53BA)),
          ),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        width: 200.w,
        height: 200.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Color(0xFFCB53BA),
              size: 32.sp,
            ),
            SizedBox(height: 8.h),
            Text(
              '图片加载失败',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
              ),
            ),
          ],
        ),
      ),
    );

    // 打开图片查看器
    Navigator.of(Get.context!).push(PageRouteBuilder(
      opaque: false,
      pageBuilder: (_, __, ___) => ImageViewer(child: imageWidget),
    ));
  }
}
