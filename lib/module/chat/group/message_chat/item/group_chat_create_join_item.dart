import 'package:flutter/material.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_base_item.dart';

class GroupChatCreateJoinItem extends GroupChatBaseItem {
  GroupChatCreateJoinItem({required super.model, required super.userId});

  @override
  Widget buildNoUserInfoItem() {
    if (model.sender_user?.id != userId) {
      return SizedBox.shrink();
    }
    return Center(
      child: Padding(padding: EdgeInsets.symmetric(vertical: 5),child: Text(
        model.content ?? "",
        style: TextStyle(fontSize: 14, color: Color.fromRGBO(152, 139, 154, 1)),
      ),),
    );
  }
}
