import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_base_item.dart';

import '../enum/group_chat_message_enum.dart';
import '../model/group_chat_message_model.dart';
import '../widgets/group_chat_generate_end_widget.dart';
import '../widgets/group_chat_generate_start_widget.dart';

class GroupChatGenerateThingItem extends GroupChatBaseItem {
  GroupChatGenerateThingItem({required super.model, required super.userId});

  @override
  Widget buildNoUserInfoItem() {
    if (model.productStatusType ==
            GroupChatMessageProductStatusType.start ||
        model.productStatusType ==
            GroupChatMessageProductStatusType.end) {
      GroupCreateMessageModel createMessageModel =
          GroupCreateMessageModel.fromJson(jsonDecode(model.messageContent ?? "{}"));
      if (model.productStatusType ==
          GroupChatMessageProductStatusType.start) {
        DateTime createdAt;
        try {
          createdAt = DateTime.parse(model.created_at ?? "");
        } catch (e) {
          createdAt = DateTime.now();
        }
        return GroupChatGenerateStartWidget(
            groupCreateMessageModel: createMessageModel, createdAt: createdAt,timezone: model.timezone);
      } else {
        return GroupChatGenerateEndWidget(
            groupCreateMessageModel: createMessageModel);
      }
    }
    return SizedBox.shrink();
  }
}
