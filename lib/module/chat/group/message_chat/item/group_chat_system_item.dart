import 'package:flutter/material.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_base_item.dart';

class GroupChatSystemItem extends GroupChatBaseItem {
  GroupChatSystemItem({required super.model, required super.userId});

  @override
  Widget buildNoUserInfoItem() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Center(
        child: Text(
          model.content ?? '', //messageModel.content ?? ""
          style: TextStyle(fontSize: 14, color: Color.fromRGBO(152, 139, 154, 1)),
        ),
      ),
    );
  }
}
