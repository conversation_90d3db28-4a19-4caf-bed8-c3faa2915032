import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/message_chat/model/group_chat_message_model.dart';
import 'package:new_agnes/utils/event_bus.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../../../../../api/StorageService.dart';
import '../../../../../utils/video_thumbnail_generator.dart';
import '../../../../../generated/l10n.dart';
import '../../../../../utils/cmUtils.dart';
import '../../../../../widget/message_input_widget.dart';
import '../../../../../widget/message_long_tap_tools.dart';
import '../enum/group_chat_message_enum.dart';
import '../logic/group_chat_message_controller.dart';

// 顶层声明：父消息文件预览类型（Dart 不支持在类内部再声明 enum / class）
enum _ParentPreviewType { image, video, ppt, report, unknown }

class _ParentFilePreviewInfo {
  final _ParentPreviewType type;
  final String? url; // 图片或视频封面 URL
  const _ParentFilePreviewInfo(this.type, this.url);
}

///带头像的基类
/// 1.带头像跟名字
///   1️⃣纯图片
///   2️⃣图片加文本
///   3️⃣纯文本
///   4️⃣文本加Agens返回的附件
///   5️⃣文本加语音播报
class GroupChatBaseUserInfoItem extends StatelessWidget {
  final GroupChatMessageModel model;
  final String userId;
  // 是否 Agnes 模式（为 true 时禁用头像长按 @ 功能）
  final bool agnes;

  GroupChatBaseUserInfoItem(
      {super.key,
      required this.model,
      required this.userId,
      this.agnes = false});

  CrossAxisAlignment get crossAxisAlignment {
    return model.getRoleEnum(userId) == GroupChatMessageRoleEnum.me
        ? CrossAxisAlignment.end
        : CrossAxisAlignment.start;
  }

  BorderRadiusGeometry get borderRadius {
    return model.getRoleEnum(userId) == GroupChatMessageRoleEnum.me
        ? const BorderRadius.only(
            topLeft: Radius.circular(10),
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10),
          )
        : const BorderRadius.only(
            topRight: Radius.circular(10),
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10),
          );
  }

  Color get backgroundColor {
    switch (model.getRoleEnum(userId)) {
      case GroupChatMessageRoleEnum.me:
        return Color(0xFF5E681D).withValues(alpha: 0.6);
      case GroupChatMessageRoleEnum.others:
        return Color(0xFF8F53C8).withValues(alpha: 0.4);
      case GroupChatMessageRoleEnum.agens:
        return Color(0xFFC853BA).withValues(alpha: 0.2);
    }
  }

  double get itemMargin {
    return 42.w;
  }

  EdgeInsets get margin {
    switch (model.getRoleEnum(userId)) {
      case GroupChatMessageRoleEnum.me:
        return EdgeInsets.only(top: 4.h, left: itemMargin);
      case GroupChatMessageRoleEnum.others:
        return EdgeInsets.only(top: 4.h, right: itemMargin);
      case GroupChatMessageRoleEnum.agens:
        return EdgeInsets.only(top: 4.h, right: itemMargin);
    }
  }

  final groupChatController = Get.isRegistered<GroupChatMessageController>()
      ? Get.find<GroupChatMessageController>()
      : Get.put(GroupChatMessageController());

  String _formatParentContent(GroupChatMessageModel messageModel) {
    final raw = messageModel.parent?.content;
    if (raw == null || raw.isEmpty) return '';
    try {
      final decoded = jsonDecode(raw);
      if (decoded is Map) {
        if (decoded['add_task_items'] is List &&
            decoded['add_task_items'].isNotEmpty) {
          final items = decoded['add_task_items'] as List;
          final contents = items
              .map((e) => e is Map && e['content'] is String
                  ? e['content'] as String
                  : null)
              .whereType<String>()
              .toList();
          if (contents.isNotEmpty) {
            return contents.join('；');
          }
        } else if (decoded['link_content'] is String) {
          return decoded['link_content'] as String;
        } else if (decoded['update_task_items'] is List &&
            decoded['update_task_items'].isNotEmpty) {
          final items = decoded['update_task_items'] as List;
          final contents = items
              .map((e) => e is Map && e['content'] is String
                  ? e['content'] as String
                  : null)
              .whereType<String>()
              .toList();
          if (contents.isNotEmpty) {
            return contents.join('；');
          }
        }
      }
    } catch (_) {
      // ignore parse errors and fallback to raw string
    }
    return raw;
  }

  @override
  Widget build(BuildContext context) {
    Timer? _longPressTimer;
    double _scale = 1.0;
    bool isFromMe = model.getRoleEnum(userId) == GroupChatMessageRoleEnum.me;
    Color lightColor = Colors.transparent;
    return StatefulBuilder(builder: (context, state) {
      eventBus.on<Map<String, dynamic>>().listen((data) {
        if (data['key'] == "recall_message") {
          if (model.id == data['messageId']) {
            if (context.mounted) {
              state(() {
                lightColor =
                    const Color(0x19A374AA); //Colors.white.withOpacity(0.3);
                Future.delayed(const Duration(seconds: 1), () {
                  if (context.mounted) {
                    state(() {
                      lightColor = Colors.transparent;
                    });
                  }
                });
              });
            }
          }
        }
      });
      return GestureDetector(
        onLongPressStart: (details) {
          if (context.mounted) {
            state(() {
              _scale = 1.05;
            });
          }

          _longPressTimer = Timer(const Duration(milliseconds: 250), () {
            final userId =
                Get.find<StorageService>().getUserInfoData().id ?? "";
            bool isFromMe =
                model.getRoleEnum(userId) == GroupChatMessageRoleEnum.me;
            eventBus.fire<String>("showCustomOverlay");
            MessageLongTapTools.showCustomOverlay(
                context,
                (MessageItemType type) {
                  switch (type) {
                    case MessageItemType.quote:
                      groupChatController.quoteMessage.value = model;
                      break;
                    case MessageItemType.copy:
                      CmUtils.fuZhi(model.content);
                      break;
                  }
                },
                isFromMe,
                contentType:
                    GroupChatMessageContentType.values[model.content_type! - 1],
                onDismiss: () {
                  eventBus.fire<String>("hideCustomOverlay");
                });
          });
        },
        onLongPressEnd: (details) {
          if (context.mounted) {
            state(() {
              _scale = 1.0;
            });
          }
          _longPressTimer?.cancel();
        },
        child: AnimatedScale(
          duration: Duration(milliseconds: 200),
          scale: _scale,
          curve: Curves.easeOut,
          child: AnimatedContainer(
            duration: Duration(milliseconds: 200),
            color: lightColor,
            curve: Curves.easeIn,
            margin: EdgeInsets.symmetric(vertical: 12),
            child: Column(
              crossAxisAlignment:
                  isFromMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                _buildIsFromMe(
                    isFromMe,
                    Row(
                      mainAxisAlignment: isFromMe
                          ? MainAxisAlignment.end
                          : MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: isFromMe
                          ? [
                              buildMessageFailed(),
                              _buildContainer(model),
                              SizedBox(width: 8.w),
                              _buildAvatar(model),
                            ]
                          : [
                              _buildAvatar(model),
                              SizedBox(width: 8.w),
                              _buildContainer(model),
                              SizedBox(width: itemMargin),
                            ],
                    )),
                _buildQuoteWidget(model, isFromMe, context),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildIsFromMe(bool isFromMe, Widget child) {
    if (isFromMe && model.sendingStatus != MessageStatus.SUCCESS) {
      return IntrinsicHeight(
        child: child,
      );
    }
    return child;
  }

  Widget _buildQuoteWidget(
      GroupChatMessageModel messageModel, bool isFromMe, BuildContext context) {
    final parent = messageModel.parent;
    debugPrint("parent: ${parent?.content}");
    return parent != null &&
            parent.sender_user != null &&
            (parent.id?.isNotEmpty ?? false)
        ? GestureDetector(
            onTap: () {
              if (messageModel.parent?.id != null) {
                groupChatController.recallMessage(messageModel.parent?.id ?? "",
                    context, messageModel.id ?? "");
              } else {
                showFailToast(S.of(context).quotedMessageHasExpired);
              }
            },
            behavior: HitTestBehavior.translucent,
            child: Container(
              constraints: BoxConstraints(
                  maxWidth:
                      MediaQuery.of(context).size.width - 120.w), // 设置最大宽度
              decoration: BoxDecoration(
                color: const Color(0x990D0D0D), // 0.6透明度
                borderRadius: BorderRadius.circular(5),
              ),
              margin: isFromMe
                  ? EdgeInsets.only(right: 40.w, top: 4.w)
                  : EdgeInsets.only(left: 40.w, top: 4.w),
              padding:
                  const EdgeInsets.only(left: 8, right: 8, bottom: 8, top: 4),
              child: _buildQuoteTypeWidget(messageModel, isFromMe, context),
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildQuoteTypeWidget(
      GroupChatMessageModel messageModel, bool isFromMe, BuildContext context) {
    final title = messageModel.parent?.payload?.title ?? "";

    if (messageModel.parent?.contentType == GroupChatMessageContentType.text) {
      return Text(
        '${messageModel.parent?.sender_user?.username ?? ""}:${_formatParentContent(messageModel)}',
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: Color.fromRGBO(152, 139, 154, 1),
        ),
      );
    } else if (messageModel.parent?.contentType ==
        GroupChatMessageContentType.file) {
      final preview = _getParentFilePreview(messageModel);
      return Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment:
              isFromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
          children: [
            Flexible(
                child: Text(
              '${messageModel.parent?.sender_user?.username ?? ""}:$title',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color.fromRGBO(152, 139, 154, 1),
              ),
            )),
            const SizedBox(width: 8),
            _buildParentFilePreviewWidget(preview, title),
          ]);
    } else if (messageModel.parent?.contentType ==
        GroupChatMessageContentType.textAndFile) {
      return Row(
          mainAxisAlignment:
              isFromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Text(
                '${messageModel.parent?.sender_user?.username ?? ""}:${_formatParentContent(messageModel)}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color.fromRGBO(152, 139, 154, 1),
                    height: 1),
              ),
            ),
            SizedBox(width: 8),
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: CachedNetworkImage(
                imageUrl:
                    messageModel.parent?.attachments?.first.attachment_url ??
                        '',
                width: 53,
                height: 30,
                fit: BoxFit.cover,
              ),
            ),
          ]);
    }
    return SizedBox.shrink();
  }

  _ParentFilePreviewInfo _getParentFilePreview(
      GroupChatMessageModel messageModel) {
    try {
      if (messageModel.parent?.assistantActionType ==
          GroupAssistantActionType.generateThingEnd) {
        GroupCreateMessageModel m = GroupCreateMessageModel.fromJson(
            jsonDecode(messageModel.parent?.messageContent ?? '{}'));
        if (m.mode == 4 && m.vtype == 'image') {
          return _ParentFilePreviewInfo(_ParentPreviewType.image, m.link_url);
        }
        if (m.mode == 4 && m.vtype == 'video') {
          return _ParentFilePreviewInfo(_ParentPreviewType.video, m.link_url);
        }
        if (m.mode == 4) {
          return _ParentFilePreviewInfo(_ParentPreviewType.image, m.link_url);
        }
        if (m.mode == 3) {
          return const _ParentFilePreviewInfo(_ParentPreviewType.ppt, null);
        }
        if (m.mode == 6) {
          return const _ParentFilePreviewInfo(_ParentPreviewType.report, null);
        }
      }
    } catch (_) {}

    // 普通附件
    final attach = messageModel.parent?.attachments?.first;
    if (attach != null) {
      switch (attach.fileType) {
        case AttachmentType.image:
          return _ParentFilePreviewInfo(
              _ParentPreviewType.image, attach.attachment_url);
        case AttachmentType.video:
          return _ParentFilePreviewInfo(
              _ParentPreviewType.video, attach.attachment_url);
        default:
          break;
      }
    }
    return const _ParentFilePreviewInfo(_ParentPreviewType.unknown, null);
  }

  Widget _buildParentFilePreviewWidget(
      _ParentFilePreviewInfo info, String title) {
    const double w = 53;
    const double h = 30;
    BorderRadius radius = BorderRadius.circular(4);
    switch (info.type) {
      case _ParentPreviewType.image:
        if ((info.url ?? '').isEmpty) {
          return _buildEmptyBox('assets/groupChat/reply_video_placeholder.png', w, h);
        }
        return ClipRRect(
          borderRadius: radius,
          child: CachedNetworkImage(
            imageUrl: info.url!,
            fit: BoxFit.cover,
            width: w,
            height: h,
            placeholder: (context, url) {
              return _buildEmptyBox('assets/groupChat/reply_image_placeholder.png', w, h);
            },
            errorWidget: (context, error, stackTrace) {
              return _buildEmptyBox('assets/groupChat/reply_image_placeholder.png', w, h);

            },
          ),
        );
      case _ParentPreviewType.video:
        // 异步获取视频第一帧图片
        if ((info.url ?? '').isEmpty) {
          return _buildEmptyBox('assets/groupChat/reply_video_placeholder.png', w, h);
        }
        return FutureBuilder<Uint8List?>(
          future: info.url == null
              ? null
              : VideoThumbnailGenerator.generateThumbnail(
                  videoUrl: info.url!,
                  maxWidth: w.toInt(),
                  maxHeight: h.toInt(),
                  quality: 75,
                ),
          builder: (context, snapshot) {
            Widget thumbWidget;
            if (snapshot.connectionState == ConnectionState.done &&
                snapshot.hasData &&
                snapshot.data != null) {
              thumbWidget = Image.memory(snapshot.data!,
                  width: w, height: h, fit: BoxFit.cover);
            } else {
              thumbWidget = _buildEmptyBox('assets/groupChat/reply_video_placeholder.png', w, h);
            }
            return Stack(
              alignment: Alignment.center,
              children: [
                ClipRRect(
                  borderRadius: radius,
                  child: thumbWidget,
                ),
                Image.asset(
                  'assets/images/ic_design_video.png',
                  width: 12,
                  height: 12,
                  fit: BoxFit.contain,
                )
              ],
            );
          },
        );
      case _ParentPreviewType.ppt:
        return _buildTagBox(title);
      case _ParentPreviewType.report:
        return _buildTagBox(title);
      case _ParentPreviewType.unknown:
        return _buildEmptyBox('assets/groupChat/reply_video_placeholder.png', w, h);
    }
  }

  Widget _buildEmptyBox(String url, double w, double h) {
    return Image.asset(
      url,
      fit: BoxFit.cover,
      width: w,
      height: h,
    );
  }

  Widget _buildTagBox(String text) {
    return Container(
      width: 53.w,
      height: 30.h,
      child: Column(
        children: [
          Container(
            width: 53.w,
            height: 23.5.h,
            decoration: BoxDecoration(
              color: Color(0xFFFF5555),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4.w),
                topRight: Radius.circular(4.w),
              ),
            ),
            child: Center(
              child: Text(
                text,
                style: TextStyle(
                    fontSize: 4.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white),
              ),
            ),
          ),
          Container(
            width: 53.w,
            height: 6.5.h,
            alignment: Alignment.centerRight,
            padding: EdgeInsets.only(right: 2.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(4.w),
                bottomRight: Radius.circular(4.w),
              ),
            ),
            child: Text(
              text,
              style: TextStyle(
                  fontSize: 3.sp,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF838384)),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildContainer(GroupChatMessageModel messageModel) {
    return Flexible(
      child: Column(
        crossAxisAlignment: crossAxisAlignment,
        children: [
          if (userId != messageModel.sender_user?.id) ...[
            Text(
              messageModel.sender_user?.username ?? "",
              style: TextStyle(fontSize: 14.sp, color: Color(0xFF988B9A)),
            )
          ],
          buildAttachmentsGrid(),
          buildContentText(),
          buildAgensResult(),
          buildAudioIcon(),
        ],
      ),
    );
  }

  Widget _buildAvatar(GroupChatMessageModel messageModel) {
    String avatarUrl = messageModel.sender_user?.avatar_url ?? "";
    bool isFromMe = model.getRoleEnum(userId) == GroupChatMessageRoleEnum.me;

    if (messageModel.sender_user?.id == userId) {
      avatarUrl =
          Get.find<StorageService>().getUserInfoData().avatarUrl?.value ??
              avatarUrl;
    }
    bool isSvg = avatarUrl.toLowerCase().endsWith(".svg");
    // 长按头像@
    // 仅在需要时响应长按，不增加额外状态，避免重建带来性能问题
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onLongPress: () {
        // agnes 模式下不触发长按 @
        if (agnes) return;
        if (!isFromMe) {
          final atId = messageModel.sender_user?.id;
          final atName = messageModel.sender_user?.username;
          if (atId != null && atName != null && atName.isNotEmpty) {
            // 直接调用输入框提供的静态方法插入 @mention
            MessageInputWidget.insertMentionExternal(atId, atName);
          }
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.sp),
        child: CachedNetworkImage(
          imageUrl: avatarUrl,
          width: 32.w,
          height: 32.h,
          fit: BoxFit.cover,
          imageBuilder: (context, imageProvider) {
            // 如果是 SVG，使用 SvgPicture.network，否则使用普通的 Image
            if (isSvg) {
              return SvgPicture.network(
                avatarUrl,
                width: 32.w,
                height: 32.h,
                fit: BoxFit.cover,
                placeholderBuilder: (context) => _buildDefaultAvatar(),
              );
            } else {
              return Image(
                image: imageProvider,
                width: 32.w,
                height: 32.h,
                fit: BoxFit.cover,
              );
            }
          },
          placeholder: (context, url) => _buildDefaultAvatar(),
          errorWidget: (context, url, error) {
            // 如果是 SVG 且 CachedNetworkImage 失败，尝试直接用 SvgPicture.network
            if (isSvg) {
              return SvgPicture.network(
                avatarUrl,
                width: 32.w,
                height: 32.h,
                fit: BoxFit.cover,
                placeholderBuilder: (context) => _buildDefaultAvatar(),
                errorBuilder: (context, error, stack) => _buildDefaultAvatar(),
              );
            }
            return _buildDefaultAvatar();
          },
        ),
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Image.asset(
      "assets/images/ic_group_agens_avatar.png",
      width: 32.w,
      height: 32.h,
      fit: BoxFit.cover,
    );
  }

  Widget buildMessageFailed() {
    if (model.sendingStatus != MessageStatus.SUCCESS) {
      return Center(
        child: Container(
          width: 12,
          height: 12,
          alignment: Alignment.center,
          margin: EdgeInsets.only(left: itemMargin - 12 - 2, right: 2),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFCB53BA)),
          ),
        ),
      );
    }
    return SizedBox(
      width: itemMargin,
    );
  }

  Widget buildContentText() {
    return SizedBox.shrink();
  }

  Widget buildAttachmentsGrid() {
    return SizedBox.shrink();
  }

  Widget buildAudioIcon() {
    return SizedBox.shrink();
  }

  Widget buildAgensResult() {
    return SizedBox.shrink();
  }
}
