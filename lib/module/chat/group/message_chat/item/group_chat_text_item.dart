import 'package:flutter/material.dart';
import 'package:new_agnes/module/chat/group/message_chat/mixin/group_chat_message_ui_mixin.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_base_user_info_item.dart';

///纯文本
class GroupChatTextItem extends GroupChatBaseUserInfoItem with GroupChatMessageUIMixin{
  GroupChatTextItem({required super.model, required super.userId});

  @override
  Widget buildContentText() {
    return buildCommonTextItem();
  }
}