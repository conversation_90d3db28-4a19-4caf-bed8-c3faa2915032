import 'dart:convert';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:new_agnes/module/chat/group/message_chat/enum/group_chat_message_enum.dart';

import '../../../../../generated/l10n.dart';

/**
 *
    {
    "attachments": [
    {
    "attachment_url": "https://example.com/image.jpg"
    }
    ],
    "content": "hello",
    "content_type": 3,
    "created_at": "2025-07-21T19:19:33.147163",
    "group_id": "group1",
    "id": "1753096773150-6d655fe4",
    "message_index": 99,
    "message_type": 2,
    "mention_user": {
    "id": "user_123",
    "username": "john_doe",
    "avatar_url": "https://example.com/avatar.jpg",
    "role_code": "slides_owner"
    },
    "parent": {
    "assistant_action_type": null,
    "attachments": [
    {
    "attachment_type": 1,
    "attachment_url": "https://example.com/image.jpg",
    "created_at": "2025-07-17T17:04:39",
    "file_name": null,
    "file_size": null,
    "id": "1752743080589-4647386d",
    "updated_at": "2025-07-17T17:04:39"
    }
    ],
    "content": "hello",
    "content_type": 3,
    "created_at": "2025-07-17T17:04:39",
    "group_id": "group1",
    "id": "1752743080589-df68a19c",
    "mention_user_id": null,
    "message_index": 20,
    "message_type": 1,
    "parent": {},
    "sender_user": {
    "avatar_url": null,
    "id": "32d7d00b-7d3d-4629-85fc-07ec22273a18",
    "role_code": "slides_editor",
    "username": "1083040740"
    },
    "third_message_id": "test_third_id",
    "updated_at": "2025-07-17T17:04:39"
    },
    "task_item": {
    "id": "1753096773150-6d655fe4",
    "title": "test_task_item",
    "content": "test_content",
    "created_at": "2025-07-21T19:19:33.147163",
    "updated_at": "2025-07-21T19:19:33.147163"
    },
    "sender_user": {
    "id": "50043f6d-d4e7-4cd2-a1a3-b88d2515f0f8"
    },
    "third_message_id": "test_third_id"
    }

 */

class GroupChatMessageModel {
  int? assistant_action_type;
  List<GroupChatAttachmentsModel>? attachments;
  String? messageContent;
  final int?
      create_join_type; // 1001 创建  1002 加入  群主：You created the group.   成员：You joined the group.
  int? content_type; // 1、文本 / JSON字符串 2、附件 3、文本附件混合
  final String? created_at;
  final String? group_id;
  String? id;
  final int? message_index;
  int? message_type; // 1、聊天 2、人员回复 3、系统消息 4、助手回复 5、@成员 6、@助手
  final GroupChatMentionUserModel? mention_user;
  GroupChatMentionUserModel? sender_user;
  GroupChatTaskItemModel? task_item;
  final String? lang;
  final String? third_message_id;
  final GroupChatMessageModel? parent;
  final String? third_chat_group_id;
  final String? messageCreateTime;
  final int? inputType; //0或者null是文本，1是语音输入
  MessageStatus? sendingStatus;
  final GroupChatPlayloadModel? payload;
  final GroupTranslationModel? translation;
  int? loading_status; // 1. 开始  2.结束
  final String? start_message_id;
  final String? timezone;

  GroupChatMessageProductStatusType productStatusType = GroupChatMessageProductStatusType.none;
  GroupChatMessageProductType productType = GroupChatMessageProductType.none;

  GroupChatMessageModel(
      {this.attachments,
      this.messageContent,
      this.create_join_type,
      this.content_type,
      this.created_at,
      this.group_id,
      this.id,
      this.message_index,
      this.message_type,
      this.mention_user,
      this.lang,
      this.sender_user,
      this.task_item,
      this.third_message_id,
      this.parent,
      this.assistant_action_type,
      this.third_chat_group_id,
      this.messageCreateTime,
      this.inputType,
      this.sendingStatus,
      this.payload,
      this.translation,
      this.loading_status,
      this.start_message_id,
      this.timezone});

  GroupChatMessageRoleEnum getRoleEnum(String userId) {
    if (userId == sender_user?.id) {
      return GroupChatMessageRoleEnum.me;
    } else if (sender_user?.id == "0") {
      return GroupChatMessageRoleEnum.agens;
    }
    return GroupChatMessageRoleEnum.others;
  }

  GroupChatMessageContentType get contentType {
    if (content_type == 1) {
      return GroupChatMessageContentType.text;
    } else if (content_type == 2) {
      return GroupChatMessageContentType.file;
    } else if (content_type == 3) {
      return GroupChatMessageContentType.textAndFile;
    }
    return GroupChatMessageContentType.text;
  }

  GroupChatMessageType get messageType {
    if (message_type == 1) {
      return GroupChatMessageType.chat;
    } else if (message_type == 2) {
      return GroupChatMessageType.reply;
    } else if (message_type == 3) {
      return GroupChatMessageType.system;
    } else if (message_type == 4) {
      return GroupChatMessageType.assistantReply;
    } else if (message_type == 5) {
      return GroupChatMessageType.atMember;
    } else if (message_type == 6) {
      return GroupChatMessageType.atAssistant;
    }
    return GroupChatMessageType.chat;
  }

  GroupAssistantActionType get assistantActionType {
    if (assistant_action_type == 1) {
      return GroupAssistantActionType.chat;
    } else if (assistant_action_type == 2) {
      return GroupAssistantActionType.summarizesComments;
    } else if (assistant_action_type == 3) {
      return GroupAssistantActionType.pendingTasks;
    } else if (assistant_action_type == 4) {
      return GroupAssistantActionType.task;
    } else if (assistant_action_type == 41) {
      return GroupAssistantActionType.confirmed;
    } else if (assistant_action_type == 42) {
      return GroupAssistantActionType.canceled;
    } else if (assistant_action_type == 5) {
      return GroupAssistantActionType.confirmExecution;
    } else if (assistant_action_type == 6) {
      return GroupAssistantActionType.cancelExecution;
    } else if (assistant_action_type == 7) {
      return GroupAssistantActionType.noPermission;
    } else if (assistant_action_type == 8) {
      return GroupAssistantActionType.generateThingEnd;
    } else if (assistant_action_type == 9) {
      return GroupAssistantActionType.generateThingStart;
    } else if (assistant_action_type == 11) {
      return GroupAssistantActionType.updateVip;
    } else if (assistant_action_type == 14) {
      return GroupAssistantActionType.generateThingError;
    } else if (assistant_action_type == 12) {
      return GroupAssistantActionType.viewPendingTasks;
    }
    return GroupAssistantActionType.unknown;
  }

  bool get isGenerateThing {
    return assistantActionType == GroupAssistantActionType.generateThingEnd ||
        assistantActionType == GroupAssistantActionType.generateThingStart ||
        assistantActionType == GroupAssistantActionType.generateThingError;
  }

  InputType get inputTypeValue {
    if (inputType == 1) {
      return InputType.audio;
    } else {
      return InputType.text;
    }
  }

  CreateJoinType get createJoinType {
    if (create_join_type == 1001) {
      return CreateJoinType.create;
    } else if (create_join_type == 1002) {
      return CreateJoinType.join;
    } else {
      return CreateJoinType.no;
    }
  }

  AgnesLoadingStatus get loadingStatus {
    if (loading_status == 1) {
      return AgnesLoadingStatus.start;
    } else if (loading_status == 2) {
      return AgnesLoadingStatus.end;
    }
    return AgnesLoadingStatus.noLoading;
  }

  String get content {
    if (Get.context == null) {
      return messageContent ?? "";
    }
    BuildContext context = Get.context!;
    if (translation != null) {
      try {
        String key = translation?.key ?? "";
        Map map = translation?.variables ?? {};
        switch (key) {
          case "task_completed_successfully":
            return S
                .of(context)
                .yourTitleTaskHasBeenSuccessfullyCompleted(map["title"]);
          case "voice_call_started":
            return S.of(context).xxxHasStartedAVoiceCall(map["username"]);
          case "voice_call_ended":
            return S.of(context).voiceCallEnded;
          case "research_task_limit_reached":
            return S
                .of(context)
                .yourRequestForTheResearchTaskLimitHasBeenReached;
          case "slides_task_limit_reached":
            return S.of(context).yourRequestForTheSlideTaskLimitHasBeenReached;
          case "no_permission_message":
            return S
                .of(context)
                .okayINoticedYouDontHaveTheRequiredPermissionsPleaseAskTheOwnerOrAnEditorToDoIt;
          case "group_rename":
            return S.of(context).xxxChangedTheGroupName(map["username"]);
          case "task_execution_failed":
            return S
                .of(context)
                .sorryYourTitleTaskFailedPleaseTryAgainLater(map["title"]);
          case "view_pending_tasks":
            return S
                .of(context)
                .thereAreCurrentlyTotalPendingTasksTask(map["total"]);
          case "delete_pending_tasks":
            return S
                .of(context)
                .tasksDeletedAsRequestedCurrentPendingTasksRemainingcountWouldYouLikeMeToShowAllPendingTasks(
                    map["remaining_count"]);
          case "createdGroup":
            return S.of(context).youCreatedTheGroup;
          case "joinedGroup":
            return S.of(context).youJoinedTheGroup;
          case "pending_tasks_empty":
            return S.of(context).noPendingTasksFoundAllTasksHaveBeenCompleted;
          case "task_execution_limit":
            String limitContent = "\n";
            if (payload != null && payload!.task_items != null) {
              for (int i = 0; i < payload!.task_items!.length; i++) {
                GroupTaskItemsModel taskItem = payload!.task_items![i];
                limitContent +=
                    "${taskItem.item_index}.${taskItem.title}(${S.of(context).cost}:${taskItem.plan_count})";
                if (i != payload!.task_items!.length - 1) {
                  limitContent += "\n";
                }
              }
            }
            return S
                    .of(context)
                    .userUsernameHasInsufficientQuotaSlidesRemainingSlidesremainingcountSlidesRequiredQuotaSlidesplancountResearchRemainingResearchremainingcountResearchRequiredQuotaResearchplancountYouCanChooseTasksWithEnoughQuotaToExecute(
                      map['user_name'],
                      map['slides_remaining_count'],
                      map['slides_plan_count'],
                      map['research_remaining_count'],
                      map['research_plan_count'],
                    ) +
                limitContent;
          default:
            return messageContent ?? "";
        }
      } catch (e) {
        return messageContent ?? "";
      }
    }
    return messageContent ?? "";
  }

  factory GroupChatMessageModel.fromJson(Map<String, dynamic> json) {
    String? _content = "";
    if (json["content"] is String) {
      _content = json["content"];
    } else if (json["content"] is Map) {
      _content = jsonEncode(json["content"]);
    } else {
      _content = json["content"].toString();
    }
    return GroupChatMessageModel(
      attachments: json['attachments'] != null
          ? (json['attachments'] as List)
              .map((i) => GroupChatAttachmentsModel.fromJson(i))
              .toList()
          : null,
      messageContent: _content,
      create_join_type: json['create_join_type'],
      content_type: json['content_type'],
      created_at: json['created_at'],
      group_id: json['group_id'],
      id: json['id'],
      message_index: json['message_index'],
      message_type: json['message_type'],
      mention_user: json['mention_user'] != null
          ? GroupChatMentionUserModel.fromJson(json['mention_user'])
          : null,
      sender_user: json['sender_user'] != null
          ? GroupChatMentionUserModel.fromJson(json['sender_user'])
          : null,
      task_item: json['task_item'] != null
          ? GroupChatTaskItemModel.fromJson(json['task_item'])
          : null,
      third_message_id: json['third_message_id'],
      parent: json['parent'] != null
          ? GroupChatMessageModel.fromJson(json['parent'])
          : null,
      assistant_action_type: json['assistant_action_type'],
      lang: json['lang'],
      third_chat_group_id: json['third_chat_group_id'],
      messageCreateTime: json['messageCreateTime'] is int
          ? json['messageCreateTime'].toString()
          : json['messageCreateTime'],
      inputType: json['inputType'],
      payload: json['payload'] != null
          ? GroupChatPlayloadModel.fromJson(json['payload'])
          : null,
      translation: json['translation'] != null
          ? GroupTranslationModel.fromJson(json['translation'])
          : null,
      loading_status: json['loading_status'] ?? 0,
      start_message_id: json['start_message_id'],
      timezone: json['timezone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attachments':
          attachments?.map((attachment) => attachment.toJson()).toList(),
      'content': messageContent,
      'create_join_type': create_join_type,
      'content_type': content_type,
      'created_at': created_at,
      'group_id': group_id,
      'id': id,
      'lang': lang,
      'message_index': message_index,
      'message_type': message_type,
      'mention_user': mention_user?.toJson(),
      'sender_user': sender_user?.toJson(),
      "task_item": task_item?.toJson(),
      'third_message_id': third_message_id,
      'parent': parent?.toJson(),
      'assistant_action_type': assistant_action_type,
      'third_chat_group_id': third_chat_group_id,
      'messageCreateTime': messageCreateTime,
      'inputType': inputType,
    };
  }
}

class GroupChatAttachmentsModel {
  final String? attachment_url;
  final int? attachment_type;
  final String? created_at;
  final dynamic file_name;
  final dynamic file_size;
  final String? id;
  final String? updated_at;
  final int? duration_in_seconds;
  final String? title;
  final String? productMode;

  GroupChatAttachmentsModel(
      {this.attachment_url,
      this.attachment_type,
      this.created_at,
      this.file_name,
      this.file_size,
      this.id,
      this.updated_at,
      this.duration_in_seconds,
      this.title,
      this.productMode});

  AttachmentType get fileType {
    switch (attachment_type) {
      case 1:
        return AttachmentType.image;
      case 2:
        return AttachmentType.audio;
      case 3:
        return AttachmentType.file;
      case 101:
        return AttachmentType.video;
      default:
        return AttachmentType.unknown;
    }
  }

  factory GroupChatAttachmentsModel.fromJson(Map<String, dynamic> json) {
    return GroupChatAttachmentsModel(
      attachment_url: json['attachment_url'],
      attachment_type: json['attachment_type'],
      created_at: json['created_at'],
      file_name: json['file_name'],
      file_size: json['file_size'],
      id: json['id'],
      updated_at: json['updated_at'],
      duration_in_seconds: json['duration_in_seconds'],
      title: json['title'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['attachment_url'] = this.attachment_url;
    data['attachment_type'] = this.attachment_type;
    data['created_at'] = this.created_at;
    data['file_name'] = this.file_name;
    data['file_size'] = this.file_size;
    data['id'] = this.id;
    data['updated_at'] = this.updated_at;
    data['duration_in_seconds'] = this.duration_in_seconds;
    data['title'] = this.title;
    return data;
  }
}

class GroupChatMentionUserModel {
  final String? id;
  final String? username;
  final String? avatar_url;
  final String? role_code;

  GroupChatMentionUserModel(
      {this.id, this.username, this.avatar_url, this.role_code});

  factory GroupChatMentionUserModel.fromJson(Map<String, dynamic> json) {
    return GroupChatMentionUserModel(
      id: json['id'],
      username: json['username'],
      avatar_url: json['avatar_url'],
      role_code: json['role_code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'avatar_url': avatar_url,
      'role_code': role_code,
    };
  }
}

class GroupChatTaskItemModel {
  final String? id;
  final String? group_id;
  final String? user_id;
  final String? task_id;
  final String? message_id;
  final String? title;
  final String? content;
  final String? created_at;
  final String? updated_at;

  GroupChatTaskItemModel({
    this.id,
    this.group_id,
    this.user_id,
    this.task_id,
    this.message_id,
    this.title,
    this.content,
    this.created_at,
    this.updated_at,
  });

  factory GroupChatTaskItemModel.fromJson(Map<String, dynamic> json) {
    return GroupChatTaskItemModel(
      id: json['id'],
      group_id: json['group_id'],
      user_id: json['user_id'],
      task_id: json['task_id'],
      message_id: json['message_id'],
      title: json['title'],
      content: json['content'],
      created_at: json['created_at'],
      updated_at: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['group_id'] = this.group_id;
    data['user_id'] = this.user_id;
    data['task_id'] = this.task_id;
    data['message_id'] = this.message_id;
    data['title'] = this.title;
    data['content'] = this.content;
    data['created_at'] = this.created_at;
    data['updated_at'] = this.updated_at;
    return data;
  }
}

class GroupCreateMessageModel {
  final String? conversation_id;
  final int? mode;
  final String? link_content;
  final String? link_url;
  final String? vtype;
  final String? title;
  final dynamic dimensions;

  GroupCreateMessageModel(
      {this.conversation_id, this.mode, this.link_content, this.link_url,this.vtype,this.title,this.dimensions});

  int get type{
    if(vtype == AttachmentType.image.name){
      return AttachmentType.image.value;
    }else if(vtype == AttachmentType.video.name){
      return AttachmentType.video.value;
    }else if(vtype == AttachmentType.audio.name){
      return AttachmentType.audio.value;
    }else if(vtype == AttachmentType.file.name){
      return AttachmentType.file.value;
    }else {
      return AttachmentType.unknown.value;
    }
  }

  factory GroupCreateMessageModel.fromJson(Map<String, dynamic> json) {
    return GroupCreateMessageModel(
      conversation_id: json['conversation_id'],
      mode: json['mode'],
      link_content: json['link_content'],
      link_url: json['link_url'],
      vtype: json['vtype'],
      title: json['title'],
      dimensions: json['dimensions'],
    );
  }
}

class GroupChatPlayloadModel {
  final String? task_item_id;
  final int? task_item_index;
  final String? title;
  final String? content;
  final String? start_message_id;
  final String? start_third_message_id;
  List<GroupWebLinkModel>? web_links;
  List<GroupTaskItemsModel>? task_items;

  GroupChatPlayloadModel(
      {this.task_item_id,
      this.task_item_index,
      this.title,
      this.content,
      this.start_message_id,
      this.start_third_message_id,
      this.web_links,
      this.task_items});

  factory GroupChatPlayloadModel.fromJson(Map<String, dynamic> json) {
    return GroupChatPlayloadModel(
      task_item_id: json['task_item_id'],
      task_item_index: json['task_item_index'],
      title: json['title'],
      content: json['content'],
      start_message_id: json['start_message_id'],
      start_third_message_id: json['start_third_message_id'],
      web_links: json['web_links'] != null
          ? (json['web_links'] as List)
              .map((i) => GroupWebLinkModel.fromJson(i))
              .toList()
          : null,
      task_items: json['task_items'] != null
          ? (json['task_items'] as List)
              .map((i) => GroupTaskItemsModel.fromJson(i))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'task_item_id': task_item_id,
      'task_item_index': task_item_index,
      'title': title,
      'content': content,
    };
  }
}

class GroupTranslationModel {
  final String? key;
  dynamic variables;

  GroupTranslationModel({this.key, this.variables});

  factory GroupTranslationModel.fromJson(Map<String, dynamic> json) {
    return GroupTranslationModel(
      key: json['key'],
      variables: json['variables'],
    );
  }
}

class GroupWebLinkModel {
  final String? title;
  final String? url;

  GroupWebLinkModel({this.title, this.url});

  factory GroupWebLinkModel.fromJson(Map<String, dynamic> json) {
    return GroupWebLinkModel(
      title: json['title'],
      url: json['url'],
    );
  }
}

class GroupTaskItemsModel {
  final String? id;
  final String? task_id;
  final int? item_index;
  final String? title;
  final String? content;
  final int? status;
  final dynamic remark;
  final int? plan_count;

  GroupTaskItemsModel({
    this.id,
    this.task_id,
    this.item_index,
    this.title,
    this.content,
    this.status,
    this.remark,
    this.plan_count,
  });

  factory GroupTaskItemsModel.fromJson(Map<String, dynamic> json) {
    return GroupTaskItemsModel(
      id: json['id'],
      task_id: json['task_id'],
      item_index: json['item_index'],
      title: json['title'],
      content: json['content'],
      status: json['status'],
      remark: json['remark'],
      plan_count: json['plan_count'],
    );
  }
}
