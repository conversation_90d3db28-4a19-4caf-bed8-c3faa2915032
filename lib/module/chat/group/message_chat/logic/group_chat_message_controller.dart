import 'dart:convert';
import 'dart:io';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mime/mime.dart';
import 'package:new_agnes/api/Api.dart';
import 'package:new_agnes/api/ApiProvider.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_logic.dart';
import 'package:new_agnes/utils/event_bus.dart';
import 'package:new_agnes/utils/image_upload_util.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/message_input_widget.dart';
import 'package:path/path.dart' as path;
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:scrollview_observer/scrollview_observer.dart';

import '../../../../../api/StorageService.dart';
import '../../../../../generated/l10n.dart';
import '../../../../../utils/loading_util.dart';
import '../../../../mine/model/UserInfoModel.dart';
import '../../../model/grouplist/GroupUserBean.dart';
import '../../group_chat/agora_logic.dart';
import '../enum/group_chat_message_enum.dart';
import '../model/group_chat_message_model.dart';

class GroupChatMessageController extends GetxController {
  final RxList<GroupChatMessageModel> messageData =
      <GroupChatMessageModel>[].obs;
  final List<ChatMessage> retryQueue = [];

  String userId = "";
  int? messageIndex; //请求数据的Index
  String channelId = '';
  final RxList<MessageUploadModel> uploadFiles = <MessageUploadModel>[].obs;
  final List<GroupUserBean> groupUsers = <GroupUserBean>[];
  final pageSize = 50;
  String? cursor;
  final rtcLogic =
      Get.isRegistered<RtcLogic>() ? Get.find<RtcLogic>() : Get.put(RtcLogic());

  // 图片上传工具
  final ImageUploadUtil _uploadUtil = ImageUploadUtil.instance;
  var quoteMessage = Rxn<GroupChatMessageModel>();

  bool noMoreData = false;
  bool isLoadingHistory = false;
  final showScrollBottom = false.obs;
  List<GroupChatMessageModel> tempData = [];
  final isInterception = false.obs;

  final Map<String, GroupAssistantActionType> productMap = {};
  final Map<String, GroupChatMessageModel> loadingMap =
      {}; //把loading消息存下来，不往数据里面存。key是id
  late ChatScrollObserver scrollObserver;

  late ListObserverController listObserverController;
  late ScrollController scrollController;
  late ChatConversationType type;
  late String thirdId;

  late double maxWidth;
  late double screenHeight;

  bool filterEvent(String? event) {
    if (event != "group_chat" &&
        event != "loading" &&
        event != "group_custom_noti") {
      return true;
    }
    return false;
  }

  bool filterMessage(Map<dynamic, dynamic> params) {
    if (params["app_data"] == null &&
        params["app_customexts"] == null &&
        params["group_custom_noti"] == null) {
      return true;
    }
    return false;
  }

  void recallMessage(String messageId,BuildContext context,String curId) async {
    int index = messageData.indexWhere((t)=> t.id == messageId);
    int curIndex = messageData.indexWhere((t)=> t.id == curId);
    if(index < 0){
      if(noMoreData){
        showFailToast(S.of(context).quotedMessageHasExpired);
        LoadingUtil.dismiss();
        return;
      }
      LoadingUtil.show();
      getHistoryMessage(thirdId, type,successCallback: (){
        recallMessage(messageId,context,curId);
      });
    } else {
      double height = index * average();
      if((height > screenHeight * 3) && (curIndex - index).abs() > 50) {
        scrollController.jumpTo(height);
      }
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        int timeStart = DateTime.now().millisecondsSinceEpoch;
        logError("开始时间${timeStart}");
        listObserverController.animateTo(index: index,duration: Duration(milliseconds: 100),curve: Curves.easeIn).then((v){
          eventBus.fire<Map<String,dynamic>>({"key":"recall_message","messageId":messageId});
          logError("运行时间${DateTime.now().millisecondsSinceEpoch - timeStart}");
        });
        LoadingUtil.dismiss();
      });
    }
  }

  double average() {
    double t156 = messageData.where((t) => t.message_type == 1 || t.message_type == 5 || t.message_type == 6).length * 95;
    double t24 = messageData.where((t) => t.message_type == 2 || t.message_type == 4).length * 110;
    double t3 = messageData.where((t) => t.message_type == 3).length * 54;
    return (t156 + t24 + t3) / messageData.length;
  }

  void getHistoryMessage(String thirdId, ChatConversationType type,
      {RefreshController? refreshController,
      VoidCallback? successCallback}) async {
    isLoadingHistory = true;
    // LoadingUtil.show();
    userId = Get.find<StorageService>().getUserInfoData().id ?? "";
    ChatCursorResult<ChatMessage>? result = await Get.find<AgoraLogic>()
        .fetchHistoryMessages(
            targetId: thirdId,
            chatType: type,
            cursor: cursor,
            pageSize: pageSize);
    List<ChatMessage> messages = result?.data ?? [];
    // LoadingUtil.dismiss();
    noMoreData = result?.cursor == null ||
        result?.cursor == "undefined" ||
        messages.length < pageSize;
    cursor = result?.cursor;
    isLoadingHistory = false;
    List<GroupChatMessageModel> models = messages
        .map((item) {
          item.hasRead = true;
          Map<String, dynamic> body = item.body.toJson();
          if (body.isEmpty) {
            return null;
          }
          if (filterEvent(body["event"])) {
            return null;
          }
          Map<dynamic, dynamic>? params = body["params"];
          if (params == null || params.isEmpty) {
            return null;
          }
          if (filterMessage(params)) {
            return null;
          }
          String paramsMap = (params["app_data"] ?? params["app_customexts"]) ??
              params["group_custom_noti"];

          GroupChatMessageModel model =
              GroupChatMessageModel.fromJson(jsonDecode(paramsMap));
          model.sendingStatus = item.status;
          if(model.sendingStatus != MessageStatus.SUCCESS){
            retryQueue.add(item);
          }
          if (body["event"] == "group_custom_noti") {
            model.message_type = 3;
          }
          if (_handleLoadingModel(model)) {
            return null;
          }
          _handleProductHistoryMessage(model);
          return model;
        })
        .whereType<GroupChatMessageModel>()
        .toList();
    _handleMoveLoadingToMessage(models);
    messageData.addAll(models);
    successCallback?.call();
    retrySendMessage(thirdId);
  }

  void getGroupMembers(String groupId) async {
    groupUsers.clear();
    Response res = await Get.find<ApiProvider>()
        .get("${Api.groupUserList}?group_id=$groupId");
    if (res.statusCode == 200) {
      groupUsers.addAll((res.body['users'] as List)
          .map((e) => GroupUserBean.fromJson(e))
          .toList());
    } else {
      // showFailToast(res.statusText ??
      //     S.of(Get.context!).networkConnectionFailedPleaseTryAgain);
    }
  }

  void appendMessage(ChatMessage item, String curThirdId) {
    if (item.to != curThirdId) {
      return;
    }
    Map<String, dynamic> body = item.body.toJson();
    if (body.isEmpty) {
      return;
    }
    if (filterEvent(body["event"])) {
      return null;
    }
    Map<dynamic, dynamic>? params = body["params"];
    if (params == null || params.isEmpty) {
      return;
    }
    if (filterMessage(params)) {
      return;
    }
    String paramsMap = (params["app_data"] ?? params["app_customexts"]) ??
        params["group_custom_noti"];
    var model = GroupChatMessageModel.fromJson(jsonDecode(paramsMap));
    model.sendingStatus = item.status;
    if(model.sendingStatus != MessageStatus.SUCCESS){
      retryQueue.add(item);
    }
    if (body["event"] == "group_custom_noti") {
      model.message_type = 3;
    }
    //修改生成物的消息
    _handleProductModel(model);
    //如果是自己发的，并且messageCreateTime不为空且一样的情况下，说明上条消息跟这条消息是一样的，更新一下数据否则添加数据
    if (model.sender_user?.id == userId) {
      int index = -1;
      for (int i = 0; i < messageData.length; i++) {
        GroupChatMessageModel modelData = messageData[i];
        if (modelData.sender_user?.id == userId &&
            modelData.messageCreateTime != null &&
            modelData.messageCreateTime == model.messageCreateTime) {
          index = i;
          break;
        }
      }
      if(index != -1){
        messageData[index] = model;
        return;
      }
      isInterceptionData(model);
    } else {
      if (_handleLoadingModel(model)) {
        _handleMoveLoadingToMessage(messageData);
        return;
      }
     isInterceptionData(model);
    }
  }

  // 长按时数据暂时保存
  void isInterceptionData(GroupChatMessageModel model){
    if (isInterception.value) {
      tempData.add(model);
    } else {
      scrollObserver.standby();
      messageData.insert(0, model);
    }
  }

  void refreshData(){
    if (tempData.isNotEmpty) {
      for (var model in tempData) {
        scrollObserver.standby();
        messageData.insert(0, model);
      }
      tempData.clear();
    }
  }

  void onAddImage(List<File> files) async {
    for (var file in files) {
      String fileName = path.basename(file.path);
      final mimeType = lookupMimeType(fileName) ?? 'image/jpeg';
      uploadFiles.add(MessageUploadModel(
          file_title: fileName,
          mime_type: mimeType,
          localFilePath: file.path,
          uploadStatus: UploadStatus.loading));
    }

    // 立即开始上传所有图片
    _startUploadingFiles();
  }

  /// 开始上传所有文件 - 使用新的工具类
  void _startUploadingFiles() async {
    if (uploadFiles.isEmpty) return;

    // 准备文件列表
    List<File> files = uploadFiles
        .where((file) => file.localFilePath != null)
        .map((file) => File(file.localFilePath!))
        .toList();

    // 使用工具类批量上传
    _uploadUtil.uploadMultipleFiles(
      files: files,
      uploadMode: UploadMode.sequential,
      onSingleSuccess: (index, fileUrl, fileName, size, content) {
        if (index < uploadFiles.length) {
          uploadFiles[index] = uploadFiles[index].copyWith(
            file_url: fileUrl,
            uploadStatus: UploadStatus.success,
          );
          uploadFiles.refresh();
        }
      },
      onSingleError: (index, fileName, error) {
        if (index < uploadFiles.length) {
          uploadFiles[index] =
              uploadFiles[index].copyWith(uploadStatus: UploadStatus.failed);
          uploadFiles.refresh();
          debugPrint("Upload failed: $error");
        }
      },
    );
  }

  /// 重试上传指定索引的文件
  void retryUploadFile(int index) {
    if (index >= uploadFiles.length) return;

    MessageUploadModel file = uploadFiles[index];
    if (file.localFilePath == null || file.uploadStatus == UploadStatus.success)
      return;

    // 设置为loading状态
    uploadFiles[index] = file.copyWith(uploadStatus: UploadStatus.loading);
    uploadFiles.refresh();

    // 使用工具类重试上传
    _uploadUtil.uploadSingleFile(
      filePath: file.localFilePath!,
      onSuccess: (fileUrl, fileName, size, content) {
        if (index < uploadFiles.length) {
          uploadFiles[index] = uploadFiles[index].copyWith(
            file_url: fileUrl,
            uploadStatus: UploadStatus.success,
          );
          uploadFiles.refresh();
        }
      },
      onError: (error) {
        if (index < uploadFiles.length) {
          uploadFiles[index] =
              uploadFiles[index].copyWith(uploadStatus: UploadStatus.failed);
          uploadFiles.refresh();
          debugPrint("Upload retry failed: $error");
        }
      },
    );
  }

  Future<List<MessageUploadModel>> onUploadFile() async {
    if (uploadFiles.isEmpty) {
      return [];
    }

    // 只返回已成功上传的文件
    List<MessageUploadModel> successFiles = uploadFiles
        .where((file) => file.uploadStatus == UploadStatus.success)
        .toList();

    // 清空已处理的文件
    uploadFiles.clear();

    return successFiles;
  }

  void sendMessage(String thirdId, List<MessageUploadModel> uploadModels,
      String content, MessageAtModel atModel) {
    if (uploadModels.isEmpty && content.isEmpty) {
      return;
    }
    //生成上传文件模型,暂时只有图片
    List<GroupChatAttachmentsModel> attachments = uploadModels
        .map((e) => GroupChatAttachmentsModel(
              attachment_type: AttachmentType.image.value,
              attachment_url: e.file_url,
              file_name: e.file_title,
            ))
        .toList();
    //获取内容类型
    GroupChatMessageContentType contentType = GroupChatMessageContentType.text;
    if (uploadModels.isNotEmpty && content.isNotEmpty) {
      contentType = GroupChatMessageContentType.textAndFile;
    } else if (uploadModels.isNotEmpty) {
      contentType = GroupChatMessageContentType.file;
    }
    //构建临时消息
    UserInfoModel userInfoModel = Get.find<StorageService>().getUserInfoData();
    String messageCreateTime = DateTime.now().millisecondsSinceEpoch.toString();
    int messageType;
    GroupChatMessageModel groupchatmessagemodel = GroupChatMessageModel();
    if (atModel.atId == "0") {
      messageType = 6;
    } else if (atModel.atId != null) {
      messageType = 5;
    } else {
      messageType = 1;
    }
    if (quoteMessage.value != null) {
      messageType = 2;
      groupchatmessagemodel.id = quoteMessage.value?.id;
      groupchatmessagemodel.messageContent = quoteMessage.value?.messageContent;
      groupchatmessagemodel.sender_user = quoteMessage.value?.sender_user;
      groupchatmessagemodel.attachments = quoteMessage.value?.attachments;
      groupchatmessagemodel.assistant_action_type = quoteMessage.value?.assistant_action_type;
      groupchatmessagemodel.content_type = quoteMessage.value?.content_type;

    }

    GroupChatMessageModel tempMessage = GroupChatMessageModel(
        third_chat_group_id: thirdId,
        lang: ApiProvider.getLanguageCode(),
        sender_user: GroupChatMentionUserModel(
            id: userInfoModel.id,
            username: userInfoModel.username?.value,
            avatar_url: userInfoModel.avatarUrl?.value),
        messageCreateTime: messageCreateTime,
        messageContent: content,
        content_type: contentType.value,
        message_type: messageType,
        attachments: attachments,
        group_id:
            quoteMessage.value != null ? quoteMessage.value?.group_id : null,
        parent: quoteMessage.value == null ? null : groupchatmessagemodel,
        sendingStatus: MessageStatus.CREATE,
        mention_user: (atModel.atId?.isNotEmpty == true)
            ? GroupChatMentionUserModel(
                id: atModel.atId, username: atModel.atName)
            : null);

    //把临时消息 添加进去不走推送，这样不会有延时，等成功后更新一下数据
    //发送消息
    Get.find<AgoraLogic>().sendTextMessageMap(
        thirdId, ChatType.GroupChat, tempMessage.toJson(), success: () {
      tempMessage.sendingStatus = MessageStatus.SUCCESS;
      messageData.insert(0, tempMessage);
      messageData.refresh();
      quoteMessage.value = null;
    }, fail: (error) {
      tempMessage.sendingStatus = MessageStatus.FAIL;
      showFailToast(error);
    });
  }

  void retrySendMessage(String groupId){
    if(retryQueue.isEmpty){
      return;
    }
    bool isRegister = Get.isRegistered<AgoraLogic>();
    if (isRegister) {
      AgoraLogic agoraLogic = Get.find<AgoraLogic>();
      for (var item in retryQueue) {
        agoraLogic.resendMessage(item);
      }
      retryQueue.clear();
    }
  }

  void _handleProductHistoryMessage(GroupChatMessageModel model) {
    try {
      if (model.isGenerateThing) {
        GroupCreateMessageModel productModel = GroupCreateMessageModel.fromJson(
            jsonDecode(model.messageContent ?? "{}"));
        if (model.assistantActionType ==
            GroupAssistantActionType.generateThingEnd) {
          if(model.contentType == GroupChatMessageContentType.text){
            model.productType = GroupChatMessageProductType.none;
          }else {
            model.productType = GroupChatMessageProductType.agnesTextAndFile;
          }
          model.attachments ??= [];
          model.attachments!.add(GroupChatAttachmentsModel(
              id: productModel.conversation_id,
              attachment_url: productModel.link_url,
              attachment_type: productModel.type,
              productMode: productModel.mode?.toString(),
              title: model.payload?.title));
          productMap[model.payload?.start_message_id ?? ""] =
              GroupAssistantActionType.generateThingEnd;
        } else if (model.assistantActionType ==
            GroupAssistantActionType.generateThingError) {
          productMap[model.payload?.start_message_id ?? ""] =
              GroupAssistantActionType.generateThingError;
        } else if (model.assistantActionType ==
            GroupAssistantActionType.generateThingStart) {
          model.productStatusType = GroupChatMessageProductStatusType.start;
          if (productMap.containsKey(model.id ?? "")) {
            if (productMap[model.id] ==
                GroupAssistantActionType.generateThingError) {
              model.productStatusType =
                  GroupChatMessageProductStatusType.error;
            } else {
              model.productStatusType =
                  GroupChatMessageProductStatusType.end;
            }
            productMap.remove(model.payload?.start_message_id ?? "");
          }
        }
      } else if (model.assistantActionType ==
          GroupAssistantActionType.updateVip) {
        productMap[model.payload?.start_message_id ?? ""] =
            GroupAssistantActionType.updateVip;
      }
    } catch (e) {}
  }

  void _handleProductModel(GroupChatMessageModel model) {
    try {
      if (model.assistantActionType == GroupAssistantActionType.updateVip) {
        productMap[model.payload?.start_message_id ?? ""] =
            GroupAssistantActionType.updateVip;
      } else if (model.assistantActionType ==
          GroupAssistantActionType.generateThingEnd) {
        GroupCreateMessageModel productModel = GroupCreateMessageModel.fromJson(
            jsonDecode(model.messageContent ?? "{}"));
        if(model.contentType == GroupChatMessageContentType.text){
          model.productType = GroupChatMessageProductType.none;
        }else {
          model.productType = GroupChatMessageProductType.agnesTextAndFile;
        }
        model.attachments ??= [];
        model.attachments!.add(GroupChatAttachmentsModel(
            id: productModel.conversation_id,
            attachment_url: productModel.link_url,
            attachment_type: productModel.type,
            productMode: productModel.mode?.toString(),
            title: model.payload?.title));
        productMap[model.payload?.start_message_id ?? ""] =
            GroupAssistantActionType.generateThingEnd;
      } else if (model.assistantActionType ==
          GroupAssistantActionType.generateThingError) {
        GroupCreateMessageModel productModel = GroupCreateMessageModel.fromJson(
            jsonDecode(model.messageContent ?? "{}"));
        productMap[model.payload?.start_message_id ?? ""] =
            GroupAssistantActionType.generateThingError;
      } else if(model.assistantActionType == GroupAssistantActionType.generateThingStart){
        model.productStatusType = GroupChatMessageProductStatusType.start;
      }
      if (productMap.isNotEmpty) {
        for (var historyModel in messageData) {
          if (productMap.isEmpty) {
            break;
          }
          if (historyModel.assistantActionType !=
              GroupAssistantActionType.generateThingStart) {
            continue;
          }
          if (productMap.containsKey(historyModel.id ?? "")) {
            if (productMap[historyModel.id ?? ""] ==
                GroupAssistantActionType.generateThingError) {
              historyModel.productStatusType =
                  GroupChatMessageProductStatusType.error;
            } else {
              historyModel.productStatusType =
                  GroupChatMessageProductStatusType.end;
            }
            productMap.remove(historyModel.id ?? "");
            messageData.refresh();
          }
        }
      }
    } catch (e) {}
  }

  bool _handleLoadingModel(GroupChatMessageModel model) {
    if (model.loadingStatus == AgnesLoadingStatus.start ||
        model.loadingStatus == AgnesLoadingStatus.end) {
      if (model.loadingStatus == AgnesLoadingStatus.end) {
        loadingMap[model.start_message_id ?? ""] = model;
      } else {
        if (loadingMap.containsKey(model.start_message_id ?? "")) {
          return true;
        }
        loadingMap[model.start_message_id ?? ""] = model;
      }
      return true;
    }
    return false;
  }

  void _handleMoveLoadingToMessage(List<GroupChatMessageModel> models) {
    if (loadingMap.isEmpty) {
      return;
    }
    for (int i = 0; i < models.length; i++) {
      // 检查是否有对应的loading消息需要插入
      String loadingId = models[i].id ?? "";
      if (loadingMap.containsKey(loadingId)) {
        models[i].loading_status = loadingMap[loadingId]!.loadingStatus.value;
        loadingMap.remove(loadingId);
      }
    }
  }

  @override
  void dispose() {
    // 清理上传工具资源
    _uploadUtil.dispose();

    noMoreData = false;
    cursor = null;
    isLoadingHistory = false;
    productMap.clear();
    loadingMap.clear();
    super.dispose();
  }
}
