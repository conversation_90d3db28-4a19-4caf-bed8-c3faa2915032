import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:markdown_widget/markdown_widget.dart';
import 'package:new_agnes/api/stream/stream_request.dart';
import 'package:new_agnes/module/chat/group/message_chat/model/group_chat_message_model.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/CustomListView.dart';

import '../../../../../api/Api.dart';
import '../../../../../api/ApiProvider.dart';
import '../../../../../api/history/historyMessage.dart';
import '../../../../../api/stream/render/HistoryHandler.dart';
import '../../../../../api/stream/render/StreamEventHandler.dart';
import '../../../../../api/stream/render/entity.dart';
import '../../../../../api/stream/stream_message_type_enum.dart';
import '../../../../../api/stream/stream_result_entity.dart';
import '../../../../../data/caseAndQuestion.dart';
import '../../../../../generated/l10n.dart';
import '../../../../../widget/ContainerBox.dart';
import '../../../model/history_deep_research_model.dart';
import '../../../model/tool_call_model.dart';
import '../../../widget/deep_research_lay.dart';

/// 产品创建回调类型定义
typedef ProductCreateCallBack = Function();

/// 产品创建模态底部弹窗工具类
class ProductCreateModalUtils {
  /// 显示产品创建弹窗
  static Future<void> showProductCreateModal({
    required BuildContext context,
    ProductCreateCallBack? onProductCreated,
    required GroupCreateMessageModel groupCreateMessageModel,
    required bool isEnd,
    required SearchType searchType,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ProductCreateModal(
        onProductCreated: onProductCreated,
        groupCreateMessageModel: groupCreateMessageModel,
        isEnd: isEnd,
        searchType: searchType,
      ),
    );
  }
}

/// 产品创建模态弹窗组件
class ProductCreateModal extends StatefulWidget {
  final ProductCreateCallBack? onProductCreated;
  final GroupCreateMessageModel groupCreateMessageModel;
  final bool isEnd;
  final SearchType searchType;

  const ProductCreateModal(
      {Key? key,
      this.onProductCreated,
      required this.groupCreateMessageModel,
      required this.isEnd,
      required this.searchType})
      : super(key: key);

  @override
  State<ProductCreateModal> createState() => _ProductCreateModalState();
}

class _ProductCreateModalState extends State<ProductCreateModal> {
  final controller = Get.put(ProductCreateController());

  @override
  void initState() {
    super.initState();
    controller.onInitHandler(widget.searchType);
    if (widget.isEnd) {
      controller.onEndStream(widget.groupCreateMessageModel, widget.searchType);
    } else {
      controller.onResumeStream(
          widget.groupCreateMessageModel, widget.searchType);
    }
  }

  @override
  void didUpdateWidget(covariant ProductCreateModal oldWidget) {
    super.didUpdateWidget(oldWidget);
    controller.onInitHandler(widget.searchType);
  }

  @override
  void dispose() {
    controller.onReset();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.5;

    return Container(
      height: maxHeight,
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
      decoration: const BoxDecoration(
        color: Color(0xFF000A19),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          // 拖拽条和标题区域
          _buildHeader(),
          // 内容区域
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  /// 构建头部区域（拖拽条和标题）
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          // 拖拽条
          Container(
            width: 50,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          // 标题区域
          Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(left: 18.w),
            child: Stack(
              children: [
                Positioned(
                    bottom: 2.w,
                    child: ContainerBox(
                      jBColors: [
                        Color(0xFFFF3BDF),
                        Color(0xFFFF91EE),
                      ],
                      radius: 180,
                      width: 75.w,
                      height: 4.w,
                    )),
                Text(
                  "PROCESS",
                  style: TextStyle(
                      color: Colors.white, fontWeight: FontWeight.w500),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Obx((){
        if(controller.expandableItems.value.isEmpty){
          return Center(
            child: Image.asset(
              "assets/images/ic_empty.webp",
              width: 130.w,
              height: 113.h,
            ),
          );
        }else {
          return CustomListView(
              key: controller.listViewKey,   // 添加key用于外部调用
              itemCount: controller.expandableItems.value.length,
              itemBuilder: (context, index) {
                return _buildExpandableItem(index);
              },
              autoScrollToBottom: true,  // 启用自动滚动到底部
              bottomThreshold: 100.0,    // 距离底部100像素内认为是在底部
          );
        }
      }),
    );
  }

  /// 构建可展开的列表项
  Widget _buildExpandableItem(int index) {
    final item = controller.expandableItems[index];

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 标题行，包含左边图标、文本和右边展开按钮
        InkWell(
          onTap: item.content.isNotEmpty
              ? () => controller.toggleExpansion(index)
              : null,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 8.h),
            child: Row(
              children: [
                // 左边图标
                Image.asset(
                  "assets/groupChat/ic_group_product_arrow.png",
                  width: 16.w,
                  height: 16.h,
                ),
                SizedBox(width: 12.w),
                // 中间文本
                Expanded(
                  child: Text(
                    item.title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
                // 右边展开收起按钮 - 只有在 content 不为空时才显示
                if (item.content.isNotEmpty)
                  AnimatedRotation(
                    turns: item.isExpanded ? 0.5 : 0,
                    duration: Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Color(0xFF988B9A),
                      size: 24.w,
                    ),
                  ),
              ],
            ),
          ),
        ),
        // 展开的内容区域
        AnimatedSize(
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: item.isExpanded
              ? Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(left: 8.w),
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(
                        color: Color(0xFF988B9A),
                        width: 1.w,
                      ),
                    ),
                  ),
                  padding: EdgeInsets.only(left: 20.w),
                  child: MarkdownBlock(
                    data: controller.getContent(item.content),
                    config: _getMarkdownConfig(),
                  ),
                )
              : SizedBox.shrink(),
        ),
      ],
    );
  }

  MarkdownConfig _getMarkdownConfig() {
    return MarkdownConfig(configs: [
      HrConfig.darkConfig,
      H1Config.darkConfig,
      H2Config.darkConfig,
      H3Config.darkConfig,
      H4Config.darkConfig,
      H5Config.darkConfig,
      H6Config.darkConfig,
      PreConfig.darkConfig,
      PConfig(
          textStyle: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              height: 1,
              backgroundColor: Colors.transparent)),
      CodeConfig(
          style: TextStyle(
              backgroundColor: Colors.transparent, color: Colors.white)),
      BlockquoteConfig.darkConfig,
      ListConfig(
          marginLeft: 16.w,
          marginBottom: 4.h,
          marker: (a, b, c) {
            return Padding(
              padding: EdgeInsets.only(top: 10.h),
              child: Container(
                alignment: Alignment.center,
                width: 4.w,
                height: 4.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
              ),
            );
          }),
      PreConfig(builder: (code, language) {
        if (language == 'json') {
          if (code is Map) {
            return RecommendLay(code);
          }
          try {
            final jsonData = json.decode(code); //会话详情
            return RecommendLay(jsonData);
          } catch (e) {
            return SizedBox.shrink();
          }
        } else {
          return SizedBox.shrink();
        }
      })
    ]);
  }
}

/// 可展开的列表项模型
class ExpandableListItem {
  final String title;
  List<RenderTask> content;
  bool isExpanded;

  ExpandableListItem({
    required this.title,
    required this.content,
    this.isExpanded = false,
  });
}

class ProductCreateController extends GetxController {
  var status = RxStatus.loading();
  final List<StreamEventHandler> _handlers = [];
  RenderContext? _context;
  StreamRequest? _streamRequest;

  // 可展开列表数据
  var expandableItems = <ExpandableListItem>[].obs;
  
  // CustomListView的GlobalKey，用于调用滚动检查
  final GlobalKey listViewKey = GlobalKey();

  List<RenderTask> getRenderList(SearchType type) {
    if (type == SearchType.research || type == SearchType.deepResearch) {
      List<RenderTask> temp = _context?.workflow.renderList ??[];
      return temp
          .firstWhere((e) => e.type() == StreamMessageTypeEnum.message_thinking)
          .thinkTasks();
    } else {
      return _context?.workflow.renderList??[];
    }
  }

  final RxList<RenderTask> headerList = <RenderTask>[].obs;

  void onInitHandler(SearchType searchType) {
    _handlers.clear();
    _handlers.addAll([
      StartOfAgentHandler(),
      EndOfAgentHandler(),
      StartOfLlmHandler(),
      EndOfLlmHandler(),
      MessageFoldHandler(),
      ToolCallHandler(),
      GroupChatToolCallResultHandler(),
      ToolTagHandler(),
      SearchUrlsHandler(),
      MessageHandler(),
      HandoffHandler(),
      GoButtonHandler(),
      EndOfWorkflowHandler(),
      FinalSessionStateHandler(),
      GroupChatProductHistoryHandler(
          searchType: searchType,
          onAddItem: (item) {
            expandableItems.add(item);
          }),
      ErrorHandler(),
      UpgradeMembershipHandler(),
      // ... 其他处理器
    ]);
  }

  void _addExpandableItem(
      List<ExpandableListItem> expandableItems, RenderTask task) {
    expandableItems.add(ExpandableListItem(
      title: task.content(),
      content: [],
      isExpanded: false,
    ));
  }

  void _appendExpandableItem(
      List<ExpandableListItem> expandableItems, RenderTask task) {
    if (expandableItems.isNotEmpty) {
      if (expandableItems.last.content.isNotEmpty) {
        if (expandableItems.last.content.last.messageId == task.messageId) {
          expandableItems.last.content.add(task);
        }
      }
      expandableItems.last.content.add(task);
    } else {
      // headerList.add(task);
    }
  }

  /// 切换展开状态
  void toggleExpansion(int index) {
    if (index >= 0 && index < expandableItems.length) {
      final wasExpanded = expandableItems[index].isExpanded;
      expandableItems[index].isExpanded = !expandableItems[index].isExpanded;
      expandableItems.refresh();
      
      // 展开收起后的智能滚动处理
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final listViewState = listViewKey.currentState;
        if (listViewState != null && listViewState is CustomListViewState) {
          if (!wasExpanded && expandableItems[index].isExpanded) {
            // 展开操作：使用智能滚动，特别处理屏幕底部的item
            listViewState.scrollForItemExpansion(index, expandableItems.length);
          } else {
            // 收起操作：使用常规的自动滚动检查
            listViewState.checkAndScrollIfNeeded();
          }
        }
      });
    }
  }

  void dispatchRender(StreamResultEntity entity) {
    final handler = _handlers.firstWhere(
      (h) => h.canHandle(entity.event),
      orElse: () => DefaultHandler(), // 默认处理器
    );
    if(_context != null){
      handler.handle(entity, _context!);
    }
  }

  void onResumeStream(GroupCreateMessageModel model, SearchType searchType) {
    _context = RenderContext(
      workflow: Workflow(),
      messageBuffer: StringBuffer(),
      triageBuffer: StringBuffer(),
      jsonContentBuffer: StringBuffer(),
    );
    _streamRequest =
        StreamRequest(url: Api.chatResumeStream + "/${model.conversation_id}")
          ..sendRequest(
              onSuccess: (StreamResultEntity resultData) {
                dispatchRender(resultData);
                List<RenderTask> list = List.from(getRenderList(searchType));
                if (searchType == SearchType.deepResearch ||
                    searchType == SearchType.research) {
                  //手动添加tag
                  for(var itemList in list){
                    if(itemList.agentName != "planner" && itemList.agentName != "researcher"){
                      continue;
                    }
                    ExpandableListItem createItem = expandableItems.firstWhere((e)=> e.title == itemList.agentName,orElse: (){
                      return ExpandableListItem(title: itemList.agentName, content: [itemList]);
                    });
                    createItem.content = [itemList];
                    if(itemList.toolCalls?.isNotEmpty == true){
                      for(var toolCall in itemList.toolCalls!) {
                        createItem.content.add(RenderTask()
                          ..type.value =
                              StreamMessageTypeEnum.message_toolCallResult
                          ..agentName = "tool_call"
                          ..content.value = jsonEncode(toolCall));
                      }
                    }
                    if(expandableItems.contains(createItem)){

                    }else {
                      expandableItems.add(createItem);
                    }
                    expandableItems.refresh();
                  }
                }else {
                  List<ExpandableListItem> itemList = [];
                  for (var item in list) {
                    if (item.type() == StreamMessageTypeEnum.tag) {
                      _addExpandableItem(itemList, item);
                    } else {
                      _appendExpandableItem(itemList, item);
                    }
                  }
                  // 将 itemList 中 expandableItems 没有的数据添加进去，如果title相同则覆盖content
                  for (var newItem in itemList) {
                    int existingIndex = expandableItems.indexWhere(
                            (existingItem) => existingItem.title == newItem.title);
                    if (existingIndex != -1) {
                      // 如果找到相同title，覆盖content
                      expandableItems[existingIndex].content = newItem.content;
                      expandableItems.refresh();
                    } else {
                      // 如果没找到，添加新项目
                      expandableItems.add(newItem);
                    }
                  }
                }
              },
              onDone: () {},
              onError: (String error) {},
              model: (model.mode?.toString() ?? ""));
  }

  void onEndStream(GroupCreateMessageModel model, SearchType searchType) async {
    _context = RenderContext(
      workflow: Workflow(),
      messageBuffer: StringBuffer(),
      triageBuffer: StringBuffer(),
      jsonContentBuffer: StringBuffer(),
    );
    Response res = await Get.find<ApiProvider>()
        .get(Api.conversations + "/${model.conversation_id}/dialogue");
    if (res.statusCode == 200) {
      var entity = StreamResultEntity(
          event: "sse_history",
          data: jsonEncode(res.body),
          conversationId: model.conversation_id);
      dispatchRender(entity);
    } else {
      showFailToast(res.statusText ?? S.of(Get.context!).networkConnectionLost);
    }
  }

  void insertTagToResearch(List<RenderTask> list, String agentName) {
    //手动添加tag
    int index = list.indexWhere((e) {
      return e.agentName == agentName;
    });
    if (index != -1) {
      list.insert(
          index,
          RenderTask()
            ..type.value = StreamMessageTypeEnum.tag
            ..content.value = agentName);
    }
  }

  String getContent(List<RenderTask> tasks) {
    String content = "";
    for (var task in tasks) {
      String taskContent = task.content();
      // 特殊处理 agentName 为 planer 的任务
      if (task.agentName == "planner") {
        taskContent = taskContent.replaceAll("```json", "");
        taskContent = taskContent.replaceAll("```", "");
        String planerContent = _parsePlanerJson(taskContent);
        if (planerContent.isNotEmpty) {
          content += "\n$planerContent";
        } else {
          content += "\n$taskContent";
        }
      } else if (task.type() == StreamMessageTypeEnum.message_toolCallResult) {
        content += "\n```json\n$taskContent\n```";
      } else {
        content += "\n$taskContent";
      }
    }
    return content;
  }

  /// 专门解析 planner 类型的 JSON 数据，按照 ChatAiSlidesPlannerWidget 组件的样式返回
  /// 固定格式：{
  ///   "thought": "",
  ///   "title": "",
  ///   "steps": [
  ///     {
  ///       "agent_name": "",
  ///       "title": "",
  ///       "description": "",
  ///       "note": ""
  ///     }
  ///   ]
  /// }
  String _parsePlanerJson(String taskContent) {
    if (taskContent.isEmpty) return "";

    try {
      // 首先尝试从 markdown 格式中提取 JSON
      String jsonToParse =  taskContent;

      dynamic jsonResult = jsonDecode(jsonToParse);

      if (jsonResult is Map<String, dynamic>) {
        StringBuffer buffer = StringBuffer();

        // 解析 title（标题部分，加粗显示）
        String title = jsonResult['title']?.toString()?.trim() ?? '';
        if (title.isNotEmpty && title != 'null') {
          buffer.write("**$title**\n\n");
        }

        // 解析 thought（思考过程部分）
        String thought = jsonResult['thought']?.toString()?.trim() ?? '';
        if (thought.isNotEmpty && thought != 'null') {
          buffer.write("$thought\n\n");
        }

        // 解析 steps（步骤列表部分，按照组件样式格式化）
        List<dynamic>? steps = jsonResult['steps'];
        if (steps != null && steps.isNotEmpty) {
          for (int i = 0; i < steps.length; i++) {
            var step = steps[i];
            if (step is Map<String, dynamic>) {
              String stepTitle = step['title']?.toString()?.trim() ?? '';
              String description =
                  step['description']?.toString()?.trim() ?? '';
              String note = step['note']?.toString()?.trim() ?? '';

              // 按照组件格式：${index + 1}、${stepTitle} ${stepDescription} ${stepNote}
              List<String> parts = [];
              if (stepTitle.isNotEmpty && stepTitle != 'null') {
                parts.add(stepTitle);
              }
              if (description.isNotEmpty && description != 'null') {
                parts.add(description);
              }
              if (note.isNotEmpty && note != 'null') {
                parts.add(note);
              }

              if (parts.isNotEmpty) {
                buffer.write("${i + 1}、${parts.join(' ')}\n\n");
              }
            }
          }
        }

        return buffer.toString().trim();
      }
    } catch (e) {
      // 如果解析失败，返回空字符串，让原有逻辑处理
      return "";
    }

    return "";
  }

  void onReset() {
    _streamRequest?.cancelRequest();
    _context?.workflow.renderList.clear();
    headerList.clear();
    _context?.reset();
    expandableItems.clear();
  }

  @override
  void dispose() {
    super.dispose();
  }
}

class GroupChatProductHistoryHandler extends StreamEventHandler {
  final SearchType searchType;
  final Function(ExpandableListItem item) onAddItem;

  GroupChatProductHistoryHandler(
      {required this.searchType, required this.onAddItem});

  @override
  bool canHandle(String eventType) => eventType == "sse_history";

  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    Map<String, dynamic> data = entity.dataJson();
    List<HistoryMessage> messages = data["messages"]
        .map((item) => HistoryMessage.fromJson(item))
        .toList()
        .reversed
        .toList()
        .cast<HistoryMessage>();
    //处理research数据
    if (searchType == SearchType.research ||
        searchType == SearchType.deepResearch) {
      for (var history in messages) {
        if (history.type != "research") {
          continue;
        }
        HistoryDeepResearchModel researchModel =
            HistoryDeepResearchModel.fromJson(jsonDecode(history.message));
        List<HistoryDeepResearchModelWorkflowSteps> steps =
            (researchModel.workflow?.steps ?? [])
                .whereType<HistoryDeepResearchModelWorkflowSteps>()
                .toList();
        for (var step in steps) {
          if (step.agentName == "planner" || step.agentName == "researcher") {
            List<HistoryDeepResearchModelWorkflowStepsTasks> task = step.tasks
                    ?.whereType<HistoryDeepResearchModelWorkflowStepsTasks>()
                    .toList() ??
                [];
            List<RenderTask> contentTask = [];
            for (var taskItem in task) {
              String content = '';
              StreamMessageTypeEnum type = StreamMessageTypeEnum.message;
              if (taskItem.type == "thinking") {
                content = taskItem.payload?.text ?? '';
              } else if (taskItem.type == "tool_call") {
                content = jsonEncode(ToolCallModel(
                        toolCallId: taskItem.id,
                        toolName: taskItem.payload?.toolName,
                        toolInput: ToolCallModelToolInput(
                            query: taskItem.payload?.input?.query),
                        toolResult: taskItem.payload?.output)
                    .toJson());
                type = StreamMessageTypeEnum.message_toolCallResult;
              }
              contentTask.add(RenderTask()
                ..content.value = content
                ..type.value = type
                ..agentName = step.agentName!);
            }
            onAddItem(ExpandableListItem(
                title: step.agentName!, content: contentTask));
          }
        }
      }
    } else if (searchType == SearchType.aiSlides || searchType == SearchType.aiDesign) {
      //处理ppt数据
      for (var history in messages) {
        if (history.type == "tag") {
          Map<String, dynamic> tagMap = jsonDecode(history.message);
          if (tagMap.containsKey("message")) {
            String content = tagMap["message"];
            onAddItem(ExpandableListItem(title: content, content: []));
          }
        } else if (history.type == "message_fold") {
          Map<String, dynamic> messageFoldMap = jsonDecode(history.message);
          String? title;
          String? content;
          String? agentName;
          if (messageFoldMap.containsKey("data")) {
            content = messageFoldMap["data"];
          }
          if (messageFoldMap.containsKey("title")) {
            title = messageFoldMap["title"];
          }
          if (messageFoldMap.containsKey("type")) {
            agentName =
                messageFoldMap["type"] == "plan_schedule" ? "planner" : "";
          }
          onAddItem(ExpandableListItem(title: title ?? "", content: [
            RenderTask()
              ..content.value = content ?? ""
              ..agentName = agentName ?? ""
          ]));
        }
      }
    }
  }
}

class GroupChatToolCallResultHandler extends ToolCallResultHandler{
  @override
  void handle(StreamResultEntity entity, RenderContext context) {
    final data = getValidData(entity);
    if (data == null) return;

    final toolResult = getStringValue(data, "tool_result");
    final toolCallId = getStringValue(data, "tool_call_id");

    if (toolCallId != null && context.currentTask?.toolCalls != null) {
      for (final toolCall in context.currentTask!.toolCalls!) {
        if (toolCall.tool_call_id == toolCallId) {
          toolCall.tool_result = toolResult ?? "";
          break;
        }
      }
    }
  }
}
