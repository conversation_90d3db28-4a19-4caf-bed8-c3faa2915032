import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/module/chat/group/message_chat/widgets/group_chat_generate_end_widget.dart';
import 'package:new_agnes/module/chat/widget/rotating_loading_image.dart';
import 'package:timezone/timezone.dart';

import '../../../../../data/caseAndQuestion.dart';
import '../../../../../generated/l10n.dart';
import '../model/group_chat_message_model.dart';
import 'group_chat_create_product_widget.dart';

class GroupChatGenerateStartWidget extends StatefulWidget {
  final GroupCreateMessageModel? groupCreateMessageModel;
  final DateTime createdAt;
  final String? timezone;
  const GroupChatGenerateStartWidget(
      {super.key,
      required this.groupCreateMessageModel,
      required this.createdAt,
      required this.timezone});

  @override
  State<GroupChatGenerateStartWidget> createState() => _GroupChatGenerateStartWidgetState();
}

class _GroupChatGenerateStartWidgetState extends State<GroupChatGenerateStartWidget> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.groupCreateMessageModel == null) {
      return const SizedBox.shrink();
    }
    DateTime now = DateTime.now();
    if(widget.timezone != null){
      now = TZDateTime.now(getLocation(widget.timezone!));
    }
    int seconds = now.difference(widget.createdAt).inSeconds;
    if (seconds > 600) {
      return GroupChatGenerateEndWidget(
          groupCreateMessageModel: widget.groupCreateMessageModel);
    }
    SearchType type = SearchType.values.firstWhere(
        (e) => widget.groupCreateMessageModel?.mode?.toString() == e.param);
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        ProductCreateModalUtils.showProductCreateModal(
            context: context,
            groupCreateMessageModel: widget.groupCreateMessageModel!,
            isEnd: false,
            searchType: type
        );
      },
      child: Container(
        margin: EdgeInsets.only(left: 40.w, right: 42.w, bottom: 8.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Color(0xFF9BFBC6),
              Color(0xFF87C0F5),
              Color(0xFFC8BFFF),
              Color(0xFFEFB5E8),
            ],
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 10.h),
        child: Row(
          children: [
            SizedBox(
              width: 10.w,
            ),
            RotatingLoadingImage(
                imagePath: "assets/images/icon_load.webp",
                width: 20.w,
                height: 20.h),
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              child: Text(
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                type.name(context) +
                    S.of(context).researchForZdSeconds(
                        DateTime.now().difference(widget.createdAt).inSeconds),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Color(0xFF0D0D0D),
                ),
              ),
            ),
            SizedBox(
              width: 16.w,
            ),
            Icon(
              Icons.arrow_right_alt,
              size: 20,
              color: Color(0xFF0D0D0D),
            ),
            SizedBox(
              width: 16.w,
            ),
          ],
        ),
      ),
    );
  }

  void startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
      });
    });
  }

  void stopTimer() {
    _timer?.cancel();
    _timer = null;
  }

  @override
  void dispose() {
    super.dispose();
    stopTimer();
  }
}
