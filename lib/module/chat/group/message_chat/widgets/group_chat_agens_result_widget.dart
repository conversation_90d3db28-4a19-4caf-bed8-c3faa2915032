import 'dart:async';
import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/StorageService.dart';
import 'package:new_agnes/data/caseAndQuestion.dart';
import 'package:new_agnes/module/web/single_page_web/page.dart';

import '../../../../../api/stream/render/entity.dart';
import '../../../../../utils/cmUtils.dart';
import '../../../../../utils/event_bus.dart';
import '../../../../../utils/video_thumbnail_generator.dart';
import '../../../../../widget/image_viewer_widget.dart';
import '../../../../../widget/message_long_tap_tools.dart';
import '../../../page/chat_ai_slides_ppt_page.dart';
import '../../../widget/chat_ai_images_video_widget.dart';
import '../enum/group_chat_message_enum.dart';
import '../logic/group_chat_message_controller.dart';
import '../model/group_chat_message_model.dart';

class GroupChatAgensResultWidget extends StatefulWidget {
  final double itemMargin;
  final GroupChatMessageModel model;
  final GroupCreateMessageModel createMessageModel;

  const GroupChatAgensResultWidget({
    super.key,
    required this.itemMargin,
    required this.model,
    required this.createMessageModel,
  });

  @override
  State<GroupChatAgensResultWidget> createState() =>
      _GroupChatAgensResultWidgetState();
}

/// 图片下载功能Mixin
class _ImageDownloadMixin with MediaDownloadMixin {
  // 继承MediaDownloadMixin的所有下载功能
}

/// 视频下载功能Mixin
class _VideoDownloadMixin with MediaDownloadMixin {
  // 继承MediaDownloadMixin的所有下载功能
}

class _GroupChatAgensResultWidgetState
    extends State<GroupChatAgensResultWidget> {
  final groupChatController = Get.isRegistered<GroupChatMessageController>()
      ? Get.find<GroupChatMessageController>()
      : Get.put(GroupChatMessageController());
  String _getProcessedUrl() {
    final StorageService storageService = Get.find<StorageService>();
    final appendText =
        storageService.getStorage().read('debug_url_append_text') ?? '';

    if (appendText.isEmpty) {
      return widget.createMessageModel.link_url ?? "";
    }

    return appendText + widget.createMessageModel.link_url ?? "";
  }

  SearchType get searchType {
    if (widget.createMessageModel.vtype == 'video' ||
        widget.createMessageModel.vtype == 'image') {
      return SearchType.aiDesign;
    }

    return SearchType.values.firstWhere(
        (e) => e.param == widget.createMessageModel.mode?.toString(),
        orElse: () => SearchType.tools);
  }

  String get title {
    return widget.createMessageModel.title ??
        (widget.model.payload?.title ?? "");
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("${widget.model}");
    return GestureDetector(
        onTap: () {
          if (searchType != SearchType.aiDesign) {
            final processedUrl = _getProcessedUrl();

            if (searchType == SearchType.aiSlides) {
              Get.to(ChatAiSlidesPptPage(
                url: processedUrl,
                title: title,
                id: widget.createMessageModel.conversation_id ?? "",
              ));
            } else {
              Get.to(SingleWidgetWebWidget(
                processedUrl,
                title: title,
                hasShare: true,
              ));
            }
          }
        },
        child: searchType == SearchType.aiDesign
            ? LayoutBuilder(builder: (context, box) {
                return _buildAiDesign(box.maxWidth);
              })
            : _buildAiSlidesOrResearch());
    ;
  }

  Widget _buildAiSlidesOrResearch() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 160.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            children: [
              Container(
                height: 94.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                decoration: BoxDecoration(
                    color: Color(0xFFFF5555),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10.r),
                        topRight: Radius.circular(10.r))),
                child: Center(
                  child: Text(
                    title ?? "",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white),
                  ),
                ),
              ),
              Container(
                  height: 26.h,
                  alignment: Alignment.centerRight,
                  margin: EdgeInsets.only(right: 8.w),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(10.r),
                          bottomRight: Radius.circular(10.r))),
                  child: Text(title ?? "",
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Color(0xFF838384),
                        fontWeight: FontWeight.w400,
                      )))
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAiDesign(double maxWidth) {
    bool isVideo = widget.createMessageModel.vtype == "video";
    double? width;
    double? height;
    try {
      Map dimensions = jsonDecode(widget.createMessageModel.dimensions ?? "{}");
      width = double.tryParse(dimensions["width"]?.toString() ?? "");
      height = double.tryParse(dimensions["height"]?.toString() ?? "");
    } catch (e) {}
    double? aspectRatio;
    double containerWidth = 0;
    double containerHeight = 158.h; // 默认高度

    if (width != null && height != null) {
      aspectRatio = width / height;
      // 如果有宽高比，根据容器宽度计算高度
      // 这里假设容器宽度是屏幕宽度减去边距
      containerWidth = maxWidth - widget.itemMargin;
      containerHeight = containerWidth / aspectRatio;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 300.w,
          width: 300.w,
          margin: EdgeInsets.only(right: widget.itemMargin),
          child: isVideo ? _buildAiDesignVideo() : _buildAiDesignImage(),
        ),
        Container(
            width: 300.w,
            margin: EdgeInsets.only(right: widget.itemMargin),
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: Color(0x733D7D78),
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(10.r),
                  bottomRight: Radius.circular(10.r)),
            ),
            child: Text(title ?? "",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.white)))
      ],
    );
  }

  Widget _buildAiDesignImage() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        ImageViewerDialog.show(
            context: context,
            images: [
              ImageItem.network(widget.createMessageModel.link_url ?? "")
            ],
            initialIndex: 0,
            showDownloadButton: true,
            showBottomButtons: true,
            showPageIndicator: false,
            onDownload: (index) async {
              // 使用现有的下载逻辑
              final downloadMixin = _ImageDownloadMixin();
              await downloadMixin.downloadMedia(
                context: context,
                item: ProductImageModel(
                  widget.createMessageModel.link_url, // url
                  null, // media_info
                  null, // thumbnail
                  null, // id
                  "image", // type
                  title, // title
                  null, // version
                  null, // index
                ),
                isVideo: false,
              );
            });
      },
      child: ClipRRect(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10.r), topRight: Radius.circular(10.r)),
        child: CachedNetworkImage(
          imageUrl: widget.createMessageModel.link_url ?? "",
          fit: BoxFit.cover,
          placeholder: (context, url) {
            return _buildPlaceholder();
          },
          errorWidget: (context, url, error) {
            return _buildError();
          },
        ),
      ),
    );
  }

  Widget _buildAiDesignVideo() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        VideoViewerDialog.show(
            context: context,
            videos: [
              VideoItem(url: widget.createMessageModel.link_url, title: title)
            ],
            initialIndex: 0,
            showDownloadButton: true,
            showPageIndicator: false,
            onDownload: (index) async {
              final productImageModel = ProductImageModel(
                widget.createMessageModel.link_url, // url
                null, // media_info
                null, // thumbnail
                null, // id
                "video", // type
                title, // title
                null, // version
                null, // index
              );

              final downloadMixin = _VideoDownloadMixin();
              await downloadMixin.downloadMedia(
                context: context,
                item: productImageModel,
                isVideo: true,
              );
            });
      },
      child: ClipRRect(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10.r), topRight: Radius.circular(10.r)),
        child: Stack(
          fit: StackFit.expand,
          children: [
            VideoThumbnailWidget(
              videoUrl: widget.createMessageModel.link_url ?? "",
              fit: BoxFit.cover,
              placeholder: _buildPlaceholder(),
              errorWidget: _buildError(),
            ),
            Center(
              child: Image.asset(
                'assets/images/ic_design_video.png',
                width: 28.w,
                height: 28.h,
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFF5C5C5C),
            Color(0xFF232323),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFCB53BA)),
        ),
      ),
    );
  }

  Widget _buildError() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        CachedNetworkImage.evictFromCache(
            widget.createMessageModel.link_url ?? "");
        setState(() {});
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFF5C5C5C),
              Color(0xFF232323),
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
        ),
        child: Center(
          child: Icon(
            Icons.refresh,
            color: Color(0xFFCB53BA),
            size: 32.sp,
          ),
        ),
      ),
    );
  }
}
