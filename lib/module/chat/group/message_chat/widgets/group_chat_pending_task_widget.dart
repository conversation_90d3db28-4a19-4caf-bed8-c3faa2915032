import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:new_agnes/module/chat/group/message_chat/enum/group_chat_message_enum.dart';

import '../../../../../generated/l10n.dart';
import '../model/group_chat_message_model.dart';

/**
 * 助手总结待办任务
 */
class GroupChatPendingTaskWidget extends StatelessWidget {
  final GroupChatMessageModel model;

  const GroupChatPendingTaskWidget(
      {super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    String pendingTask = model.messageContent ?? "{}";
    Map<dynamic, dynamic> map;
    try{
      map = jsonDecode(pendingTask);
    }catch(e){
      map = {};
    }

    List? addItemTask = map["add_task_items"];
    List? updateItemTask = map["update_task_items"];
    String msg = S.of(context).atPresentThereIsNoConsensusOnTheTasksToBeDoneFeelFreeToDiscussFurtherAndAskQuestionsIfYouHaveAny;

    List<PendingTaskModel> addList =
        addItemTask?.map((e) => PendingTaskModel.fromJson(e)).toList()??[];
    List<PendingTaskModel> updateList =
        updateItemTask?.map((e) => PendingTaskModel.fromJson(e)).toList()??[];

    // Okay, I'll summarize the consensus tasks:
    // 1.南京美食ppt制作，内容包括南京特色菜肴、小吃、餐饮文化等方面
    // 2.小米汽车报告，内容包括。。。
    // Do you want to execute these tasks?

    String startDescText = S.of(context).okayIllSummarizeTheConsensusTasks;
    String endDescText = S.of(context).doYouWantToExecuteTheseTasks;
    if(model.assistantActionType == GroupAssistantActionType.viewPendingTasks && model.translation != null){
      startDescText = model.content;
      endDescText = S.of(context).youCanExecuteModifyOrRemoveHowWouldYouLikeToProceed;
      msg = S.of(context).noPendingTasksFoundAllTasksHaveBeenCompleted;
      if(model.translation?.variables is Map){
        //"task_items" -> "[{\"id\": \"1757669652969-595bc154\", \"item_index\": 1, \"title\": \"南京旅游研究报告\", \"content\": \"撰写一份关于南京旅游的研究报告，..."
        List taskItems = jsonDecode(model.translation?.variables["task_items"]??"[]");
        addList.clear();
        updateList.clear();
        addList.addAll(taskItems.map((e) => PendingTaskModel.fromJson(e)).toList());
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          (addList.isEmpty && updateList.isEmpty) ? msg : startDescText,
          style: TextStyle(fontSize: 16, color: Colors.white),
        ),
        for (int i = 0; i < addList.length; i++)
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${addList[i].item_index}.${addList[i].title}",
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
              Text(
                "${addList[i].content}",
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),

            ],
          ),
        for (int i = 0; i < updateList.length; i++)
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${updateList[i].item_index}.${updateList[i].title}",
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
              Text(
                "${updateList[i].content}",
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ],
          ),
        if(updateList.isNotEmpty || addList.isNotEmpty)...[
          Text(
            endDescText,
            style: TextStyle(fontSize: 16, color: Colors.white),
          )
        ],
      ],
    );
  }
}

class PendingTaskModel {
  final String? id;
  final String? title;
  final String? content;
  final int? mode;
  final int? item_index;
  PendingTaskModel({
    this.id,
    this.title,
    this.content,
    this.mode,
    this.item_index,
  });

  factory PendingTaskModel.fromJson(Map<String, dynamic> json) {
    return PendingTaskModel(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      mode: json['mode'],
      item_index: int.tryParse(json['item_index']?.toString() ?? ""),
    );
  }
}
