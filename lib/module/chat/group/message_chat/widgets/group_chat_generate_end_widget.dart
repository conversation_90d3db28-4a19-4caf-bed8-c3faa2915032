import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/data/caseAndQuestion.dart';

import '../../../../../generated/l10n.dart';
import '../model/group_chat_message_model.dart';
import 'group_chat_create_product_widget.dart';

class GroupChatGenerateEndWidget extends StatelessWidget {
  final GroupCreateMessageModel? groupCreateMessageModel;

  const GroupChatGenerateEndWidget(
      {super.key, required this.groupCreateMessageModel});

  @override
  Widget build(BuildContext context) {
    if (groupCreateMessageModel == null) {
      return const SizedBox.shrink();
    }
    SearchType type = SearchType.values.firstWhere(
        (e) => groupCreateMessageModel?.mode?.toString() == e.param);
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        ProductCreateModalUtils.showProductCreateModal(
            context: context,
            groupCreateMessageModel: groupCreateMessageModel!,
            isEnd: true,
            searchType: type
        );
      },
      child: Container(
        margin: EdgeInsets.only(left: 40.w, right: 42.w, bottom: 8.h),
        padding: EdgeInsets.symmetric(vertical: 10.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(
            color: const Color(0xFF988B9A),
            width: 1.0,
          ),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              child: Text(
                type.name(context),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            ),
            SizedBox(
              width: 16.w,
            ),
            Icon(
              Icons.arrow_right_alt,
              size: 20,
              color: Colors.white,
            ),
            SizedBox(
              width: 16.w,
            ),
          ],
        ),
      ),
    );
  }
}
