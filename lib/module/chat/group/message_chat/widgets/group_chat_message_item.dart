
import 'package:flutter/material.dart';
import 'package:new_agnes/module/chat/group/message_chat/enum/group_chat_message_enum.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_create_join_item.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_generate_thing_item.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_system_item.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_text_agens_result_item.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_text_audio_item.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_text_file_item.dart';
import 'package:new_agnes/module/chat/group/message_chat/item/group_chat_text_item.dart';
import 'package:new_agnes/module/chat/group/message_chat/model/group_chat_message_model.dart';


/// 统一的群聊消息组件
/// 根据消息角色自动选择合适的渲染方式：
/// - me: 简单文本消息
/// - others: 简单文本消息
/// - agens: Markdown 渲染消息
class GroupChatMessageItem extends StatelessWidget {
  final GroupChatMessageModel messageModel;
  final String userId;
  final bool agnes;

  const GroupChatMessageItem(
      {super.key, required this.messageModel, required this.userId,this.agnes=false});

  @override
  Widget build(BuildContext context) {
    if (!messageModel.assistantActionType.visible) return SizedBox.shrink();
    if (!messageModel.productStatusType.visible) return SizedBox.shrink();
    //创建加入类消息
    if (messageModel.createJoinType != CreateJoinType.no) {
      return GroupChatCreateJoinItem(model: messageModel, userId: userId);
    }
    //生成物推送消息
    if (messageModel.productStatusType ==
        GroupChatMessageProductStatusType.start ||
        messageModel.productStatusType ==
            GroupChatMessageProductStatusType.end) {
      return GroupChatGenerateThingItem(model: messageModel, userId: userId);
    }
    //系统消息
    if (messageModel.messageType == GroupChatMessageType.system) {
      return GroupChatSystemItem(model: messageModel, userId: userId);
    }
    //带小话筒的语音信息
    if(messageModel.inputTypeValue == InputType.audio){
      return GroupChatTextAudioItem(model: messageModel, userId: userId, agnes: agnes);
    }
    if(messageModel.productType != GroupChatMessageProductType.none){
      return GroupChatTextAgensResultItem(model: messageModel, userId: userId);
    }
    if (messageModel.contentType == GroupChatMessageContentType.text) {
      //纯文本消息
      return GroupChatTextItem(model: messageModel, userId: userId, agnes: agnes);
    } else if (messageModel.contentType == GroupChatMessageContentType.file ||
        messageModel.contentType == GroupChatMessageContentType.textAndFile) {
      //返回带文件的消息
      return GroupChatTextFileItem(model: messageModel, userId: userId, agnes: agnes);
    } else {
      return SizedBox.shrink();
    }
  }
}
