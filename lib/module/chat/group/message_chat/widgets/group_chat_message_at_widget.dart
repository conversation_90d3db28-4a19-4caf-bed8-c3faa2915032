import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:lpinyin/lpinyin.dart';

import '../../../../../generated/l10n.dart';
import '../../../model/grouplist/GroupUserBean.dart';

extension GroupUserBeanExtension on GroupUserBean {
  String get name {
    return username ?? '';
  }

  String get avatar {
    return avatarUrl ?? '';
  }

  String get emailValue {
    return email ?? '';
  }

  /// 获取唯一标识符
  String get uniqueId {
    // 使用邮箱+姓名的组合作为唯一标识，避免重复
    return '${email}_${name}';
  }

  /// 获取姓名首字母
  String get firstLetter {
    if (name.isEmpty) return '#';

    // 检查第一个字符是否为数字
    if (RegExp(r'[0-9]').hasMatch(name[0])) {
      return '#';
    }

    // 检查第一个字符是否为英文字母
    if (RegExp(r'[A-Za-z]').hasMatch(name[0])) {
      return name[0].toUpperCase();
    }

    // 中文字符转拼音
    String firstPinyin = PinyinHelper.getShortPinyin(name[0]);
    final firstChar = firstPinyin.toUpperCase();
    if (RegExp(r'[A-Z]').hasMatch(firstChar)) {
      return firstChar;
    }
    return '#';
  }
}

/// 分组后的联系人数据
class GroupedContactInfo {
  final String letter;
  final List<GroupUserBean> contacts;

  GroupedContactInfo({
    required this.letter,
    required this.contacts,
  });
}

typedef MessageAtCallBack = Function(
    List<GroupUserBean> contacts, List<GroupUserBean> selects);

/// 模态底部弹窗工具类
class ModalBottomSheetUtils {
  /// 显示联系人选择弹窗
  static Future<GroupUserBean?> showContactPickerModal({
    required BuildContext context,
    required List<GroupUserBean> contacts,
    MessageAtCallBack? onContactSelected,
    GroupUserBean? agens,
  }) {
    return showModalBottomSheet<GroupUserBean>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ContactPickerModal(
        contacts: contacts,
        onContactSelected: onContactSelected,
        agens: agens,
      ),
    );
  }
}

/// 联系人选择模态弹窗组件
class ContactPickerModal extends StatefulWidget {
  final List<GroupUserBean> contacts;
  final MessageAtCallBack? onContactSelected;
  final GroupUserBean? agens;

  const ContactPickerModal({
    Key? key,
    required this.contacts,
    required this.agens,
    this.onContactSelected,
  }) : super(key: key);

  @override
  State<ContactPickerModal> createState() => _ContactPickerModalState();
}

class _ContactPickerModalState extends State<ContactPickerModal> {
  late List<GroupedContactInfo> groupedContacts;
  late List<GroupedContactInfo> filteredGroupedContacts;
  late ScrollController scrollController;
  final Map<String, GlobalKey> letterKeys = {};
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  // 选择状态管理
  bool _isSelectionMode = false;
  final List<GroupUserBean> _selectedContacts = <GroupUserBean>[];

  // 防抖相关
  String? _lastProcessedLetter;
  int _lastProcessedIndex = -1;

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    _groupContacts();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  /// 将联系人按首字母分组
  void _groupContacts() {
    final Map<String, List<GroupUserBean>> grouped = {};

    for (final contact in widget.contacts) {
      final letter = contact.firstLetter;
      if (!grouped.containsKey(letter)) {
        grouped[letter] = [];
      }
      grouped[letter]!.add(contact);
    }

    // 排序字母并创建分组数据
    final sortedLetters = grouped.keys.toList()..sort();
    groupedContacts = sortedLetters.map((letter) {
      letterKeys[letter] = GlobalKey();
      return GroupedContactInfo(
        letter: letter,
        contacts: grouped[letter]!..sort((a, b) => a.name.compareTo(b.name)),
      );
    }).toList();

    // 初始化过滤后的数据
    filteredGroupedContacts = groupedContacts;
  }

  /// 搜索文本变化监听
  void _onSearchChanged() {
    final searchText = _searchController.text.trim().toLowerCase();
    setState(() {
      _isSearching = searchText.isNotEmpty;
      if (_isSearching) {
        _filterContacts(searchText);
      } else {
        filteredGroupedContacts = groupedContacts;
      }
    });
  }

  /// 根据搜索文本过滤联系人
  void _filterContacts(String searchText) {
    final Map<String, List<GroupUserBean>> filteredGrouped = {};

    // 首先检查管理员是否匹配搜索条件
    bool isAgensMatched = false;
    if (widget.agens != null) {
      final agens = widget.agens!;
      // 匹配管理员姓名（直接字符串匹配，支持数字）
      final nameMatch = agens.name.toLowerCase().contains(searchText);

      // 匹配管理员邮箱
      final emailMatch = agens.emailValue.toLowerCase().contains(searchText);

      // 拼音匹配（仅对非纯数字的姓名进行拼音匹配）
      bool pinyinMatch = false;
      bool pinyinShortMatch = false;

      // 检查姓名是否为纯数字，如果不是则进行拼音匹配
      if (!RegExp(r'^[0-9]+$').hasMatch(agens.name)) {
        try {
          // 匹配拼音
          pinyinMatch = PinyinHelper.getPinyinE(agens.name, separator: '')
              .toLowerCase()
              .contains(searchText);
          // 匹配拼音首字母
          pinyinShortMatch = PinyinHelper.getShortPinyin(agens.name)
              .toLowerCase()
              .contains(searchText);
        } catch (e) {
          // 拼音转换失败时，忽略拼音匹配
          pinyinMatch = false;
          pinyinShortMatch = false;
        }
      }

      isAgensMatched = nameMatch || pinyinMatch || pinyinShortMatch || emailMatch;
    }

    for (final group in groupedContacts) {
      final filteredContacts = group.contacts.where((contact) {
        // 匹配姓名（直接字符串匹配，支持数字）
        final nameMatch = contact.name.toLowerCase().contains(searchText);

        // 匹配邮箱
        final emailMatch =
            contact.emailValue.toLowerCase().contains(searchText);

        // 拼音匹配（仅对非纯数字的姓名进行拼音匹配）
        bool pinyinMatch = false;
        bool pinyinShortMatch = false;

        // 检查姓名是否为纯数字，如果不是则进行拼音匹配
        if (!RegExp(r'^[0-9]+$').hasMatch(contact.name)) {
          try {
            // 匹配拼音
            pinyinMatch = PinyinHelper.getPinyinE(contact.name, separator: '')
                .toLowerCase()
                .contains(searchText);
            // 匹配拼音首字母
            pinyinShortMatch = PinyinHelper.getShortPinyin(contact.name)
                .toLowerCase()
                .contains(searchText);
          } catch (e) {
            // 拼音转换失败时，忽略拼音匹配
            pinyinMatch = false;
            pinyinShortMatch = false;
          }
        }

        return nameMatch || pinyinMatch || pinyinShortMatch || emailMatch;
      }).toList();

      if (filteredContacts.isNotEmpty) {
        filteredGrouped[group.letter] = filteredContacts;
      }
    }

    // 重新构建过滤后的分组数据
    final sortedLetters = filteredGrouped.keys.toList()..sort();
    filteredGroupedContacts = sortedLetters.map((letter) {
      return GroupedContactInfo(
        letter: letter,
        contacts: filteredGrouped[letter]!,
      );
    }).toList();

    // 如果管理员匹配但没有其他联系人匹配，确保显示管理员
    if (isAgensMatched && filteredGroupedContacts.isEmpty) {
      // 创建一个空的分组用于显示管理员header
      filteredGroupedContacts = [
        GroupedContactInfo(
          letter: '',
          contacts: [],
        )
      ];
    }
  }

  /// 滚动到指定字母位置
  void _scrollToLetter(String letter) {
    final key = letterKeys[letter];
    if (key?.currentContext != null) {
      Scrollable.ensureVisible(
        key!.currentContext!,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.8;

    return Container(
      height: maxHeight,
      decoration: const BoxDecoration(
        color: Color(0xFF000A19),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          // 拖拽条和标题区域
          _buildHeader(),
          // 联系人列表
          Expanded(
            child: Stack(
              children: [
                _buildContactList(),
                if (!_isSearching && !_isSelectionMode) _buildLetterIndex(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建管理员Header组件
  Widget _buildAdministratorHeader(GroupUserBean agens) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _enterSelectionMode(agens);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // 头像
            _buildAvatar(agens.avatar),
            const SizedBox(width: 12),
            // 姓名和邮箱
            Expanded(
              child: Text(
                agens.name,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建头部区域（拖拽条和标题）
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          // 拖拽条
          Container(
            width: 50,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          Container(
            alignment: Alignment.centerRight,
            margin: const EdgeInsets.only(right: 16),
            child: _isSelectionMode
                ? GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      widget.onContactSelected
                          ?.call(widget.contacts, _selectedContacts);
                      Get.back();
                    },
                    child: IntrinsicWidth(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.h),
                          gradient: LinearGradient(
                            colors: [
                              Color(0xFFFF3BDF),
                              Color(0xFFFF91EE),
                            ],
                            begin: Alignment.bottomCenter,
                            end: Alignment.topCenter,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            "Finish(${_selectedContacts.length})",
                            style: TextStyle(
                                fontSize: 16, color: Color(0xFF0D0D0D)),
                          ),
                        ),
                      ),
                    ),
                  )
                : Text(
                    S.of(context).multipleChoices,
                    style: TextStyle(fontSize: 14.sp, color: Color(0xFF988B9A)),
                  ),
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            height: 48.h,
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Color(0xFF8F8F8F), width: 1),
            ),
            child: Row(
              children: [
                Expanded(
                    child: TextField(
                  controller: _searchController,
                  maxLines: 1,
                  decoration: InputDecoration(
                    hintText: S.of(context).searchByNameEmailOrPhone,
                    hintStyle:
                        TextStyle(fontSize: 14.sp, color: Color(0xFF988B9A)),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
                  style: TextStyle(fontSize: 14.sp, color: Colors.white),
                )),
                if (_isSearching)
                  GestureDetector(
                    onTap: () {
                      _searchController.clear();
                    },
                    child: Icon(
                      Icons.clear,
                      color: Color(0xFF988B9A),
                      size: 20.w,
                    ),
                  )
                else
                  Image.asset(
                    "assets/groupChat/ic_group_find.png",
                    width: 20.w,
                    height: 20.h,
                  ),
                SizedBox(width: 16.w)
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建联系人列表
  Widget _buildContactList() {
    // 检查是否应该显示管理员header
    bool shouldShowAgens = widget.agens != null;
    
    if (_isSearching) {
      // 搜索模式下，需要检查管理员是否匹配搜索条件
      if (widget.agens != null) {
        final searchText = _searchController.text.trim().toLowerCase();
        final agens = widget.agens!;
        
        // 检查管理员是否匹配搜索条件
        final nameMatch = agens.name.toLowerCase().contains(searchText);
        final emailMatch = agens.emailValue.toLowerCase().contains(searchText);
        
        bool pinyinMatch = false;
        bool pinyinShortMatch = false;
        
        if (!RegExp(r'^[0-9]+$').hasMatch(agens.name)) {
          try {
            pinyinMatch = PinyinHelper.getPinyinE(agens.name, separator: '')
                .toLowerCase()
                .contains(searchText);
            pinyinShortMatch = PinyinHelper.getShortPinyin(agens.name)
                .toLowerCase()
                .contains(searchText);
          } catch (e) {
            pinyinMatch = false;
            pinyinShortMatch = false;
          }
        }
        
        shouldShowAgens = nameMatch || pinyinMatch || pinyinShortMatch || emailMatch;
      }
      
      // 如果搜索中没有匹配的管理员和联系人，显示空结果
      if (!shouldShowAgens && filteredGroupedContacts.isEmpty) {
        return _buildEmptySearchResult();
      }
      
      // 如果只有管理员匹配但没有其他联系人，确保至少有一个空分组
      if (shouldShowAgens && filteredGroupedContacts.isEmpty) {
        filteredGroupedContacts = [
          GroupedContactInfo(
            letter: '',
            contacts: [],
          )
        ];
      }
    }

    // 计算总的Item数量：管理员Header(如果应该显示) + 联系人分组
    final totalItemCount =
        filteredGroupedContacts.length + (shouldShowAgens ? 1 : 0);

    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.only(bottom: 20),
      itemCount: totalItemCount,
      itemBuilder: (context, index) {
        // 如果应该显示管理员且是第一个Item，显示管理员Header
        if (shouldShowAgens && index == 0) {
          return _buildAdministratorHeader(widget.agens!);
        }

        // 计算实际的分组索引
        final groupIndex = shouldShowAgens ? index - 1 : index;
        final group = filteredGroupedContacts[groupIndex];

        return _buildContactGroup(group, index == 0);
      },
    );
  }

  /// 构建空搜索结果页面
  Widget _buildEmptySearchResult() {
    return Center(
      child: Image.asset(
        "assets/images/ic_empty.webp",
        width: 130.w,
        height: 113.h,
      ),
    );
  }

  /// 构建联系人分组
  Widget _buildContactGroup(GroupedContactInfo group, bool first) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!first) ...[
          Container(
            width: double.infinity,
            height: 1.h,
            color: Color(0xFF733D7D).withValues(alpha: 0.47),
          )
        ],
        SizedBox(
          height: 16.h,
        ),
        // 字母标题
        Container(
          key: letterKeys[group.letter],
          width: double.infinity,
          margin: EdgeInsets.only(left: 16.w, right: 16.w),
          child: Text(
            group.letter,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF988B9A),
            ),
          ),
        ),
        // 联系人列表
        ...group.contacts.map((contact) => _buildContactItem(contact)),
      ],
    );
  }

  /// 构建单个联系人项
  Widget _buildContactItem(GroupUserBean contact) {
    final isSelected =
        _selectedContacts.any((c) => c.uniqueId == contact.uniqueId);

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // if (_isSelectionMode) {
        //   // 选择模式下切换选中状态
        //   _toggleContactSelection(contact);
        // }
        // 长按进入选择模式
        _enterSelectionMode(contact);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // 单选框（选择模式下显示）
            if (_isSelectionMode) ...[
              Image.asset(
                isSelected
                    ? "assets/groupChat/ic_group_find_select.png"
                    : "assets/groupChat/ic_group_find_un_select.png",
                width: 20.w,
                height: 20.h,
              ),
              SizedBox(width: 12.w),
            ],
            // 头像
            _buildAvatar(contact.avatar),
            const SizedBox(width: 12),
            // 姓名和邮箱
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    contact.name,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    contact.emailValue,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Color(0xFF988B9A),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar(String avatarUrl) {
    bool isSvg = avatarUrl.toLowerCase().endsWith(".svg");

    return ClipRRect(
      borderRadius: BorderRadius.circular(20.sp),
      child: CachedNetworkImage(
        imageUrl: avatarUrl,
        width: 40.w,
        height: 40.h,
        fit: BoxFit.cover,
        imageBuilder: (context, imageProvider) {
          // 如果是 SVG，使用 SvgPicture.network，否则使用普通的 Image
          if (isSvg) {
            return SvgPicture.network(
              avatarUrl,
              width: 40.w,
              height: 40.h,
              fit: BoxFit.cover,
              placeholderBuilder: (context) => _buildDefaultAvatar(),
            );
          } else {
            return Image(
              image: imageProvider,
              width: 40.w,
              height: 40.h,
              fit: BoxFit.cover,
            );
          }
        },
        placeholder: (context, url) => _buildDefaultAvatar(),
        errorWidget: (context, url, error) {
          // 如果是 SVG 且 CachedNetworkImage 失败，尝试直接用 SvgPicture.network
          if (isSvg) {
            return SvgPicture.network(
              avatarUrl,
              width: 40.w,
              height: 40.h,
              fit: BoxFit.cover,
              placeholderBuilder: (context) => _buildDefaultAvatar(),
              errorBuilder: (context, error, stack) => _buildDefaultAvatar(),
            );
          }
          return _buildDefaultAvatar();
        },
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Image.asset(
      "assets/images/ic_group_agens_avatar.png",
      width: 40.w,
      height: 40.h,
      fit: BoxFit.cover,
    );
  }

  /// 进入选择模式
  void _enterSelectionMode(GroupUserBean contact) {
    setState(() {
      _isSelectionMode = true;
      _selectedContacts.add(contact);
    });
    HapticFeedback.mediumImpact();
    widget.onContactSelected
        ?.call(widget.contacts, _selectedContacts);
    Get.back(closeOverlays: true);
  }

  /// 切换联系人选中状态
  void _toggleContactSelection(GroupUserBean contact) {
    setState(() {
      final existingIndex =
          _selectedContacts.indexWhere((c) => c.uniqueId == contact.uniqueId);
      if (existingIndex != -1) {
        _selectedContacts.removeAt(existingIndex);
        // 如果没有选中任何联系人，退出选择模式
        if (_selectedContacts.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedContacts.add(contact);
      }
    });
    HapticFeedback.lightImpact();
  }

  /// 构建右侧字母索引
  Widget _buildLetterIndex() {
    return Positioned(
      right: 0,
      top: 50,
      bottom: 50,
      child: Container(
        width: 24,
        child: _buildInteractiveLetterIndex(),
      ),
    );
  }

  /// 构建可交互的字母索引
  Widget _buildInteractiveLetterIndex() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return GestureDetector(
          onPanStart: (details) =>
              _handlePanStart(details, constraints.maxHeight),
          onPanUpdate: (details) =>
              _handlePanUpdate(details, constraints.maxHeight),
          onPanEnd: (details) => _handlePanEnd(details),
          onTapDown: (details) =>
              _handleTapDown(details, constraints.maxHeight),
          child: Container(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: filteredGroupedContacts.map((group) {
                return Container(
                  height: 20,
                  alignment: Alignment.center,
                  child: Text(
                    group.letter,
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF988B9A),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  /// 处理滑动开始
  void _handlePanStart(DragStartDetails details, double containerHeight) {
    // 重置防抖状态，开始新的滑动
    _lastProcessedIndex = -1;
    _lastProcessedLetter = null;
    _handlePositionUpdate(details.localPosition, containerHeight);
  }

  /// 处理滑动更新
  void _handlePanUpdate(DragUpdateDetails details, double containerHeight) {
    _handlePositionUpdate(details.localPosition, containerHeight);
  }

  /// 处理滑动结束
  void _handlePanEnd(DragEndDetails details) {
    // 重置防抖状态
    _lastProcessedIndex = -1;
    _lastProcessedLetter = null;
  }

  /// 处理点击
  void _handleTapDown(TapDownDetails details, double containerHeight) {
    _handlePositionUpdate(details.localPosition, containerHeight);
  }

  /// 处理位置更新，根据Y坐标确定对应的字母
  void _handlePositionUpdate(Offset localPosition, double containerHeight) {
    if (filteredGroupedContacts.isEmpty) return;

    // 添加边界检查，防止超出范围
    double normalizedY = localPosition.dy.clamp(0.0, containerHeight - 1);

    // 计算每个字母项的高度
    final itemHeight = containerHeight / filteredGroupedContacts.length;

    // 计算索引，使用更稳定的方法
    int index = (normalizedY / itemHeight).round();

    // 确保索引在有效范围内
    index = index.clamp(0, filteredGroupedContacts.length - 1);

    // 添加稳定性检查：只有当索引真正改变时才更新
    if (index == _lastProcessedIndex) return;

    // 获取目标字母
    final targetLetter = filteredGroupedContacts[index].letter;

    // 只有当字母真正改变时才更新
    if (targetLetter == _lastProcessedLetter) return;

    // 更新记录
    _lastProcessedIndex = index;
    _lastProcessedLetter = targetLetter;

    // 只执行滚动和触觉反馈
    _scrollToLetter(targetLetter);
    _provideFeedback();
  }

  /// 提供触觉反馈
  void _provideFeedback() {
    // 提供轻微的触觉反馈
    HapticFeedback.lightImpact();
  }
}
