import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:markdown_widget/config/all.dart';
import 'package:markdown_widget/widget/all.dart';
import 'package:new_agnes/module/chat/model/search_urls_model.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/widget/message_long_tap_tools.dart';
import 'package:path/path.dart';

import '../../../../../dialog/sources_dialog.dart';
import '../../../../../widget/ImageUrl.dart';
import '../../../../web/single_page_web/page.dart';
import '../../../widget/deep_research_lay.dart';
import '../enum/group_chat_message_enum.dart';
import '../item/group_chat_base_user_info_item.dart';
import '../model/group_chat_message_model.dart';
import '../widgets/group_chat_pending_task_widget.dart';

mixin GroupChatMessageUIMixin on GroupChatBaseUserInfoItem{
  Widget buildCommonTextItem(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: borderRadius,
            color: backgroundColor,
          ),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
          child: _buildMain(),
        ),
        if(model.loadingStatus == AgnesLoadingStatus.start)...[
          Lottie.asset('assets/json/loading.json',
              height: 48.h, width: 48.w)
        ],
        if(model.payload?.web_links != null)...[
          _buildWebLinks()
        ],
      ],
    );
  }

  Widget _buildMain(){
    switch (model.getRoleEnum(userId)) {
      case GroupChatMessageRoleEnum.agens:
      // AI 消息使用 Markdown 渲染
        return IgnorePointer(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //任务列表 3
              if (model.assistantActionType ==
                  GroupAssistantActionType.pendingTasks || model.assistantActionType == GroupAssistantActionType.viewPendingTasks) ...[
                GroupChatPendingTaskWidget(model: model,)
              ],
              if (model.assistantActionType !=
                  GroupAssistantActionType.pendingTasks && model.assistantActionType != GroupAssistantActionType.viewPendingTasks) ...[
                MarkdownBlock(
                  data: model.content ?? "",
                  generator: MarkdownGenerator(
                      linesMargin: EdgeInsets.symmetric(vertical: 0)),
                  config: _getMarkdownConfig(),
                )
              ]
            ],
          ),
        );
      default:
      // 用户消息使用简单文本渲染，支持@username高亮
        return _buildTextWithMentions();
    }
  }

  Widget _buildTextWithMentions() {
    String content = model.content ?? "";
    String? mentionUsername = model.mention_user?.username;

    // 如果没有mention用户或内容为空，直接返回普通文本
    if (mentionUsername == null || mentionUsername.isEmpty || content.isEmpty) {
      return Text(
        content,
        style: TextStyle(fontSize: 16, color: Colors.white),
      );
    }

    String mentionPattern = "@$mentionUsername";
    List<TextSpan> spans = [];

    // 分割文本，找到@username并高亮显示
    List<String> parts = content.split(mentionPattern);

    for (int i = 0; i < parts.length; i++) {
      // 添加普通文本部分
      if (parts[i].isNotEmpty) {
        spans.add(TextSpan(
          text: parts[i],
          style: TextStyle(fontSize: 16, color: Colors.white),
        ));
      }

      // 添加@username部分（除了最后一个分割部分）
      if (i < parts.length - 1) {
        spans.add(TextSpan(
          text: mentionPattern,
          style: TextStyle(fontSize: 16, color: Colors.blue),
        ));
      }
    }

    return RichText(
      text: TextSpan(children: spans,recognizer: LongPressGestureRecognizer()..onLongPress = () {}),
    );
  }

  Widget _buildWebLinks(){
    List<GroupWebLinkModel> webLinks = model.payload?.web_links??[];
    if(webLinks.isEmpty) return SizedBox.shrink();
    List<SearchUrlsModel> searchList = model.payload?.web_links
            ?.map((e) {
              String toolDataImage = e.url ?? "";
              String toolDataContent = '';
              List<String> imageList1 = toolDataImage.split('//');
              if (imageList1.length > 1 && imageList1[1].isNotEmpty) {
                List<String> imageList2 = imageList1[1].split('/');
                toolDataImage =
                    '${imageList1[0]}//${imageList2[0]}/favicon.ico';
                toolDataContent = imageList2[0];
              }

              return SearchUrlsModel(
                  title: e.title,
                  url: e.url,
                  content: toolDataContent,
                  image: toolDataImage);
            })
            .whereType<SearchUrlsModel>()
            .toList() ??
        [];
    if(searchList.isEmpty) return SizedBox.shrink();
    return Container(
      margin: margin,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: (){
                Get.to(SingleWidgetWebWidget(
                  searchList.first.url,
                  title: searchList.first.title,
                ));
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 6,vertical: 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: Color(0xFF000000).withValues(alpha: 0.25),
                  border: Border.all(color: Color(0xFF7BD33A)),
                ),
                child: Row(
                  children: [
                    UrlImage('${searchList.first.image??""}',
                        width: 16.w,
                        height: 16.h,
                        radius: 180,
                        errorWidget: ErrorLay()),
                    Expanded(child: LayoutBuilder(builder: (context,box){
                      TextStyle style = TextStyle(
                          color: Color(0xFF7BD33A),
                          fontSize: 14);
                      return getOverflowEllipsisMiddle(
                          box.maxWidth, webLinks.first.title ?? "", style);
                    })),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: (){
              if(searchList.length == 1){
                return;
              }
              Get.bottomSheet(SourcesDialog(searchList.sublist(1,searchList.length)),
                  backgroundColor: Colors.transparent,
                  isScrollControlled: true,
                  elevation: 0,
                  barrierColor: Colors.transparent);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 6,vertical: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30.r),
                color: Color(0xFF000000).withValues(alpha: 0.25),
                border: Border.all(color: Color(0xFFFF3BDF)),
              ),
              child: Text("+${webLinks.length - 1} sources", style: TextStyle(
                  color: Color(0xFFFF3BDF),
                  fontSize: 14,
                  fontWeight: FontWeight.w600),),
            ),
          ),
        ],
      ),
    );
  }

  MarkdownConfig _getMarkdownConfig() {
    return MarkdownConfig(configs: [
      HrConfig.darkConfig,
      H1Config.darkConfig,
      H2Config.darkConfig,
      H3Config.darkConfig,
      H4Config.darkConfig,
      H5Config.darkConfig,
      H6Config.darkConfig,
      PreConfig.darkConfig,
      PConfig(
          textStyle: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              height: 1,
              backgroundColor: Colors.transparent)),
      CodeConfig(
          style: TextStyle(
              backgroundColor: Colors.transparent, color: Colors.white)),
      BlockquoteConfig.darkConfig,
      ListConfig(
          marginLeft: 16.w,
          marginBottom: 4.h,
          marker: (a, b, c) {
            return Padding(
              padding: EdgeInsets.only(top: 10.h),
              child: Container(
                alignment: Alignment.center,
                width: 4.w,
                height: 4.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
              ),
            );
          })
    ]);
  }

  Widget getOverflowEllipsisMiddle(
      double maxWidth, String text, TextStyle style) {
    Widget defaultText = Text(
      text,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
      style: style,
    );
    // 如果文本为空，直接返回
    if (text.isEmpty) return defaultText;

    // 测试原始文本是否能够放下
    TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
    )..layout();

    // 如果原始文本能够放下，直接返回
    if (textPainter.width <= maxWidth) {
      return defaultText;
    }
    if (text.length < 4) {
      return defaultText;
    }
    // 如果放不下，需要截断文本并添加省略号

    return Row(
      children: [
        Expanded(
          child: Text(
            text,
            maxLines: 1,
            style: style,
            overflow: TextOverflow.clip,
          ),
        ),
        Text(
          "...",
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          style: style,
        ),
        Text(
          text.substring(text.length - 3,text.length),
          maxLines: 1,
          style: style,
          overflow: TextOverflow.clip,
        )
      ],
    );
  }
}