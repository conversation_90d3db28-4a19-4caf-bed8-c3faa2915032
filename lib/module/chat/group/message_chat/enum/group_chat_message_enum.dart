import 'package:flutter/material.dart';

enum GroupChatMessageRoleEnum {
  me, //自己
  others, //其他人
  agens, //ai
}

enum GroupChatMessageContentType{
  text,// 1、文本 / JSON字符串 2、附件 3、文本附件混合
  file,
  textAndFile
}

enum GroupChatMessageProductType{
  none,
  agnesText,
  agnesFile,
  agnesTextAndFile
}

enum GroupChatMessageProductStatusType{
  none,
  start,
  end,
  error,
}

extension GroupChatMessageRoleEnumExtension on GroupChatMessageProductStatusType {
  bool get visible {
    switch (this) {
      case GroupChatMessageProductStatusType.error:
        return false;
      default:
        return true;
    }
  }
}

extension GroupChatMessageContentTypeExtension on GroupChatMessageContentType {
  int get value {
    switch (this) {
      case GroupChatMessageContentType.text:
        return 1;
      case GroupChatMessageContentType.file:
        return 2;
      case GroupChatMessageContentType.textAndFile:
        return 3;
    }
  }
}

enum GroupChatMessageType{
  chat,// 1、聊天 2、人员回复 3、系统消息 4、助手回复 5、@成员 6、@助手
  reply,//人员回复
  system,//系统消息
  assistantReply,//助手回复
  atMember,//@成员
  atAssistant,//@助手
}

extension GroupChatMessageTypeExtension on GroupChatMessageType{
  int get value{
    switch(this){
      case GroupChatMessageType.chat:
        return 1;
      case GroupChatMessageType.reply:
        return 2;
      case GroupChatMessageType.system:
        return 3;
      case GroupChatMessageType.assistantReply:
        return 4;
      case GroupChatMessageType.atMember:
        return 5;
      case GroupChatMessageType.atAssistant:
        return 6;
    }
  }
}

enum GroupAssistantActionType{
  chat, // 1、助手闲聊 2、助手总结评论 3、助手总结待办任务 4、助手执行待确认反馈(41:已确认 42:已取消) 5、确认执行 6、取消执行 7、没权限,8,生成物结束，9，生成物开始
  summarizesComments,
  pendingTasks,
  viewPendingTasks,
  task,
  confirmed,
  canceled,
  confirmExecution,
  cancelExecution,
  noPermission,
  generateThingStart,
  generateThingEnd,
  generateThingError,
  updateVip,
  unknown,
}

extension GroupAssistantActionTypeExtension on GroupAssistantActionType{
  int get value{
    switch(this){
      case GroupAssistantActionType.chat:
        return 1;
      case GroupAssistantActionType.summarizesComments:
        return 2;
      case GroupAssistantActionType.pendingTasks:
        return 3;
      case GroupAssistantActionType.task:
        return 4;
      case GroupAssistantActionType.confirmed:
        return 41;
      case GroupAssistantActionType.canceled:
        return 42;
      case GroupAssistantActionType.confirmExecution:
        return 5;
      case GroupAssistantActionType.cancelExecution:
        return 6;
      case GroupAssistantActionType.noPermission:
        return 7;
      case GroupAssistantActionType.unknown:
        return 10000;
      case GroupAssistantActionType.generateThingStart:
        return 9;
      case GroupAssistantActionType.generateThingEnd:
        return 8;
      case GroupAssistantActionType.updateVip:
        return 11;
      case GroupAssistantActionType.generateThingError:
        return 14;
      case GroupAssistantActionType.viewPendingTasks:
        return 12;
    }
  }

  String get desc{
    switch (this) {
      case GroupAssistantActionType.task:
        return "任务";
      case GroupAssistantActionType.confirmed:
        return "助手执行待确认反馈 已确认";
      case GroupAssistantActionType.canceled:
        return "助手执行待确认反馈 已取消";
      case GroupAssistantActionType.confirmExecution:
        return "确认执行";
      case GroupAssistantActionType.cancelExecution:
        return "取消执行";
      case GroupAssistantActionType.noPermission:
        return "无权限";
      case GroupAssistantActionType.unknown:
        return "未知";
      case GroupAssistantActionType.chat:
        return "助手闲聊";
      case GroupAssistantActionType.summarizesComments:
        return "助手总结评论";
      case GroupAssistantActionType.pendingTasks:
        return "助手总结待办任务";
      case GroupAssistantActionType.generateThingStart:
        return "生成物开始";
      case GroupAssistantActionType.generateThingEnd:
        return "生成物结束";
      case GroupAssistantActionType.updateVip:
        return "升级会员";
      case GroupAssistantActionType.generateThingError:
        return "生成物失败";
      case GroupAssistantActionType.viewPendingTasks:
        return "总结待办任务";
    }
  }

  bool get visible {
    switch (this) {
      case GroupAssistantActionType.confirmed:
      case GroupAssistantActionType.canceled:
      case GroupAssistantActionType.generateThingError:
        return false;
      default:
        return true;
    }
  }
}

enum AttachmentType {
  image,
  video,
  audio,
  file,
  unknown,
}

extension AttachmentTypeExtension on AttachmentType {
  int get value {
    switch (this) {
      case AttachmentType.image:
        return 1;
      case AttachmentType.audio:
        return 2;
      case AttachmentType.file:
        return 3;
      case AttachmentType.unknown:
        return 4;
      case AttachmentType.video:
        return 101;
    }
  }
}

enum InputType {
  text,
  audio,
}

extension InputTypeExtension on InputType {
  int get value {
    switch (this) {
      case InputType.text:
        return 0;
      case InputType.audio:
        return 1;
    }
  }
}

enum CreateJoinType{
  create,//1001 创建  1002 加入 1004修改群名称  群主：You created the group.   成员：You joined the group.
  join,
  editGroupName,
  no,
}

enum MessageSendingStatus {
  sendLoading,
  sendSuccess,
  sendFailed,
}

enum AgnesLoadingStatus {
  start,
  end,
  noLoading,
}

extension AgnesLoadingStatusExtension on AgnesLoadingStatus {
  int get value {
    switch (this) {
      case AgnesLoadingStatus.start:
        return 1;
      case AgnesLoadingStatus.end:
        return 2;
      case AgnesLoadingStatus.noLoading:
        return 0;
    }
  }
}