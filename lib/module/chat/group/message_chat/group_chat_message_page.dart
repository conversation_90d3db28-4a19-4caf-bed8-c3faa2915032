import 'dart:async';
import 'dart:math' as math;

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_logic.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_speech_send_message_utils.dart';
import 'package:new_agnes/module/chat/group/group_chat_room/CallFloatingButtonWidget.dart';
import 'package:new_agnes/module/chat/group/group_chat_room/group_chat_room_view.dart';
import 'package:new_agnes/module/chat/group/message_chat/widgets/group_chat_message_at_widget.dart';
import 'package:new_agnes/module/chat/group/message_chat/widgets/group_chat_message_item.dart';
import 'package:new_agnes/module/chat/model/grouplist/GroupUserBean.dart';
import 'package:new_agnes/utils/bgCover.dart';
import 'package:new_agnes/utils/event_bus.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';
import 'package:new_agnes/widget/message_input_widget.dart';
import 'package:scrollview_observer/scrollview_observer.dart';

import '../../../../generated/l10n.dart';
import '../../../../utils/photo_utils.dart';
import '../../../../widget/ComAppbar.dart';
import '../group_chat/agora_logic.dart';
import '../group_chat_room/group_rtc_select_people.dart';
import '../group_setting/group_set_page.dart';
import 'logic/group_chat_message_controller.dart';
import 'model/group_chat_message_model.dart';

class GroupChatMessagePage extends StatefulWidget {
  final String groupName; //群聊名称

  final String groupId;
  final String thirdId;

  final bool? agnes;

  final ChatConversationType type;

  const GroupChatMessagePage({
    super.key,
    required this.groupName,
    required this.groupId,
    required this.type,
    required this.thirdId,
    this.agnes,
  });

  @override
  State<GroupChatMessagePage> createState() => _GroupChatMessagePageState();
}

class _GroupChatMessagePageState extends State<GroupChatMessagePage>
    with RtcSpeechMessageCallback, WidgetsBindingObserver {
  final controller = Get.put(GroupChatMessageController());
  final rtcLogic = Get.find<RtcLogic>();
  final ScrollController _scrollController = ScrollController();
  final FocusScopeNode _pageScope = FocusScopeNode();
  final FocusNode _dummy = FocusNode(skipTraversal: true);
  late String _groupName;

  StreamSubscription? _messageSubscription;
  StreamSubscription? _groupInfoSubscription;
  StreamSubscription? _rtcSubscription;
  ScrollController _danumScrollController = ScrollController();
  late ListObserverController listObserverController;
  late ChatScrollObserver scrollObserver;

  AgoraLogic? findAgoraLogic() {
    bool isRegistered = Get.isRegistered<AgoraLogic>();
    if (isRegistered) {
      return Get.find<AgoraLogic>();
    } else {
      return null;
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _groupName = widget.groupName;
    RtcSpeechSendMessageUtils.instance.setRtcSpeechMessageCallback(this);

    /// 初始化 ListObserverController
    listObserverController =
        ListObserverController(controller: _scrollController)
          ..cacheJumpIndexOffset = false;

    /// 初始化 ChatScrollObserver
    scrollObserver = ChatScrollObserver(listObserverController)
      // 超过该偏移量才会触发保持当前聊天位置的功能
      ..fixedPositionOffset = 0
      ..toRebuildScrollViewCallback = () {
        // 这里可以重建指定的滚动视图即可
        // setState(() {});
      };
    controller
      ..listObserverController = listObserverController
      ..scrollController = _scrollController
      ..scrollObserver = scrollObserver
      ..thirdId = widget.thirdId
      ..type = widget.type;
    _scrollController.addListener(_onScroll);
    // 添加滚动监听器
    _scrollController.addListener(() {
      final screenHeight = MediaQuery.of(context).size.height;
      if (_scrollController.hasClients) {
        final offset = _scrollController.offset;
        final shouldShow = offset > screenHeight;
        if (controller.showScrollBottom.value != shouldShow) {
          controller.showScrollBottom.value = shouldShow;
        }
      }
    });

    _groupInfoSubscription = eventBus.on<GroupInfoEvent>().listen((value) {
      if (value.renameGroup) {
        if (value.msg != null) {
          controller.appendMessage(value.msg!, widget.thirdId);
        }
        setState(() {
          _groupName = value.groupName;
        });
      }
    });
    _rtcSubscription = eventBus.on<RtcEvent>().listen((value) {
      if (value.type == 1) {
        rtcLogic.exchangeSystemGroupIdByAgoraGroupId(widget.groupId);
      }
    });
    _messageSubscription = eventBus.on<AgoraMessageEvent>().listen((value) {
      controller.appendMessage(value.message, widget.thirdId);
      ChatClient.getInstance.chatManager
          .getConversation(widget.thirdId)
          .then((curCon) {
        curCon?.markAllMessagesAsRead();
      });
      // getChannelId();
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getChannelId();
      controller.productMap.clear();
      controller.getHistoryMessage(widget.thirdId, widget.type,
          successCallback: () {
        ChatClient.getInstance.chatManager
            .getConversation(widget.thirdId)
            .then((curCon) {
          curCon?.markAllMessagesAsRead();
        });
      });
      controller.getGroupMembers(widget.groupId);
      // 页面加载完成后把焦点给 dummy
      if (mounted) _pageScope.requestFocus(_dummy);
    });

    eventBus.on<String>().listen((value) {
      if (value == "showCustomOverlay") {
        controller.isInterception.value = true;
      }
      if (value == "hideCustomOverlay") {
        controller.isInterception.value = false;
        controller.refreshData();
      }
    });
    findAgoraLogic()?.addAgoraConnectedListener(
        this.hashCode.toString(), agoraConnectedListener);
  }

  void getChannelId() async {
    await rtcLogic.exchangeSystemGroupIdByAgoraGroupId(widget.groupId);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    controller.maxWidth = MediaQuery.of(context).size.width - 125.w;
    controller.screenHeight = MediaQuery.of(context).size.height;
  }

  @override
  Widget build(BuildContext context) {
    return FocusScope(
      node: _pageScope,
      child: buildMineBg(
          child: Scaffold(
        appBar: ComAppBar(
          context,
          _groupName,
          backgroundColor: Colors.transparent,
          textColor: Colors.white,
          titleFontSize: 16.sp,
          titleFontWeight: FontWeight.w400,
          showLeading: false,
          leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Container(
              padding: EdgeInsets.only(
                left: 16,
                right: 16,
              ),
              child: Image.asset(
                'assets/images/icon_back.webp',
                width: 41,
                height: 41,
                color: Colors.white,
              ),
            ),
          ),
          actions: widget.agnes == true
              ? []
              : [
                  rtcLogic.isFeatureRtcChat
                      ? Obx(() => !rtcLogic.isExistChannel.value
                          ? GestureDetector(
                              onTap: () {
                                if (rtcLogic.isExistChannel.value) {
                                  // showInfoToast(S.of(context).groupContacting);
                                } else {
                                  if (rtcLogic.isJoin) {
                                    // showInfoToast(S.of(context).calling);
                                  } else {
                                    Get.to(
                                      GroupRtcSelectPeoplePage(
                                        groupId: widget.groupId,
                                        groupName: widget.groupName,
                                        thirdId: widget.thirdId,
                                      ),
                                    );

                                    // Get.to(
                                    //     GroupChatRoomPage(
                                    //       groupId: widget.groupId,
                                    //       groupName: widget.groupName,
                                    //       thirdId: widget.thirdId,
                                    //       type: 0,
                                    //     ),
                                    //     transition: Transition.zoom,
                                    //     duration: const Duration(
                                    //         milliseconds: 250))
                                    //     ?.then((_) {
                                    //   rtcLogic
                                    //       .exchangeSystemGroupIdByAgoraGroupId(
                                    //       widget.groupId);
                                    // });
                                  }
                                }
                              },
                              child: Container(
                                  margin: EdgeInsets.only(right: 16.w),
                                  child: Image.asset(
                                    "assets/images/ic_call_rtc.png",
                                    width: 24.w,
                                    height: 24.h,
                                  )))
                          : SizedBox())
                      : SizedBox(),
                  GestureDetector(
                    onTap: () {
                      Get.to(() => GroupSetPage(
                            third_group_id: widget.groupId,
                            groupName: _groupName,
                          ));
                    },
                    child: Container(
                      margin: EdgeInsets.only(right: 16.w),
                      child: Image.asset(
                        "assets/images/ic_group_chat_more.png",
                        width: 24.w,
                        height: 24.h,
                      ),
                    ),
                  ),
                ],
        ),
        body: SafeArea(
            child: Container(
          child: Column(
            children: [
              Container(
                child: Obx(() {
                  return Visibility(
                    visible: rtcLogic.isExistChannel.value,
                    child: Padding(
                      padding: EdgeInsets.only(left: 16, right: 16, bottom: 1),
                      child: GradientBorderContainer.single(
                        strokeWidth: 1,
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFFFFFFF),
                            Color(0xFF988B9A),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.all(Radius.circular(14.w)),
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 14.w),
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.all(Radius.circular(14.w)),
                            color: Color(0x45FFFFFF),
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                "assets/icon/icon_phone_alert.png",
                                width: 24.w,
                                height: 24.w,
                              ),
                              Expanded(
                                flex: 5,
                                child: Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 10.w),
                                  child: Text(
                                    S.of(context).xMembersInVoice(
                                        rtcLogic.model?.members?.length ?? 0),
                                    style: TextStyle(
                                        fontSize: 14.sp, color: Colors.white),
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: TextButton(
                                    onPressed: () {
                                      if (rtcLogic.isJoin) {
                                        rtcLogic.hangUpOtherInvite(() {
                                          rtcLogic.leaveChannel().then((_) {
                                            rtcLogic.isExistChannel.value =
                                                !rtcLogic.isExistChannel.value;
                                            Get.back();
                                            CallFloatingButtonWidgetManager()
                                                .closeOverlay();
                                            Get.to(GroupChatRoomPage(
                                              groupId: widget.groupId,
                                              groupName: widget.groupName,
                                              thirdId: widget.thirdId,
                                            ))?.then((_) {
                                              rtcLogic
                                                  .exchangeSystemGroupIdByAgoraGroupId(
                                                      widget.groupId);
                                            });
                                          });
                                        });
                                      } else {
                                        rtcLogic.isExistChannel.value =
                                            !rtcLogic.isExistChannel.value;
                                        CallFloatingButtonWidgetManager()
                                            .closeOverlay();
                                        Get.to(GroupChatRoomPage(
                                          groupId: widget.groupId,
                                          groupName: widget.groupName,
                                          thirdId: widget.thirdId,
                                        ))?.then((_) {
                                          rtcLogic
                                              .exchangeSystemGroupIdByAgoraGroupId(
                                                  widget.groupId);
                                        });
                                      }
                                    },
                                    style: TextButton.styleFrom(
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.r),
                                      ),
                                    ),
                                    child: Container(
                                        width: double.infinity,
                                        height: 36.h,
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10.r),
                                          gradient: LinearGradient(
                                            colors: [
                                              const Color(0xFFFF91EE),
                                              const Color(0xFFFF3ADF)
                                            ],
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                          ),
                                        ),
                                        child: Text(
                                          S.of(context).join,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                              fontSize: 14.sp,
                                              color: Colors.black),
                                        ))),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
              // 消息列表
              Expanded(
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.topCenter,
                      child: ListViewObserver(
                        controller: listObserverController,
                        child: Obx(() => ListView.builder(
                            controller: _scrollController,
                            reverse: true,
                            shrinkWrap: true,
                            physics: ChatObserverBouncingScrollPhysics(
                                observer: scrollObserver),
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            itemCount: controller.messageData.length,
                            itemBuilder: (context, index) {
                              GroupChatMessageModel messageModel =
                                  controller.messageData[index];
                              return Column(
                                children: [
                                  Visibility(
                                      visible: controller.isLoadingHistory &&
                                          index ==
                                              controller.messageData.length - 1,
                                      child: SizedBox(
                                        width: 16.w,
                                        height: 16.h,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          backgroundColor: Colors.grey,
                                          valueColor: AlwaysStoppedAnimation<
                                                  Color>(
                                              Color.fromRGBO(255, 145, 238, 1)),
                                        ),
                                      )),
                                  GroupChatMessageItem(
                                    messageModel: messageModel,
                                    userId: controller.userId ?? "",
                                    agnes: widget.agnes ?? false,
                                  )
                                ],
                              );
                            })),
                      ),
                    ),
                    _buildScrollBottom()
                  ],
                ),
              ),
              _danumLay(), //弹幕
              // 输入框
              Obx(() => MessageInputWidget(
                    height: 84,
                    hintText: S.of(context).agnesForSummariesReportsOrSlides,
                    onSendMessage: (message, searchType, atModel) async {
                      //点击发送，如果有文件，需要先上传文件
                      List<MessageUploadModel> uploadModels =
                          await controller.onUploadFile();
                      controller.sendMessage(
                          widget.thirdId, uploadModels, message, atModel,widget.agnes?? false);
                    },
                    onAddFilePressed: () {
                      showSelectFileTypeDialog();
                    },
                    onRetryUpload: (index) {
                      controller.retryUploadFile(index);
                    },
                    voicePressed: rtcLogic.isFeatureRtcChat
                        ? () {
                            rtcLogic.exchangeSystemGroupIdByAgoraGroupId(
                                widget.groupId, callBack: () {
                              if (rtcLogic.isExistChannel.value) {
                                // showInfoToast(S.of(context).groupContacting);
                              } else {
                                if (rtcLogic.isJoin) {
                                  // showInfoToast(S.of(context).calling);
                                } else {
                                  Get.to(GroupChatRoomPage(
                                    groupId: widget.groupId,
                                    groupName: widget.groupName,
                                    thirdId: widget.thirdId,
                                    type: 0,
                                  ))?.then((_) {
                                    rtcLogic
                                        .exchangeSystemGroupIdByAgoraGroupId(
                                            widget.groupId);
                                  });
                                }
                              }
                            });
                          }
                        : null,
                    onTextChanged: (preText, text, atModel) {
                      List<GroupUserBean> groupUsers =
                          List.from(controller.groupUsers);
                      if (groupUsers.isEmpty) {
                        controller.getGroupMembers(widget.groupId);
                        return;
                      }

                      // 获取新插入的字符（支持在中间插入）
                      String? insertedChars = _getInsertedText(preText, text);

                      // 检查用户是否刚刚输入了@字符,并且没有@过人的情况才弹弹窗
                      if (insertedChars != null &&
                          insertedChars == "@" &&
                          atModel().atId == null) {
                        if (widget.agnes == true) return;
                        int agensIndex = -1;
                        for (int i = 0; i < groupUsers.length; i++) {
                          if (groupUsers[i].id == "0") {
                            agensIndex = i;
                            break;
                          }
                        }
                        GroupUserBean? agens =
                            agensIndex != -1 ? groupUsers[agensIndex] : null;
                        groupUsers.removeWhere(
                            (e) => e.id == controller.userId || e.id == "0");
                        ModalBottomSheetUtils.showContactPickerModal(
                            agens: agens,
                            context: context,
                            contacts: groupUsers,
                            onContactSelected: (List<GroupUserBean> contacts,
                                List<GroupUserBean> selects) {
                              if (selects.isNotEmpty) {
                                atModel.value = MessageAtModel(
                                    atId: selects.first.id,
                                    atName: selects.first.name);
                              }
                            });
                      }
                    },
                    uploadFiles: controller.uploadFiles(),
                  )),
            ],
          ),
        )),
      )),
    );
  }

  void _onScroll() {
    if (controller.noMoreData) return;
    if (!_scrollController.hasClients || controller.isLoadingHistory) return;

    // 由于ListView使用了reverse: true，所以当滚动到列表头部时，
    // position.pixels会接近maxScrollExtent
    final position = _scrollController.position;
    final threshold = 200.0; // 距离头部200像素时开始加载

    // 检查是否接近头部（考虑reverse的情况）
    if (position.pixels >= position.maxScrollExtent - threshold) {
      if (controller.isLoadingHistory) return;
      controller.getHistoryMessage(widget.thirdId, widget.type);
    }
  }

  void showSelectFileTypeDialog() {
    PhotoUtils.goToPhotoDialog(
        context, 230.h, title: S.of(context).chooseImage, 2, (path) {
      controller.onAddImage(path);
    }, isMulti: true);
  }

  Widget _buildScrollBottom() {
    return Obx(() => controller.showScrollBottom.value
        ? Positioned(
            right: 0,
            bottom: 8,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: () {
                    if (_scrollController.hasClients) {
                      final position = _scrollController.position;
                      final target = position.axisDirection == AxisDirection.up
                          ? position.minScrollExtent
                          : position.maxScrollExtent;
                      _scrollController.animateTo(
                        target,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                      );
                    }
                  },
                  behavior: HitTestBehavior.translucent,
                  child: Container(
                    padding: EdgeInsets.only(right: 8.w),
                    child: Image.asset(
                      'assets/groupChat/quote_scroll_icon.png',
                      width: 40.w,
                      height: 40.w,
                    ),
                  ),
                )
              ],
            ))
        : Container());
  }

  Widget _danumLay() {
    return Obx(() {
      return rtcLogic.isShowDanum.value &&
              rtcLogic.isJoin &&
              rtcLogic.currentGroupId == widget.groupId
          ? ContainerBox(
              boxColor: Color(0x1AFFFFFF),
              borderColor: Color(0x1AFFFFFF),
              radius: 180,
              padding: EdgeInsets.only(
                  left: 16.w, right: 16.w, top: 8.w, bottom: 8.w),
              // margin: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 8.w),
              alignment: Alignment.centerLeft,
              child: rtcLogic.lastSubtitle.value.name.isNotEmpty
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          constraints: BoxConstraints(maxWidth: 50.w),
                          child: Text(
                            '${rtcLogic.lastSubtitle.value.name}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w400,
                                color: Color(0xFF988B9A)),
                          ),
                        ),
                        Container(
                          constraints: BoxConstraints(maxWidth: 80.w),
                          child: Text(
                            '：',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w400,
                                color: Color(0xFF988B9A)),
                          ),
                        ),
                        Expanded(
                            child: Container(
                          height: 22.h,
                          child: SingleChildScrollView(
                            controller: _danumScrollController,
                            physics: NeverScrollableScrollPhysics(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  child: Obx(() {
                                    String content = rtcLogic.lastSubtitle.value
                                            ?.content.value ??
                                        '';
                                    // 在这里执行你需要的操作，比如滚动到底部
                                    if (content.isNotEmpty) {
                                      scrollGoToBottom();
                                    }
                                    return Text(
                                      '${rtcLogic.lastSubtitle.value.content.value}',
                                      textAlign: TextAlign.left,
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w400,
                                          height: 1.4,
                                          color: Colors.white),
                                    );
                                  }),
                                )
                              ],
                            ),
                          ),
                        )),
                      ],
                    )
                  : Container(
                      width: 1.sw,
                      child: Text(
                        S.of(context).transcriptionEnabledLoadingData,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: Color(0xFF988B9A),
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
            )
          : SizedBox();
    });
  }

  @override
  void onMessageReceived(
      String thirdGroupId, GroupChatMessageModel messageData) {
    if (widget.thirdId == thirdGroupId) {
      scrollObserver.standby();
      controller.isInterceptionData(messageData);
      // scrollObserver.standby();
      // controller.messageData.insert(0, messageData);
    }
  }

  /// 获取在文本中插入的新字符（支持在任意位置插入）
  String? _getInsertedText(String preText, String currentText) {
    if (currentText.length <= preText.length) {
      return null; // 没有新增字符或删除了字符
    }

    // 简单方法：找到第一个不同的位置
    int diffIndex = 0;
    int minLength = math.min(preText.length, currentText.length);

    // 找到从前往后第一个不同的字符位置
    while (
        diffIndex < minLength && preText[diffIndex] == currentText[diffIndex]) {
      diffIndex++;
    }

    // 如果前面都相同，说明是在末尾添加的
    if (diffIndex == preText.length) {
      return currentText.substring(preText.length);
    }

    // 从后往前找到第一个不同的位置
    int preIndex = preText.length - 1;
    int currentIndex = currentText.length - 1;

    while (preIndex >= diffIndex &&
        currentIndex >= diffIndex &&
        preText[preIndex] == currentText[currentIndex]) {
      preIndex--;
      currentIndex--;
    }

    // 返回插入的字符
    int insertStart = diffIndex;
    int insertEnd = currentIndex + 1;

    if (insertStart <= insertEnd) {
      return currentText.substring(insertStart, insertEnd);
    }

    return null;
  }

  void scrollGoToBottom() {
    if (_danumScrollController.hasClients) {
      Future.delayed(Duration(milliseconds: 1000), () {
        _danumScrollController.animateTo(
          _danumScrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  void agoraConnectedListener() {
    controller.retrySendMessage(widget.groupId);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _dummy.dispose();
    _pageScope.dispose();
    super.dispose();
    RtcSpeechSendMessageUtils.instance.removeRtcSpeechMessageCallback();
    _messageSubscription?.cancel();
    _groupInfoSubscription?.cancel();
    _rtcSubscription?.cancel();
    findAgoraLogic()?.removeAgoraConnectedListener(this.hashCode.toString());
  }

  var keyboardHeight = (false).obs;

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // final bottomInset = View.of(context).viewInsets.bottom;
    // if (bottomInset > 0 && !keyboardHeight.value) {
    //   keyboardHeight.value = true;
    //   if (mounted) {
    //     _scrollController.jumpTo(_scrollController.position.minScrollExtent);
    //   }
    // } else {
    //   if (bottomInset == 0 && keyboardHeight.value) {
    //     keyboardHeight.value = false;
    //     logError("软键盘收起");
    //   }
    // }
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;

    if (bottomInset > 0 && !keyboardHeight.value) {
      keyboardHeight.value = true;
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.minScrollExtent);
      }
    } else if (bottomInset == 0 && keyboardHeight.value) {
      keyboardHeight.value = false;
      logError("软键盘收起");
    }
  }
}
