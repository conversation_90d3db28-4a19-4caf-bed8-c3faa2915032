import 'dart:async';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/creat_chat/invite_people.dart';
import 'package:new_agnes/module/chat/widget/deep_research_lay.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/ocolor.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart' hide RefreshIndicator;

import '../../../../api/StorageService.dart';
import '../../../../dialog/message_dialog.dart';
import '../../../../generated/l10n.dart';
import '../../../../utils/event_bus.dart';
import '../../../home/<USER>/widgets/roles_bottom_navigation_bar.dart';
import '../message_chat/group_chat_message_page.dart';
import '../notifications/notifications_view.dart';
import 'agora_logic.dart';
import 'components/chat_item.dart';
import 'components/group_empty_widget.dart';
import 'components/group_nav_bar.dart';

class GroupChatPage extends StatefulWidget {
  static const TAG = "GroupChatPage";

  const GroupChatPage({super.key});

  @override
  State<GroupChatPage> createState() => _GroupChatPageState();
}

class _GroupChatPageState extends State<GroupChatPage> {
  final logic = Get.find<AgoraLogic>();

  StreamSubscription? _avatarSubscription;

  StreamSubscription? _groupInfoSubscription;

  final RefreshController _refreshController =
  RefreshController(initialRefresh: false);

  var timestamp = 0.obs;

  @override
  void initState() {
    super.initState();
    _avatarSubscription = eventBus.on<AgoraAvatarEvent>().listen((value) {
      logic.refreshAvatar();
    });
    _groupInfoSubscription = eventBus.on<GroupInfoEvent>().listen((value) {
      if (value.renameGroup) {
        if (value.msg != null) {
          String? id = value.msg?.conversationId;
          if (id != null) {
            logic.refreshGroupLastMessage(id);
          }
        }
      }
    });
    Future.microtask(() {
      try {
        precacheImage(
          CachedNetworkImageProvider(
              Get.find<StorageService>().getUserInfoData().avatarUrl?.value ??
                  ""),
          Get.context!,
        );
      } catch (e) {}
    });
  }

  @override
  void dispose() {
    _avatarSubscription?.cancel();
    _groupInfoSubscription?.cancel();
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GroupAppBar(
        title: 'CoVibe',
        onBallPressed: () {
          Get.to(NotificationsPage());
        },
        onMessagePressed: () async {
          Get.to(InvitePeople())?.then((group) {
            logError("Get.back : ${group != null}");
            if (group != null && (group is ChatGroup)) {
              toGroupChatMessage(group);
            }
          });
        },
      ),
      resizeToAvoidBottomInset: false,
      body: Obx(
        () => Container(
          margin: EdgeInsets.only(bottom: CmUtils.bottomBarHeight.value),
          child: Column(
            children: [
              Expanded(
                  child: SmartRefresher(
                      controller: _refreshController,
                      header: ClassicHeader(
                        idleText: '',
                        releaseText: '',
                        refreshingText: S.of(context).loading,
                        completeText: '',
                        failedText: '',
                        textStyle: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                      enablePullUp: false,
                      enablePullDown: true,
                      onRefresh: () async {
                        if(logic.mAppKey.value.isEmpty) {
                          showFailToast("Chat Server not init");
                        } else {
                          if (DateTime.now().millisecondsSinceEpoch -
                              timestamp.value >
                              30000) {
                            logic.isLoading.value = false;
                            logic.getJoinedGroupFromServer(callback: (size) {
                              _refreshController.refreshCompleted(
                                  resetFooterState: true);
                              timestamp.value =
                                  DateTime.now().millisecondsSinceEpoch;
                            });
                          } else {
                            await Future.delayed(Duration(seconds: 1));
                            _refreshController.refreshCompleted(
                                resetFooterState: true);
                          }
                        }
                      },
                      child: CustomScrollView(slivers: [
                        Obx(() {
                          return SliverToBoxAdapter(
                            child: logic.agora_connect_status.value.isEmpty
                                ? SizedBox()
                                : Container(
                                    height: 36,
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 16),
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    child: Container(
                                      decoration: BoxDecoration(
                                          color: Color.fromRGBO(
                                              255, 211, 211, 0.7),
                                          borderRadius:
                                              BorderRadius.circular(10)),
                                      alignment: Alignment.center,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Image.asset(
                                            'assets/images/ic_tip.png',
                                            width: 20,
                                            height: 20,
                                          ),
                                          const SizedBox(
                                            width: 8,
                                          ),
                                          Text(
                                            S
                                                .of(context)
                                                .serverConnectionFailed,
                                            style: getStyle(
                                                color: Color.fromRGBO(
                                                    229, 38, 38, 1),
                                                fontSize: 14),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                          );
                        }),
                        Obx(() {
                          if (!logic.loadComplete.value) {
                            return SliverFillRemaining(
                              child: Center(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(height: 120),
                                    Image.asset(
                                      'assets/groupChat/group_chat_empty.png',
                                      width: 262,
                                      height: 243,
                                      fit: BoxFit.cover,
                                    ),
                                    SizedBox(height: 32),
                                    SizedBox(
                                      width: 30,
                                      height: 30,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 3,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                        backgroundColor: Color(0xffFF91EE),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            );
                          }
                          if (logic.chatGroups.isEmpty) {
                            return SliverFillRemaining(
                              child: GroupEmptyWidget(
                                onButtonPressed: () async {
                                  Get.to(InvitePeople())?.then((group) {
                                    logError("Get.back : ${group != null}");
                                    if (group != null && (group is ChatGroup)) {
                                      toGroupChatMessage(group);
                                    }
                                  });
                                },
                              ),
                            );
                          } else {
                            return SliverPadding(
                              padding: EdgeInsets.symmetric(vertical: 8),
                              sliver: SliverList(
                                  delegate: SliverChildBuilderDelegate(
                                (context, index) {
                                  final group = logic.chatGroups[index];
                                  bool last =
                                      index == logic.chatGroups.length - 1;

                                  bool isGroupType0 = logic.isGroupTypeZero(group);
                                  return GestureDetector(
                                    onTap: () async {
                                      toGroupChatMessage(group,agnes: isGroupType0);
                                    },
                                    child: ChatItem(
                                      key: ValueKey(group.groupId),
                                      owner: group.owner ?? "",
                                      id: group.groupId ?? "",
                                      name: group.name ?? "",
                                      isGroupType0: isGroupType0,
                                      last: last,
                                      onPin: () {},
                                      onDelete: () {
                                        showDialog(
                                            context: Get.context!,
                                            builder: (context) {
                                              return MessageDialog(
                                                () {
                                                  Get.back();
                                                },
                                                () async {
                                                  logic.deleteConversation(
                                                      group.groupId);
                                                },
                                                data: S
                                                    .of(Get.context!)
                                                    .onceDeletedGroupChatHistoryWillBeCleared,
                                                title: "",
                                                onLeftName:
                                                    S.of(Get.context!).cancel,
                                                onRightName:
                                                    S.of(Get.context!).delete,
                                                height: 210,
                                                isTitleCenter: true,
                                              );
                                            });
                                      },
                                    ),
                                  );
                                },
                                childCount: logic.chatGroups.length,
                              )),
                            );
                          }
                        }),
                        SliverToBoxAdapter(
                          child: BottomHeightLay(context,
                              height: RolesBottomNavigationBar
                                  .getNavigationBarHeight(context)),
                        ),
                      ]))),
              // BottomHeightLay(context,
              //     height: CmUtils.getBottomBarHeight(context) -
              //         RolesBottomNavigationBar.getNavigationBarHeight(context))
            ],
          ),
        ),
      ),
    );
  }

  void toGroupChatMessage(ChatGroup group,{bool? agnes}) async {
    final result = await Get.to(
      () => GroupChatMessagePage(
        groupName: group.name ?? "",
        groupId: group.groupId ?? "",
        thirdId: group.groupId ?? "",
        agnes: agnes,
        type: ChatConversationType.GroupChat,
      ),
    );
    if (result == null) {
      logic.unreadCounts[group.groupId] = 0;
      logic.mentionMeCache[group.groupId] = false;
      logic.getNewGroupInfo(group.groupId);
      // if (await ChatClient.getInstance.isConnected()) {
      //   ChatClient.getInstance.chatManager
      //       .getConversation(group.groupId)
      //       .then((con) {
      //     con?.markAllMessagesAsRead().then((asRead) {
      //       if (mounted) {
      //         // logic.unreadCounts[group.groupId] = 0;
      //         // logic.mentionMeCache[group.groupId] = false;
      //         // logic
      //         //     .getLastMessage(group.groupId)
      //         //     .then((vv) {
      //         //   if (vv != null) {
      //         //     logic.groupLastMessage[group.groupId] =
      //         //         vv;
      //         //   }
      //         // });
      //       }
      //     });
      //   });
      // }
    }
  }
}
