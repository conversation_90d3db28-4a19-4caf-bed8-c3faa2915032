import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/loading_util.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../../../../api/Api.dart';
import '../../../../api/ApiProvider.dart';
import '../../../../dialog/common_confirm_dialog.dart';
import '../../../../utils/inAppLogUtil.dart';
import '../../model/grouplist/GroupInfoBean.dart';
import '../../model/grouplist/GroupUserBean.dart';
import '../../model/grouplist/SearchUserBean.dart';

class GroupController extends GetxController {
  //群聊列表
  var groups = <ChatConversation>[].obs;

  @override
  void onInit() {
    super.onInit();
  }

  // //获取群聊列表
  getGroupList() async {
    Response res = await Get.find<ApiProvider>().get(Api.groupList);
    List<GroupInfoBean> beans =
        convertJson(res, "groups", (json) => GroupInfoBean.fromJson(json));
    for (var b in beans) {
      Log.e("群组信息${b.toJson()}");
      if (b.groupId != null) {
        b.conversation = await addParam(b.groupId!);
      }
    }
    // groups.value = beans;
  }

  Future<ChatConversation?> addParam(String groupId) async {
    if (!(await ChatClient.getInstance.isConnected())) {
      return null;
    }

    LoadingUtil.show();
    final result =
        await ChatClient.getInstance.chatManager.getConversation(groupId);

    return result;
  }

  // 缓存用户信息和群信息
  final userNameCache = <String, ChatUserInfo>{}.obs;
  final groupNameCache = <String, String>{}.obs;

  // 预加载所有会话的名称到缓存
  Future<void> _preloadNames(List<ChatConversation> conversations) async {
    //筛选出非群聊的会话
    List<ChatConversation> chatConversations = conversations
        .where((conv) => conv.type == ChatConversationType.Chat)
        .toList();
    List<ChatConversation> groupChatConversations = conversations
        .where((conv) => conv.type != ChatConversationType.Chat)
        .toList();
    userNameCache.value =
        await _getUserName(chatConversations.map((conv) => conv.id).toList());
    for (var conv in groupChatConversations) {
      if (conv.type == ChatConversationType.GroupChat) {
        // 群聊：获取群名称
        if (!groupNameCache.containsKey(conv.id)) {
          String name = await _getGroupName(conv.id);
          groupNameCache[conv.id] = name;
        }
      }
    }
    logError("group信息$groupNameCache");
  }

  // 获取用户名
  Future<Map<String, ChatUserInfo>> _getUserName(List<String> userIds) async {
    Map<String, ChatUserInfo> userInfos = {};
    try {
      userInfos = await ChatClient.getInstance.userInfoManager
          .fetchUserInfoById(userIds);
      userInfos.forEach((key, value) {
        logError("$key用户名缓存：${value.avatarUrl}");
      });
      return userInfos;
    } catch (e) {
      print("获取用户名失败: $e");
      return userInfos;
    }
  }

  // 获取群名称
  Future<String> _getGroupName(String groupId) async {
    try {
      // 尝试从本地缓存获取
      ChatGroup? group =
          await ChatClient.getInstance.groupManager.getGroupWithId(groupId);

      logError("扩展群名称${group?.toJson()}");

      if (group != null && group.name?.isNotEmpty == true) {
        return group.name!;
      }
      return group?.name ?? groupId;
    } catch (e) {
      print("获取群名称失败: $e");
      return groupId; // 失败时使用群ID
    }
  }

  //修改群聊名称
  changeGroupName(String groupId, String name) async {
    Response res = await Get.find<ApiProvider>()
        .post(Api.renameGroup, {"group_id": groupId, "new_name": name});

    logError(res.body);
  }

  void markAsUnread(String id) {
    Get.snackbar('提示', '已标记未读');
  }

  void hideGroup(String id) {
    Get.snackbar('提示', '已隐藏');
  }

  void deleteGroup(String id, BuildContext context) {
    CommonConfirmDialog.show(
      context,
      content: 'Once deleted, group chat history will be cleared.',
      leftButtonText: 'Cancel',
      rightButtonText: 'Delete',
      onLeftPressed: () {
        // 取消逻辑
      },
      onRightPressed: () {
        // 删除逻辑
      },
    );
  }
}

typedef FromJson<T> = T Function(Map<String, dynamic> json);

List<T> convertJson<T>(Response res, String key, FromJson<T> fromJson) {
  try {
    if (res.statusCode != 200 || res.body == null) return [];

    final body = res.body as Map<String, dynamic>;
    if (!body.containsKey(key)) return [];

    final json = body[key];
    if (json == null) return [];
    if (json is List) {
      final list = <T>[];
      for (var o in json) {
        if (o is Map<String, dynamic>) {
          list.add(fromJson(o));
        }
      }
      return list;
    } else if (json is Map<String, dynamic>) {
      return [fromJson(json)];
    } else {
      return [];
    }
  } catch (e) {
    logError(e.toString());
    rethrow;
  }
}
