import 'dart:async';
import 'dart:convert';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_group_person_data_model.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_logic.dart';
import 'package:new_agnes/utils/event_bus.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../../../../api/Api.dart';
import '../../../../api/ApiProvider.dart';
import '../../../../api/StorageService.dart';
import '../../../../generated/l10n.dart';
import '../../../../utils/NetworkStatusManager.dart';
import '../message_chat/model/group_chat_message_model.dart';

enum EventType {
  group_chat,
  group_custom_noti,
  group_chat_invite,
  rtc_group_invite,
  UNIQUE_HANDLER_ID,
  rtc_group_cancel,
}

abstract class ChatEvent {}

/// 群信息修改事件
class GroupInfoEvent extends ChatEvent {
  final String groupName;
  final bool addMember;
  final bool renameGroup;

  final ChatMessage? msg;

  GroupInfoEvent(
      {this.groupName = '',
      this.addMember = false,
      this.renameGroup = false,
      this.msg});
}

class AgoraAvatarEvent extends ChatEvent {}

/// 事件
class AgoraMessageEvent extends ChatEvent {
  final bool success;
  final ChatMessage message;
  final int type; //1 消息  3消息失败server unAvailable

  AgoraMessageEvent({
    required this.success,
    required this.message,
    required this.type,
  });
}

class AgoraLogic extends GetxController {
  var mAppKey = ''.obs;

  var isJoined = false.obs;

  var hasJoinedTips = false.obs;

  final int weekAgo =
      DateTime.now().millisecondsSinceEpoch - 7 * 24 * 60 * 60 * 1000;

  var loadComplete = false.obs; //首次加载完成

  static const CONN_STATUS = "failed";

  var agora_connect_status = CONN_STATUS.obs;

  late final agnesAvatar;

  var agnesGroupId = "".obs;

  setAppKey(String _appKey) {
    mAppKey.value = _appKey;
  }

  ///初始化agoraChat群聊sdk
  Future<bool> initSDK(String _appKey) async {
    try {
      ChatOptions options = ChatOptions(
          appKey: _appKey,
          autoLogin: false,
          autoAcceptGroupInvitation: true,
          debugMode: true);
      Get.find<RtcLogic>().initRtc();
      await ChatClient.getInstance.init(options);
      addChatListener();
      // await Future.delayed(Duration(milliseconds: 300));
      return true;
    } catch (e) {
      showFailToast("SDK初始化失败");
      return false;
    }
  }

  Future<void> sign() async {
    if (await ChatClient.getInstance.isConnected()) {
      showFailToast("已经是登陆状态");
    } else {
      getAgoraInfo(tokenSuccess: (userId, token) {
        signIn(userId, token, signSuccess: () async {});
      });
    }
  }

  // 获取声网的token和userId
  Future<void> getAgoraInfo(
      {Function(String userId, String token)? tokenSuccess}) async {
    late Response res;
    res = await Get.find<ApiProvider>().post(Api.getAgoraInfo, {});

    if (res.isOk) {
      final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
      if (res.statusCode == 200) {
        if (responseBody['agora_user_id'] != null &&
            responseBody['token'] != null &&
            responseBody['agora_user_id'].toString().isNotEmpty &&
            responseBody['token'].toString().isNotEmpty) {
          tokenSuccess?.call(
              responseBody['agora_user_id'], responseBody['token']);
        } else {
          showFailToast("登陆Agora失败!agora数据为空");
        }
      } else {
        showFailToast("登陆Agora失败!${res.statusCode}");
      }
    } else {
      showFailToast("获取Agora userid信息失败,请求接口异常");
    }
  }

  // 声网im登录
  void signIn(String userId, String token, {Function()? signSuccess}) async {
    agoraUserId.value = userId;
    agoraToken.value = token;
    try {
      //缓存声网用户Id
      Get.find<StorageService>().setAgoraInfo(userId, token);
      await ChatClient.getInstance.loginWithToken(
        userId,
        token,
      );
      signSuccess?.call();
      print("agora: login succeed, userId: ${userId}");
    } on ChatError catch (e) {
      isJoined.value = false;
      print("login failed, code: ${e.code}, desc: ${e.description}");
      if (e.code == 200 &&
          e.description.contains("The user is already logged in")) {
        signOut(resign: true);
      } else if (e.code == 108 && e.description.contains("Token expired")) {
        signOut(resign: true);
      } else if (e.code == 204 && e.description.contains("does not exist")) {
        showFailToast("agora-${e.description}");
      } else if (e.code == 218 &&
          e.description.contains("Another user is already logged in")) {
        logError("agora-error: ${e.description}");
        signOut(resign: true);
      } else {
        showFailToast("agora-${e.description}");
      }
    }
  }

  // 声网im登出
  void signOut({bool? resign}) async {
    try {
      if (ChatClient.getInstance != null) {
        await ChatClient.getInstance.logout(true);
        debugPrint("sign out succeed");
        if (resign == true) {
          sign();
        }
      }
    } catch (e) {
      debugPrint("sign out failed, code: ${e.toString()}");
    }
  }

  agoraDispose() {
    ChatClient.getInstance.chatManager
        .removeMessageEvent(EventType.group_chat.name);
    ChatClient.getInstance.chatManager
        .removeEventHandler(EventType.group_chat.name);
  }

  //本地消息使用
  ChatMessage getCustomMessageBody(
      String message, String groupId, int create_join_type,
      {Map<String, dynamic>? translation}) {
    return ChatMessage.createCustomSendMessage(
        targetId: groupId,
        event: EventType.group_chat.name,
        chatType: ChatType.GroupChat,
        params: {
          "app_data": jsonEncode({
            "third_chat_group_id": groupId,
            "sender_user": {
              "id": Get.find<StorageService>().getUserInfoData().id,
            },
            "create_join_type": create_join_type,
            "content": message,
            "content_type": 1,
            "message_type": 1,
            "translation": translation
          })
        });
  }

  ///重新发送消息
  resendMessageWithId(String msgId, String groupId) async {
    try {
      ChatMessage? message = await getTargetMessageWithId(msgId, groupId);
      if (message != null) {
        ChatClient.getInstance.chatManager.sendMessage(message);
      }
    } catch (e) {
      showFailToast("resend error${e.toString()}");
    }
  }

  ///重新发送消息
  resendMessage(ChatMessage message) {
    try {
      ChatClient.getInstance.chatManager.sendMessage(message);
    } catch (e) {
      showFailToast("resend error${e.toString()}");
    }
  }

  ///根据消息id查询指定群组中的消息
  Future<ChatMessage?> getTargetMessageWithId(
      String msgId, String groupId) async {
    ChatConversation? conversation =
        await ChatClient.getInstance.chatManager.getConversation(groupId);
    if (conversation != null) {
      ChatMessage? message = await conversation.loadMessage(msgId);
      return message;
    }
    return null;
  }

  sendTextMessageMap(
    String thirdGroupId,
    ChatType type,
    Map<String, dynamic> map, {
    Function()? success,
    Function(String error)? fail,
  }) async {
    Log.e("创建的id${thirdGroupId}");
    try {
      var customMessage = ChatMessage.createCustomSendMessage(
          targetId: thirdGroupId,
          event: EventType.group_chat.name,
          chatType: type,
          params: {"app_customexts": jsonEncode(map)});

      customMessage.from = agoraUserId.value;
      customMessage.direction = MessageDirection.SEND;

      logError("发送内容${customMessage}");
      await ChatClient.getInstance.chatManager.sendMessage(customMessage);
      success?.call();
    } catch (e) {
      fail?.call(e.toString());
      logError("发送消息失败:${e}");
    }
  }

  // Receive messages
  void onMessagesReceived(List<ChatMessage> messages) {
    for (var msg in messages) {
      switch (msg.body.type) {
        case MessageType.TXT:
          {
            ChatTextMessageBody body = msg.body as ChatTextMessageBody;
            debugPrint(
              "receive text message: ${body.content}, from: ${msg.from}",
            );
          }
          break;
        case MessageType.IMAGE:
          {
            debugPrint(
              "receive image message, from: ${msg.from}",
            );
          }
          break;
        case MessageType.VIDEO:
          {
            debugPrint(
              "receive video message, from: ${msg.from}",
            );
          }
          break;
        case MessageType.LOCATION:
          {
            debugPrint(
              "receive location message, from: ${msg.from}",
            );
          }
          break;
        case MessageType.VOICE:
          {
            debugPrint(
              "receive voice message, from: ${msg.from}",
            );
          }
          break;
        case MessageType.FILE:
          {
            debugPrint(
              "receive image message, from: ${msg.from}",
            );
          }
          break;
        case MessageType.CUSTOM:
          {
            eventBus
                .fire(AgoraMessageEvent(success: true, message: msg, type: 1));
            debugPrint(
              "receive custom message, from:${msg.conversationId}=${msg.chatType}= ${msg.body.toJson()}",
            );
            if (msg.conversationId != null &&
                msg.chatType == ChatType.GroupChat) {
              String gid = msg.conversationId!;
              Get.find<StorageService>().removeDeleteConversationId(gid);
              // 刷新未读数
              getUnreadCount(gid).then((count) {
                unreadCounts[gid] = count ?? 0;
              });

              chatMessageHasMentionMe(msg, gid);
              mentionMeCache.refresh();

              // 刷新最后一条消息
              groupLastMessage[gid] = msg;
              groupLastMessage.refresh();

              // 把该群移动到顶部
              int index = chatGroups.indexWhere((e) => e.groupId == gid);
              if (index >= 0) {
                final g = chatGroups[index];
                chatGroups.removeAt(index);
                bool gt0 = isGroupTypeZero(g);
                chatGroups.insert(gt0 ? 0 : 1, g);

                final body = msg.body.toJson();
                Map<dynamic, dynamic> params = body['params'];
                if (params["group_custom_noti"] != null) {
                  logError("修改群名称消息");
                  ChatClient.getInstance.groupManager
                      .getGroupWithId(gid)
                      .then((newGroup) {
                    int ssIndex =
                        chatGroups.indexWhere((e) => e.groupId == gid);
                    if (newGroup != null && ssIndex >= 0) {
                      chatGroups[ssIndex] = newGroup;
                      chatGroups.refresh();
                    }
                  });
                }
              } else {
                ChatClient.getInstance.groupManager
                    .getGroupWithId(gid)
                    .then((newGroup) {
                  if (newGroup != null) {
                    int ssIndex =
                        chatGroups.indexWhere((e) => e.groupId == gid);
                    if (ssIndex >= 0) {
                      chatGroups.removeAt(ssIndex);
                    }
                    bool gt0 = isGroupTypeZero(newGroup);
                    chatGroups.insert(gt0 ? 0 : 1, newGroup);
                    getGroupMember(gid).then((ss) {
                      groupAvatars[gid] = ss;
                      groupAvatars.refresh();
                    });
                  }
                });
              }
            }
            Get.find<RtcLogic>().addRtcMessageListener(msg);
          }
          break;
        case MessageType.CMD:
          {
            // Receiving command messages does not trigger the `onMessagesReceived` event, but triggers the `onCmdMessagesReceived` event instead.
          }
          break;
        case MessageType.COMBINE:
          throw UnimplementedError();
      }
    }
  }

  ///删除会话
  deleteConversation(String gid) async {
    ChatClient.getInstance.chatManager.getConversation(gid).then((getCon) {
      ChatClient.getInstance.chatManager.deleteConversation(gid).then((value) {
        ChatClient.getInstance.chatManager
            .deleteRemoteConversation(gid,
                conversationType:
                    getCon?.type ?? ChatConversationType.GroupChat)
            .then((del) {
          int index = chatGroups.indexWhere((t) => t.groupId == gid);
          if (index >= 0) {
            try {
              Get.find<StorageService>().setDeleteConversation(gid);
            } catch (e) {}
            chatGroups.removeAt(index);
            Get.back();
          }
        });
      });
    });
  }

  ///重新排序
  sortChatGroups() {
    chatGroups.sort((a, b) {
      final timeA = groupLastMessage[a.groupId]?.serverTime;
      final timeB = groupLastMessage[b.groupId]?.serverTime;

      if (timeA != null && timeB != null) return timeB.compareTo(timeA);
      if (timeA != null && timeB == null) return -1;
      if (timeA == null && timeB != null) return 1;
      return 0;
    });
  }

  StreamSubscription? _connectivitySubscription;

  @override
  void onInit() {
    super.onInit();
    getAgnesMsg();
    _connectivitySubscription?.cancel();
    _connectivitySubscription =
        eventBus.on<NetworkStatusEvent>().listen((result) {
      bool hasNet = result.status
              .where((t) =>
                  t == ConnectivityResult.mobile ||
                  t == ConnectivityResult.wifi)
              .toList()
              .length >
          0;
      if (hasNet) {
        print("agora: 网络恢复");
        if (agora_connect_status.value.isNotEmpty && mAppKey.value.isNotEmpty) {
          print("agora: 网络恢复重连");
          signOut(resign: true);
        }
      } else {
        agora_connect_status.value = CONN_STATUS;
        logError("agora: 网络断开");
      }
    });
  }

  ///获取agnes头像
  getAgnesMsg() async {
    final response =
        await Get.find<ApiProvider>().post("${Api.rtcGroupJoinUser}1", {});

    if (response.statusCode == 200) {
      if (response.body != null) {
        try {
          RtcGroupPersonDataModel rtcGroupPersonDataModel =
              RtcGroupPersonDataModel.fromJson(response.body);
          agnesAvatar = rtcGroupPersonDataModel.user.avatarUrl;
        } catch (e) {
          agnesAvatar = "";
        }
      }
    }
  }

  preOut(String u, String t) {
    try {
      ChatClient.getInstance.logout(true).then((logout) {
        logError("agora: logout--signIn");
        signIn(u, t);
      });
    } on ChatError catch (e) {
      logError("agora: signIn${e.toString()}");
      signIn(u, t);
    }
  }

  @override
  void onClose() {
    _timers.clear();
    _connectivitySubscription?.cancel();
    super.onClose();
  }

  createAgnesGroup() async {
    final Map<String, dynamic> data = {
      'source': 2,
      'is_pre_existing_artifact': false,
      'cardinality': 1,
      'group_type': 0,
      'group_name': "Agnes",
    };
    Response res = await Get.find<ApiProvider>().post(
      Api.create_group,
      data,
    );
    if(res.isOk && res.statusCode == 200 && res.body != null && res.body is Map) {
      final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
      agnesGroupId.value = responseBody['third_chat_group_id']??"";
      if(agnesGroupId.value.isNotEmpty) {
        ChatClient.getInstance.chatManager.pinConversation(conversationId: agnesGroupId.value, isPinned: true);
      }
      print("agora: 创建agnes群聊${res.body}");
    }
  }

  final Map<String, RxString> _timers = {};

  // 添加监听会话
  void addChatListener() async {
    try {
      ChatClient.getInstance.chatManager.addEventHandler(
        EventType.group_chat.name,
        ChatEventHandler(
          onMessagesReceived: onMessagesReceived,
          // 消息已读事件
          onMessagesRead: (List<ChatMessage> messages) {
            _handleMessage(messages, "已读");
          },
          onMessageContentChanged: (message, str, code) {
            Log.e("agora==消息内容变化");
          },
          // 消息被撤回事件
          onMessagesRecalled: (List<ChatMessage> messages) {
            _handleMessage(messages, "撤回");
          },
          onConversationsUpdate: () {
            Log.e("agora==会话刷新");
          },
          onConversationRead: (from, to) {
            Log.e("agora==会话已读");
          },
        ),
      );

      ChatClient.getInstance.groupManager.addEventHandler(
          "1",
          ChatGroupEventHandler(
            onInvitationReceivedFromGroup:
                (groupId, groupName, inviterId, reason) {
              logError("agora==当前用户收到入群邀请的回调");
            },
            onInvitationAcceptedFromGroup: (groupId, invitee, reason) {
              logError("agora==当前用户收到对端用户同意入群邀请触发的回调");
            },
            onAutoAcceptInvitationFromGroup:
                (groupId, inviter, inviteMessage) async {
              logError("agora==当前用户自动同意入群邀请的回调$groupId");
              ChatClient.getInstance.groupManager
                  .getGroupWithId(groupId)
                  .then((group) {
                if (group != null) {
                  bool gt0 = isGroupTypeZero(group);
                  int index =
                      chatGroups.indexWhere((e) => e.groupId == group.groupId);
                  if (index < 0) {
                    chatGroups.insert(gt0 ? 0 : 1, group);
                  } else {
                    chatGroups.removeAt(index);
                    chatGroups.insert(gt0 ? 0 : 1, group);
                  }

                  final eq = group.owner == agoraUserId.value;
                  logError("agora==当前用户自动同意入群邀请的回调$eq");
                  final msg = getCustomMessageBody(
                      eq
                          ? S.current.youCreatedTheGroup
                          : S.current.youJoinedTheGroup,
                      groupId,
                      eq ? 1001 : 1002,
                      translation: {
                        "key": eq ? "createdGroup" : "joinedGroup"
                      });
                  if (group.owner != agoraUserId.value) {
                    hasJoinedTips.value = true;
                  }

                  if (!gt0) {
                    ChatClient.getInstance.chatManager
                        .getConversation(groupId)
                        .then((conv) {
                      msg.status = MessageStatus.SUCCESS;
                      conv?.insertMessage(msg);
                    });
                  }

                  groupLastMessage[groupId] = msg;
                  groupLastMessage.refresh();
                  unreadCounts[groupId] = 0;
                  unreadCounts.refresh();
                  getGroupMember(groupId).then((ss) {
                    groupAvatars[groupId] = ss;
                    groupAvatars.refresh();
                  });
                }
              });
            },
            onMemberJoinedFromGroup: (groupId, member) {
              logError("agora==当前新成员入群");

              if (!_timers.containsKey(groupId)) {
                _timers[groupId] = ''.obs;

                debounce<String>(
                  _timers[groupId]!,
                  (_) {
                    logError("agora==当前新成员入群刷新");
                    ChatClient.getInstance.groupManager
                        .getGroupWithId(groupId)
                        .then((newMember) {
                      if (newMember != null) {
                        getGroupMember(groupId).then((ss) {
                          groupAvatars[groupId] = ss;
                          groupAvatars.refresh();
                        });
                        int index = chatGroups
                            .indexWhere((e) => e.groupId == newMember.groupId);
                        if (index >= 0) {
                          chatGroups[index] = newMember;
                          chatGroups.refresh();
                        } else {
                          chatGroups.insert(1, newMember);
                        }
                      }
                    });
                  },
                  time: const Duration(milliseconds: 500),
                );
              }
              _timers[groupId]?.value = DateTime.now().toIso8601String();
            },
            onMemberExitedFromGroup: (groupId, member) {
              logError("agora==当前群成员退出群");
              ChatClient.getInstance.groupManager
                  .getGroupWithId(groupId)
                  .then((newMember) {
                if (newMember != null) {
                  getGroupMember(groupId).then((ss) {
                    groupAvatars[groupId] = ss;
                    groupAvatars.refresh();
                  });
                }
              });
            },
            onUserRemovedFromGroup: (groupId, member) {
              logError("agora==当前群成员被移除");
              //删除群聊本地数据
              ChatClient.getInstance.chatManager.deleteConversation(groupId);
              unreadCounts.remove(groupId);
              groupLastMessage.remove(groupId);
              groupAvatars.remove(groupId);
              mentionMeCache.remove(groupId);
              chatGroups.removeWhere((element) => element.groupId == groupId);
              showInfoToast(S.current.youHaveBeenRemovedFromTheGroupChat);
              Get.until((route) => route.isFirst);
            },
          ));

      ChatClient.getInstance.addConnectionEventHandler(
        //_connectionEventHandler
        EventType.group_chat.name,
        ConnectionEventHandler(
            onConnected: () {
              print("debug initSDK 已连接到服务器");
              ChatClient.getInstance.startCallback();
              isJoined.value = true;
              isLoading.value = false;
              agora_connect_status.value = "";
              getJoinedGroupFromServer();

              if (listenerMap.isNotEmpty) {
                listenerMap.forEach((key, value) {
                  value.call();
                });
              }
            },
            onDisconnected: () {
              agora_connect_status.value = CONN_STATUS;
              isJoined.value = false;
            },
            onTokenWillExpire: onTokenWillExpire,
            onTokenDidExpire: onTokenDidExpire,
            onUserAuthenticationFailed: () {
              showFailToast("Agora: 用户鉴权失败,请重新登录");
            },
            onUserDidLoginFromOtherDevice: (state) {
              showFailToast("在其他设备登陆");
            },
            onUserKickedByOtherDevice: () {
              showFailToast("被其他设备踢出");
            }),
      );

      ChatClient.getInstance.chatManager.addMessageEvent(
        //cacheHandleMap
        EventType.group_chat.name,
        ChatMessageEvent(
          onSuccess: (msgId, msg) {
            logError("agora==send message: $msg");
          },
          onError: (msgId, msg, error) {
            if (error.code == 217) {
              showFailToast("用户已在其他设备登陆");
            } else if (error.code == 301) {
              showFailToast("请求服务超时");
            } else if (error.code == 508) {
              if (error.description.contains("custom internal error")) {
                showFailToast("custom internal error,send failed");
              }
            } else if (error.code == 300 || error.code == 201) {
              //send message failed, code: 300, desc: Server is unreachable  失败后，消息会入库
              eventBus.fire(
                  AgoraMessageEvent(success: false, message: msg, type: 3));
            }
            if (error.code == 508) {
              removeMessage(msg, msgId);
            }
            debugPrint(
              "send message failed, code: ${error.code}, desc: ${error.description}",
            );
          },
        ),
      );
    } catch (e) {
      logError("添加监听会话失败:${e}");
    }
  }

  removeMessage(ChatMessage msg, String msgId) {
    if (msg.conversationId != null) {
      ChatClient.getInstance.chatManager
          .getConversation(msg.conversationId ?? "")
          .then((del) {
        del?.deleteMessage(msgId);
      });
    }
  }

  void _handleMessage(List<ChatMessage> messages, String str) {
    for (var msg in messages) {
      debugPrint("agora==== message, from: ${msg.from} is $str");
    }
  }

  /*拉取本地历史消息  start*/
  Future<ChatCursorResult<ChatMessage>?> fetchHistoryMessages({
    required String targetId, // 对方用户ID（单聊）或群ID（群聊）
    required ChatConversationType chatType, // 聊天类型：PeerChat 或 GroupChat
    bool isNewestFirst = true,
    String? cursor,
    int pageSize = 50,
  }) async {
    try {
      ChatConversation? conversation =
          await ChatClient.getInstance.chatManager.getConversation(targetId);
      List<ChatMessage> localMessages = [];
      if (conversation != null) {
        localMessages = await conversation.loadMessages(
            startMsgId: cursor ?? "", loadCount: pageSize);
      }

      ChatCursorResult<ChatMessage> result;

      if (localMessages.isEmpty || localMessages.length < pageSize) {
        ChatMessage? firsts;
        if (localMessages.isNotEmpty) {
          if (localMessages.first.from?.startsWith("agnes") == true) {
            firsts = localMessages.first;
            localMessages.remove(firsts);
          }
        }
        final ccc =
            localMessages.isNotEmpty ? localMessages.first.msgId : cursor;
        result = await ChatClient.getInstance.chatManager
            .fetchHistoryMessagesByOption(
          targetId,
          chatType,
          pageSize: pageSize,
          cursor: ccc,
          options: FetchMessageOptions(needSave: true), // 拉取后存本地
        );
        if (firsts != null) {
          localMessages.insert(0, firsts);
        }
        if (localMessages.isNotEmpty) {
          // final resultMsgIds = result.data.map((msg) => msg.msgId).toSet();
          // localMessages = localMessages.where((msg) => !resultMsgIds.contains(msg.msgId)).toList();
          result.data.insertAll(0, localMessages);
        }
        print("拉取历史消息成功$cursor获取 ${result.data.length} 条消息");
      } else {
        result = ChatCursorResult.fromJson(
            {"list": localMessages, "cursor": localMessages.first.msgId},
            dataItemCallback: (item) {
          return item as ChatMessage;
        });
        print(
            "拉取本地历史消息成功$cursor获取 ${result.data.length} 条消息${localMessages.first.toJson()}");
      }

      if (isNewestFirst) {
        result.data.sort((a, b) => b.serverTime.compareTo(a.serverTime));
      } else {
        result.data.sort((a, b) => a.serverTime.compareTo(b.serverTime));
      }

      return result;
    } on ChatError catch (e) {
      print("拉取历史消息失败: $e");
      showFailToast("${e.toString()}");
      // if (e.code == 201 && e.toString().contains("User is not logged in"))
      return null;
    }
  }

/*拉取历史消息  end*/

  //是否有人@我
  final mentionMeCache = <String, bool>{}.obs;

  //群头像
  final groupAvatars = <String, List<String>>{}.obs;

  //群组列表
  var chatGroups = <ChatGroup>[].obs;
  //置顶的群组
  var pinnedGroups = <ChatGroup>[].obs;

  final groupLastMessage = <String, ChatMessage?>{}.obs;

  final unreadCounts = <String, int?>{}.obs;

  var isLoading = false.obs;

  bool isGroupTypeZero(ChatGroup group) {
    try {
      final ext = group.extension;

      if (ext == null) return false;

      if (ext.isEmpty) return false;

      final json = jsonDecode(ext);

      if (json != null && json is Map) {
        return json["group_type"] == 0;
      }
      return false;
    } catch (e) {
      logError("群组类型group_type错误:${e}");
      return false;
    }
  }

  getJoinedGroupFromServer({Function(int size)? callback}) async {
    mentionMeCache.clear();
    groupLastMessage.clear();
    unreadCounts.clear();
    groupAvatars.clear();
    pinnedGroups.clear();
    try {
      if (isLoading.value) {
        return;
      }
      isLoading.value = true;

      final ggp = await firstInnerLoadGroups();
      chatGroups.clear();
      chatGroups.assignAll(ggp);
      callback?.call(ggp.length);
      // await Future.wait(ggp.map((gc) async {
      //   getGroupMember(gc.groupId).then((ss) {
      //     groupAvatars[gc.groupId] = ss;
      //     groupAvatars.refresh();
      //   });
      //
      //   // 获取会话对象
      //   ChatConversation? con = await ChatClient.getInstance.chatManager
      //       .getConversation(gc.groupId, createIfNeed: false);
      //
      //   if (con != null) {
      //
      //     ChatMessage? localLast = await con.latestMessage();
      //     ChatMessage? localReceiveLast = await con.lastReceivedMessage();
      //
      //     int unread = await con.unreadCount();
      //     unreadCounts[gc.groupId] = localReceiveLast == null
      //         ? 0
      //         : localReceiveLast.hasRead
      //             ? 0
      //             : unread;
      //     unreadCounts.refresh();
      //
      //     getHasMentionMe(con, unread);
      //
      //     groupLastMessage[gc.groupId] = localLast;
      //     groupLastMessage.refresh();
      //   }
      // }));
      // chatGroups.sort((a, b) {
      //   final timeA = groupLastMessage[a.groupId]?.serverTime;
      //   final timeB = groupLastMessage[b.groupId]?.serverTime;
      //
      //   if (timeA != null && timeB != null) return timeB.compareTo(timeA);
      //   if (timeA != null && timeB == null) return -1;
      //   if (timeA == null && timeB != null) return 1;
      //   return 0;
      // });
      // chatGroups.refresh();
    } catch (e) {
      logError("获取群列表出错：$e");
    } finally {
      isLoading.value = chatGroups.length % 20 != 0;
    }
  }

  Future<void> otherParams(
    List<ChatGroup> ggp,
  ) async {
    await Future.wait(ggp.map((gc) async {
      final ss = await getGroupMember(gc.groupId);
      groupAvatars[gc.groupId] = ss;
      // 获取会话对象
      ChatConversation? con = await ChatClient.getInstance.chatManager
          .getConversation(gc.groupId, createIfNeed: false);

      if (con != null) {
        ChatMessage? localLast = await con.latestMessage();
        ChatMessage? localReceiveLast = await con.lastReceivedMessage();

        int unread = await con.unreadCount();
        unreadCounts[gc.groupId] = localReceiveLast == null
            ? 0
            : localReceiveLast.hasRead
                ? 0
                : unread;

        getHasMentionMe(con, unread);

        groupLastMessage[gc.groupId] = localLast;
      }
    }));
    groupAvatars.refresh();
    unreadCounts.refresh();
    groupLastMessage.refresh();
    chatGroups.sort((a, b) {
      final timeA = groupLastMessage[a.groupId]?.serverTime;
      final timeB = groupLastMessage[b.groupId]?.serverTime;

      if (timeA != null && timeB != null) return timeB.compareTo(timeA);
      if (timeA != null && timeB == null) return -1;
      if (timeA == null && timeB != null) return 1;
      return 0;
    });
    chatGroups.refresh();
    exchangeDefaultGroup();
  }

  exchangeDefaultGroup() async {
    int gt0 = chatGroups.indexWhere((gt0) => isGroupTypeZero(gt0));
    if (gt0 > 0) {
      ChatGroup gt0Group = chatGroups[gt0];
      chatGroups.removeAt(gt0);
      chatGroups.insert(0, gt0Group);
    } else {
      print("agora: 创建群组判断$agnesGroupId");
      if (agnesGroupId.value.isEmpty) {
        createAgnesGroup();
      }
    }
  }

  bool exchangeGroup() {
    int gt0 = chatGroups.indexWhere((gt0) => isGroupTypeZero(gt0));
    return gt0 >= 0;
  }

  ///是否第一次进入,第一次从远端拉取所有群组
  Future<List<ChatGroup>> firstInnerLoadGroups({Function? callback}) async {
    try {
      final logic = Get.find<StorageService>();
      if (!logic.getHasSync(agoraUserId.value)) {
        final groups = await fetchAllJoinedGroups();
        int count = 0;
        List<LoadCursor> ls = [];

        if(groups.isNotEmpty) {
          unawaited(Future.wait(groups.map((gc) async {
            ChatCursorResult<ChatMessage> result = await ChatClient
                .getInstance.chatManager
                .fetchHistoryMessagesByOption(
                gc.groupId, ChatConversationType.GroupChat,
                cursor: null, options: FetchMessageOptions(needSave: true));

            if ((result.cursor ?? "").isEmpty ||
                result.cursor == "undefined" ||
                result.data.length < 50) {
            } else {
              ls.add(LoadCursor(gc, result.cursor));
            }

            count++;
            if (count >= groups.length) {
              unawaited(otherParams(
                groups,
              ));
              syncMessages(ls);
            }
          })));
        } else {
          createAgnesGroup();
        }

        loadComplete.value = true;
        logic.setHasSync(true, agoraUserId.value);
        print("agnes: 从远端拉取数据");
        return groups;
      } else {
        final groups =
            await ChatClient.getInstance.groupManager.getJoinedGroups();
        otherParams(groups);
        verifyGroup(groups);
        print("agnes: 从本地拉取数据");
        loadComplete.value = true;
        return groups;
      }
    } catch (e) {
      loadComplete.value = true;
      logError("获取群组失败${e.toString()}");
      showFailToast("获取群组失败${e.toString()}");
      return [];
    }
  }

  ///同步剩余的消息
  syncMessages(List<LoadCursor> groups) async {
    unawaited(Future.wait(groups.map((gc) async {
      String? cursor = gc.cursor;
      while (true) {
        ChatCursorResult<ChatMessage> result = await ChatClient
            .getInstance.chatManager
            .fetchHistoryMessagesByOption(
                gc.gc.groupId, ChatConversationType.GroupChat,
                cursor: cursor, options: FetchMessageOptions(needSave: true));

        if (result.data.isEmpty) break;

        if (result.cursor == null ||
            result.cursor == "undefined" ||
            result.data.length < 50) break;

        cursor = result.cursor;
      }
      logError("消息拉取完成${gc.gc.name}");
    })));
  }

  ///去重
  List<ChatGroup> verifyGroup(List<ChatGroup> groups) {
    try {
      final delId = Get.find<StorageService>().getDeleteConversation();
      if (delId.isEmpty) {
        return groups;
      }
      for (var g in List.from(groups)) {
        final hasConv = delId.contains(g.groupId);
        if (hasConv) {
          groups.remove(g);
        }
      }
      return groups;
    } catch (e) {
      return groups;
    }

    // final conversations =
    //     await ChatClient.getInstance.chatManager.loadAllConversations();
    // if (conversations.length != groups.length) {
    //   for (var g in List.from(groups)) {
    //     final hasConv = conversations.any((c) => c.id == g.groupId);
    //     if (!hasConv) {
    //       groups.remove(g);
    //     }
    //   }
    // }
    // return groups;
  }

  ///同步所有群组
  Future<List<ChatGroup>> fetchAllJoinedGroups() async {
    List<ChatGroup> allGroups = [];
    int pageNum = 0;
    const int pageSize = 20;

    while (true) {
      // 分页拉取
      List<ChatGroup> page = await ChatClient.getInstance.groupManager
          .fetchJoinedGroupsFromServer(pageNum: pageNum, pageSize: pageSize);

      if (page.isEmpty) break;

      allGroups.addAll(page);

      if (page.length < pageSize) break;

      pageNum++;
    }
    return allGroups;
  }

  ///获取新的群组信息
  getNewGroupInfo(String id) async {
    ChatGroup? group =
        await ChatClient.getInstance.groupManager.getGroupWithId(id);
    if (group != null) {
      int index = chatGroups.indexWhere((t) => t.groupId == id);
      if (index >= 0) {
        chatGroups[index] = group;
      }
    }
  }

  ///刷新指定会话的最后一条消息
  refreshGroupLastMessage(String groupId) async {
    getLastMessage(groupId).then((last) {
      if (last != null) {
        groupLastMessage[groupId] = last;
        groupLastMessage.refresh();
      }
    });
  }

  //获取最后一条消息
  Future<ChatMessage?> getLastMessage(String groupId) async {
    ChatConversation? conversation =
        await ChatClient.getInstance.chatManager.getConversation(groupId);

    return conversation?.latestMessage();
  }

  Future<ChatConversation?> getConversation(String groupId) async {
    ChatConversation? conversation =
        await ChatClient.getInstance.chatManager.getConversation(groupId);
    return conversation;
  }

  Future<int?> getUnreadCount(String groupId) async {
    ChatConversation? conversation =
        await ChatClient.getInstance.chatManager.getConversation(groupId);
    return conversation?.unreadCount();
  }

  //是否有@我数据
  Future<void> getHasMentionMe(ChatConversation getCon, int unreadCount) async {
    List<ChatMessage> messages =
        await getCon.loadMessages(loadCount: unreadCount);

    messages.forEach((msg) {
      chatMessageHasMentionMe(msg, getCon.id);
    });
  }

  //消息提是否是@我的
  chatMessageHasMentionMe(ChatMessage msg, String conId) async {
    Map<String, dynamic> body = msg.body.toJson();
    if (body["event"] == "group_chat" && body.containsKey("params")) {
      try {
        Map<String, dynamic>? appData = jsonDecode(
            body["params"]?['app_data'] ?? body["params"]?['app_customexts']);
        if (appData != null) {
          GroupChatMessageModel model = GroupChatMessageModel.fromJson(appData);
          if (model.mention_user != null &&
              model.message_type == 5 &&
              model.mention_user?.id ==
                  Get.find<StorageService>().getUserInfoData().id &&
              !msg.hasRead) {
            mentionMeCache[conId] = true;
          }
        }
      } catch (e) {
        print("解析历史消息出错${e.toString()}");
      }
    }
  }

  ///刷新头像
  refreshAvatar() {
    Future.wait(chatGroups.map((f) async {
      getGroupMember(f.groupId).then((ss) {
        groupAvatars[f.groupId] = ss;
      });
    })).then((end) {
      groupAvatars.refresh();
    });
  }

  ///获取群成员
  Future<List<String>> getGroupMember(String groupId) async {
    try {
      if (!(await ChatClient.getInstance.isConnected())) {
        return [];
      }

      ChatGroup group = await ChatClient.getInstance.groupManager
          .fetchGroupInfoFromServer(groupId);

      chatGroups.replaceByGroupId(group);

      if (isGroupTypeZero(group)) {
        agnesGroupId.value = group.groupId;
        ChatClient.getInstance.chatManager.pinConversation(conversationId: agnesGroupId.value, isPinned: true);
        print("agora: 创建群组:默认群组$agnesGroupId");
        return [agnesAvatar ?? ""];
      }

      List<String> members = (await ChatClient.getInstance.groupManager
              .fetchMemberListFromServer(groupId, cursor: '', pageSize: 9))
          .data;

      if (group.adminList != null && group.adminList!.isNotEmpty) {
        members.insertAll(0, group.adminList!);
      }

      if (group.owner != null) {
        members.insert(0, group.owner!);
      }

      Map<String, ChatUserInfo> memberInfo = await ChatClient
          .getInstance.userInfoManager
          .fetchUserInfoById(members);

      Iterable<ChatUserInfo> users = memberInfo.values
          .where((user) => user.avatarUrl != null && user.avatarUrl!.isNotEmpty)
          .toList()
          .reversed;
      List<String> avatars = users
          .take(users.length >= 4 ? 4 : users.length)
          .map((user) => user.avatarUrl!)
          .toList();
      return avatars;
    } catch (e) {
      return [];
    }
  }

  var agoraUserId = "".obs;
  var agoraToken = "".obs;

  /// Callback for token expiration warning.
  void onTokenWillExpire() async {
    agoraToken.value = "";
    logError("Token will expire soon. Fetching a new token...");
    getAgoraInfo(tokenSuccess: (userId, token) async {
      agoraUserId.value = userId;
      agoraToken.value = token;
      await ChatClient.getInstance.renewAgoraToken(token);
      logError("Token renewed successfully");
    });
  }

  /// Callback for token expiration.
  void onTokenDidExpire() async {
    logError("Token expired. Fetching a new token...");
    // Re-login with the new token
    try {
      if (agoraUserId.value.isEmpty || agoraToken.value.isEmpty) {
        getAgoraInfo(tokenSuccess: (userId, token) async {
          await ChatClient.getInstance.loginWithToken(userId, token);
          isJoined.value = true;
          Get.find<StorageService>()
              .setAgoraInfo(agoraUserId.value, agoraToken.value);
          logError("Re-logged in successfully with the new token");
        });
      } else {
        await ChatClient.getInstance
            .loginWithToken(agoraUserId.value, agoraToken.value);
        isJoined.value = true;
        Get.find<StorageService>()
            .setAgoraInfo(agoraUserId.value, agoraToken.value);
      }
    } catch (e) {
      logError("Failed to re-login: $e");
      isJoined.value = false;
    }
  }

  final listenerMap = <String, AgoraConnectedListener>{}.obs;

  void removeAgoraConnectedListener(String listenerId) {
    if (listenerMap.containsKey(listenerId)) listenerMap.remove(listenerId);
  }

  void addAgoraConnectedListener(
      String listenerId, AgoraConnectedListener connectedListener) {
    if (listenerId.isEmpty) {
      return;
    }
    listenerMap[listenerId] = connectedListener;
  }
}

class LoadCursor {
  ChatGroup gc;
  String? cursor;
  LoadCursor(this.gc, this.cursor);
}

class TabEvent extends ChatEvent {
  final int type; //1 打开侧边栏 2关闭侧边栏 3 tab刷新

  TabEvent({
    required this.type,
  });
}

typedef AgoraConnectedListener = Function();

extension ListExtensions<T extends ChatGroup> on RxList<T> {
  void replaceByGroupId(T newGroup) {
    final index = this.indexWhere((group) => group.groupId == newGroup.groupId);
    if (index != -1) {
      this[index] = newGroup;
    }
  }
}
