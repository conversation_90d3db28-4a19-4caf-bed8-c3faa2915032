class FeatureAccessModel {
  List<Feature> features;
  int total;
  String userId;

  FeatureAccessModel({
    required this.features,
    required this.total,
    required this.userId,
  });

  factory FeatureAccessModel.fromJson(Map<String, dynamic> json) {
    return FeatureAccessModel(
      features: (json['features'] as List)
          .map((e) => Feature.fromJson(e))
          .toList(),
      total: json['total'],
      userId: json['user_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'features': features.map((e) => e.toJson()).toList(),
      'total': total,
      'user_id': userId,
    };
  }
}

class Feature {
  String? featureCode;
  String? featureName;
  String? expiresAt;
  String? grantedAt;
  String? grantedBy;

  Feature({
     this.featureCode,
     this.featureName,
    this.expiresAt,
    this.grantedAt,
    this.grantedBy,
  });

  factory Feature.fromJson(Map<String, dynamic> json) {
    return Feature(
      featureCode: json['feature_code'],
      featureName: json['feature_name'],
      expiresAt: json['expires_at'] != null
          ? json['expires_at']
          : null,
      grantedAt: json['granted_at'] != null
          ? json['granted_at']
          : null,
      grantedBy: json['granted_by'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'feature_code': featureCode,
      'feature_name': featureName,
      'expires_at': expiresAt,
      'granted_at': grantedAt,
      'granted_by': grantedBy,
    };
  }
}
