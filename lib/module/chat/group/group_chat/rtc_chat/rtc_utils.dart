import 'dart:async';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_speech_send_message_utils.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:typed_data';
import 'package:new_agnes/module/chat/group/group_chat/protobuffer/Audio2TextProtobuffer.pb.dart'
    as AgoraRTT;

import '../../../../../api/ApiProvider.dart';
import '../../../../../utils/inAppLogUtil.dart';
import 'asr_utils.dart';
import 'rtc_event_model.dart';

class RtcUtils {
  static final RtcUtils _instance = RtcUtils._internal();

  static RtcUtils get instance => _instance;

  RxBool isAudio = false.obs; //true 是闭麦  false 是开麦

  RtcUtils._internal();

  late RtcEngine _engine;

  // 原始音频帧观察者（用于获取PCM数据）
  late AudioFrameObserver _audioFrameObserver;

  // 是否已注册音频观察者
  bool _isAudioObserverRegistered = false;

  // 音频缓冲区相关
  List<int> _audioBuffer = [];
  Timer? _audioPushTimer;
  StreamController<Uint8List>? _audioStreamController;
  StreamSubscription<List<ConnectivityResult>>? subscription;
  bool isFirst = false;

  //初始化
  Future<void> init(appId,
      {onJoinChanner,
      onUserJoined,
      onUserOffline,
      onTokenConnect,
      onAudioVolume,
      onCloseChannel,
      onStreamMessage}) async {
    _engine = createAgoraRtcEngine();
    await _engine.initialize(RtcEngineContext(
      appId: appId,
      channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
    ));
    await _engine.enableAudio();
    await _engine.setClientRole(
        role: ClientRoleType.clientRoleBroadcaster); //设置可以直接聊天
    // 设置音频采样率和声道数
    await _engine.setRecordingAudioFrameParameters(
      sampleRate: 16000, // 采样率
      channel: 1, // 单通道
      mode: RawAudioFrameOpModeType.rawAudioFrameOpModeReadOnly,
      samplesPerCall: 1600,
    );

    await _engine.setPlaybackAudioFrameParameters(
      sampleRate: 16000, // 采样率
      channel: 1, // 单通道
      mode: RawAudioFrameOpModeType.rawAudioFrameOpModeReadOnly,
      samplesPerCall: 1600,
    );
    // 如果启用Azure语音识别，初始化音频观察者
    _initAudioFrameObserver();

    setupEventHandlers(
        onJoinChanner: onJoinChanner,
        onTokenConnect: onTokenConnect,
        onUserJoined: onUserJoined,
        onUserOffline: onUserOffline,
        onAudioVolume: onAudioVolume,
        onCloseChannel: onCloseChannel,
        onStreamMessage: onStreamMessage);
    await _engine.enableAudioVolumeIndication(
        interval: 500, smooth: 3, reportVad: true); // 设置音量回调间隔(毫秒)，建议200-300ms
    await _engine.setAudioProfile(
        profile: AudioProfileType.audioProfileDefault,
        scenario: AudioScenarioType.audioScenarioDefault);
  }

  //权限申请
  Future<void> requestPermissions() async {
    await [Permission.microphone].request();
  }

  // 初始化原始音频帧观察者
  Future<void> _initAudioFrameObserver() async {
    _audioFrameObserver = AudioFrameObserver(
      // 本地录制音频帧回调
      onRecordAudioFrame: (String channelId, AudioFrame audioFrame) {
        if (!isAudio.value) {
          _processRawAudioFrame(audioFrame);
        }
      },
      // 其他回调保持不变
      onPlaybackAudioFrame: (String channelId, AudioFrame audioFrame) {},
      onMixedAudioFrame: (String channelId, AudioFrame audioFrame) {},
    );
    AsrUtils.instance.init();
  }

  // 处理原始PCM音频帧
  void _processRawAudioFrame(AudioFrame audioFrame) {
    if (audioFrame.buffer != null) {
      // 将音频数据添加到缓冲区
      _audioBuffer.addAll(audioFrame.buffer!);
      // 启动定时推送（如果尚未启动）
      _startAudioPushTimer();
    } else {
      LoggerUtils.w("音频帧buffer为空，跳过处理");
    }
  }

  // 启动音频数据定时推送
  void _startAudioPushTimer() {
    if (_audioPushTimer == null) {
      // 每40ms推送一次数据
      _audioPushTimer = Timer.periodic(Duration(milliseconds: 40), (timer) {
        // 检查缓冲区是否有足够的数据（1280字节）
        int size = 1280;
        if (_audioBuffer.length >= size) {
          // 取出前1280字节的数据
          Uint8List chunk = Uint8List.fromList(_audioBuffer.sublist(0, size));
          // 从缓冲区中移除已推送的数据
          // _audioBuffer = await _audioBuffer.sublist(size);
          _audioBuffer.removeRange(0, size);
          // 推送到语音识别
          _pushAudioChunkToRecognition(chunk);
        }
      });
    }
  }

  // 推送音频块到语音识别
  void _pushAudioChunkToRecognition(Uint8List audioChunk) {
    if (_audioStreamController != null && !_audioStreamController!.isClosed) {
      _audioStreamController!.add(audioChunk);
    }
  }

  // 启动腾讯语音识别
  Future<void> _startSpeechRecognition() async {
    // 初始化音频流控制器
    _audioStreamController = StreamController<Uint8List>();
    AsrUtils.instance.startAsrSpeechRecognition(_audioStreamController!.stream);
  }

  void setupNetworkListener() {
    isFirst = false;
    subscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> result) {
      if (result.isNotEmpty) {
        LoggerUtils.d("ASR识别网络状态：${result.first}");
        switch (result.first) {
          case ConnectivityResult.wifi:
            asrReconnect();
            break;
          case ConnectivityResult.mobile:
            asrReconnect();
            break;
          case ConnectivityResult.ethernet:
            break;
          case ConnectivityResult.vpn:
            break;
          case ConnectivityResult.none:
            break;
          default:
            break;
        }
        isFirst = true;
      }
    });
  }

  void asrReconnect() {
    if (isFirst) {
      if (AsrUtils.instance.reconnectNum < 5) {
        AsrUtils.instance.reconnectNum++;
        Future.delayed(Duration(seconds: 1)).then((data) async {
          // 停止定时器
          _audioPushTimer?.cancel();
          _audioPushTimer = null;
          // 清空缓冲区
          _audioBuffer.clear();
          // 关闭流控制器
          if (_audioStreamController != null) {
            await _audioStreamController!.close();
            _audioStreamController = null;
          }
          AsrUtils.instance.stopAsrSpeechRecognition();
          _startSpeechRecognition();
        });
      }
    }
  }

  // 停止腾讯语音识别
  Future<void> _stopSpeechRecognition() async {
    // 停止定时器
    _audioPushTimer?.cancel();
    _audioPushTimer = null;
    // 清空缓冲区
    _audioBuffer.clear();
    // 关闭流控制器
    if (_audioStreamController != null) {
      await _audioStreamController!.close();
      _audioStreamController = null;
    }
    AsrUtils.instance.stopAsrSpeechRecognition();
  }

  // 注册音频帧观察者（通过MediaEngine）
  Future<void> _registerAudioFrameObserver() async {
    if (!_isAudioObserverRegistered) {
      try {
        // 通过MediaEngine注册原始音频帧观察者
        _engine
            .getMediaEngine()
            .registerAudioFrameObserver(_audioFrameObserver);

        _isAudioObserverRegistered = true;
        LoggerUtils.d("成功注册原始音频帧观察者");
      } catch (e) {
        LoggerUtils.e("注册原始音频帧观察者失败: $e");
      }
    }
  }

  // 取消注册音频帧观察者（通过MediaEngine）
  Future<void> _unregisterAudioFrameObserver() async {
    if (_isAudioObserverRegistered) {
      try {
        // 通过MediaEngine取消注册音频帧观察者
        _engine
            .getMediaEngine()
            .unregisterAudioFrameObserver(_audioFrameObserver);
        _isAudioObserverRegistered = false;
        LoggerUtils.d("成功取消注册音频帧观察者");
      } catch (e) {
        LoggerUtils.e("取消注册音频帧观察者失败: $e");
      }
    }
  }

  // rtc 人员监听
  void setupEventHandlers(
      {onJoinChanner,
      onUserJoined,
      onUserOffline,
      onTokenConnect,
      onAudioVolume,
      onCloseChannel,
      onStreamMessage}) {
    _engine.registerEventHandler(
      RtcEngineEventHandler(
          onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
        debugPrint("本地用户 ${connection.localUid} 加入");
        // 加入频道成功后注册音频帧观察者
        _registerAudioFrameObserver();
        _startSpeechRecognition();
        setupNetworkListener();
        if (onJoinChanner != null) {
          RtcEventModel model = new RtcEventModel();
          model.connection = connection;
          model.elapsed = elapsed;
          onJoinChanner(model);
        }
      }, onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
        debugPrint("远程用户 $remoteUid 加入");
        if (onUserJoined != null) {
          RtcEventModel model = new RtcEventModel();
          model.connection = connection;
          model.elapsed = elapsed;
          model.remoteUid = remoteUid;
          onUserJoined(model);
        }
      }, onUserOffline: (RtcConnection connection, int remoteUid,
              UserOfflineReasonType reason) {
        debugPrint("远程用户 $remoteUid 离开");
        if (onUserOffline != null) {
          RtcEventModel model = new RtcEventModel();
          model.connection = connection;
          model.remoteUid = remoteUid;
          model.reason = reason;
          onUserOffline(model);
        }
      }, onTokenPrivilegeWillExpire: (RtcConnection connection, String token) {
        debugPrint('用户重新连接 connection: ${connection.toJson()}, token: $token');
        if (onTokenConnect != null) {
          RtcEventModel model = new RtcEventModel();
          model.connection = connection;
          model.token = token;
          onTokenConnect(model);
        }
      }, onAudioVolumeIndication: (RtcConnection connection,
              List<AudioVolumeInfo> speakers,
              int speakerNumber,
              int totalVolume) {
        List<AudioVolumeInfo> audioVolumList = [];
        for (var speaker in speakers) {
          // if (speaker.volume! > 10 && speaker.vad == 1) {
          // 音量阈值，可根据需要调整 volume: 音量值(0-255) vad: 是否检测到人声(1=检测到，0=未检测到)
          audioVolumList.add(speaker);
          // }
        }
        RtcEventModel model = new RtcEventModel();
        model.connection = connection;
        model.speakerNumber = speakerNumber;
        model.audioVolumList = audioVolumList;
        onAudioVolume(model);
      }, onConnectionStateChanged: (RtcConnection connection,
              ConnectionStateType type, ConnectionChangedReasonType reason) {
        LoggerUtils.d(
            "connection. ----${connection.toJson()} type------${type.name},reason-----${reason.name}");
      }, onError: (err, str) {
        LoggerUtils.d("error type----${err.name} str------$str");
      }, onLeaveChannel: (RtcConnection connection, RtcStats stats) {
        _unregisterAudioFrameObserver();
        _stopSpeechRecognition();
        if (subscription != null) {
          subscription!.cancel();
          subscription = null;
        }
        if (onCloseChannel != null) {
          RtcEventModel model = new RtcEventModel();
          model.connection = connection;
          onCloseChannel(model);
        }
      }, onStreamMessage: (RtcConnection connection, int remoteUid,
              int streamId, Uint8List data, int length, int sentTs) {
        // 处理转录
        var sttResult = AgoraRTT.Text.fromBuffer(data);
        // var curRTTResult = '';
        // 处理uid为无符号int
        var uid = sttResult.uid & 0xFFFFFFFF;
        // debugPrint("[onStreamMessage]: 收到远程用户 $uid 的消息: ${sttResult.words.toString()}");
        // debugPrint("-------------------------------------------------");
        sttResult.words.forEach((word) {
          // curRTTResult += word.text;
          if (onStreamMessage != null) {
            RtcStreamModel model =
                new RtcStreamModel(word.text.obs, word.isFinal);
            model.connection = connection;
            model.remoteUid = uid;
            onStreamMessage(model);
          }
        });
      }),
    );
  }

  //加入通话
  Future<void> joinChannel(token, channel, {uid = 0}) async {
    await requestPermissions();
    await _engine.joinChannel(
      token: token,
      channelId: channel,
      options: const ChannelMediaOptions(
        autoSubscribeAudio: true,
        publishMicrophoneTrack: true,
        clientRoleType: ClientRoleType.clientRoleBroadcaster,
      ),
      uid: uid,
    );
  }

  // 关闭本地音频推流
  Future<void> muteLocalAudioStream(bool isStop) async {
    isAudio.value = isStop;
    if (isStop) {
      RtcSpeechSendMessageUtils.instance.closeMicrophone(); //关闭语音识别
      _stopSpeechRecognition();
    } else {
      _startSpeechRecognition();
    }
    _engine.muteLocalAudioStream(isStop);
  }

  /// 开始麦克风:true 停止:false
  Future<void> enableAudio(bool enable) async {
    _engine.enableLocalAudio(enable);
  }

  //清除通道
  Future<void> cleanChannel() async {
    // 清理时取消注册音频帧观察者
    await _unregisterAudioFrameObserver();

    await _engine.leaveChannel();
  }

  Future<void> cleanEngine() async {
    // 清理时取消注册音频帧观察者
    await _engine.release();
  }
}
