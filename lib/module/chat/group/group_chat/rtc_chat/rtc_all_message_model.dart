import 'rtc_message_model.dart';

class RtcAllMessageModel {
  String? thirdChatGroupId;
  SenderUser? senderUser;
  String? content;
  int? contentType;
  int? messageType;
  List<dynamic>? attachments;
  RtcMessageModel? rtc;
  String? groupId;
  int? objectType;
  String? id;
  String? thirdMessageId;
  int? messageIndex;
  String? createdAt;
  int? messageSourceType;

  RtcAllMessageModel({
    this.thirdChatGroupId,
    this.senderUser,
    this.content,
    this.contentType,
    this.messageType,
    this.attachments,
    this.rtc,
    this.groupId,
    this.objectType,
    this.id,
    this.thirdMessageId,
    this.messageIndex,
    this.createdAt,
    this.messageSourceType,
  });

  RtcAllMessageModel.fromJson(Map<String, dynamic> json) {
    thirdChatGroupId = json['third_chat_group_id'];
    senderUser = json['sender_user'] != null
        ? SenderUser.fromJson(json['sender_user'])
        : null;
    content = json['content'];
    contentType = json['content_type'];
    messageType = json['message_type'];
    attachments = json['attachments'];
    rtc = json['rtc']!=null?RtcMessageModel.fromJson(json['rtc']):null;
    groupId = json['group_id'];
    objectType = json['object_type'];
    id = json['id'];
    thirdMessageId = json['third_message_id'];
    messageIndex = json['message_index'];
    createdAt = json['created_at'];
    messageSourceType = json['message_source_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['third_chat_group_id'] = thirdChatGroupId;
    if (senderUser != null) {
      data['sender_user'] = senderUser!.toJson();
    }
    data['content'] = content;
    data['content_type'] = contentType;
    data['message_type'] = messageType;
    data['attachments'] = attachments;
    data['group_id'] = groupId;
    if (rtc != null) {
      data['rtc'] = rtc!.toJson();
    }
    data['object_type'] = objectType;
    data['id'] = id;
    data['third_message_id'] = thirdMessageId;
    data['message_index'] = messageIndex;
    data['created_at'] = createdAt;
    data['message_source_type'] = messageSourceType;
    return data;
  }
}

class SenderUser {
  // 根据提供的数据，sender_user 为 null，暂时未提供具体字段
  // 可根据实际数据结构添加相应字段

  SenderUser();

  SenderUser.fromJson(Map<String, dynamic> json) {}

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    return data;
  }
}


