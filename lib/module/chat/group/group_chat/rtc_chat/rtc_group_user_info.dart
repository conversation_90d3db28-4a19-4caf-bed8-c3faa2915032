import 'package:get/get.dart';

class RtcGroupUserInfo {
  final String status;
  final String message;
  final String timestamp;
  final List<Member> members;
  final int isMember;
  final int memberCount;

  RtcGroupUserInfo({
    required this.status,
    required this.message,
    required this.timestamp,
    required this.members,
    required this.isMember,
    required this.memberCount,
  });

  factory RtcGroupUserInfo.fromJson(Map<String, dynamic> json) {
    return RtcGroupUserInfo(
      status: json['status'] as String,
      message: json['message'] as String,
      timestamp: json['timestamp'] as String,
      members: (json['members'] as List)
          .map((e) => Member.fromJson(e as Map<String, dynamic>))
          .toList(),
      isMember: json['is_member'] as int,
      memberCount: json['member_count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'timestamp': timestamp,
      'members': members.map((e) => e.toJson()).toList(),
      'is_member': isMember,
      'member_count': memberCount,
    };
  }
}

class Member {
    String userId;
   String username;
   String avatarUrl;
   String rtcUserId;
  RxBool? isSpeak; // 改为非空，且使用 late 初始化

  void changeIsSpeak(bool value) {
    isSpeak?.value = value;
    isSpeak?.refresh();
  }

  Member({
    required this.userId,
    required this.username,
    required this.avatarUrl,
    required this.rtcUserId,
    this.isSpeak, // 添加参数
  });

  factory Member.fromJson(Map<String, dynamic> json) {
    return Member(
      userId: json['user_id'] as String,
      username: json['username'] as String,
      avatarUrl: json['avatar_url'] as String,
      rtcUserId: json['rtc_user_id'] as String,
      isSpeak: json['isSpeak'] != null ? json['isSpeak'].obs : false.obs,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'username': username,
      'avatar_url': avatarUrl,
      'rtc_user_id': rtcUserId,
      'isSpeak': isSpeak!.value, // 保存实际的布尔值
    };
  }
}
