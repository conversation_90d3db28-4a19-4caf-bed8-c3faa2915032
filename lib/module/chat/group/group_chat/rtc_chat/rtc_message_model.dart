class RtcMessageModel {
  /// rtcTo
  String? token;

  String? channelId;
  String? groupId;

  /// 头像
  String? avatarUrl;

  /// 用户名
  String? userName;

  /// 用户id
  String? userId;

  /// 群组名
  String? groupName;
  String? thirdId;
  List<String>? targets;

  RtcMessageModel({
    this.token,
    this.channelId,
    this.groupId,
    this.avatarUrl,
    this.userName,
    this.userId,
    this.groupName,
    this.thirdId,
    this.targets,
  });

  /// 从 Map 转换为对象
  factory RtcMessageModel.fromJson(Map<String, dynamic> json) {
    return RtcMessageModel(
        token: json['token'] as String?,
        channelId: json['channelId'] as String?,
        avatarUrl: json['avatarUrl'] as String?,
        groupId: json['groupId'] as String?,
        userName: json['userName'] as String?,
        userId: json['userId'] as String?,
        groupName: json['groupName'] as String?,
        thirdId: json['thirdId'] as String?,
        targets:
            json['targets'] != null ? List<String>.from(json['targets']) : []);
  }

  /// 从对象转换为 Map
  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'channelId': channelId,
      'avatarUrl': avatarUrl,
      'userName': userName,
      'groupId': groupId,
      'userId': userId,
      'groupName': groupName,
      'thirdId': thirdId,
      'targets': targets,
    };
  }
}
