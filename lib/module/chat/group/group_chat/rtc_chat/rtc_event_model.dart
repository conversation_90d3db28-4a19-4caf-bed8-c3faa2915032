import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:get/get.dart';

class RtcEventModel {
  RtcConnection? connection;
  int? elapsed;
  int? remoteUid;
  int? speakerNumber;
  UserOfflineReasonType? reason;
  String? token;
  List<AudioVolumeInfo>? audioVolumList;

  RtcEventModel(
      {this.connection, this.elapsed, this.remoteUid,this.speakerNumber, this.reason, this.token,this.audioVolumList});
}

class RtcStreamModel extends RtcEventModel {
  Rx<String> content = "".obs;
  bool isFinal;
  RtcStreamModel(this.content, this.isFinal,
      {RtcConnection? connection,
      int? elapsed,
      int? remoteUid,
      int? speakerNumber,
      UserOfflineReasonType? reason,
      String? token,
      List<AudioVolumeInfo>? audioVolumList})
      : super(
          connection: connection,
          elapsed: elapsed,
          remoteUid: remoteUid,
          speakerNumber: speakerNumber,
          reason: reason,
        );
}
