import 'dart:async';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/ApiProvider.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../../../../../api/StorageService.dart';
import '../../../../mine/model/UserInfoModel.dart';
import '../../message_chat/enum/group_chat_message_enum.dart';
import '../../message_chat/model/group_chat_message_model.dart';
import '../agora_logic.dart';

class RtcSpeechSendMessageUtils {
  String thirdGroupId = "";
  String speechingText = "";

  // 添加防抖定时器
  Timer? _navigationTimer;
  String completedText = '';
  int get key => this.hashCode;

  static RtcSpeechSendMessageUtils instance = RtcSpeechSendMessageUtils();

  // 私有构造函数
  RtcSpeechSendMessageUtils._();

  // 静态实例
  static final RtcSpeechSendMessageUtils _instance =
      RtcSpeechSendMessageUtils._();

  // 工厂构造函数
  factory RtcSpeechSendMessageUtils() {
    return _instance;
  }

  RtcSpeechMessageCallback? _rtcSpeechMessageCallback;

  void setRtcSpeechMessageCallback(RtcSpeechMessageCallback callback) {
    _rtcSpeechMessageCallback = callback;
  }

  void removeRtcSpeechMessageCallback() {
    _rtcSpeechMessageCallback = null;
  }


  void joinRoom(String thirdGroupId) async {
    this.thirdGroupId = thirdGroupId;
    if (this.thirdGroupId.isEmpty) {
      return;
    }
  }

  void openMicrophone() {
    joinRoom(this.thirdGroupId);
  }

  void closeMicrophone() {
    _cancelPendingNavigation();
  }

  void leaveRoom() {
    _rtcSpeechMessageCallback = null;
    this.thirdGroupId = "";
  }

  void sendMessage(String content, {String? tId}) {
    _cancelPendingNavigation();
    final thirdId = tId ?? this.thirdGroupId;
    // 设置新的2秒延迟导航
    _navigationTimer = Timer(Duration(seconds: 1), () {
      GroupChatMessageContentType contentType =
          GroupChatMessageContentType.textAndFile;
      //构建临时消息
      UserInfoModel userInfoModel = Get
          .find<StorageService>()
          .getUserInfoData();
      String messageCreateTime = DateTime
          .now()
          .millisecondsSinceEpoch
          .toString();
      GroupChatMessageModel tempMessage = GroupChatMessageModel(
          third_chat_group_id: thirdId,
          lang: ApiProvider.getLanguageCode(),
          sender_user: GroupChatMentionUserModel(
              id: userInfoModel.id,
              username: userInfoModel.username?.value,
              avatar_url: userInfoModel.avatarUrl?.value),
          messageCreateTime: messageCreateTime,
          messageContent: content,
          content_type: contentType.value,
          message_type: 1,
          attachments: [
            GroupChatAttachmentsModel(
                attachment_type: AttachmentType.audio.value,
                attachment_url: "",
                duration_in_seconds: 0)
          ],
          inputType: InputType.audio.value);
      //发送消息
      Get.find<AgoraLogic>().sendTextMessageMap(
          thirdId, ChatType.GroupChat, tempMessage.toJson(), success: () {
        if (_rtcSpeechMessageCallback != null) {
          _rtcSpeechMessageCallback?.onMessageReceived(thirdId, tempMessage);
        }
      }, fail: (error) {
        showFailToast(error);
      });
      completedText = '';
      speechingText = '';
    });
  }

  // 取消待处理的导航
  void _cancelPendingNavigation() {
    _navigationTimer?.cancel();
    _navigationTimer = null;
  }
}



mixin RtcSpeechMessageCallback {
  //返回当前发送的消息
  void onMessageReceived(
      String thirdGroupId, GroupChatMessageModel messageData);
}
