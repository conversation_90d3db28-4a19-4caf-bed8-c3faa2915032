class RtcGroupPersonDataModel {
  final int status;
  final String message;
  final User user;

  RtcGroupPersonDataModel({
    required this.status,
    required this.message,
    required this.user,
  });

  factory RtcGroupPersonDataModel.fromJson(Map<String, dynamic> json) {
    return RtcGroupPersonDataModel(
      status: json['status'] as int,
      message: json['message'] as String,
      user: User.fromJson(json['user'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'user': user.toJson(),
    };
  }
}

class User {
  final String? userPhone;
  final String? appleId;
  final String username;
  final bool isActive;
  final String lastLogin;
  final String id;
  final String email;
  final String avatarUrl;
  final String passwordHash;
  final String createdAt;
  final String authProvider;

  User({
    this.userPhone,
    this.appleId,
    required this.username,
    required this.isActive,
    required this.lastLogin,
    required this.id,
    required this.email,
    required this.avatarUrl,
    required this.passwordHash,
    required this.createdAt,
    required this.authProvider,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userPhone: json['user_phone'] ??'',
      appleId: json['apple_id'] ??'',
      username: json['username'] ??'',
      isActive: json['is_active'] as bool,
      lastLogin:json['last_login']!=null? json['last_login'] as String:'',
      id: json['id'] ??'',
      email: json['email'] ??'',
      avatarUrl: json['avatar_url']!=null? json['avatar_url'] as String:'',
      passwordHash: json['password_hash'] ??'',
      createdAt: json['created_at'] ??'',
      authProvider: json['auth_provider'] ??'',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_phone': userPhone,
      'apple_id': appleId,
      'username': username,
      'is_active': isActive,
      'last_login': lastLogin,
      'id': id,
      'email': email,
      'avatar_url': avatarUrl,
      'password_hash': passwordHash,
      'created_at': createdAt,
      'auth_provider': authProvider,
    };
  }
}
