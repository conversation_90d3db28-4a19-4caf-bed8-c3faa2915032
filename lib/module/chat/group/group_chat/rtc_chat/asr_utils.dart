import 'package:asr_plugin/asr_plugin.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:new_agnes/generated/l10n.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../../../../../api/Api.dart';
import '../../../../../api/ApiProvider.dart';
import '../../../../../api/StorageService.dart';
import '../../../../../utils/inAppLogUtil.dart';
import '../../../../../utils/logger_utils.dart';
import '../../../../debug/global.dart';
import '../../../model/third_sdk_model.dart';
import 'rtc_speech_send_message_utils.dart';

class AsrUtils {
  static final AsrUtils _instance = AsrUtils._internal();

  static AsrUtils get instance => _instance;

  AsrUtils._internal();

  late ASRControllerConfig config;
  late ASRController controller;
  List<String> sentences = [];
  String result = "";
  int dataSourceType = 0;
  int reconnectNum = 0;

  //初始化
  Future<void> init() async {
    config = ASRControllerConfig();
    config.filter_dirty = 1; //是否过滤脏词
    config.filter_modal = 0; //过滤语气词
    config.filter_punc = 0; //过滤句末的句号
    // config.silence_detect = true; //静音检测功能,开启后检测到静音会停止识别
    // config.silence_detect_duration = 5000; //静音检测时长,开启静音检测功能后生效
    config.vad_silence_time = 800; //语音断句检测阈值
    config.is_compress = false;
    config.hotword_id = '5b8b63928e1c11f0a90c446a2eb5fd98';
    ThirdSDKModel thirdSDKData = Get.find<StorageService>().getThirdSDKData();
    if (thirdSDKData != null &&
        thirdSDKData.TENCENT_APP_ID!.isNotEmpty &&
        thirdSDKData.TENCENT_SECRET_ID!.isNotEmpty &&
        thirdSDKData.TENCENT_SECRET_KEY!.isNotEmpty) {
      config.appID = int.parse('${thirdSDKData.TENCENT_APP_ID}');
      config.secretID = thirdSDKData.TENCENT_SECRET_ID!;
      config.secretKey = thirdSDKData.TENCENT_SECRET_KEY!;
    }
    controller = await config.build();
  }

  Future<void> startAsrSpeechRecognition(Stream<Uint8List> source) async {
    result = "";
    sentences.clear(); // 清空句子列表，避免累积导致性能下降
    try {
      if (controller != null) {
        await controller?.release();
      }
      controller = await config.build();
      Stream<ASRData> asr_stream;
      if (source == null) {
        asr_stream = controller!.recognize();
      } else {
        asr_stream = controller!.recognizeWithDataSource(source);
        logError("ASR识别 初始化流：");
      }
      asr_stream.listen((val) {
        switch (val.type) {
          case ASRDataType.SLICE:
            var id = val.id!;
            var res = val.res!;
            // if (id >= sentences.length) {
            //   for (var i = sentences.length; i <= id; i++) {
            //     sentences.add("");
            //   }
            // }
            // sentences[id] = res;
            // result = sentences.map((e) => e).join("");
            // Log.e("ASR识别 片 ：${result}");
            logError("ASR识别 片：id=${id}   ${res}");
            reconnectNum = 0;
            break;
          case ASRDataType.SEGMENT:
            logError(
                "${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second} ASR识别 结果：${val.res}");
            if (val.res!.isNotEmpty) {
              RtcSpeechSendMessageUtils.instance.sendMessage(val.res!);
            }
            result = "";
            sentences.clear(); // 清空句子列表
            break;
          case ASRDataType.SUCCESS:
            result = val.result!;
            sentences.clear(); // 清空句子列表
            logError("ASR识别 最终结果 ：${result}");
            break;
          case ASRDataType.NOTIFY:
            logError("ASASR识别R 通知：${val.info}");
            break;
        }
      }, onError: (error) {
        logError("ASR识别流错误: ${error.code} ${error.message}");
        sentences.clear(); // 出错时清空句子列表
      }, onDone: () {
        LoggerUtils.d("ASR识别流完成");
        sentences.clear(); // 完成时清空句子列表

      });
    } on ASRError catch (e) {
      logError("ASR识别 错误码：${e.code} \n错误信息: ${e.message} \n详细信息: ${e.resp}");
      sentences.clear(); // 出错时清空句子列表
    } catch (e) {
      logError("ASR识别 未知错误: $e");
      sentences.clear(); // 出错时清空句子列表
    }
  }

  Future<void> stopAsrSpeechRecognition() async {
    if(controller!=null){
      await controller?.stop();
    }
    sentences.clear(); // 停止时清空句子列表
  }
}
