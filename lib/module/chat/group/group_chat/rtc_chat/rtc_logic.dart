import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/material.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_all_message_model.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_group_user_info.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_speech_send_message_utils.dart';
import 'package:new_agnes/module/chat/group/group_chat/rtc_chat/rtc_utils.dart';
import 'package:new_agnes/module/chat/group/group_chat_room/CallFloatingButtonWidget.dart';
import 'package:new_agnes/module/chat/group/group_chat_room/rtc_call_timer.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../../api/Api.dart';
import '../../../../../api/ApiProvider.dart';
import '../../../../../api/StorageService.dart';
import '../../../../../dialog/message_dialog.dart';
import '../../../../../generated/l10n.dart';
import '../../group_chat_room/CallAndMessageToastUtils.dart';
import '../../../../mine/model/UserInfoModel.dart';
import '../../../model/third_sdk_model.dart';
import '../../group_chat_room/ChannelStatusModel.dart';
import '../../group_chat_room/group_chat_room_view.dart';
import '../../message_chat/group_chat_message_page.dart';
import '../agora_logic.dart';
import 'FeatureAccessModel.dart';
import 'rtc_event_model.dart';
import 'rtc_group_person_data_model.dart';
import 'rtc_message_model.dart';

enum OperationType {
  CHANNEL_CREATED, //频造创建
  CHANNEL_DESTROY, //频道更新
  BROADCASTER_JOINED, //主播加入
  BROADCASTER_LEFT, //主播离开
  MEMBER_JOINED, //频道感员加入
  MEMBER_LEFT, //频道成员离开
  ;

  int get type {
    switch (this) {
      case OperationType.CHANNEL_CREATED:
        return 101;
      case OperationType.CHANNEL_DESTROY:
        return 102;
      case OperationType.BROADCASTER_JOINED:
        return 103;
      case OperationType.BROADCASTER_LEFT:
        return 104;
      case OperationType.MEMBER_JOINED:
        return 107;
      case OperationType.MEMBER_LEFT:
        return 108;
    }
  }
}

class RtcLogic extends GetxController {
  RxList<Member> userList = <Member>[].obs;
  int localUid = 0;
  int rtcType = 0; //0 创建  1 加入
  String rtcChannelId = '';
  String? appKey;
  int userSpeekNum = 0; //远程用户说话
  bool isOneselfSpeek = false; //自己说话
  // 判断当前群是否存在群聊
  final isExistChannel = false.obs;

  // 是否显示加入群聊弹窗
  final isShowJoinChannel = false.obs;
  RxBool isShowDanum = false.obs;

  // 群聊回调数据model
  ChannelStatusModel? model;

  /// 用户是否加入房间
  bool isJoin = false;
  final rxJoin = false.obs;

  /// 顶部来电弹窗和悬浮按钮显示时间总时长
  int totalSeconds = 30;

  /// 顶部来电弹窗是否显示
  bool isShowCallingAlert = false;

  /// 来电挂断弹窗是否显示
  bool inviteAlertIsShow = false;

  RxBool isShowLoading = true.obs;
  String thirdGroupId = "";
  String currentGroupId = "";

  /// 接受邀请加入的去聊消息
  RtcMessageModel? messageModel;
  final robotJoin = false.obs;

  /// 用于取消云转录重试的标志
  bool _shouldCancelRetry = false;

  //用户根据灰度权限显示是否能够发起语音聊天
  bool isFeatureRtcChat = false;
  RxList userPageList = [].obs;

  // 字幕相关
  RxList<Subtitle> subtitleList = <Subtitle>[].obs; // 字幕列表
  Rx<Subtitle> lastSubtitle = Subtitle("".obs, "", "", 0).obs; // 最后更新的一行字幕

  @override
  void onInit() {
    getFeatureAccess();
    super.onInit();
  }

  /**
   * rtc 初始化 与 监听
   */
  void initRtc() {
    CallAndMessageToastUtils.init(Get.context!);
    ThirdSDKModel thirdSDKData = Get.find<StorageService>().getThirdSDKData();
    if (thirdSDKData.AGORA_ORG_NAME!.isNotEmpty &&
        thirdSDKData.AGORA_APP_NAME!.isNotEmpty) {
      RtcUtils.instance.init(
        '${thirdSDKData.AGORA_APP_ID}',
        onJoinChanner: (RtcEventModel model) {
          Log.e('本地加入房间成功=${model.connection!.localUid}');
          RtcSpeechSendMessageUtils().joinRoom(thirdGroupId);
          getRtcGroupLog(OperationType.BROADCASTER_JOINED.type);
          UserInfoModel userInfoModel =
              Get.find<StorageService>().getUserInfoData();
          userList.add(Member(
            userId: '${model.connection!.localUid}',
            username: userInfoModel.username!.value!,
            avatarUrl: userInfoModel.avatarUrl!.value!,
            rtcUserId: '${model.connection!.localUid}',
            isSpeak: false.obs,
          ));
          getPageViewUserList();
          if (rtcType == 0) {
            isShowLoading.value = false;
            //创建群的时候插入 agnes
          }
          if (rtcType == 1) {
            //其他用户的加入频道的时候
            getGroupAudioData(rtcChannelId, callback: () async {});
          }

          isJoin = true;
          rxJoin.value = true;
          if (RtcCallTimer.isTimerRunning) {
            RtcCallTimer.stopTimer();
          } else {
            RtcCallTimer.startTimer();
          }
          RtcCallTimer.timmeCallBack = ((time) {
            // CallFloatingButton.refreshText(CallStatus.contacting, time);
            // if (CallFloatingButtonWidgetManager().isCreate) {
            CallFloatingButtonWidgetManager()
                .refreshText(CallButtonWidgetStatus.contacting, time);
            // }
          });
        },
        onUserJoined: (RtcEventModel model) {
          Log.e('远程加入房间成功=${model.remoteUid}');
          // Agora的转录机器人进入
          if (model.remoteUid == 2 || model.remoteUid == 3) {
            Log.e('转录机器人加入房间成功=${model.remoteUid}');
            // 忽略Agora的转录机器人进入
            return;
          }

          bool isHaveUser = userList.any((data) {
            if (data.userId == '${model.remoteUid}' &&
                data.username.isNotEmpty) {
              return true; // 找到用户后立即返回 true
            }
            return false;
          });

          {
            if (!isHaveUser) {
              userList.add(Member(
                userId: '${model.remoteUid}',
                username: '',
                avatarUrl: '',
                rtcUserId: '${model.remoteUid}',
                isSpeak: false.obs,
              ));
              getPageViewUserList();
              getGroupAudioJoinUserData('${model.remoteUid}');
            }
          }
        },
        onUserOffline: (RtcEventModel model) {
          Log.e('远程离开房间成功=${model.remoteUid}');
          userList.forEach((Member data) {
            if (data.userId == '${model.remoteUid}') {
              userList.remove(data);
              getPageViewUserList();
            }
          });
        },
        onTokenConnect: (RtcEventModel model) {},
        onAudioVolume: (RtcEventModel model) {
          if (userList != null && userList!.length > 0) {
            initOneselfSpeek();
            if (model.audioVolumList!.length > 0) {
              model.audioVolumList!.forEach((audioList) {
                // print('Rtc语音=${model.audioVolumList!.length}   speakerNumber=${model.speakerNumber}  ${audioList.volume}');
                for (int a = 0; a < userList.length; a++) {
                  if (audioList.uid == 0) {
                    if (audioList.volume! > 10 &&
                        audioList.vad == 1 &&
                        !RtcUtils.instance.isAudio.value) {
                      if ('${localUid}' == userList[a].userId) {
                        userList[a].isSpeak!.value = true;
                      }
                    }
                    isOneselfSpeek = true;
                  } else {
                    if (audioList.volume! > 10 &&
                        audioList.vad == 1 &&
                        '${audioList.uid}' == userList[a].userId) {
                      userList[a].isSpeak!.value = true;
                      // if (RtcUtils.instance.isAudio.value) {
                      //   isOneselfSpeek = true;
                      // }
                    }
                  }
                }
              });
            }
          }
        },
        onCloseChannel: (RtcEventModel model) {
          Log.e('关闭房间成功=${model.remoteUid}');
          getRtcGroupLog(OperationType.BROADCASTER_LEFT.type,
              callback: () async {
            if (userList.length <= 2) {
              try {
                bool isConnected = await ChatClient.getInstance.isConnected();
                UserInfoModel userInfoModel =
                    Get.find<StorageService>().getUserInfoData();
                RtcMessageModel rtcMessageModel = new RtcMessageModel();
                rtcMessageModel.channelId = rtcChannelId;
                rtcMessageModel.groupId = currentGroupId;
                rtcMessageModel.token = '';
                rtcMessageModel.userId = userInfoModel.id;
                rtcMessageModel.userName = userInfoModel.username!.value!;
                rtcMessageModel.avatarUrl = userInfoModel.avatarUrl!.value!;
                rtcMessageModel.groupName = "";
                rtcMessageModel.thirdId = thirdGroupId;
                if (isConnected) {
                  var customMessage = ChatMessage.createCustomSendMessage(
                      targetId: thirdGroupId,
                      event: EventType.rtc_group_cancel.name,
                      chatType: ChatType.GroupChat,
                      params: {
                        "app_customexts": jsonEncode({
                          "third_chat_group_id": thirdGroupId,
                          "sender_user": {
                            "id": Get.find<StorageService>().getAgoraUserId(),
                          },
                          "content": "",
                          "content_type": 4,
                          // 1、文本 / JSON字符串 2、附件 3、文本附件混合 4语音消息
                          "message_type": 1,
                          // 1、聊天 2、人员回复 3、系统消息 4、助手回复 5、@成员 6、@助手
                          "attachments": [],
                          // "rtc": '${jsonEncode(rtcMessageModel)}',
                          "rtc": rtcMessageModel,
                        })
                      });
                  Log.e("Rtc发送内容${jsonEncode(customMessage)}");
                  customMessage.deliverOnlineOnly = true;
                  await ChatClient.getInstance.chatManager
                      .sendMessage(customMessage);
                } else {
                  Get.find<AgoraLogic>().sign();
                }
              } catch (e) {
                Log.e("Rtc发送消息失败:${e}");
              }
              getRtcGroupLog(OperationType.CHANNEL_DESTROY.type);
              stopAgoraRtcTranscription();
            }
            userList.clear();
            getPageViewUserList();
          });
        },
        onStreamMessage: (RtcStreamModel streamModel) {
          // debugPrint(
          //     '接收到Rtc来自【${streamModel.remoteUid}】的消息【${streamModel.content}】----isEnd【${streamModel.isFinal}】');

          // 查找用户列表，取头像跟名称
          var user = userList.firstWhereOrNull((u) {
            return u.rtcUserId == '${streamModel.remoteUid}';
          });

          // 没找到说话的人，正常流程不会找不到
          if (user == null) {
            debugPrint("unknown user");
            return;
          }

          bool isHandled = false;
          // 添加或更新字幕
          var sub = subtitleList.lastWhere((sub) {
            return sub.uid == streamModel.remoteUid;
          }, orElse: () {
            isHandled = true;
            // 没找到，添加新的字幕
            Subtitle subtitle = Subtitle(
              streamModel.content,
              user.avatarUrl,
              user.username,
              streamModel.remoteUid!,
            );

            subtitle.isFinal.value = streamModel.isFinal;
            subtitleList.add(subtitle);
            lastSubtitle.value = subtitle;
            // 按照群成员数量，移除最前面的字幕
            if (subtitleList.length > userList.length) {
              subtitleList.removeRange(
                  0, subtitleList.length - max(userList.length, 5));
            }

            return subtitle;
          });

          if (isHandled) {
            // 已处理，跳过
            return;
          }

          if (sub.isFinal.value) {
            // 判断是否结束一句话，结束了就添加新的字幕
            var newSub = sub.copyWith(content: streamModel.content);
            newSub.isFinal.value = streamModel.isFinal;
            subtitleList.add(newSub);
            lastSubtitle.value = newSub;
            // 按照群成员数量，移除最前面的字幕
            if (subtitleList.length > max(userList.length, 5)) {
              // 保留最后 keepCount 项，删除其余所有项
              subtitleList.removeRange(
                  0, subtitleList.length - max(userList.length, 5));
            }
          } else {
            sub.content.value = streamModel.content.value;
            sub.isFinal.value = streamModel.isFinal;
            lastSubtitle.value = sub;
          }
        },
      );
    }
  }

  void initOneselfSpeek() {
    if (userList != null && userList!.length > 0) {
      if (isOneselfSpeek) {
        for (int a = 0; a < userList.length; a++) {
          userList[a].isSpeak!.value = false;
        }
        isOneselfSpeek = false;
      }
    }
  }

  /*
    chatId:群组Id
    message:消息内容
    senderUserId:发送者Id
  * */
  void sendRtcMessage(
      String channelId,
      String groupName,
      String groupId,
      String thirdGroupId,
      String message,
      ChatType type,
      List<String> selectIds,
      {int count = 0,
      Function()? success}) async {
    try {
      bool isConnected = await ChatClient.getInstance.isConnected();
      UserInfoModel userInfoModel =
          Get.find<StorageService>().getUserInfoData();
      RtcMessageModel rtcMessageModel = new RtcMessageModel();
      rtcMessageModel.channelId = channelId;
      rtcMessageModel.groupId = groupId;
      rtcMessageModel.token = '';
      rtcMessageModel.userId = userInfoModel.id;
      rtcMessageModel.userName = userInfoModel.username!.value!;
      rtcMessageModel.avatarUrl = userInfoModel.avatarUrl!.value!;
      rtcMessageModel.groupName = groupName;
      rtcMessageModel.thirdId = thirdGroupId;
      rtcMessageModel.targets = selectIds;
      if (isConnected) {
        var customMessage = ChatMessage.createCustomSendMessage(
            targetId: thirdGroupId,
            event: EventType.rtc_group_invite.name,
            chatType: type,
            params: {
              "app_customexts": jsonEncode({
                "third_chat_group_id": thirdGroupId,
                "sender_user": {
                  "id": Get.find<StorageService>().getAgoraUserId(),
                },
                "content": message,
                "content_type": 4, // 1、文本 / JSON字符串 2、附件 3、文本附件混合 4语音消息
                "message_type": 1, // 1、聊天 2、人员回复 3、系统消息 4、助手回复 5、@成员 6、@助手
                "attachments": [],
                // "rtc": '${jsonEncode(rtcMessageModel)}',
                "rtc": rtcMessageModel,
              })
            });
        LoggerUtils.d("Rtc发送内容${jsonEncode(customMessage)}");
        customMessage.deliverOnlineOnly = true;
        await ChatClient.getInstance.chatManager.sendMessage(customMessage);
        success?.call();
      } else {
        Get.find<AgoraLogic>().sign();
      }
    } catch (e) {
      Log.e("Rtc发送消息失败:${e}");
    }
  }

  /**
   * 挂断接听其他邀请弹窗
   */
  hangUpOtherInvite(Function callBack) {
    inviteAlertIsShow = true;
    showDialog(
        context: Get.context!,
        builder: (context) {
          return MessageDialog(
            () {
              inviteAlertIsShow = false;
              Get.back();
            },
            () {
              inviteAlertIsShow = false;
              Get.back();
              callBack();
            },
            title: "",
            data: S
                .of(context)
                .ifYouAnswerTheIncomingCallYourcurrentCallWillBeEnded,
            isEdit: false,
            maxLine: 2,
            height: 220.h,
            onLeftName: S.of(context).cancel,
            onRightName: S.of(context).answerCall,
          );
        }).then((value) {
      inviteAlertIsShow = false;
      Log.d("弹窗消失回调-----$value");
    });
  }

  /**
   * rtc 语音消息监听
   */
  void addRtcMessageListener(ChatMessage msg) {
    Log.e("Rtc收到消息:${jsonEncode(msg)}");
    var body = msg.body.toJson();
    Map<dynamic, dynamic> params = body['params'];
    if (params['app_customexts'] != null) {
      RtcAllMessageModel rtcAllModel =
          RtcAllMessageModel.fromJson(jsonDecode(params['app_customexts']));
      UserInfoModel userInfoModel =
          Get.find<StorageService>().getUserInfoData();
      bool isHave = false;
      if (rtcAllModel.rtc!.targets!.length > 0) {
        isHave = rtcAllModel.rtc!.targets!
            .any((item) => item == '${userInfoModel.id}'); // 返回 true
      }
      if (isHave) {
        if (body['event'] == EventType.rtc_group_invite.name) {
          LoggerUtils.d(
              "当前悬浮框的状态-----$isShowCallingAlert,-------${CallFloatingButtonWidgetManager().status}");
          if (!isShowCallingAlert &&
              (CallFloatingButtonWidgetManager().status ==
                      CallButtonWidgetStatus.contacting ||
                  CallFloatingButtonWidgetManager().status ==
                      CallButtonWidgetStatus.idle)) {
            CmUtils.zhenDong();
            if (rtcAllModel.rtc != null) {
              messageModel = rtcAllModel!.rtc!;
              CallFloatingButtonWidgetManager().currentGroupId =
                  rtcAllModel.rtc!.groupId!;
              showPhoneCallAlertWidget();
            }
          }
        }
      }
        if (body["event"] == EventType.rtc_group_cancel.name) {
          if (currentGroupId == rtcAllModel.rtc!.groupId!) {
            isExistChannel.value = false;
          }
          if (CallFloatingButtonWidgetManager().currentGroupId.isNotEmpty &&
              CallFloatingButtonWidgetManager().currentGroupId ==
                  rtcAllModel.rtc!.groupId) {
            CallAndMessageToastUtils.cancelAllToast();
            if (CallFloatingButtonWidgetManager().status !=
                CallButtonWidgetStatus.contacting) {
              CallFloatingButtonWidgetManager().closeOverlay();
              CallFloatingButtonWidgetManager()
                  .setCallStatus(CallButtonWidgetStatus.idle);
            }
            if (inviteAlertIsShow) {
              Get.back();
            }

        }
      }
    }
  }

  /// 来电通知显示
  void showPhoneCallAlertWidget() {
    String groupId = messageModel!.groupId!;
    String groupName = messageModel!.groupName!;
    String thirdId = messageModel!.thirdId!;
    String channelId = messageModel!.channelId!;
    isShowCallingAlert = true;
    // 监听群聊邀请
    CallAndMessageToastUtils.callFloatingAlert(
      name: messageModel!.userName!,
      image: messageModel!.avatarUrl!,
      groupName: messageModel!.groupName!,
      duration: Duration(seconds: totalSeconds),
      acceptCallBack: () {
        totalSeconds = 30;
        if (isJoin) {
          hangUpOtherInvite(() {
            leaveChannel().then((_) {
              // CallFloatingButtonWidgetManager().hideBtn();
              CallFloatingButtonWidgetManager().closeOverlay();
              CallFloatingButtonWidgetManager()
                  .setCallStatus(CallButtonWidgetStatus.contacting);
              Get.back();
              Get.to(
                  GroupChatRoomPage(
                    groupId: groupId,
                    groupName: groupName,
                    thirdId: thirdId,
                  ));
            });
          });
        } else {
          // if(CallFloatingButtonWidgetManager().isCreate){
          //   CallFloatingButtonWidgetManager().setCallStatus(
          //       CallButtonWidgetStatus.contacting);
          //   CallFloatingButtonWidgetManager().hideBtn();
          // }
          CallFloatingButtonWidgetManager().closeOverlay();
          CallFloatingButtonWidgetManager()
              .setCallStatus(CallButtonWidgetStatus.contacting);
          Get.to(
              GroupChatRoomPage(
                groupId: groupId,
                groupName: groupName,
                thirdId: thirdId,
              ));
        }
      },
      timerCallBack: (time) {
        totalSeconds = time;
        if (totalSeconds == 0) {
          totalSeconds = 30;
          isShowCallingAlert = false;
          if (CallFloatingButtonWidgetManager().status !=
              CallButtonWidgetStatus.contacting) {
            CallFloatingButtonWidgetManager()
                .setCallStatus(CallButtonWidgetStatus.idle);
          }
        }
      },
      floatIsShowingCallBack: (isShowing) {
        isShowCallingAlert = isShowing;
      },
      draggingCallBack: () async {
        if (CallFloatingButtonWidgetManager().status ==
            CallButtonWidgetStatus.contacting) {
          /// 接听时销毁倒计时计时器，以防进入房间后倒计时结束悬浮按钮隐藏
          CallFloatingButtonWidgetManager().destroyTimer();
        }
        if (CallFloatingButtonWidgetManager().status ==
            CallButtonWidgetStatus.idle) {
          if (totalSeconds != 0) {
            showSystemAlertWindowPermissionDialog(() {
              CallFloatingButtonWidgetManager()
                  .setCallStatus(CallButtonWidgetStatus.waiting);
              showFloatingButtonWidget(
                groupName: groupName,
                groupId: groupId,
                thirdId: thirdId,
                type: rtcType,
              );
            });
          }
        }
      },
      endingCallBack: () {
        totalSeconds = 30;
        if (CallFloatingButtonWidgetManager().status !=
            CallButtonWidgetStatus.contacting) {
          CallFloatingButtonWidgetManager()
              .setCallStatus(CallButtonWidgetStatus.idle);
        }
        CallAndMessageToastUtils.callFloatingCancel(sendMessageCallBack: () {
          Get.to(GroupChatMessagePage(
            groupName: groupName,
            groupId: groupId,
            thirdId: thirdId,
            type: ChatConversationType.GroupChat,
          ));
        });
      },
      autoDismissCallBack: () {
        totalSeconds = 30;
        isShowCallingAlert = false;
      },
    );
  }

  /// 悬浮框 widget 显示
  void showFloatingButtonWidget({
    required int type,
    required String groupId,
    required String thirdId,
    required String groupName,
    Function()? callback,
  }) async {
    try {
      LoggerUtils.d("显示悬浮按钮 Widget");
      CallFloatingButtonWidgetManager().setOnButtonTapCallBack(callback: () {
        handleFloatingButtonClick(
            type: type,
            groupId: groupId,
            thirdId: thirdId,
            groupName: groupName);
      });
      CallFloatingButtonWidgetManager()
          .initOverlay(status: CallFloatingButtonWidgetManager().status);
      if (CallFloatingButtonWidgetManager().status ==
          CallButtonWidgetStatus.waiting) {
        CallFloatingButtonWidgetManager().startTimer(totalSeconds);
        CallFloatingButtonWidgetManager()
            .initOverlay(status: CallButtonWidgetStatus.waiting);
        CallFloatingButtonWidgetManager().refreshText(
            CallButtonWidgetStatus.waiting, 0,
            waiting: S.of(Get.context!).waiting);
      }
      if (CallFloatingButtonWidgetManager().status ==
          CallButtonWidgetStatus.contacting) {
        CallFloatingButtonWidgetManager().refreshText(
            CallButtonWidgetStatus.contacting, RtcCallTimer.totalSeconds);
        RtcCallTimer.timmeCallBack = (time) {
          CallFloatingButtonWidgetManager()
              .refreshText(CallButtonWidgetStatus.contacting, time);
        };
      }
      if (await Permission.systemAlertWindow.status.isGranted &&
          await Permission.notification.status.isGranted) {
        if (await FlutterOverlayWindow.isActive()) {
          // CallFloatingButtonWidgetManager().isCreate = true;
          // CallFloatingButtonWidgetManager().showBtnAgain();
          return;
        } else {
          // CallFloatingButtonWidgetManager().isCreate = true;
          callback?.call();
          await FlutterOverlayWindow.showOverlay(
            enableDrag: true,
            overlayTitle: "${groupName ?? ''}",
            overlayContent: S.of(Get.context!).inAVoiceCall,
            flag: OverlayFlag.defaultFlag,
            visibility: NotificationVisibility.visibilityPublic,
            alignment: OverlayAlignment.centerRight,
            positionGravity: PositionGravity.auto,
            height: 200.w.toInt(),
            width: 200.w.toInt(),
            startPosition: const OverlayPosition(0, -259),
          );
          // await FlutterOverlayWindow.resizeOverlay(width, height, enableDrag);
        }
      } else {
        final List<Permission> _requiredPermissions = [
          Permission.notification,
          Permission.systemAlertWindow,
        ];
        // 先检查所有权限状态
        Map<Permission, PermissionStatus> statuses =
            await _requiredPermissions.request();
        final result = await statuses.values.every((value) => value.isGranted);
        if (result) {
          if (await FlutterOverlayWindow.isActive()) {
            return;
            // CallFloatingButtonWidgetManager().showBtnAgain();
            // CallFloatingButtonWidgetManager().isCreate = true;
          } else {
            callback?.call();
            await FlutterOverlayWindow.showOverlay(
              enableDrag: true,
              overlayTitle: "${groupName ?? ''}",
              overlayContent: '语音通话中',
              flag: OverlayFlag.defaultFlag,
              visibility: NotificationVisibility.visibilityPublic,
              positionGravity: PositionGravity.auto,
              alignment: OverlayAlignment.centerRight,
              height: 200.w.toInt(),
              width: 200.w.toInt(),
              startPosition: const OverlayPosition(0, -259),
            );
          }
        }
      }
    } catch (e) {
      LoggerUtils.d("打印报错信息----$e");
    }
  }

  /// 悬浮框权限弹窗
  Future<bool?> showSystemAlertWindowPermissionDialog(Function callBack) async {
    if (await Permission.notification.status.isGranted) {
      if (await Permission.systemAlertWindow.status.isGranted) {
        // _showCallFloatingButton();
        callBack();
      } else {
        await showDialog(
            context: Get.context!,
            builder: (context) {
              return MessageDialog(
                () {
                  Get.back();
                },
                () async {
                  final result = await Permission.systemAlertWindow.request();
                  if (result.isGranted) {
                    Get.back();
                    callBack();
                  }
                },
                title: S.of(context).floatingWindowNotEnabled,
                isTitleCenter: true,
                height: 250.w,
                data: S
                    .of(context)
                    .youCantMinimizeAVideoCallAsAgnesIsntAuthorizedToUseFloatingWindows,
                isEdit: false,
                maxLine: 2,
                onLeftName: S.of(context).cancel,
                onRightName: S.of(context).setNow,
              );
            });
      }
    } else {
      await Permission.notification.request();
    }
  }

  /**
   * 创建语音群聊天的接口  与 加入群聊语音聊天的接口
   */

  void createGroupAudioChat(int type, String groupId,
      {Function? callback}) async {
    rtcType = type;
    if (!isJoin) {
      isShowLoading.value = true;
      try {
        final response =
            await Get.find<ApiProvider>().get("${Api.createGroupChat}$groupId");
        if (response.statusCode == 200) {
          if (response.bodyString != null) {
            final map = json.decode(response.bodyString!);
            localUid = int.parse(map["rtc_user_id"]);
            rtcChannelId = '${map["channel_id"]}';
            userList.clear();
            getPageViewUserList();
            if (rtcType == 0) {
              getRtcGroupLog(OperationType.CHANNEL_CREATED.type, callback: () {
                getRtcAgnes();
                // 开启Agora云转录
                startAgoraRtcTranscription();
              });
            }
            await RtcUtils.instance.joinChannel(map['token'], map['channel_id'],
                uid: int.parse(map["rtc_user_id"]));
            callback?.call();
          }
        }
      } catch (e) {
        Log.e("创建语音群聊失败");
      }
    }
  }

  Future<void> leaveChannel() async {
    RtcSpeechSendMessageUtils.instance.leaveRoom();
    try {
      // 取消正在进行的云转录重试
      _shouldCancelRetry = true;
      RtcUtils.instance.isAudio.value = false;
      getChannelStatus(rtcChannelId);
      isJoin = false;
      rxJoin.value = false;
      isShowCallingAlert = false;
      inviteAlertIsShow = false;
      totalSeconds = 30;
      CallFloatingButtonWidgetManager().closeOverlay();
      CallFloatingButtonWidgetManager()
          .setCallStatus(CallButtonWidgetStatus.idle);
      CallFloatingButtonWidgetManager().currentGroupId = "";
      RtcCallTimer.stopTimer();
      await RtcUtils.instance.cleanChannel();
    } catch (e) {
      Log.e("退出语音群聊失败");
    }
  }

  @override
  void onClose() {
    // TODO: implement onClose
    leaveChannel();
    CallFloatingButtonWidgetManager().dispose();
    destroyEngine();
    super.onClose();
  }

  /// 销毁引擎
  void destroyEngine() async {
    try {
      await RtcUtils.instance.cleanChannel();
    } catch (e) {
      Log.e("销毁语音引擎失败");
    }
  }

  /**
   * 获取语音群聊详情
   */

  void getGroupAudioData(String channelId, {Function? callback}) async {
    if (channelId.isEmpty) {
      return;
    }
    final response =
        await Get.find<ApiProvider>().post("${Api.rtcGroupData}$channelId", {});
    isShowLoading.value = false;
    if (response.statusCode == 200) {
      if (response.body != null) {
        RtcGroupUserInfo rtcGroupUserInfo =
            RtcGroupUserInfo.fromJson(response.body);
        if (rtcGroupUserInfo.members != null &&
            rtcGroupUserInfo.members.length > 0) {
          rtcGroupUserInfo.members.forEach((data) {
            data.userId = data.rtcUserId;
            bool isHave = userList.any((userData) {
              if (userData.userId == '${data.userId}') {
                return true; // 找到用户后立即返回 true
              }
              return false;
            });
            if (!isHave) {
              userList.add(data);
              getPageViewUserList();
              print('Rtc添加全部用户=${data.userId}');
            }
          });
          agnesSorting();
          oneselfSorting();
        }
        callback?.call();
      }
    }
  }

  //agnes排序
  void agnesSorting() {
    Member? member;
    bool isHaveAgnes = false;

    for (int a = 0; a < userList.length; a++) {
      if (userList[a].userId == '1') {
        isHaveAgnes = true;
        member = userList[a];
        userList.removeAt(a);
        break;
      }
    }
    if (isHaveAgnes) {
      userList.insert(0, member!);
      getPageViewUserList();
    }
  }

  //自己在最后一个
  void oneselfSorting() {
    Member? memberOneself;
    bool isHaveOneself = false;
    if (userList.length > 2) {
      for (int a = 0; a < userList.length; a++) {
        if (userList[a].userId == '${localUid}') {
          isHaveOneself = true;
          memberOneself = userList[a];
          userList.removeAt(a);
          break;
        }
      }
      if (isHaveOneself) {
        userList.insert(1, memberOneself!);
        getPageViewUserList();
      }
    }
  }

  /**
   * 获取语音群加入人的用户信息
   */

  void getGroupAudioJoinUserData(String rtcUserId, {Function? callback}) async {
    final response = await Get.find<ApiProvider>()
        .post("${Api.rtcGroupJoinUser}$rtcUserId", {});
    if (response.statusCode == 200) {
      if (response.body != null) {
        RtcGroupPersonDataModel rtcGroupPersonDataModel =
            RtcGroupPersonDataModel.fromJson(response.body);
        if (rtcGroupPersonDataModel.status == 200) {
          bool isHave = false;
          int index = 0;
          for (int a = 0; a < userList.length; a++) {
            if (userList[a].userId == '${rtcUserId}' &&
                userList[a].username.isEmpty) {
              isHave = true;
              index = a;
            }
          }
          if (isHave) {
            // userList[index].userId=rtcUserId;
            // userList[index].username=rtcGroupPersonDataModel.user.username;
            // userList[index].avatarUrl= rtcGroupPersonDataModel.user.avatarUrl;
            // userList[index].rtcUserId=rtcUserId;
            // userList[index].isSpeak=false.obs;
            userList[index] = Member(
              userId: rtcUserId,
              username: rtcGroupPersonDataModel.user.username,
              avatarUrl: rtcGroupPersonDataModel.user.avatarUrl,
              rtcUserId: rtcUserId,
              isSpeak: false.obs,
            );
          }
          agnesSorting();
          print('Rtc添加用户=${rtcUserId}');
        }
      }
    }
  }

  /**
   * Rtc 后台状态通知
   */

  void getRtcGroupLog(int operationType, {Function? callback}) async {
    final response =
        await Get.find<ApiProvider>().post("${Api.rtcGroupOperationLog}", {
      'channel_id': '${rtcChannelId}',
      'rtc_user_id': '${localUid}',
      'operation_type': operationType,
    });
    if (response.statusCode == 200) {
      if (operationType == OperationType.BROADCASTER_LEFT.type ||
          operationType == OperationType.CHANNEL_DESTROY.type) {
        getChannelStatus(rtcChannelId);
      }
      callback?.call();
    }
  }

  /**
   * 获取用户灰度权限
   */

  void getFeatureAccess({Function? callback}) async {
    isFeatureRtcChat = false;
    final response = await Get.find<ApiProvider>().get("${Api.featureAccess}");
    if (response.statusCode == 200) {
      FeatureAccessModel featureAccessModel =
          FeatureAccessModel.fromJson(response.body);
      if (featureAccessModel != null) {
        featureAccessModel?.features?.forEach((data) {
          if (data!.featureCode == 'ai_chat_v2') {
            //AI聊天V2版本
          }
          if (data!.featureCode == 'ppt_generation') {
            //PPT生成功能
          }
          if (data!.featureCode == 'audio_chat') {
            //语音聊天
            isFeatureRtcChat = true;
          }
        });
      }
      callback?.call();
    }
  }

  /**
   * Rtc 插入agnes
   */

  void getRtcAgnes({Function? callback}) async {
    final response =
        await Get.find<ApiProvider>().post("${Api.rtcInviteAgnes}", {
      'channel_id': '${rtcChannelId}',
    });
    if (response.statusCode == 500) {
      callback?.call();
    }
  }

  //根据声网group_id交换后台系统group_id
  Future<void> exchangeSystemGroupIdByAgoraGroupId(String agoraChatGroupId,
      {Function? callBack}) async {
    Response res = await Get.find<ApiProvider>().get(
        "${Api.getSystemGroupId}?third_chat_group_id=$agoraChatGroupId&third_audio_group_id=");
    if (res.statusCode == 200) {
      final channelId = res.body['group_id'];
      getChannelStatus(channelId, callBack: callBack);
    }
  }

  //获取当前是否有语音群聊
  void getChannelStatus(String channelId, {Function? callBack}) async {
    try {
      if (channelId.isEmpty) return;
      isShowJoinChannel.value = false;
      final response = await Get.find<ApiProvider>()
          .post("${Api.get_channel_status}$channelId", {});
      if (response.statusCode == 200) {
        if (response.bodyString != null && response.bodyString!.isNotEmpty) {
          model =
              ChannelStatusModel.fromJson(json.decode(response.bodyString!));
          UserInfoModel userInfoModel =
              Get.find<StorageService>().getUserInfoData();
          //不显示
          if (model!.members != null) {
            if (model!.members!.length < 2) {
              isShowJoinChannel.value = false;
            } else {
              bool isHave = model!.members!.any((data) {
                if (data.userId == '${userInfoModel.id}') {
                  return true; // 找到用户后立即返回 true
                }
                return false;
              });
              if (isHave && isJoin) {
                isShowJoinChannel.value = false;
              }
              if (isHave && !isJoin) {
                isShowJoinChannel.value = true;
              }
              if (!isHave) {
                isShowJoinChannel.value = true;
              }
            }
          }

          // if (model?.status == "online" && model?.isMember != 1) {
          //   isShowJoinChannel.value = true;
          // } else {
          //   isShowJoinChannel.value = false;
          // }
        }
      }
      isExistChannel.value = isShowJoinChannel.value;
      callBack?.call();
    } catch (e) {
      LoggerUtils.d("get_channel_status接口报错---$e");
    }
  }

  //获取PageView显示的数据
  void getPageViewUserList() {
    int chunkSize = isShowDanum.value ? 9 : 12;
    // List list=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30];
    var list = userList;
    userPageList.clear();
    userPageList.value = List.generate(
      (list.length / chunkSize).ceil(),
      (index) => list.sublist(
        index * chunkSize,
        (index + 1) * chunkSize > list.length
            ? list.length
            : (index + 1) * chunkSize,
      ),
    );
  }

  /// 处理悬浮按钮点击事件
  void handleFloatingButtonClick({
    required int type,
    required String groupId,
    required String thirdId,
    required String groupName,
  }) {
    LoggerUtils.d("_handleFloatingButtonClick 被调用");
    if (CallFloatingButtonWidgetManager().status ==
        CallButtonWidgetStatus.waiting) {
      if (totalSeconds != 0) {
        // CallFloatingButtonWidgetManager().hideBtn();
        CallFloatingButtonWidgetManager().closeOverlay();
        CallFloatingButtonWidgetManager().destroyTimer();
        showPhoneCallAlertWidget();
      } else {
        totalSeconds = 30;
        // CallFloatingButtonWidgetManager().hideBtn();
        CallFloatingButtonWidgetManager().closeOverlay();
      }
      CallFloatingButtonWidgetManager()
          .setCallStatus(CallButtonWidgetStatus.idle);
    }
    if (CallFloatingButtonWidgetManager().status ==
        CallButtonWidgetStatus.contacting) {
      CallFloatingButtonWidgetManager().closeOverlay();
      Get.to(
              GroupChatRoomPage(
                groupId: groupId,
                groupName: groupName,
                thirdId: thirdId,
                type: type,
              ))
          ?.then((_) {
        getChannelStatus(rtcChannelId);
      });
    }
  }

  /**
   * 根据 RTC 的 channelId 开启云转录（支持自动重试，最多重试5次）
   */
  Future<void> startAgoraRtcTranscription() async {
    robotJoin.value = false;
    _shouldCancelRetry = false; // 重置取消标志
    await _startAgoraRtcTranscriptionWithRetry();
  }

  /**
   * 内部方法：具有重试机制的云转录启动
   * 最多重试5次，每次重试间隔递增（1秒、2秒、3秒、4秒、5秒）
   */
  Future<void> _startAgoraRtcTranscriptionWithRetry(
      {int maxRetries = 5}) async {
    int attempt = 0;

    while (attempt < maxRetries && !_shouldCancelRetry) {
      try {
        // 检查是否需要取消
        if (_shouldCancelRetry) {
          Log.e('云转录重试已被取消');
          return;
        }

        Log.e('开始第${attempt + 1}次尝试启动Agora云转录，channelId: $rtcChannelId');

        final response =
            await Get.find<ApiProvider>().post(Api.start_agora_transcription, {
          'channel_id': '${rtcChannelId}',
        });

        if (response.statusCode == 200) {
          robotJoin.value = true;
          Log.e('Agora云转录启动成功，第${attempt + 1}次尝试');
          return; // 成功后直接返回
        } else {
          throw Exception('API返回状态码: ${response.statusCode}');
        }
      } catch (e) {
        attempt++;
        Log.e('第${attempt}次启动Agora云转录失败: $e');

        // 再次检查是否需要取消
        if (_shouldCancelRetry) {
          Log.e('云转录重试已被取消');
          return;
        }

        if (attempt >= maxRetries) {
          // 所有重试都失败了
          robotJoin.value = false;
          Log.e('Agora云转录启动失败，已达到最大重试次数($maxRetries)');
          // 只有在没有被取消的情况下才显示弹窗
          if (!_shouldCancelRetry) {
            _showTranscriptionFailedDialog();
          }
          return;
        }

        // 等待一段时间后重试
        int delaySeconds = 5;
        Log.e('等待${delaySeconds}秒后进行第${attempt + 1}次重试');
        await Future.delayed(Duration(seconds: delaySeconds));
      }
    }
  }

  Future<void> stopAgoraRtcTranscription() async {
    final response =
        await Get.find<ApiProvider>().post(Api.stop_agora_transcription, {
      'channel_id': '${rtcChannelId}',
    });
  }

  /**
   * 显示云转录启动失败的弹窗
   */
  void _showTranscriptionFailedDialog() {
    showDialog(
        context: Get.context!,
        builder: (context) {
          return MessageDialog(
            () {
              // 左侧按钮：关闭弹窗和页面
              Get.back(); // 先关闭弹窗
              leaveChannel(); // 离开RTC频道
              Get.back(); // 再关闭当前页面
            },
            () {
              // 右侧按钮：重新尝试
              Get.back();
              startAgoraRtcTranscription();
            },
            title: "云转录启动失败",
            data: "云转录服务启动失败，已重试5次。您可以手动重新尝试",
            isEdit: false,
            maxLine: 3,
            onLeftName: "退出",
            onRightName: "重试",
          );
        });
  }
}

class Subtitle {
  Rx<String> content = "".obs;
  String avatarUrl;
  String name;
  int uid;
  var isFinal = false.obs;

  Subtitle(this.content, this.avatarUrl, this.name, this.uid);

  Subtitle copyWith({
    Rx<String>? content,
    String? avatarUrl,
    String? name,
    int? uid,
  }) {
    return Subtitle(
      content ?? this.content,
      avatarUrl ?? this.avatarUrl,
      name ?? this.name,
      uid ?? this.uid,
    );
  }

  toJson() {
    return {
      "content": content.value,
      "avatarUrl": avatarUrl,
      "name": name,
      "uid": uid,
      "isFinal": isFinal,
    };
  }
}

class RtcEvent {
  int type; //1 通知会话页面刷新是否有群语音的接口

  RtcEvent({required this.type});
}
