import 'package:flutter/material.dart';

import '../../../../../generated/l10n.dart';

class GroupEmptyWidget extends StatelessWidget {
  final VoidCallback? onButtonPressed;

  const GroupEmptyWidget({
    Key? key,
    this.onButtonPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 100),
          Image.asset(
            'assets/groupChat/group_chat_empty.png',
            width: 262,
            height: 243,
            fit: BoxFit.cover,
          ),
          SizedBox(height: 20),
          Container(
            width: 270,
            alignment: Alignment.center,
            child: Text(
              S.of(context).connectWithFriendsToStartAGroupChat,
              maxLines: 2,
              style: TextStyle(fontSize: 18, color: Colors.white, height: 1.2),
            ),
          ),
          SizedBox(height: 24),
          GestureDetector(
            onTap: onButtonPressed,
            child: Container(
              width: 132,
              height: 36,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                gradient: const LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Color(0xFFFF3BDF),
                    Color(0xFFFF91EE),
                  ],
                  stops: [0.3173, 1.0],
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                '+ ${S.of(context).inviteFriends}',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
