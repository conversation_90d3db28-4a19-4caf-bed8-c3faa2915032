import 'dart:convert';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:new_agnes/module/chat/group/group_chat/agora_logic.dart';

import '../../../../../generated/l10n.dart';
import '../../message_chat/model/group_chat_message_model.dart';

class ChatItem extends StatelessWidget {
  final String id;
  final String name;
  final String owner;
  final VoidCallback onPin;
  final VoidCallback onDelete;

  final bool isGroupType0;

  final bool last;

  const ChatItem({
    Key? key,
    required this.id,
    required this.name,
    required this.onPin,
    required this.onDelete,
    required this.owner,
    required this.last,
    required this.isGroupType0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<AgoraLogic>();
    final width = MediaQuery.of(context).size.width - 76;
    return Slidable(
      enabled: !isGroupType0,
      key: ValueKey(id),
      endActionPane: ActionPane(
        motion: const DrawerMotion(),
        extentRatio: 0.2, // 3个按钮
        children: [
          SlidableAction(
            onPressed: (_) => onDelete(),
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            label: S.of(context).delete,
          ),
          // Obx((){
          //   return SlidableAction(
          //     onPressed: (_) => onPin(),
          //     backgroundColor: Colors.blue,
          //     foregroundColor: Colors.white,
          //     icon: Icons.mark_email_unread,
          //     label: logic.isPinned(id) ? "取消置顶" : '置顶',
          //   );
          // }),
        ],
      ),
      child: Container(
        height: 80,
        color: Colors.transparent,
        child: Stack(
          children: [
            Row(
              children: [
                Obx(() {
                  return groupAvatars(logic.groupAvatars[id] ?? []);
                }),
                SizedBox(width: 16),
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                        right:
                            lastMsgTime(logic.groupLastMessage[id]).length > 5
                                ? 120
                                : 60),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          name,
                          softWrap: true,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 6),
                        Row(
                          children: [
                            Obx(() {
                              int count = logic.unreadCounts[id] ?? 0;
                              bool mentionMe =
                                  logic.mentionMeCache[id] ?? false;
                              return Text(
                                mentionMe
                                    ? "[${S.of(context).someoneMe}]"
                                    : (count == 0 ? "" : '[$count Items]'),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: mentionMe
                                      ? Color(0xffFF3BDF)
                                      : Color.fromRGBO(152, 139, 154, 1),
                                ),
                              );
                            }),
                            Obx(() {
                              int count = logic.unreadCounts[id] ?? 0;
                              bool mentionMe =
                                  logic.mentionMeCache[id] ?? false;
                              return SizedBox(
                                  width: count > 0 || mentionMe ? 8 : 0);
                            }),
                            Expanded(child: Obx(() {
                              ChatMessage? lastMsg = logic.groupLastMessage[id];
                              String content = "";
                              Map<String, dynamic>? body =
                                  lastMsg?.body.toJson();
                              final json = body?["params"]?['app_data'] ??
                                  body?["params"]?['app_customexts'];
                              if (json != null) {
                                Map<String, dynamic>? appData =
                                    jsonDecode(json);
                                if (appData != null) {
                                  GroupChatMessageModel model =
                                      GroupChatMessageModel.fromJson(appData);
                                  dynamic u =
                                      appData["sender_user"]?["username"];
                                  // logError("$name最后$u==${body}");
                                  if (u == null) {
                                    u = appData["rtc"]?["userName"];
                                  }

                                  if (u != null &&
                                      u != "null" &&
                                      u.toString().isNotEmpty) {
                                    u = "$u: ";
                                  } else {
                                    u = "";
                                  }

                                  if (appData['create_join_type'] != null) {
                                    content = model.content;
                                  } else {
                                    if (appData["content_type"] == 1) {
                                      final c = model.content;
                                      if (c.isNotEmpty &&
                                          appData['message_type'] == 4) {
                                        try {
                                          Map<dynamic, dynamic> map =
                                              jsonDecode(c);
                                          List updateItemTask =
                                              map["update_task_items"] ?? [];
                                          List add_task_items =
                                              map["add_task_items"] ?? [];
                                          if (updateItemTask.isNotEmpty) {
                                            content =
                                                "$u ${updateItemTask.first["title"]}";
                                          } else if (add_task_items
                                              .isNotEmpty) {
                                            content =
                                                "$u ${add_task_items.first["title"]}";
                                          } else if (appData['payload'] !=
                                              null) {
                                            content =
                                                "$u ${appData['payload']["title"]}";
                                          } else {
                                            content = "$u ${map['msg']}";
                                          }
                                        } catch (e) {
                                          content = "$u $c";
                                        }
                                      } else {
                                        content = u == null ? c : "$u $c";
                                      }
                                    } else if (appData["content_type"] == 2) {
                                      final String c = model.content;
                                      try {
                                        final map = jsonDecode(c);
                                        if (map != null) {
                                          if (map["vtype"] == "video") {
                                            content =
                                                u == null ? '视频' : "$u 视频";
                                          } else if (map["vtype"] == "image") {
                                            content =
                                                u == null ? "图片" : "$u 图片";
                                          }
                                        }
                                      } catch (e) {
                                        content = "$u $c";
                                      }
                                    } else if (appData["content_type"] == 3) {
                                      content = "$u ${appData["content"]}";
                                    } else if (appData["content_type"] == 4) {
                                      content = "$u ${appData["content"]}";
                                    }
                                  }
                                  if (appData['assistant_action_type'] == 41 ||
                                      appData['assistant_action_type'] == 42) {
                                    content = "";
                                  }
                                } else {
                                  content = "";
                                }
                              } else if (body?["params"]
                                      ?['group_custom_noti'] !=
                                  null) {
                                try {
                                  Map<String, dynamic>? appData = jsonDecode(
                                      body?["params"]?['group_custom_noti']);
                                  if (appData != null) {
                                    GroupChatMessageModel model =
                                        GroupChatMessageModel.fromJson(appData);
                                    content = model.content;
                                  }
                                } catch (e) {
                                  content = "";
                                }
                              }

                              return Text(content,
                                  softWrap: true,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color.fromRGBO(152, 139, 154, 1),
                                  ));
                            }))
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Positioned(
              right: 16,
              top: 16,
              child: Obx(() {
                ChatMessage? snap = logic.groupLastMessage[id];

                return Text(
                  lastMsgTime(snap),
                  style: TextStyle(
                    fontSize: 12,
                    color: Color.fromRGBO(152, 139, 154, 1),
                  ),
                );
              }),
            ),
            Positioned(
                left: 59,
                top: 11,
                child: Obx(() {
                  int? count = logic.unreadCounts[id];
                  if ((count ?? 0) > 0) {
                    return Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle, color: Color(0xFFFF080C)));
                  }
                  return SizedBox();
                })),
            Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: width,
                  height: last ? 0 : 0.5,
                  color: Color.fromRGBO(115, 61, 125, 0.47),
                ))
          ],
        ),
      ),
    );
  }

  String lastMsgTime(ChatMessage? snap) {
    String createTime = "";
    Map<String, dynamic>? body = snap?.body.toJson();
    final json =
        body?["params"]?['app_data'] ?? body?["params"]?['app_customexts'];
    if (json != null) {
      Map<String, dynamic>? appData = jsonDecode(json);
      if (appData?["created_at"] != null) {
        createTime =
            formatSmartDate(appData?["created_at"] ?? appData?['serverTime']);
      } else {
        createTime = "";
      }
    } else if (body?["params"]?['group_custom_noti'] != null) {
      try {
        Map<String, dynamic>? appData =
            jsonDecode(body?["params"]?['group_custom_noti']);
        if (appData != null) {
          GroupChatMessageModel model = GroupChatMessageModel.fromJson(appData);
          createTime = formatSmartDate(model.created_at ?? "");
        }
      } catch (e) {
        createTime = "";
      }
    }
    return createTime;
  }

  Widget groupAvatars(List<String> avatarUrls) {
    int count = avatarUrls.length > 4 ? 4 : avatarUrls.length;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: Color.fromRGBO(152, 139, 154, 1),
      ),
      margin: EdgeInsets.only(left: 16),
      width: 48,
      height: 48,
      alignment: Alignment.center,
      child: count == 1
          ? ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: avatarUrls[0].endsWith(".svg")
                  ? SvgPicture.network(
                      avatarUrls[0],
                      fit: BoxFit.cover,
                      errorBuilder: (_, __, ___) => const SizedBox(),
                    )
                  : CachedNetworkImage(
                      imageUrl: avatarUrls[0],
                      fit: BoxFit.cover,
                      errorWidget: (_, __, ___) => const SizedBox(),
                    ),
            )
          : Center(
              child: Wrap(
                spacing: 2,
                runSpacing: 2,
                alignment: WrapAlignment.center,
                children: List.generate(count, (index) {
                  String avatarUrl = avatarUrls[index];
                  return Container(
                    margin: EdgeInsets.symmetric(
                        horizontal: (count == 3 && index == 0) ? 5 : 0),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(2),
                      child: avatarUrl.endsWith(".svg")
                          ? SvgPicture.network(
                              avatarUrl,
                              width: 22,
                              height: 22,
                              fit: BoxFit.cover,
                              errorBuilder: (_, __, ___) {
                                return Image.asset(
                                  'assets/images/ic_default_user_avatar.webp',
                                  width: 22,
                                  height: 22,
                                  fit: BoxFit.fill,
                                );
                              },
                            )
                          : CachedNetworkImage(
                              imageUrl: avatarUrl,
                              width: 22,
                              height: 22,
                              fit: BoxFit.cover,
                              errorWidget: (_, __, ___) {
                                return Image.asset(
                                    'assets/images/ic_default_user_avatar.webp',
                                    width: 22,
                                    height: 22,
                                    fit: BoxFit.fill);
                              },
                            ),
                    ),
                  );
                }),
              ),
            ),
    );
  }

  String formatSmartDate(String rawDate) {
    DateTime inputDate = DateTime.parse(rawDate);
    DateTime now = DateTime.now();

    // 去掉时间部分，只保留日期
    DateTime today = DateTime(now.year, now.month, now.day);
    DateTime yesterday = today.subtract(Duration(days: 1));
    DateTime inputDay =
        DateTime(inputDate.year, inputDate.month, inputDate.day);

    if (inputDay == today) {
      // 今天：显示小时和分钟
      return DateFormat('HH:mm').format(inputDate);
    } else if (inputDay == yesterday) {
      // 昨天
      return '昨天';
    } else if (inputDay.year == now.year) {
      return DateFormat('MM/dd').format(inputDate);
    } else {
      // 其他日期：显示年月日
      return DateFormat('yyyy/MM/dd').format(inputDate);
    }
  }
}
