import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/group_chat/agora_logic.dart';

class GroupAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onLeftPressed;
  final VoidCallback? onBallPressed;
  final VoidCallback? onMessagePressed;
  const GroupAppBar({
    Key? key,
    required this.title,
    this.onLeftPressed,
    this.onBallPressed,
    this.onMessagePressed,
  }) : super(key: key);

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<AgoraLogic>();

    return AppBar(
      // leading: IconButton(
      //   icon: Image.asset('assets/groupChat/menu.png', width: 24, height: 24),
      //   onPressed: onLeftPressed,
      // ),
      title: Text(
        title,
        style: TextStyle(
            fontSize: 16, fontWeight: FontWeight.w400, color: Colors.white),
      ),
      centerTitle: true,
      backgroundColor: Colors.transparent,
      actions: [
        Stack(
          alignment: Alignment.center,
          children: [
            GestureDetector(
              onTap: onBallPressed,
              child: Container(
                width: 24,
                height: 24,
                color: Colors.transparent,
                margin: EdgeInsets.only(right: 8),
                child: Image.asset('assets/groupChat/smallBall.png',
                    width: 24, height: 24),
              ),
            ),
            Positioned(
                right: 9,
                top: 16,
                child: Obx(() {
                  return Visibility(
                      visible: logic.hasJoinedTips.value,
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(255, 8, 12, 1),
                          shape: BoxShape.circle,
                        ),
                      ));
                }))
          ],
        ),
        Center(
          child: GestureDetector(
            onTap: onMessagePressed,
            child: Container(
              width: 24,
              height: 24,
              color: Colors.transparent,
              margin: EdgeInsets.only(left: 8, right: 16),
              child: Image.asset('assets/groupChat/message.png',
                  width: 24, height: 24),
            ),
          ),
        ),
      ],
      elevation: 0,
    );
  }
}
