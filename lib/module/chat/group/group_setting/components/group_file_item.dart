import 'package:flutter/material.dart';

import '../../../model/grouplist/group_file_bean.dart';
import '../utils/group_manage_util.dart';

class GroupChatFileItem extends StatelessWidget {
  final GroupChatFile chatFile;

  const GroupChatFileItem({
    Key? key,
    required this.chatFile,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 18, bottom: 18, right: 12),
                width: 32,
                height: 32,
                child: _buildFileIcon(),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 16, bottom: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        chatFile.title ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        GroupManageUtils.formatDate(chatFile.createdAt),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color.fromRGBO(152, 139, 154, 1),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        // 分割线
        Container(
          margin: const EdgeInsets.only(left: 60, right: 16), // 32+16+12=60
          height: 1,
          color: const Color.fromRGBO(115, 61, 125, 0.47),
        ),
      ],
    );
  }

  Widget _buildFileIcon() {
    String assetPath;
    // switch (chatFile.objectType) {
    //   case 1:
    //     assetPath = 'assets/groupChat/pptx.png';
    //     break;
    //   case 2:
    //     assetPath = 'assets/groupChat/pdf.png';
    //     break;
    //   default:
    //     assetPath = 'assets/groupChat/pptx.png';
    // }
    assetPath = 'assets/groupChat/icon_html.webp';

    return Image.asset(
      width: 32,
      height: 32,
      assetPath,
      fit: BoxFit.cover,
    );
  }
}
