import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:new_agnes/module/chat/group/message_chat/widgets/group_chat_message_at_widget.dart';
import 'package:path/path.dart';
import '../../../model/grouplist/GroupUserBean.dart';
import '../controllers/group_set_logic.dart';
import '../utils/group_manage_util.dart';
import '../../../../../widget/gradient_popover_selector.dart';

class MemberListItem extends StatelessWidget {
  final String groupId;
  final GroupUserBean member;
  final VoidCallback onTypeTap;

  MemberListItem({
    super.key,
    required this.groupId,
    required this.member,
    required this.onTypeTap,
  });

  final GroupChatSetLogic groupChatSetController =
      Get.find<GroupChatSetLogic>();

  @override
  Widget build(BuildContext context) {
    final canEditRole = groupChatSetController.canEditMemberRole(member);
    final avatarUrl = member.avatarUrl ?? '';
    final isSvg = avatarUrl.toLowerCase().endsWith('.svg');
    // 获取当前成员的 role_code
    final memberRoleCode = groupChatSetController.getMemberRoleCode(member);
    final selectedRoleIntlKey =
        GroupManageUtils.getRoleIntlKey(memberRoleCode, context);

    Widget avatarWidget;
    if (avatarUrl.isEmpty) {
      avatarWidget = Container(
        width: 40.r,
        height: 40.r,
        decoration: BoxDecoration(
          color: Colors.grey[400],
          shape: BoxShape.circle,
        ),
        alignment: Alignment.center,
        child: Text(
          member.name.isNotEmpty ? member.name[0] : '?',
          style: TextStyle(color: Colors.white, fontSize: 20.sp),
        ),
      );
    } else if (isSvg) {
      avatarWidget = SvgPicture.network(
        avatarUrl,
        width: 40.r,
        height: 40.r,
        fit: BoxFit.cover,
      );
    } else {
      avatarWidget = Image.network(
        avatarUrl,
        width: 40.r,
        height: 40.r,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 40.r,
            height: 40.r,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              shape: BoxShape.circle,
            ),
            alignment: Alignment.center,
            child: Text(
              member.name.isNotEmpty ? member.name[0] : '?',
              style: TextStyle(color: Colors.white, fontSize: 20.sp),
            ),
          );
        },
      );
    }

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.symmetric(vertical: 5),
      child: Row(
        children: [
          ClipOval(child: avatarWidget),
          SizedBox(width: 12.w),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(right: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    member.name,
                    style: TextStyle(color: Colors.white, fontSize: 16.sp),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    member.userPhone ?? '',
                    style: TextStyle(
                        color: Color.fromRGBO(152, 139, 154, 1),
                        fontSize: 12.sp),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          canEditRole
              ? GradientPopoverSelector(
                  items: groupChatSetController.getMemberRoleList().map((role) {
                    final roleType =
                        GroupManageUtils.getRoleType(role.role_code);
                    return {
                      'text': GroupManageUtils.getRoleIntlKey(
                          role.role_code, context),
                      'icon': GroupManageUtils.getRoleIcon(roleType),
                    };
                  }).toList(),
                  showDeleteItem: true,
                  onDelete: () {
                    groupChatSetController.remove_member(groupId, member);
                  },
                  selectedIndex:
                      groupChatSetController.getMemberRoleIndex(member),
                  onSelected: (index) {
                    final role =
                        groupChatSetController.getMemberRoleList()[index];
                    groupChatSetController.edit_member(
                      groupId,
                      member,
                      role.role_id ?? '',
                      onResult: (success) {
                        if (success) {
                          final newRoleId = role.role_id ?? '';
                          final idx = groupChatSetController.groupMembers
                              .indexWhere((e) => e.username == member.username);
                          if (idx != -1) {
                            final old =
                                groupChatSetController.groupMembers[idx];
                            groupChatSetController.groupMembers[idx] =
                                GroupUserBean(
                              avatarUrl: old.avatarUrl,
                              username: old.username,
                              userPhone: old.userPhone,
                              email: old.email,
                              roleId: newRoleId,
                              // 其它字段按你的 GroupUserBean 构造函数继续补全
                            );
                          }
                        }
                      },
                    );
                  },
                  child: Container(
                    width: 100,
                    padding: EdgeInsets.symmetric(vertical: 4.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Flexible(
                          child: Text(
                            selectedRoleIntlKey,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style:
                                TextStyle(color: Colors.white, fontSize: 16.sp),
                            textAlign: TextAlign.right,
                          ),
                        ),
                        Image.asset(
                          'assets/groupChat/arrow.png',
                          width: 20,
                          height: 20,
                          fit: BoxFit.cover,
                        ),
                      ],
                    ),
                  ),
                )
              : roleWidget(selectedRoleIntlKey, context),
        ],
      ),
    );
  }

  Widget roleWidget(String roleName, BuildContext context) {
    return Container(
      width: 100,
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Flexible(
            child: Text(
              roleName,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: Colors.white, fontSize: 16.sp),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
