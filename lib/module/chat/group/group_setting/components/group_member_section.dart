import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../generated/l10n.dart';
import '../../../model/grouplist/GroupUserBean.dart';
import '../utils/group_manage_util.dart';

class GroupMemberSection extends StatelessWidget {
  final List<GroupUserBean> members;
  final VoidCallback onManageTap;
  final int currentPage;
  final bool isOwner;
  final Function(int) onPageChanged;

  const GroupMemberSection(
      {super.key,
      required this.members,
      required this.onManageTap,
      required this.currentPage,
      required this.onPageChanged,
      required this.isOwner});

  @override
  Widget build(BuildContext context) {
    int pageCount = (members.length / 10).ceil();
    bool showDots = pageCount > 1;
    bool isSingleRow = pageCount == 1 && members.length <= 5;
    double gridHeight = isSingleRow ? 80 : 160; // 80为一行高度，可根据实际调整

    return Container(
      decoration: BoxDecoration(
        color: Color.fromRGBO(96, 66, 118, 0.56),
        borderRadius: BorderRadius.circular(8.r),
      ),
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          Offstage(
            offstage: isOwner,
            child: SizedBox(
              height: 8,
            ),
          ),
          Offstage(
            offstage: !isOwner,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: onManageTap,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    padding: EdgeInsets.only(right: 8, top: 8, bottom: 12),
                    child: Text(
                      S.of(context).invite,
                      style: TextStyle(
                          fontSize: 14,
                          color: Color.fromRGBO(152, 139, 154, 1)),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.only(right: 8, top: 8, bottom: 12),
                    child: Image.asset(
                      "assets/groupChat/go.png",
                      width: 14,
                      height: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: gridHeight,
            child: PageView.builder(
              itemCount: pageCount,
              onPageChanged: onPageChanged,
              itemBuilder: (context, pageIndex) {
                final pageMembers =
                    members.skip(pageIndex * 10).take(10).toList();
                return GridView.builder(
                  physics: NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 5,
                    crossAxisSpacing: 8.w,
                    childAspectRatio: 0.7,
                  ),
                  itemCount: pageMembers.length,
                  itemBuilder: (context, index) {
                    final member = pageMembers[index];
                    final avatarUrl = member.avatarUrl ?? '';
                    final isSvg = avatarUrl.toLowerCase().endsWith('.svg');
                    return Column(
                      children: [
                        isSvg
                            ? CircleAvatar(
                                radius: 20.r,
                                backgroundColor: Colors.transparent,
                                child: ClipOval(
                                  child: SvgPicture.network(
                                    avatarUrl,
                                    width: 40.r,
                                    height: 40.r,
                                    fit: BoxFit.cover,
                                    placeholderBuilder: (context) => Container(
                                      color: Colors.grey[200],
                                      width: 40.r,
                                      height: 40.r,
                                    ),
                                  ),
                                ),
                              )
                            : CircleAvatar(
                                backgroundImage: avatarUrl.isNotEmpty
                                    ? NetworkImage(avatarUrl)
                                    : null,
                                radius: 20.r,
                                backgroundColor: Colors.grey[200],
                              ),
                        SizedBox(height: 2.h),
                        Text(
                          member.username ?? '',
                          style:
                              TextStyle(color: Colors.white, fontSize: 16.sp),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    );
                  },
                );
              },
            ),
          ),
          SizedBox(height: 8.h),
          if (showDots)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(pageCount, (index) {
                return Container(
                  margin: EdgeInsets.symmetric(horizontal: 2.w),
                  width: 4.w,
                  height: 5.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: currentPage == index
                        ? Color.fromRGBO(255, 59, 233, 1)
                        : Color.fromRGBO(115, 61, 125, 1),
                  ),
                );
              }),
            ),
        ],
      ),
    );
  }
}
