import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../generated/l10n.dart';

class AddMemberEntry extends StatelessWidget {
  final VoidCallback onTap;

  const AddMemberEntry({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 20),
        child: Row(
          children: [
            Container(
              child: Image.asset(
                "assets/groupChat/add_user_icon.png",
                width: 24,
                height: 24,
              ),
            ),
            SizedBox(width: 12.w),
            Text(
              S.of(context).addPeopleToChat,
              style: TextStyle(color: Colors.white, fontSize: 16.sp),
            ),
            Spacer(),
            Container(
              child: Image.asset(
                "assets/groupChat/go.png",
                width: 16,
                height: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
