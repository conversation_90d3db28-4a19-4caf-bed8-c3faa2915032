import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../generated/l10n.dart';

class GroupActionSection extends StatelessWidget {
  final VoidCallback onRenameTap;
  final VoidCallback onFileTap;
  final String groupName;
  final bool isViewer;

  const GroupActionSection(
      {super.key,
      required this.onRenameTap,
      required this.onFileTap,
      required this.groupName,
      required this.isViewer});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: isViewer ? 52.h : 104.h,
      decoration: BoxDecoration(
        color: Color.fromRGBO(96, 66, 118, 0.56),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Offstage(
            offstage: isViewer,
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 0, horizontal: 16.w),
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: onRenameTap,
                child: SizedBox(
                  height: 52.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(S.of(context).groupName,
                          style:
                              TextStyle(color: Colors.white, fontSize: 16.sp)),
                      Expanded(
                        child: Container(
                          alignment: Alignment.centerRight,
                          margin: EdgeInsets.only(right: 8, left: 12),
                          child: Text(
                            groupName,
                            style: TextStyle(
                              color: Color(0xFF988B9A),
                              fontSize: 16.sp,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(right: 8),
                        child: Image.asset(
                          "assets/groupChat/go.png",
                          width: 16,
                          height: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Offstage(
            offstage: isViewer,
            child: Container(
              height: 1.h,
              color: Color.fromRGBO(115, 61, 125, 0.47),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 0, horizontal: 16.w),
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: onFileTap,
              child: SizedBox(
                height: 51.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(S.of(context).groupFiles,
                        style: TextStyle(color: Colors.white, fontSize: 16.sp)),
                    Container(
                      margin: EdgeInsets.only(right: 8),
                      child: Image.asset(
                        "assets/groupChat/go.png",
                        width: 16,
                        height: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
