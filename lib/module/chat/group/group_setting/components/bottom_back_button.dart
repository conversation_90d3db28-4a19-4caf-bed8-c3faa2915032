import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BottomBackButton extends StatelessWidget {
  final String title;
  final VoidCallback onTap;

  const BottomBackButton({super.key, required this.title,required this.onTap});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        margin: EdgeInsets.all(16.w),
        width: double.infinity,
        height: 44.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          gradient: LinearGradient(
            colors: [Color(0xFFFF3BDF), Color(0xFFFF91EE)],
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(10.r),
            onTap: onTap,
            child: Center(
              child: Text(
                title,
                style: TextStyle(color: Colors.black, fontSize: 16.sp),
              ),
            ),
          ),
        ),
      ),
    );
  }
}