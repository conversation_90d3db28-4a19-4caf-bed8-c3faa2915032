import 'dart:convert';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/loading_util.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../../../../../api/Api.dart';
import '../../../../../api/ApiProvider.dart';
import '../../../../../api/StorageService.dart';
import '../../../../../utils/event_bus.dart';
import '../../../model/grouplist/GroupUserBean.dart';
import '../../../model/grouplist/group_file_bean.dart';
import '../../../model/grouplist/role_info_bean.dart';
import '../../group_chat/agora_logic.dart';
import '../utils/group_manage_util.dart';

class GroupChatSetLogic extends GetxController {
  /// 获取成员当前角色 code
  String getMemberRoleCode(GroupUserBean member) {
    return GroupManageUtils.getRoleCodeById(roles, member.roleId);
  }

  final List<RoleInfoBean> roles = Get.find<StorageService>().getRoleInfoData();
  late GroupUserBean currentUser = GroupUserBean();
  var groupName = ''.obs;
  var groupMembers = <GroupUserBean>[].obs;
  var groupFiles = <GroupChatFile>[].obs;
  var systemGroupId = '';
  var agoraGroupId = '';

  @override
  void onInit() {
    super.onInit();
  }

  /// 获取成员当前角色名
  String getMemberRoleName(GroupUserBean member) {
    return GroupManageUtils.getRoleNameById(roles, member.roleId);
  }

  /// 获取成员当前角色id
  String getMemberRoleId(GroupUserBean member) {
    return member.roleId ?? '';
  }

  /// 获取可选角色列表（不含群主）
  List<RoleInfoBean> getMemberRoleList() {
    return GroupManageUtils.getFilteredRoles(roles, filterGroupOwner: true);
  }

  /// 获取成员当前角色在可选列表中的index
  int getMemberRoleIndex(GroupUserBean member) {
    final list = getMemberRoleList();
    return list.indexWhere((role) => role.role_id == member.roleId);
  }

  /// 判断某个成员是否为群主
  bool isMemberGroupOwner(GroupUserBean member) {
    return GroupManageUtils.isGroupOwner(roles, member.roleId);
  }

  // 判断当前用户是否仅可查看群成员
  bool currentUserViewer() {
    for (final role in roles) {
      if (role.role_id == currentUser.roleId) {
        return role.role_code == 'group_viewer';
      }
    }
    return false;
  }

  // 判断当前用户是否可以编辑群成员
  bool canCurrentUserEditRole() {
    for (final role in roles) {
      if (role.role_id == currentUser.roleId) {
        return role.role_code == 'group_owner';
      }
    }
    return false;
  }

  // 判断当前用户是否可以编辑该成员
  bool canEditMemberRole(GroupUserBean member) {
    return canCurrentUserEditRole() && !isMemberGroupOwner(member);
  }

  // 切换成员类型逻辑
  void onChangeType(int index) {
    final member = groupMembers[index];
    groupMembers[index] = GroupUserBean(
      avatarUrl: member.avatarUrl,
      username: member.username,
      roleId: member.roleId,
    );
  }

  //根据声网group_id交换后台系统group_id
  void exchangeSystemGroupIdByAgoraGroupId(
      {String agoraChatGroupId = '', String agoraVideoGroupId = ''}) async {
    Response res = await Get.find<ApiProvider>().get(
        "${Api.getSystemGroupId}?third_chat_group_id=$agoraChatGroupId&third_audio_group_id=$agoraChatGroupId");
    if (res.statusCode == 200) {
      systemGroupId = res.body['group_id'];
    }
  }

  //获取群成员列表
  Future<void> getGroupMembers(String groupId) async {
    try {
      LoadingUtil.show();

      Response res = await Get.find<ApiProvider>()
          .get("${Api.groupUserList}?group_id=$groupId");
      if (res.statusCode == 200) {
        groupMembers.clear();
        groupMembers.value = (res.body['users'] as List)
            .map((e) => GroupUserBean.fromJson(e))
            .where((user) => user.roleId != '0')
            .toList();
        if(res.body['current_user'] != null) {
          currentUser = GroupUserBean.fromJson(res.body['current_user']);
        }
      }
    } catch (e) {
      rethrow;
    } finally {
      LoadingUtil.dismiss();
    }
  }

  //获取群文件
  Future<void> getGroupFiles(String groupId) async {
    LoadingUtil.show();

    Response res = await Get.find<ApiProvider>()
        .get("${Api.getGroup_files}?group_id=$groupId");
    LoadingUtil.dismiss();
    if (res.statusCode == 200) {
      groupFiles.value = (res.body['files'] as List)
          .map((e) => GroupChatFile.fromJson(e))
          .toList();
    }
  }

  //修改群名
  void renameGroup(String groupId, String newName) async {
    try {
      LoadingUtil.show();
      Response res = await Get.find<ApiProvider>().put(
        "${Api.rename_group}/$groupId",
        {"new_name": newName},
      );
      if (res.statusCode == 200) {
        groupName.value = newName;
        var trans = <String, dynamic>{
          "variables": {
            "username":
                Get.find<StorageService>().getUserInfoData().username?.value ??
                    ""
          },
          "key": "group_rename"
        };
        var customMessage = ChatMessage.createCustomSendMessage(
            targetId: agoraGroupId,
            event: EventType.group_custom_noti.name,
            chatType: ChatType.GroupChat,
            params: {
              "group_custom_noti": jsonEncode({
                "translation": trans,
              })
            });
        ChatClient.getInstance.chatManager.sendMessage(customMessage);
        customMessage.status = MessageStatus.SUCCESS;
        ChatClient.getInstance.chatManager
            .getConversation(agoraGroupId)
            .then((value) {
          value
              ?.insertMessage(
                  /*ChatMessage.createCustomSendMessage(
              targetId: groupId,
              event: EventType.group_chat.name,
              chatType: ChatType.GroupChat,
              params: {
                "app_data": jsonEncode({
                  "third_chat_group_id": groupId,
                  "sender_user": {
                    "id": Get.find<StorageService>().getUserInfoData().id,
                  },
                  "content": S.current.group_rename(Get.find<StorageService>().getUserInfoData().username?.value ?? ""),
                  "content_type": 1,
                  "message_type": 3,
                  "translation": trans
                })
              })*/
                  customMessage)
              .then((val) {
            eventBus.fire(GroupInfoEvent(
                groupName: newName, renameGroup: true, msg: customMessage));
          });
        });
        Get.back();
      }
    } catch (e) {
    } finally {
      LoadingUtil.dismiss();
    }
  }

  //删除群成员
  void remove_member(String groupId, GroupUserBean member) async {
    try {
      LoadingUtil.show();

      String? registrationCredential = member.userPhone?.isNotEmpty == true
          ? member.userPhone
          : member.email;
      String authType =
          member.userPhone?.isNotEmpty == true ? 'phone' : 'email';

      final params = {
        "group_id": groupId,
        "user_infos": [
          {
            "role_id": member.roleId,
            "registration_credential": registrationCredential,
            "auth_type": authType,
          }
        ]
      };

      Response res = await Get.find<ApiProvider>().post(
        "${Api.remove_member}",
        params,
      );
      if (res.statusCode == 200) {
        groupMembers.removeWhere((e) => e.id == member.id);
      } else {
        showFailToast("Error: ${res.bodyString ?? 'Remove failed'}");
      }
    } catch (e) {
      showFailToast(
        "Error: Network error",
      );
    } finally {
      LoadingUtil.dismiss();
    }
  }

  //修改成员权限
  void edit_member(
    String groupId,
    GroupUserBean member,
    String roleId, {
    Function(bool success)? onResult,
  }) async {
    try {
      String? registrationCredential = member.userPhone?.isNotEmpty == true
          ? member.userPhone
          : member.email;
      String authType =
          member.userPhone?.isNotEmpty == true ? 'phone' : 'email';

      final params = {
        "group_id": groupId,
        "user_infos": [
          {
            "role_id": roleId,
            "registration_credential": registrationCredential,
            "auth_type": authType,
          }
        ]
      };

      Response res = await Get.find<ApiProvider>().post(
        "${Api.edit_member_type}",
        params,
      );
      if (res.statusCode == 200) {
        if (onResult != null) onResult(true);
      } else {
        if (onResult != null) onResult(false);
      }
    } catch (e) {
      if (onResult != null) onResult(false);
    } finally {}
  }
}
