import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../../generated/l10n.dart';
import '../../../model/grouplist/role_info_bean.dart';

/// 群组角色类型
enum GroupRoleType { viewer, editor, owner, unknown }

/// 群组角色相关工具类
class GroupManageUtils {
  /// 判断输入是否为有效搜索条件：数字>=3位，非数字>=2位
  static bool isValidSearchInput(String input) {
    final isDigitsReally = RegExp(r'^\d+$').hasMatch(input);
    if (isDigitsReally) {
      return input.length >= 3;
    } else {
      return input.length >= 2;
    }
  }

  /// 根据 role_code 返回可国际化的 key
  static String getRoleIntlKey(String? roleCode, BuildContext context) {
    switch (roleCode) {
      case 'group_viewer':
        return S.of(context).viewer;
      case 'group_editor':
        return S.of(context).editor;
      case 'group_owner':
        return S.of(context).owner;
      default:
        return S.of(context).viewer;
    }
  }

  /// 格式化日期
  static String formatDate(String dateStr) {
    final dateTime = DateTime.parse(dateStr);
    final formatter = DateFormat('MM月dd日 HH:mm');
    return formatter.format(dateTime);
  }

  /// 通过 code 获取角色类型枚举
  static GroupRoleType getRoleType(String? code) {
    switch (code) {
      case "group_viewer":
        return GroupRoleType.viewer;
      case "group_editor":
        return GroupRoleType.editor;
      case "group_owner":
        return GroupRoleType.owner;
      default:
        return GroupRoleType.unknown;
    }
  }

  /// 获取角色图标路径
  static String getRoleIcon(GroupRoleType type) {
    switch (type) {
      case GroupRoleType.viewer:
        return 'assets/groupChat/viewer.png';
      case GroupRoleType.editor:
        return 'assets/groupChat/editor.png';
      case GroupRoleType.owner:
        return 'assets/groupChat/owner.png';
      default:
        return '';
    }
  }

  /// 判断是否为邮箱
  static bool isEmail(String input) {
    final emailReg = RegExp(r'^[\w\.-]+@[\w\.-]+\.\w+$');
    return emailReg.hasMatch(input);
  }

  /// 获取过滤后的角色列表（可选是否排除群主）
  static List<RoleInfoBean> getFilteredRoles(List<RoleInfoBean> roles,
      {bool filterGroupOwner = false}) {
    if (filterGroupOwner) {
      return roles.where((e) => e.role_code != 'group_owner').toList();
    }
    return roles;
  }

  /// 获取默认角色（优先 group_viewer，没有则取第一个）
  static RoleInfoBean? getDefaultRole(List<RoleInfoBean> roles,
      {bool filterGroupOwner = false}) {
    final filtered =
        getFilteredRoles(roles, filterGroupOwner: filterGroupOwner);
    if (filtered.isEmpty) return null;
    return filtered.firstWhere(
      (e) => e.role_code == 'group_editor',
      orElse: () => filtered.first,
    );
  }

  /// 通过 role_id 获取角色名
  static String getRoleNameById(List<RoleInfoBean> roles, String? roleId) {
    final matched = roles.firstWhere(
      (role) => role.role_id == roleId,
      orElse: () => RoleInfoBean(role_id: '', role_name: '', role_code: ''),
    );
    return matched.role_name ?? '';
  }

  /// 通过 role_id 获取角色 code
  static String getRoleCodeById(List<RoleInfoBean> roles, String? roleId) {
    final matched = roles.firstWhere(
      (role) => role.role_id == roleId,
      orElse: () => RoleInfoBean(role_id: '', role_name: '', role_code: ''),
    );
    return matched.role_code ?? '';
  }

  /// 判断是否为群主
  static bool isGroupOwner(List<RoleInfoBean> roles, String? roleId) {
    final matched = roles.firstWhere(
      (role) => role.role_id == roleId,
      orElse: () => RoleInfoBean(role_id: '', role_name: '', role_code: ''),
    );
    return matched.role_code == 'group_owner';
  }
}
