import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../generated/l10n.dart';
import '../../../../utils/bgCover.dart';
import '../../../../utils/event_bus.dart';
import '../../../../widget/ComAppbar.dart';
import '../creat_chat/invite_people.dart';
import '../group_chat/agora_logic.dart';
import '../group_chat/group_list_logic.dart';
import 'components/bottom_back_button.dart';
import 'components/group_action_section.dart';
import 'components/group_member_section.dart';
import 'controllers/group_set_logic.dart';
import 'group_chat_file_page.dart';
import 'group_name_edit_page.dart';
import 'manage_member_page.dart';

class GroupSetPage extends StatefulWidget {
  final String third_group_id;
  final String groupName;
  const GroupSetPage(
      {super.key, required this.third_group_id, required this.groupName});

  @override
  State<GroupSetPage> createState() => _GroupSetPageState();
}

class _GroupSetPageState extends State<GroupSetPage> {
  final GroupChatSetLogic controller = Get.put(GroupChatSetLogic());
  StreamSubscription? _messageSubscription;

  int memberPage = 0;

  @override
  void initState() {
    super.initState();
    _messageSubscription = eventBus.on<GroupInfoEvent>().listen((value) {
      if (value.addMember) {
        controller.getGroupMembers(widget.third_group_id);
      }
    });

    controller.groupName.value = widget.groupName;
    controller.agoraGroupId = widget.third_group_id;
    controller.exchangeSystemGroupIdByAgoraGroupId(
        agoraChatGroupId: widget.third_group_id);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.getGroupMembers(widget.third_group_id);
    });
  }

  @override
  void dispose() {
    super.dispose();
    _messageSubscription?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return buildMineBg(
      child: Scaffold(
        appBar: ComAppBar(
          context,
          S.of(context).chatSettings,
          backgroundColor: Colors.transparent,
          textColor: Colors.white,
        ),
        body: Obx(() => Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  SizedBox(height: 16.h),
                  GroupMemberSection(
                    isOwner: controller.canCurrentUserEditRole(),
                    members: controller.groupMembers.value,
                    onManageTap: () {
                      Get.to(InvitePeople(
                        group_id: controller.systemGroupId,
                        agoraGroupId: controller.agoraGroupId,
                        currentUser: controller.currentUser,
                      ));

                    },
                    currentPage: memberPage,
                    onPageChanged: (page) {
                      setState(() {
                        memberPage = page;
                      });
                    },
                  ),
                  SizedBox(height: 16.h),
                  GroupActionSection(
                    isViewer: controller.currentUserViewer(),
                    groupName: controller.groupName.value,
                    onRenameTap: () {
                      Get.to(() => GroupNameEditPage(
                            groupName: controller.groupName.value,
                            groupId: controller.systemGroupId,
                          ));
                    },
                    onFileTap: () {
                      Get.to(() => GroupChatFilePage(
                            groupId: controller.systemGroupId,
                          ));
                    },
                  ),
                ],
              ),
            )),
        backgroundColor: Colors.transparent,
      ),
    );
  }
}
