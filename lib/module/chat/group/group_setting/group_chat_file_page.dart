import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../generated/l10n.dart';
import '../../../../utils/bgCover.dart';
import '../../../../widget/ComAppbar.dart';
import '../../../../widget/empty_wiget.dart';
import '../../../web/single_page_web/page.dart';
import 'components/group_file_item.dart';
import 'controllers/group_set_logic.dart';

class GroupChatFilePage extends StatefulWidget {
  final String groupId;
  const GroupChatFilePage({Key? key, required this.groupId}) : super(key: key);

  @override
  State<GroupChatFilePage> createState() => _GroupChatFilePageState();
}

class _GroupChatFilePageState extends State<GroupChatFilePage> {
  final GroupChatSetLogic groupChatSetController =
      Get.find<GroupChatSetLogic>();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      groupChatSetController.getGroupFiles(widget.groupId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return buildMineBg(
      child: Scaffold(
        appBar: ComAppBar(
          context,
          S.of(context).groupFiles,
          backgroundColor: Colors.transparent,
          textColor: Colors.white,
        ),
        body: Obx(() {
          final files = groupChatSetController.groupFiles;
          if (files.isEmpty) {
            return EmptyWidget(
              imagePath: 'assets/groupChat/file_empty.png',
              message:  S.of(context).noMoreFiles,
            );
          }
          return ListView.builder(
            itemCount: files.length,
            itemBuilder: (context, index) {
              final file = files[index];
              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                child: GroupChatFileItem(chatFile: file),
                onTap: () {
                  Get.to(SingleWidgetWebWidget(
                    file.artifactUrl,
                    title: file.title,
                  ));
                },
              );
            },
          );
        }),
      ),
    );
  }}
