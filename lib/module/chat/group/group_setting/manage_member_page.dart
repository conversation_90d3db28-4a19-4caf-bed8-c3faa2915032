import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../generated/l10n.dart';
import '../../../../utils/bgCover.dart';
import '../../../../widget/ComAppbar.dart';
import '../../model/grouplist/GroupUserBean.dart';
import '../creat_chat/invite_logic.dart';
import '../creat_chat/invite_people.dart';
import 'components/add_member_entry.dart';
import 'components/member_list_item.dart';
import 'controllers/group_set_logic.dart';

class ManageMemberPage extends StatefulWidget {
  final GroupUserBean currentUser;
  final String group_id;
  final String third_group_id;

  const ManageMemberPage(
      {super.key,
      required this.currentUser,
      required this.group_id,
      required this.third_group_id});

  @override
  State<ManageMemberPage> createState() => _ManageMemberPageState();
}

class _ManageMemberPageState extends State<ManageMemberPage> {
  final GroupChatSetLogic groupChatSetController =
      Get.find<GroupChatSetLogic>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp){
      if(mounted){
        groupChatSetController.getGroupMembers(widget.third_group_id);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return buildMineBg(
      child: Scaffold(
        appBar: ComAppBar(
          context,
          S.of(context).manageMembers,
          backgroundColor: Colors.transparent,
          textColor: Colors.white,
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            children: [
              // AddMemberEntry(
              //   onTap: () {
              //     Get.to(InvitePeople(
              //       group_id: groupChatSetController.systemGroupId,
              //       agoraGroupId: groupChatSetController.agoraGroupId,
              //     ));
              //   },
              // ),
              // Container(
              //   height: 1,
              //   color: Color.fromRGBO(115, 61, 125, 0.47),
              //   margin: EdgeInsets.only(bottom: 16),
              // ),
              Expanded(
                child: Obx(() => ListView.builder(
                      itemCount: groupChatSetController.groupMembers.length,
                      itemBuilder: (context, index) {
                        final member =
                            groupChatSetController.groupMembers[index];
                        return MemberListItem(
                          groupId: widget.group_id,
                          member: member,
                          onTypeTap: () =>
                              groupChatSetController.onChangeType(index),
                        );
                      },
                    )),
              )
            ],
          ),
        ),
        backgroundColor: Colors.transparent,
      ),
    );
  }
}
