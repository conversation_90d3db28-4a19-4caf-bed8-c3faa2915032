import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../generated/l10n.dart';
import '../../../../utils/bgCover.dart';
import '../../../../widget/ComAppbar.dart';
import 'components/bottom_back_button.dart';
import 'controllers/group_set_logic.dart';

class GroupNameEditPage extends StatefulWidget {
  final String groupId;
  final String groupName;

  const GroupNameEditPage(
      {Key? key, required this.groupId, required this.groupName})
      : super(key: key);

  @override
  State<GroupNameEditPage> createState() => _GroupNameEditPageState();
}

class _GroupNameEditPageState extends State<GroupNameEditPage> {
  final GroupChatSetLogic groupChatSetController =
      Get.find<GroupChatSetLogic>();
  late final TextEditingController _controller;
  var groupName = "".obs;
  List<String> avatars = [];

  @override
  void initState() {
    super.initState();
    groupChatSetController.groupMembers.forEach((member) {
      if ((member.avatarUrl ?? "").isNotEmpty && avatars.length < 4) {
        avatars.add(member.avatarUrl!);
      }
    });
    _controller = TextEditingController(
      text: (widget.groupName.isNotEmpty) ? widget.groupName : "Group123",
    );
    groupName.value =
        (widget.groupName.isNotEmpty) ? widget.groupName : "Group123";
  }

  @override
  Widget build(BuildContext context) {
    return buildMineBg(
        child: Scaffold(
            appBar: ComAppBar(
              context,
              S.of(context).groupName,
              backgroundColor: Colors.transparent,
              textColor: Colors.white,
            ),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 30),
                  Text(
                    S.of(context).editGroupName,
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    S.of(context).otherGroupMembersWillBeNotifiedAfterTheGroupNameIsChanged,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.white, fontSize: 14),
                  ),
                  const SizedBox(height: 40),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                    child: Row(
                      children: [
                        groupAvatars(avatars),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextField(
                            controller: _controller,
                            style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.w500),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: S.of(context).enterGroupName,
                              hintStyle: TextStyle(color: Colors.white54),
                            ),
                            onChanged: (value) {
                              groupName.value = value;
                            },
                          ),
                        ),
                        Obx(() {
                          return Visibility(
                              visible: groupName.value.isNotEmpty,
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  groupName.value = "";
                                  _controller.clear();
                                },
                                child: Container(
                                  padding: EdgeInsets.all(8),
                                  width: 40,
                                  height: 40,
                                  child: Image.asset(
                                    "assets/groupChat/edit_close.png",
                                    width: 16,
                                    height: 16,
                                  ),
                                ),
                              ));
                        })
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 16),
                    height: 1,
                    color: Color.fromRGBO(115, 61, 125, 0.47),
                  ),
                  const Spacer(),
                  BottomBackButton(
                    title: 'OK',
                    onTap: () {
                      groupChatSetController.renameGroup(
                          widget.groupId, _controller.text);

                      // groupChatSetController.renameGroup(
                      //     widget.groupId, _controller.text);
                    },
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            )));
  }

  Widget groupAvatars(List<String> avatarUrls) {
    int count = avatarUrls.length > 4 ? 4 : avatarUrls.length;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: Color.fromRGBO(152, 139, 154, 1),
      ),
      margin: EdgeInsets.only(left: 16),
      width: 48,
      height: 48,
      alignment: Alignment.center,
      child: count == 1
          ? ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: avatarUrls[0].endsWith(".svg")
                  ? SvgPicture.network(
                      avatarUrls[0],
                      fit: BoxFit.cover,
                      errorBuilder: (_, __, ___) => const SizedBox(),
                    )
                  : Image.network(
                      avatarUrls[0],
                      fit: BoxFit.cover,
                      errorBuilder: (_, __, ___) => const SizedBox(),
                    ),
            )
          : Center(
              child: Wrap(
                spacing: 2,
                runSpacing: 2,
                alignment: WrapAlignment.center,
                children: List.generate(count, (index) {
                  String avatarUrl = avatarUrls[index];
                  return Container(
                    margin: EdgeInsets.symmetric(
                        horizontal: (count == 3 && index == 0) ? 5 : 0),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(2),
                      child: avatarUrl.endsWith(".svg")
                          ? SvgPicture.network(
                              avatarUrl,
                              width: 22,
                              height: 22,
                              fit: BoxFit.cover,
                            )
                          : Image.network(
                              avatarUrl,
                              width: 22,
                              height: 22,
                              fit: BoxFit.cover,
                            ),
                    ),
                  );
                }),
              ),
            ),
    );
  }
}
