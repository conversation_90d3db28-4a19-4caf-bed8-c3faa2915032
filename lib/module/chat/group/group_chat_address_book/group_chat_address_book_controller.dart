import 'dart:async';
import 'dart:typed_data';
import 'package:contacts_service_plus/contacts_service_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:lpinyin/lpinyin.dart';

import '../../../../api/Api.dart';
import '../../../../api/ApiProvider.dart';
import '../../../../utils/inAppLogUtil.dart';
import '../../../../utils/toastUtil.dart';

/// 通讯录联系人数据模型
class AddressBookContact {
  final String id;
  final String name;
  final String displayName;
  final List<String> phoneNumbers;
  final List<String> emails;
  final Uint8List? avatar;
  final String firstLetter;
  final String pinyin;
  final String pinyinShort;

  // 匹配状态
  bool isAppMember;
  bool isSelected;
  String? appUserId;
  String? appUserEmail;
  String? appUserPhone;
  String? appUserAvatar;

  AddressBookContact({
    required this.id,
    required this.name,
    required this.displayName,
    required this.phoneNumbers,
    required this.emails,
    this.avatar,
    required this.firstLetter,
    required this.pinyin,
    required this.pinyinShort,
    this.isAppMember = false,
    this.isSelected = false,
    this.appUserId,
    this.appUserEmail,
    this.appUserPhone,
    this.appUserAvatar,
  });

  /// 从系统联系人创建
  factory AddressBookContact.fromContact(Contact contact) {
    final name = contact.displayName ?? contact.givenName ?? contact.familyName ?? '';
    final cleanName = name.trim();

    // 获取拼音和首字母
    String pinyin = '';
    String pinyinShort = '';
    String firstLetter = '#';

    if (cleanName.isNotEmpty) {
      try {
        pinyin = PinyinHelper.getPinyinE(cleanName, separator: '');
        pinyinShort = PinyinHelper.getShortPinyin(cleanName);

        // 获取首字母
        if (pinyinShort.isNotEmpty) {
          firstLetter = pinyinShort[0].toUpperCase();
          // 确保首字母是A-Z
          if (!RegExp(r'^[A-Z]$').hasMatch(firstLetter)) {
            firstLetter = '#';
          }
        } else if (cleanName.isNotEmpty) {
          final firstChar = cleanName[0].toUpperCase();
          if (RegExp(r'^[A-Z]$').hasMatch(firstChar)) {
            firstLetter = firstChar;
          }
        }
      } catch (e) {
        logError('拼音转换失败: $e');
        // 如果拼音转换失败，尝试直接获取首字母
        if (cleanName.isNotEmpty) {
          final firstChar = cleanName[0].toUpperCase();
          if (RegExp(r'^[A-Z]$').hasMatch(firstChar)) {
            firstLetter = firstChar;
          }
        }
      }
    }

    // 获取电话号码
    final phoneNumbers = contact.phones?.map((phone) => phone.value ?? '').where((phone) => phone.isNotEmpty).toList() ?? [];

    // 获取邮箱
    final emails = contact.emails?.map((email) => email.value ?? '').where((email) => email.isNotEmpty).toList() ?? [];

    return AddressBookContact(
      id: contact.identifier ?? '',
      name: cleanName,
      displayName: cleanName,
      phoneNumbers: phoneNumbers,
      emails: emails,
      avatar: contact.avatar?.isNotEmpty == true ? contact.avatar : null,
      firstLetter: firstLetter,
      pinyin: pinyin,
      pinyinShort: pinyinShort,
    );
  }

  /// 复制并更新匹配状态
  AddressBookContact copyWith({
    bool? isAppMember,
    bool? isSelected,
    String? appUserId,
    String? appUserEmail,
    String? appUserPhone,
    String? appUserAvatar,
  }) {
    return AddressBookContact(
      id: id,
      name: name,
      displayName: displayName,
      phoneNumbers: phoneNumbers,
      emails: emails,
      avatar: avatar,
      firstLetter: firstLetter,
      pinyin: pinyin,
      pinyinShort: pinyinShort,
      isAppMember: isAppMember ?? this.isAppMember,
      isSelected: isSelected ?? this.isSelected,
      appUserId: appUserId ?? this.appUserId,
      appUserEmail: appUserEmail ?? this.appUserEmail,
      appUserPhone: appUserPhone ?? this.appUserPhone,
      appUserAvatar: appUserAvatar ?? this.appUserAvatar,
    );
  }
}

/// 分组联系人数据模型
class ContactGroup {
  final String letter;
  final List<AddressBookContact> contacts;

  ContactGroup({
    required this.letter,
    required this.contacts,
  });
}

/// 后台用户匹配数据模型
class AppUserInfo {
  final String id;
  final String? username;
  final String? email;
  final String? userPhone;
  final String? avatarUrl;
  final bool isActive;

  AppUserInfo({
    required this.id,
    this.username,
    this.email,
    this.userPhone,
    this.avatarUrl,
    this.isActive = true,
  });

  factory AppUserInfo.fromJson(Map<String, dynamic> json) {
    return AppUserInfo(
      id: json['id']?.toString() ?? '',
      username: json['username']?.toString(),
      email: json['email']?.toString(),
      userPhone: json['user_phone']?.toString(),
      avatarUrl: json['avatar_url']?.toString(),
      isActive: json['is_active'] ?? true,
    );
  }
}

/// 群聊通讯录控制器
class GroupChatAddressBookController extends GetxController {
  // 响应式数据
  final RxList<AddressBookContact> _allContacts = <AddressBookContact>[].obs;
  final RxList<ContactGroup> _groupedContacts = <ContactGroup>[].obs;
  final RxList<AddressBookContact> _selectedContacts = <AddressBookContact>[].obs;
  final RxList<AppUserInfo> _appUsers = <AppUserInfo>[].obs;
  final TextEditingController searchController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingAppUsers = false.obs;
  final RxBool _hasPermission = false.obs;
  final RxString _searchText = ''.obs;
  final RxBool _isSelectionMode = false.obs;

  // Getters
  List<AddressBookContact> get allContacts => _allContacts;
  List<ContactGroup> get groupedContacts => _groupedContacts;
  List<AddressBookContact> get selectedContacts => _selectedContacts;
  List<AppUserInfo> get appUsers => _appUsers;

  bool get isLoading => _isLoading.value;
  bool get isLoadingAppUsers => _isLoadingAppUsers.value;
  bool get hasPermission => _hasPermission.value;
  String get searchText => _searchText.value;
  bool get isSelectionMode => _isSelectionMode.value;

  // 搜索相关
  final RxList<ContactGroup> _filteredContacts = <ContactGroup>[].obs;
  List<ContactGroup> get filteredContacts => _filteredContacts;

  // 字母索引
  final RxList<String> _alphabetIndex = <String>[].obs;
  List<String> get alphabetIndex => _alphabetIndex;

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  @override
  void onClose() {
    searchController.dispose();
    scrollController.dispose();
    super.onClose();
  }

  /// 初始化数据
  Future<void> _initializeData() async {
    await checkAndRequestPermission();
    if (_hasPermission.value) {
      await loadContacts();
      await loadAppUsers();
    }
  }

  /// 检查并请求通讯录权限
  Future<bool> checkAndRequestPermission() async {
    try {
      _isLoading.value = true;

      final status = await Permission.contacts.status;

      if (status.isGranted) {
        _hasPermission.value = true;
        return true;
      }

      if (status.isDenied) {
        final result = await Permission.contacts.request();
        _hasPermission.value = result.isGranted;

        if (result.isDenied) {
          // showFailToast(S.of(Get.context!).weNeedAccessToYourContactsToInviteFriends);
          return false;
        }

        return result.isGranted;
      }

      if (status.isPermanentlyDenied) {
        await _showPermissionDialog();
        return false;
      }

      return false;
    } catch (e) {
      logError('检查通讯录权限失败: $e');
      showFailToast('权限检查失败，请重试');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// 显示权限设置对话框
  Future<void> _showPermissionDialog() async {
    return Get.dialog(
      AlertDialog(
        backgroundColor: Color(0xFF1A1A2E),
        title: Text(
          '需要通讯录权限',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          '为了邀请您的朋友加入群聊，我们需要访问您的通讯录。请在设置中开启通讯录权限。',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('取消', style: TextStyle(color: Colors.grey)),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('去设置', style: TextStyle(color: Color(0xFF00FFFF))),
          ),
        ],
      ),
    );
  }

  /// 加载通讯录联系人
  Future<void> loadContacts() async {
    if (!_hasPermission.value) {
      logError('没有通讯录权限');
      return;
    }

    try {
      _isLoading.value = true;

      // 获取系统联系人
      final contacts = await ContactsService.getContacts(
        withThumbnails: false, // 暂时不加载头像，提高性能
        photoHighResolution: false,
      );

      logError('获取到 ${contacts.length} 个联系人');

      // 转换为自定义数据模型
      final addressBookContacts = <AddressBookContact>[];

      for (final contact in contacts) {
        try {
          final addressBookContact = AddressBookContact.fromContact(contact);

          // 过滤掉没有姓名的联系人
          if (addressBookContact.name.isNotEmpty) {
            addressBookContacts.add(addressBookContact);
          }
        } catch (e) {
          logError('转换联系人失败: $e');
        }
      }

      _allContacts.value = addressBookContacts;

      // 分组和排序
      _groupAndSortContacts();

      logError('成功加载 ${_allContacts.length} 个有效联系人');

    } catch (e) {
      logError('加载通讯录失败: $e');
      showFailToast('加载通讯录失败，请重试');
    } finally {
      _isLoading.value = false;
    }
  }

  /// 分组和排序联系人
  void _groupAndSortContacts() {
    if (_allContacts.isEmpty) {
      _groupedContacts.clear();
      _alphabetIndex.clear();
      _filteredContacts.clear();
      return;
    }

    // 按首字母分组
    final Map<String, List<AddressBookContact>> groupMap = {};

    for (final contact in _allContacts) {
      final letter = contact.firstLetter;
      if (!groupMap.containsKey(letter)) {
        groupMap[letter] = [];
      }
      groupMap[letter]!.add(contact);
    }

    // 对每组内的联系人按拼音排序
    groupMap.forEach((letter, contacts) {
      contacts.sort((a, b) {
        // 优先按拼音排序，如果拼音相同则按原名排序
        final pinyinCompare = a.pinyin.compareTo(b.pinyin);
        if (pinyinCompare != 0) return pinyinCompare;
        return a.name.compareTo(b.name);
      });
    });

    // 创建分组列表
    final groups = <ContactGroup>[];
    final sortedLetters = groupMap.keys.toList();

    // 字母排序：A-Z 在前，# 在后
    sortedLetters.sort((a, b) {
      if (a == '#' && b != '#') return 1;
      if (a != '#' && b == '#') return -1;
      return a.compareTo(b);
    });

    for (final letter in sortedLetters) {
      groups.add(ContactGroup(
        letter: letter,
        contacts: groupMap[letter]!,
      ));
    }

    _groupedContacts.value = groups;
    _alphabetIndex.value = sortedLetters;

    // 初始化过滤结果
    _filteredContacts.value = groups;
  }

  /// 从后台加载用户信息进行匹配
  Future<void> loadAppUsers() async {
    try {
      _isLoadingAppUsers.value = true;

      // 收集所有电话号码和邮箱用于匹配
      final phoneNumbers = <String>[];
      final emails = <String>[];

      for (final contact in _allContacts) {
        phoneNumbers.addAll(contact.phoneNumbers);
        emails.addAll(contact.emails);
      }

      if (phoneNumbers.isEmpty && emails.isEmpty) {
        logError('没有可匹配的电话号码或邮箱');
        return;
      }

      // 调用后台接口匹配用户
      final response = await Get.find<ApiProvider>().post(
        Api.matchContactUsers, // 需要在Api.dart中定义这个接口
        {
          'phone_numbers': phoneNumbers,
          'emails': emails,
        },
      );

      if (response.statusCode == 200) {
        final data = response.body;
        if (data != null && data['users'] != null) {
          final userList = data['users'] as List;
          final appUsers = userList.map((json) => AppUserInfo.fromJson(json)).toList();

          _appUsers.value = appUsers;

          // 匹配联系人和App用户
          _matchContactsWithAppUsers();

          logError('成功匹配 ${appUsers.length} 个App用户');
        }
      } else {
        logError('获取App用户失败: ${response.statusCode}');
      }

    } catch (e) {
      logError('加载App用户失败: $e');
      // 不显示错误提示，因为这不是关键功能
    } finally {
      _isLoadingAppUsers.value = false;
    }
  }

  /// 匹配联系人和App用户
  void _matchContactsWithAppUsers() {
    final updatedContacts = <AddressBookContact>[];

    for (final contact in _allContacts) {
      bool isMatched = false;
      AppUserInfo? matchedUser;

      // 通过电话号码匹配
      for (final phone in contact.phoneNumbers) {
        matchedUser = _appUsers.firstWhereOrNull(
          (user) => user.userPhone == phone,
        );
        if (matchedUser != null) {
          isMatched = true;
          break;
        }
      }

      // 如果电话号码没匹配到，尝试邮箱匹配
      if (!isMatched) {
        for (final email in contact.emails) {
          matchedUser = _appUsers.firstWhereOrNull(
            (user) => user.email == email,
          );
          if (matchedUser != null) {
            isMatched = true;
            break;
          }
        }
      }

      // 更新联系人状态
      final updatedContact = contact.copyWith(
        isAppMember: isMatched,
        appUserId: matchedUser?.id,
        appUserEmail: matchedUser?.email,
        appUserPhone: matchedUser?.userPhone,
        appUserAvatar: matchedUser?.avatarUrl,
      );

      updatedContacts.add(updatedContact);
    }

    _allContacts.value = updatedContacts;

    // 重新分组
    _groupAndSortContacts();

    // 如果有搜索条件，重新过滤
    if (_searchText.value.isNotEmpty) {
      searchContacts(_searchText.value);
    }
  }

  /// 搜索联系人
  void searchContacts(String query) {
    _searchText.value = query;

    if (query.isEmpty) {
      _filteredContacts.value = _groupedContacts;
      return;
    }

    final searchText = query.toLowerCase().trim();
    final filteredGroups = <ContactGroup>[];

    for (final group in _groupedContacts) {
      final filteredContacts = group.contacts.where((contact) {
        // 匹配姓名
        final nameMatch = contact.name.toLowerCase().contains(searchText);

        // 匹配电话号码
        final phoneMatch = contact.phoneNumbers.any(
          (phone) => phone.contains(searchText),
        );

        // 匹配邮箱
        final emailMatch = contact.emails.any(
          (email) => email.toLowerCase().contains(searchText),
        );

        // 匹配拼音
        bool pinyinMatch = false;
        bool pinyinShortMatch = false;

        if (!RegExp(r'^[0-9]+$').hasMatch(contact.name)) {
          try {
            pinyinMatch = contact.pinyin.toLowerCase().contains(searchText);
            pinyinShortMatch = contact.pinyinShort.toLowerCase().contains(searchText);
          } catch (e) {
            // 忽略拼音匹配错误
          }
        }

        return nameMatch || phoneMatch || emailMatch || pinyinMatch || pinyinShortMatch;
      }).toList();

      if (filteredContacts.isNotEmpty) {
        filteredGroups.add(ContactGroup(
          letter: group.letter,
          contacts: filteredContacts,
        ));
      }
    }

    _filteredContacts.value = filteredGroups;
  }

  /// 清除搜索
  void clearSearch() {
    _searchText.value = '';
    _filteredContacts.value = _groupedContacts;
  }

  /// 切换联系人选中状态
  void toggleContactSelection(AddressBookContact contact) {
    // App成员不能被选中
    if (contact.isAppMember) {
      showFailToast('该联系人已是App成员');
      return;
    }

    final index = _selectedContacts.indexWhere((c) => c.id == contact.id);

    if (index != -1) {
      // 取消选中
      _selectedContacts.removeAt(index);
      if (_selectedContacts.isEmpty) {
        _isSelectionMode.value = false;
      }
    } else {
      // 选中
      _selectedContacts.add(contact);
      _isSelectionMode.value = true;
    }

    // 更新联系人状态
    _updateContactSelectionState(contact.id, index == -1);
  }

  /// 更新联系人选中状态
  void _updateContactSelectionState(String contactId, bool isSelected) {
    // 更新原始列表
    final allIndex = _allContacts.indexWhere((c) => c.id == contactId);
    if (allIndex != -1) {
      _allContacts[allIndex] = _allContacts[allIndex].copyWith(isSelected: isSelected);
    }

    // 更新分组列表
    for (int i = 0; i < _groupedContacts.length; i++) {
      final group = _groupedContacts[i];
      final contactIndex = group.contacts.indexWhere((c) => c.id == contactId);
      if (contactIndex != -1) {
        group.contacts[contactIndex] = group.contacts[contactIndex].copyWith(isSelected: isSelected);
        break;
      }
    }

    // 更新过滤列表
    for (int i = 0; i < _filteredContacts.length; i++) {
      final group = _filteredContacts[i];
      final contactIndex = group.contacts.indexWhere((c) => c.id == contactId);
      if (contactIndex != -1) {
        group.contacts[contactIndex] = group.contacts[contactIndex].copyWith(isSelected: isSelected);
        break;
      }
    }
  }

  /// 检查联系人是否被选中
  bool isContactSelected(AddressBookContact contact) {
    return _selectedContacts.any((c) => c.id == contact.id);
  }

  /// 全选/取消全选（仅非App成员）
  void toggleSelectAll() {
    final availableContacts = _allContacts.where((c) => !c.isAppMember).toList();

    if (_selectedContacts.length == availableContacts.length) {
      // 全部取消选中
      clearSelection();
    } else {
      // 全选
      _selectedContacts.clear();
      _selectedContacts.addAll(availableContacts);
      _isSelectionMode.value = true;

      // 更新所有联系人状态
      for (final contact in availableContacts) {
        _updateContactSelectionState(contact.id, true);
      }
    }
  }

  /// 清除所有选择
  void clearSelection() {
    final selectedIds = _selectedContacts.map((c) => c.id).toList();
    _selectedContacts.clear();
    _isSelectionMode.value = false;

    // 更新联系人状态
    for (final id in selectedIds) {
      _updateContactSelectionState(id, false);
    }
  }

  /// 获取选中的联系人数量
  int get selectedCount => _selectedContacts.length;

  /// 获取可选择的联系人数量（非App成员）
  int get availableCount => _allContacts.where((c) => !c.isAppMember).length;

  /// 刷新数据
  Future<void> refresh() async {
    await loadContacts();
    await loadAppUsers();
  }

  /// 邀请选中的联系人
  Future<void> inviteSelectedContacts() async {
    if (_selectedContacts.isEmpty) {
      showFailToast('请先选择要邀请的联系人');
      return;
    }

    try {
      // 构建邀请数据
      final inviteData = _selectedContacts.map((contact) => {
        'name': contact.name,
        'phone_numbers': contact.phoneNumbers,
        'emails': contact.emails,
      }).toList();

      // 调用邀请接口
      final response = await Get.find<ApiProvider>().post(
        Api.inviteContacts, // 需要在Api.dart中定义这个接口
        {
          'contacts': inviteData,
          'invitation_message': '邀请您加入我们的群聊',
        },
      );

      if (response.statusCode == 200) {
        showSuccessToast('邀请发送成功');
        clearSelection();

        // 可以返回邀请结果给上级页面
        Get.back(result: {
          'invited_count': inviteData.length,
          'contacts': _selectedContacts,
        });
      } else {
        showFailToast('邀请发送失败，请重试');
      }

    } catch (e) {
      logError('邀请联系人失败: $e');
      showFailToast('邀请发送失败，请重试');
    }
  }

  // 字母索引相关
  final RxString _currentVisibleLetter = ''.obs;
  final RxInt _currentLetterIndex = 0.obs;

  String get currentVisibleLetter => _currentVisibleLetter.value;
  int get currentLetterIndex => _currentLetterIndex.value;

  /// 更新当前可见的字母
  void updateCurrentVisibleLetter(String letter) {
    if (_currentVisibleLetter.value != letter) {
      _currentVisibleLetter.value = letter;
      final index = _alphabetIndex.indexOf(letter);
      if (index != -1) {
        _currentLetterIndex.value = index;
      }
    }
  }

  /// 跳转到字母索引位置
  void jumpToLetter(String letter) {
    // 这个方法会被UI层调用，通过回调函数实现跳转
    _onJumpToLetter?.call(letter);
  }

  // 跳转回调函数，由UI层设置
  Function(String)? _onJumpToLetter;

  /// 设置跳转回调函数
  void setJumpToLetterCallback(Function(String) callback) {
    _onJumpToLetter = callback;
  }

  /// 根据滚动控制器计算当前可见字母
  void updateVisibleLetterFromScroll(ScrollController scrollController) {
    if (_filteredContacts.isEmpty || !scrollController.hasClients) return;

    final scrollOffset = scrollController.offset;
    double currentOffset = 0.0;

    // 遍历所有分组，计算当前可见的分组
    for (final group in _filteredContacts) {
      // 每个分组的高度 = 标题高度 + 联系人项高度 * 联系人数量
      final headerHeight = 40.0; // 40.w 的近似值
      final itemHeight = 72.0; // ListTile 的默认高度
      final groupHeight = headerHeight + (itemHeight * group.contacts.length);

      // 如果当前滚动位置在这个分组范围内
      if (scrollOffset >= currentOffset && scrollOffset < currentOffset + groupHeight) {
        updateCurrentVisibleLetter(group.letter);
        return;
      }

      currentOffset += groupHeight;
    }
  }
}