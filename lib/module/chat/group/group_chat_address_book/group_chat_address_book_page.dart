import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';

import 'group_chat_address_book_controller.dart';

class GroupChatAddressBookPage extends StatefulWidget {
  const GroupChatAddressBookPage({Key? key}) : super(key: key);

  @override
  State<GroupChatAddressBookPage> createState() => _GroupChatAddressBookPageState();
}

class _GroupChatAddressBookPageState extends State<GroupChatAddressBookPage> {
  final controller = Get.put(GroupChatAddressBookController());
  final TextEditingController searchController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  // 字母索引相关
  final GlobalKey _listKey = GlobalKey();
  bool _isScrollingToLetter = false;

  // 字母提示相关
  final RxBool _showLetterHint = false.obs;
  final RxString _hintLetter = ''.obs;

  @override
  void initState() {
    super.initState();
    _initializeScrollListener();
    _initializeController();

    // 设置跳转回调
    controller.setJumpToLetterCallback(_jumpToLetter);
  }

  @override
  void dispose() {
    searchController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  /// 初始化滚动监听
  void _initializeScrollListener() {
    scrollController.addListener(() {
      if (!_isScrollingToLetter && scrollController.hasClients) {
        controller.updateVisibleLetterFromScroll(scrollController);
      }
    });
  }

  /// 初始化控制器
  void _initializeController() {
    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.checkAndRequestPermission().then((_) {
        if (controller.hasPermission) {
          controller.loadContacts();
          controller.loadAppUsers();
        }
      });
    });
  }

  /// 跳转到指定字母
  void _jumpToLetter(String letter) {
    if (!scrollController.hasClients) {
      print('ScrollController not ready');
      return;
    }

    _isScrollingToLetter = true;

    // 添加触觉反馈
    HapticFeedback.lightImpact();

    // 计算目标位置
    final targetOffset = _calculateLetterOffset(letter);

    if (targetOffset >= 0) {
      try {
        // 平滑滚动到目标位置
        scrollController.animateTo(
          targetOffset,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOutCubic,
        ).then((_) {
          // 延迟重置标志，避免滚动过程中触发监听
          Future.delayed(Duration(milliseconds: 400), () {
            _isScrollingToLetter = false;
          });
        }).catchError((error) {
          print('Scroll error: $error');
          _isScrollingToLetter = false;
        });
      } catch (e) {
        print('Jump to letter error: $e');
        _isScrollingToLetter = false;
      }

      // 立即更新当前字母
      controller.updateCurrentVisibleLetter(letter);
    } else {
      print('Cannot find letter $letter in contacts');
      _isScrollingToLetter = false;
    }
  }

  /// 计算字母对应的滚动偏移量
  double _calculateLetterOffset(String letter) {
    final groups = controller.filteredContacts;
    double offset = 0.0;

    for (final group in groups) {
      if (group.letter == letter) {
        return offset;
      }

      // 每个分组的高度 = 标题高度 + 联系人项高度 * 联系人数量
      final headerHeight = 40.w;
      final itemHeight = 72.0; // ListTile 的默认高度
      final groupHeight = headerHeight + (itemHeight * group.contacts.length);

      offset += groupHeight;
    }

    return -1; // 未找到
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFF0F0F23),
      appBar: _buildAppBar(),
      body: Obx(() {
        if (!controller.hasPermission) {
          return _buildPermissionDeniedView();
        }

        if (controller.isLoading) {
          return _buildLoadingView();
        }

        return Stack(
          children: [
            Column(
              children: [
                _buildSearchBar(),
                _buildSelectionInfo(),
                Expanded(
                  child: _buildContactsList(),
                ),
              ],
            ),
            // 右侧字母索引
            _buildAlphabetIndex(),
            // 字母提示浮层
            _buildLetterHint(),
          ],
        );
      }),
      bottomNavigationBar: Obx(() {
        if (controller.isSelectionMode) {
          return _buildBottomActionBar();
        }
        return SizedBox.shrink();
      }),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xFF16213E),
      foregroundColor: Colors.white,
      title: Text('通讯录'),
      actions: [
        Obx(() {
          if (controller.availableCount > 0) {
            return TextButton(
              onPressed: controller.toggleSelectAll,
              child: Text(
                controller.selectedCount == controller.availableCount
                    ? '取消全选'
                    : '全选',
                style: TextStyle(color: Color(0xFF00FFFF)),
              ),
            );
          }
          return SizedBox.shrink();
        }),
      ],
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Color(0xFF1A1A2E),
        borderRadius: BorderRadius.circular(12.w),
        border: Border.all(color: Color(0xFF00FFFF).withValues(alpha: 0.3)),
      ),
      child: TextField(
        controller: searchController,
        style: TextStyle(fontSize: 14.sp, color: Colors.white),
        decoration: InputDecoration(
          hintText: '搜索联系人',
          hintStyle: TextStyle(fontSize: 14.sp, color: Color(0xFF988B9A)),
          prefixIcon: Icon(Icons.search, color: Color(0xFF988B9A)),
          suffixIcon: Obx(() {
            if (controller.searchText.isNotEmpty) {
              return IconButton(
                icon: Icon(Icons.clear, color: Color(0xFF988B9A)),
                onPressed: () {
                  searchController.clear();
                  controller.clearSearch();
                },
              );
            }
            return SizedBox.shrink();
          }),
          border: InputBorder.none,
          contentPadding:
          EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
        ),
        onChanged: controller.searchContacts,
      ),
    );
  }

  /// 构建选择信息
  Widget _buildSelectionInfo() {
    return Obx(() {
      if (controller.isSelectionMode) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
          color: Color(0xFF1A1A2E),
          child: Row(
            children: [
              Text(
                '已选择 ${controller.selectedCount} 个联系人',
                style: TextStyle(fontSize: 14.sp, color: Color(0xFF00FFFF)),
              ),
              Spacer(),
              if (controller.selectedCount > 0)
                TextButton(
                  onPressed: controller.clearSelection,
                  child: Text(
                    '清除选择',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
            ],
          ),
        );
      }
      return SizedBox.shrink();
    });
  }

  /// 构建联系人列表
  Widget _buildContactsList() {
    return Obx(() {
      final groups = controller.filteredContacts;

      if (groups.isEmpty) {
        return _buildEmptyView();
      }

      return RefreshIndicator(
        onRefresh: controller.refresh,
        backgroundColor: Color(0xFF1A1A2E),
        color: Color(0xFF00FFFF),
        child: CustomScrollView(
          key: _listKey,
          controller: controller.scrollController,
          slivers: [
            // 构建所有分组
            ...groups.map((group) => _buildStickyContactGroup(group)),
          ],
        ),
      );
    });
  }

  /// 构建吸顶联系人分组
  Widget _buildStickyContactGroup(ContactGroup group) {
    return SliverMainAxisGroup(
      slivers: [
        // 吸顶分组标题
        SliverPersistentHeader(
          pinned: true,
          delegate: _StickyHeaderDelegate(
            child: _buildGroupHeader(group.letter),
            height: 40.w,
          ),
        ),
        // 联系人列表
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              return _buildContactItem(group.contacts[index]);
            },
            childCount: group.contacts.length,
          ),
        ),
      ],
    );
  }

  /// 构建分组标题
  Widget _buildGroupHeader(String letter) {
    return Container(
      height: 40.w,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
      decoration: BoxDecoration(
        color: Color(0xFF16213E),
        border: Border(
          bottom: BorderSide(
            color: Color(0xFF00FFFF).withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            letter,
            style:TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Color(0xFF00FFFF)
            )
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF00FFFF).withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }



  /// 构建联系人项目
  Widget _buildContactItem(AddressBookContact contact) {
    return Obx(() {
      final isSelected = controller.isContactSelected(contact);
      final isAppMember = contact.isAppMember;

      return Container(
        color: isSelected
            ? Color(0xFF00FFFF).withValues(alpha: 0.1)
            : Colors.transparent,
        child: ListTile(
          leading: CircleAvatar(
            backgroundColor: Color(0xFF1A1A2E),
            backgroundImage: contact.appUserAvatar?.isNotEmpty == true
                ? NetworkImage(contact.appUserAvatar!)
                : null,
            child: contact.appUserAvatar?.isEmpty != false
                ? Text(
                contact.name.isNotEmpty
                    ? contact.name[0].toUpperCase()
                    : '?',
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold, color: Colors.white))
                : null,
          ),
          title: Text(contact.name,
              style: TextStyle(
                fontSize: 16.sp,
                color: isAppMember ? Colors.grey : Colors.white,
              )),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (contact.phoneNumbers.isNotEmpty)
                Text(contact.phoneNumbers.first,
                    style: TextStyle(fontSize: 12.sp, color: Color(0xFF988B9A))),
              if (contact.emails.isNotEmpty)
                Text(
                  contact.emails.first,
                  style: TextStyle(fontSize: 12.sp, color: Color(0xFF988B9A)),
                ),
              if (isAppMember)
                Text('App成员',
                    style: TextStyle(fontSize: 12.sp, color: Color(0xFF00FFFF))),
            ],
          ),
          trailing: isAppMember
              ? Icon(Icons.check_circle, color: Colors.grey)
              : Checkbox(
            value: isSelected,
            onChanged: (_) => controller.toggleContactSelection(contact),
            activeColor: Color(0xFF00FFFF),
          ),
          onTap: isAppMember
              ? null
              : () => controller.toggleContactSelection(contact),
          enabled: !isAppMember,
        ),
      );
    });
  }

  /// 构建权限拒绝视图
  Widget _buildPermissionDeniedView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.contacts,
            size: 64.w,
            color: Colors.grey,
          ),
          SizedBox(height: 16.w),
          Text('需要通讯录权限',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold, color: Colors.white)),
          SizedBox(height: 8.w),
          Text(
            '为了邀请您的朋友，我们需要访问通讯录',
            style: TextStyle(fontSize: 14.sp, color: Color(0xFF988B9A)),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.w),
          ElevatedButton(
            onPressed: controller.checkAndRequestPermission,
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF00FFFF),
              foregroundColor: Colors.black,
            ),
            child: Text('授权访问'),
          ),
        ],
      ),
    );
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00FFFF)),
          ),
          SizedBox(height: 16.w),
          Text('正在加载通讯录...',
              style: TextStyle(fontSize: 14.sp, color: Colors.white)),
        ],
      ),
    );
  }

  /// 构建空视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64.w,
            color: Colors.grey,
          ),
          SizedBox(height: 16.w),
          Text(controller.searchText.isNotEmpty ? '没有找到匹配的联系人' : '通讯录为空',
              style: TextStyle(fontSize: 16.sp, color: Colors.grey)),
        ],
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Color(0xFF1A1A2E),
        border: Border(
          top: BorderSide(color: Color(0xFF00FFFF).withValues(alpha: 0.3)),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Obx(() => Text('已选择 ${controller.selectedCount} 个联系人',
                  style: TextStyle(fontSize: 14.sp, color: Colors.white))),
            ),
            ElevatedButton(
              onPressed: controller.selectedCount > 0
                  ? controller.inviteSelectedContacts
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF00FFFF),
                foregroundColor: Colors.black,
                disabledBackgroundColor: Colors.grey,
              ),
              child: Text('邀请'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建右侧字母索引
  Widget _buildAlphabetIndex() {
    return Obx(() {
      final letters = controller.alphabetIndex;

      if (letters.isEmpty) {
        return SizedBox.shrink();
      }

      return Positioned(
        right: 8.w,
        top: 100.w, // 避开搜索栏
        bottom: controller.isSelectionMode ? 80.w : 20.w, // 避开底部操作栏
        child: GestureDetector(
          onPanUpdate: (details) => _handleAlphabetPointer(details.globalPosition, letters, showHint: true),
          onPanEnd: (details) => _hideLetterHint(),
          onTapDown: (details) => _handleAlphabetPointer(details.globalPosition, letters, showHint: true),
          onTapUp: (details) => _hideLetterHint(),
          child: Container(
            width: 24.w,
            decoration: BoxDecoration(
              color: Color(0xFF1A1A2E).withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12.w),
              border: Border.all(
                color: Color(0xFF00FFFF).withValues(alpha: 0.2),
                width: 0.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: Offset(-2, 0),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: letters.asMap().entries.map((entry) {
                return _buildAlphabetItem(entry.value, entry.key, letters.length);
              }).toList(),
            ),
          ),
        ),
      );
    });
  }

  /// 处理字母索引的指针事件
  void _handleAlphabetPointer(Offset globalPosition, List<String> letters, {bool showHint = false}) {
    _selectLetterByGlobalPosition(globalPosition, letters, showHint: showHint);
  }

  /// 根据全局触摸位置选择字母
  void _selectLetterByGlobalPosition(Offset globalPosition, List<String> letters, {bool showHint = false}) {
    // 获取字母索引容器的位置信息
    final screenHeight = MediaQuery.of(context).size.height;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final appBarHeight = kToolbarHeight;

    // 计算字母索引区域的绝对位置
    final indexTop = statusBarHeight + appBarHeight + 100.w;
    final indexBottom = screenHeight - (controller.isSelectionMode ? 80.w : 20.w);
    final indexHeight = indexBottom - indexTop;

    print('Global Y: ${globalPosition.dy}, IndexTop: $indexTop, IndexBottom: $indexBottom');

    if (globalPosition.dy >= indexTop && globalPosition.dy <= indexBottom && letters.isNotEmpty) {
      final relativeY = globalPosition.dy - indexTop;
      final letterIndex = ((relativeY / indexHeight) * letters.length).floor().clamp(0, letters.length - 1);

      final selectedLetter = letters[letterIndex];
      print('Selected letter: $selectedLetter at index: $letterIndex');

      if (showHint) {
        _showLetterHintOverlay(selectedLetter);
      }

      // 直接调用跳转方法
      _jumpToLetter(selectedLetter);

      // 添加触觉反馈
      HapticFeedback.selectionClick();
    }
  }

  /// 显示字母提示
  void _showLetterHintOverlay(String letter) {
    _hintLetter.value = letter;
    _showLetterHint.value = true;
  }

  /// 隐藏字母提示
  void _hideLetterHint() {
    Future.delayed(Duration(milliseconds: 300), () {
      _showLetterHint.value = false;
    });
  }

  /// 构建字母索引项
  Widget _buildAlphabetItem(String letter, int index, int totalCount) {
    return Obx(() {
      final isActive = controller.currentVisibleLetter == letter;

      return GestureDetector(
        onTap: () {
          print('Tapped letter: $letter');
          _jumpToLetter(letter);
        },
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          width: isActive ? 22.w : 20.w,
          height: isActive ? 22.w : 20.w,
          margin: EdgeInsets.symmetric(vertical: 1.w),
          decoration: BoxDecoration(
            color: isActive ? Color(0xFF00FFFF) : Colors.transparent,
            borderRadius: BorderRadius.circular(11.w),
            border: isActive ? null : Border.all(
              color: Color(0xFF00FFFF).withValues(alpha: 0.3),
              width: 0.5,
            ),
            boxShadow: isActive ? [
              BoxShadow(
                color: Color(0xFF00FFFF).withValues(alpha: 0.3),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ] : null,
          ),
          child: Center(
            child: Text(
              letter,
              style: TextStyle(
                fontSize: isActive ? 11.sp : 10.sp,
                color: isActive ? Colors.black : Color(0xFF00FFFF),
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ),
      );
    });
  }

  /// 构建字母提示浮层
  Widget _buildLetterHint() {
    return Obx(() {
      if (!_showLetterHint.value) {
        return SizedBox.shrink();
      }

      return Positioned(
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        child: IgnorePointer(
          child: Center(
            child: AnimatedOpacity(
              opacity: _showLetterHint.value ? 1.0 : 0.0,
              duration: Duration(milliseconds: 200),
              child: Container(
                width: 80.w,
                height: 80.w,
                decoration: BoxDecoration(
                  color: Color(0xFF1A1A2E).withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(12.w),
                  border: Border.all(
                    color: Color(0xFF00FFFF),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0xFF00FFFF).withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    _hintLetter.value,
                    style: TextStyle(
                      fontSize: 32.sp,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF00FFFF),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}

/// 吸顶标题代理
class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _StickyHeaderDelegate({
    required this.child,
    required this.height,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate != this;
  }
}
