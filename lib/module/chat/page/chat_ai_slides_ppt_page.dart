import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/chat_slides_download_utils.dart';
import 'package:new_agnes/utils/loading_util.dart';
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';

import '../../../api/Api.dart';
import '../../../api/ApiProvider.dart';
import '../../../generated/l10n.dart';
import '../../../utils/dialog_utils.dart';
import '../../../utils/toastUtil.dart';
import '../widget/pdf_preview_widget.dart';
import 'chat_download_progress_page.dart';

class ChatAiSlidesPptPage extends StatefulWidget {
  final String url;
  final String? title;
  final String id;

  const ChatAiSlidesPptPage(
      {super.key, required this.url, this.title, required this.id});

  @override
  State<ChatAiSlidesPptPage> createState() => _ChatAiSlidesPptPageState();
}

class _ChatAiSlidesPptPageState extends State<ChatAiSlidesPptPage> {
  InAppWebViewController? webViewController;
  bool isLoading = true;
  double progress = 0.0;
  PPtDownloadLogic pPtDownloadLogic=Get.put(PPtDownloadLogic());
  int totalPage = 0;
  // 修改网页背景颜色的方法（保留背景图片）
  Future<void> _changeBackgroundColor(InAppWebViewController controller) async {
    try {
      await controller.evaluateJavascript(source: """
        // 修改body和html的背景颜色（只修改没有背景图的元素）
        if (window.getComputedStyle(document.body).backgroundImage === 'none') {
          document.body.style.backgroundColor = '#2C1B47';
        }
        if (window.getComputedStyle(document.documentElement).backgroundImage === 'none') {
          document.documentElement.style.backgroundColor = '#2C1B47';
        }
        
        // 遍历所有元素，只修改没有背景图的元素的背景色
        var allElements = document.querySelectorAll('*');
        allElements.forEach(function(element) {
          var computedStyle = window.getComputedStyle(element);
          
          // 只有当元素没有背景图时才修改背景色
          if (computedStyle.backgroundImage === 'none') {
            // 如果元素有背景色且不是透明，则修改为目标颜色
            if (computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' && 
                computedStyle.backgroundColor !== 'transparent') {
              element.style.backgroundColor = '#2C1B47 !important';
            }
          }
        });
        
        // 创建并注入CSS样式（只针对没有背景图的元素）
        var style = document.createElement('style');
        style.textContent = \`
          html:not([style*="background-image"]), 
          body:not([style*="background-image"]) {
            background-color: #2C1B47 !important;
          }
          
          *:not([style*="background-image"]) {
            background-color: #2C1B47 !important;
          }
          
          /* 只覆盖透明背景和纯色背景，保留图片背景 */
          *[style*="background-color"]:not([style*="background-image"]) {
            background-color: #2C1B47 !important;
          }
          
          /* 隐藏所有滚动条 */
          html, body {
            overflow: hidden !important;
            scrollbar-width: none !important; /* Firefox */
            -ms-overflow-style: none !important; /* IE and Edge */
          }
          
          /* 隐藏Webkit浏览器的滚动条 */
          html::-webkit-scrollbar, body::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
          }
          
          /* 隐藏所有元素的滚动条 */
          * {
            scrollbar-width: none !important; /* Firefox */
            -ms-overflow-style: none !important; /* IE and Edge */
          }
          
          *::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
          }
          
          /* 确保内容区域可以滚动但不显示滚动条 */
          .content, .main-content, .container, .wrapper {
            overflow: auto !important;
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
          }
          
          .content::-webkit-scrollbar, 
          .main-content::-webkit-scrollbar, 
          .container::-webkit-scrollbar, 
          .wrapper::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
          }
        \`;
        document.head.appendChild(style);
        
        // 监听DOM变化，确保新添加的元素也应用背景色规则
        var observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                  var nodeStyle = window.getComputedStyle(node);
                  
                  // 只有当新节点没有背景图时才修改背景色
                  if (nodeStyle.backgroundImage === 'none') {
                    node.style.backgroundColor = '#2C1B47';
                  }
                  
                  // 递归处理子元素
                  var children = node.querySelectorAll && node.querySelectorAll('*');
                  if (children) {
                    children.forEach(function(child) {
                      var childStyle = window.getComputedStyle(child);
                      if (childStyle.backgroundImage === 'none') {
                        child.style.backgroundColor = '#2C1B47';
                      }
                      // 隐藏新添加的header标签
                      if (child.tagName && child.tagName.toLowerCase() === 'header') {
                        child.style.display = 'none';
                      }
                      // 隐藏新添加元素的滚动条
                      child.style.scrollbarWidth = 'none';
                      child.style.msOverflowStyle = 'none';
                      if (child.style.webkitScrollbar) {
                        child.style.webkitScrollbar.display = 'none';
                      }
                    });
                  }
                  
                  // 如果新节点本身是header标签，也要隐藏
                  if (node.tagName && node.tagName.toLowerCase() === 'header') {
                    node.style.display = 'none';
                  }
                  
                  // 隐藏新节点本身的滚动条
                  node.style.scrollbarWidth = 'none';
                  node.style.msOverflowStyle = 'none';
                  if (node.style.webkitScrollbar) {
                    node.style.webkitScrollbar.display = 'none';
                  }
                }
              });
            }
          });
        });
        
        // 开始观察DOM变化
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
        
        // 隐藏所有header标签
        var headers = document.querySelectorAll('header');
        headers.forEach(function(header) {
          header.style.display = 'none';
        });
        
        console.log('Background color changed to #2C1B47 (preserving background images)');
        console.log('Header tags hidden: ' + headers.length + ' headers found and hidden');
      """);
    } catch (e) {
      debugPrint('Error changing background color: \$e');
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((time){
      // _getPages();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.url.isEmpty) {
      return Scaffold(
        backgroundColor: const Color(0xFF2C1B47),
        appBar: AppBar(
          backgroundColor: const Color(0xFF2C1B47),
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios, color: Colors.white, size: 20.sp),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            widget.title ?? 'Web View',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: true,
        ),
        body: const Center(
          child: Text('No URL provided',
              style: TextStyle(fontSize: 16, color: Colors.white70)),
        ),
      );
    }
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (value, result) {
        if (value) {
          return;
        }
        goBack();
      },
      child: Container(
        color: const Color(0xFF2C1B47),
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
        child: Column(
          children: [
            AppBar(
              backgroundColor: const Color(0xFF2C1B47),
              elevation: 0,
              leadingWidth: 24.w + 16.w,
              leading: Row(
                children: [
                  SizedBox(width: 16.w),
                  GestureDetector(
                    onTap: () {
                      goBack();
                    },
                    child: Image.asset(
                      "assets/images/ic_ppt_back.png",
                      width: 24.w,
                      height: 24.h,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              title: Text(
                widget.title ?? 'Web View',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              centerTitle: true,
              actions: [
                Builder(builder: (context) {
                  return GestureDetector(
                    onTap: () {
                      _showDownloadPopup(context);
                    },
                    child: Image.asset(
                      "assets/images/ic_ppt_download.png",
                      width: 24.w,
                      height: 24.h,
                    ),
                  );
                }),
                SizedBox(width: 8.w),
                GestureDetector(
                  onTap: () {
                    SharePlus.instance.share(
                      ShareParams(
                        title: widget.title,
                        uri: Uri.parse(widget.url ?? ""),
                      ),
                    );
                  },
                  child: Image.asset(
                    "assets/images/ic_ppt_share.png",
                    width: 24.w,
                    height: 24.h,
                  ),
                ),
                SizedBox(width: 16.w),
              ],
            ),
            Visibility(
              visible: totalPage > 1,
              child: Container(
                color: const Color(0xFF2C1B47),
                margin: EdgeInsets.only(left: 16.w, top: 16.h,bottom: 16.h),
                alignment: Alignment.centerLeft,
                child: Text(
                  "${totalPage} pages total",
                  style: TextStyle(color: Color(0xFF988B9A), fontSize: 14.sp),
                ),
              ),
            ),
            Expanded(
                child: Stack(
              children: [
                Positioned.fill(
                  child: InAppWebView(
                          initialUrlRequest: URLRequest(url: WebUri.uri(Uri.parse(widget.url)),),
                          initialSettings: InAppWebViewSettings(
                            useShouldOverrideUrlLoading: false,
                            mediaPlaybackRequiresUserGesture: false,
                            allowsInlineMediaPlayback: true,
                            javaScriptEnabled: true,
                            domStorageEnabled: true,
                            databaseEnabled: true,
                            cacheEnabled: true,
                            clearCache: false,
                            useHybridComposition: true,
                            supportZoom: false,
                            builtInZoomControls: true,
                            displayZoomControls: false,
                            // userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                            verticalScrollBarEnabled: false,
                            horizontalScrollBarEnabled: false,
                            disableVerticalScroll: false,
                            disableHorizontalScroll: false,
                            initialScale: 100,
                            minimumFontSize: 1,
                            useWideViewPort: true,
                            loadWithOverviewMode: true,
                          ),
                          onWebViewCreated: (controller) {
                            webViewController = controller;
                          },
                          onLoadStart: (controller, url) {
                            setState(() {
                              isLoading = true;
                            });
                          },
                          onLoadStop: (controller, url) async {
                            // 修改网页背景颜色
                            // await _changeBackgroundColor(controller);
                            Future.delayed(Duration(milliseconds: 500),(){
                              if(mounted){
                                setState(() {
                                  isLoading = false;
                                });
                              }
                            });
                          },
                          onProgressChanged: (controller, progress) {
                            setState(() {
                              this.progress = progress / 100;
                            });
                          },
                          onLoadError: (controller, url, code, message) {
                            debugPrint('WebView Load Error: $message');
                            setState(() {
                              isLoading = false;
                            });
                          },
                          onLoadHttpError:
                              (controller, url, statusCode, description) {
                            debugPrint(
                                'WebView HTTP Error: $statusCode - $description');
                          },
                          shouldOverrideUrlLoading:
                              (controller, navigationAction) async {
                            return NavigationActionPolicy.ALLOW;
                          },
                          onConsoleMessage: (controller, consoleMessage) {
                            debugPrint(
                                'WebView Console: ${consoleMessage.message}');
                          },
                        ),
                ),
                if (isLoading) ...[
                  // Progress Bar
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: LinearProgressIndicator(
                      value: progress,
                      backgroundColor: Colors.grey[300],
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Color(0xFF7B68EE)),
                      minHeight: 3.h,
                    ),
                  ),
                  // Loading Overlay
                  Container(
                    color: const Color(0xFF2C1B47),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 50.w,
                            height: 50.w,
                            child: const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  Color(0xFF7B68EE)),
                              strokeWidth: 3,
                            ),
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            'loading...',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 16.sp,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            '${(progress * 100).toInt()}%',
                            style: TextStyle(
                              color: Colors.white54,
                              fontSize: 14.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ))
          ],
        ),
      ),
    );
  }

  void _showDownloadPopup(BuildContext iconContext) {
    final RenderBox iconBox = iconContext.findRenderObject() as RenderBox;
    final iconPosition = iconBox.localToGlobal(Offset.zero);
    final iconSize = iconBox.size;

    showDialog(
      context: context,
      barrierColor: Colors.transparent, // 透明背景，这样点击外部会关闭
      builder: (context) => Stack(
        children: [
          // 点击外部关闭弹窗
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
          ),
          // 下载弹窗
          Positioned(
            left: iconPosition.dx - 81.w + iconSize.width, // 让弹窗中心对齐图标中心
            top: iconPosition.dy + 4.h, // 在图标下方8像素
            child: Container(
              width: 85.w,
              decoration: BoxDecoration(
                color: const Color(0xFF2E174F).withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: const Color(0xFFFF3BDF),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.25),
                    blurRadius: 4,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // PDF 选项
                  _DownloadOption(
                    iconPath: "assets/images/ic_ppt_pdf.png",
                    text: 'PDF',
                    onTap: () {
                      Navigator.of(context).pop();
                      _downloadAsPDF();
                    },
                  ),

                  // PPT 选项
                  _DownloadOption(
                    iconPath: "assets/images/ic_ppt_ppt.png",
                    text: 'PPT',
                    onTap: () {
                      Navigator.of(context).pop();
                      _downloadAsPPT();
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _downloadAsPDF() {
    // TODO: 实现PDF下载功能
    pPtDownloadLogic.getDownloadFile(widget.id, 'pdf',onResult: (pathUrl){
      DialogUtils.showDownLoadDialog(widget.id,'pdf' , '', widget.title, 1,pathUrl: pathUrl);
      // String name = (widget.title ?? "${DateTime.now().toString()}").replaceAll(".ppt", "");
      // ChatDownloadFileUtils.showDownLoadDialog(
      //     context,
      //     name,
      //     pathUrl,
      //     "pdf",
      //     _downloadSuccess,
      //     _downloadFailed);
    });
  }

  void _downloadAsPPT() {
    // TODO: 实现PPT下载功能
    pPtDownloadLogic.getDownloadFile(widget.id, 'pptx',onResult: (pathUrl){
      DialogUtils.showDownLoadDialog(widget.id, 'pptx', '', widget.title, 1,pathUrl: pathUrl);
      // String name = (widget.title ?? "${DateTime.now().toString()}").replaceAll(".ppt", "");
      // ChatDownloadFileUtils.showDownLoadDialog(
      //     context,
      //     name,
      //     pathUrl,
      //     "pptx",
      //     _downloadSuccess,
      //     _downloadFailed);
    });
  }

  void _downloadSuccess(String path, String format) {
    //销毁弹窗
    Navigator.of(context).pop();
    if (format == 'pdf') {
      Get.to(PdfPreviewWidget(path));
    } else if (format == 'pptx') {
      OpenFile.open(
        path,
      );
    }
  }

  void _downloadFailed(String error, String format) {
    //销毁弹窗
    Navigator.of(context).pop();
    Get.snackbar(
      error,
      error,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  void goBack() async {
    bool canGoBack = await webViewController?.canGoBack() ?? false;
    if (canGoBack) {
      webViewController?.goBack();
    } else {
      Get.back();
    }
  }

  void _getPages() async {
    //   https://app.agnes-ai.com/api/ppt/ppt_list/1755049948921765
    Response res = await Get.find<ApiProvider>().get(
      Api.pptList + '/${widget.id}',
    );
    if(res.statusCode == 200 && res.body != null){
      dynamic message = res.body["message"];
      if(message is List){
        totalPage = message.length;
      }
    }else {
      totalPage = 0;
    }
    if(mounted){
      setState(() {});
    }
  }
}

// 下载选项组件，带有点击效果
class _DownloadOption extends StatefulWidget {
  final String iconPath;
  final String text;
  final VoidCallback onTap;

  const _DownloadOption({
    required this.iconPath,
    required this.text,
    required this.onTap,
  });

  @override
  State<_DownloadOption> createState() => _DownloadOptionState();
}

class _DownloadOptionState extends State<_DownloadOption> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) => setState(() => _isPressed = true),
      onPointerUp: (_) => setState(() => _isPressed = false),
      onPointerCancel: (_) => setState(() => _isPressed = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.r),
                gradient: _isPressed
                    ? const LinearGradient(
                        colors: [
                          Color(0xFFFF3BDF),
                          Color(0xFF9C4DCC),
                          Color(0xFF6A4C93),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : null,
              ),
              child: Container(
                margin: EdgeInsets.all(2.w), // 边框宽度
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  color:
                      const Color(0xFF2E174F).withValues(alpha: 0.9), // 内部背景色
                ),
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      widget.iconPath,
                      width: 16,
                      height: 16,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          widget.text == 'PDF'
                              ? Icons.picture_as_pdf
                              : Icons.slideshow,
                          color: Colors.white,
                          size: 16.sp,
                        );
                      },
                    ),
                    SizedBox(width: 5.w),
                    Text(
                      widget.text,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            )),
      ),
    );
  }
}


class PPtDownloadLogic extends GetxController{
  //下载ppt
  Future<void> getDownloadFile(String id,String format,{onResult}) async {
    LoadingUtil.show();
    Get.find<ApiProvider>().httpClient.timeout=Duration(seconds: 100);
    Response res = await Get.find<ApiProvider>().post(
        Api.exportFilePPT,
        {"conversation_id": id, "format": format});
    LoadingUtil.dismiss();
    if (res.statusCode == 200) {
      if(res.body['success']==true){
        if(onResult!=null){
          onResult('${res.body['message']}'.isNotEmpty?res.body['message']:'');
        }
      }else{
        showFailToast(S.of(Get.context!).systemBusyPleaseTryAgain);
      }
    }
  }



}
