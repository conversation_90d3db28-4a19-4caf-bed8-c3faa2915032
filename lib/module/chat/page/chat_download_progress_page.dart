import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/controller/chat_download_progress_controller.dart';
import 'package:new_agnes/utils/chat_slides_download_utils.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';

import '../../../generated/l10n.dart';
import '../../../widget/ContainerBox.dart';
import '../../../widget/TapHigh.dart';

class ChatDownloadProgressWidget extends StatefulWidget {
  final String fileName;
  final String fileUrl;
  final String format;
  final Function(String filePath, String format)? onSuccess;
  final Function(String error, String format)? onError;

  ChatDownloadProgressWidget(
      {super.key,
      required this.fileName,
      required this.fileUrl,
      required this.format,
      this.onSuccess,
      this.onError});

  @override
  State<ChatDownloadProgressWidget> createState() =>
      _ChatDownloadProgressWidgetState();
}

class _ChatDownloadProgressWidgetState
    extends State<ChatDownloadProgressWidget> {
  String downloadId = "";
  final controller = Get.put(ChatDownloadProgressController());

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    downloadId = DateTime.now().millisecondsSinceEpoch.toString();
    controller.startDownload(widget.fileUrl, widget.format,
        widget.fileName, downloadId, widget.onSuccess, widget.onError);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
            child: Container(
              decoration: BoxDecoration(color: Colors.black.withOpacity(0)),
            ),
          ),
          GestureDetector(
              child: Container(color: Colors.transparent),
              onTap: () {
                _cancelDownload();
              }),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: GradientBorderContainer(
                borderRadius: BorderRadius.all(Radius.circular(21)),
                strokeWidth: 2,
                gradients: [
                  LinearGradient(
                    colors: [
                      Color(0xFFFF3BDF),
                      Color(0x08FF3BDF),
                      Color(0xFF00FFFF),
                      Color(0xFF543C86),
                    ],
                    stops: [0, 0.34, 0.76, 1],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ],
                child: Container(
                  width: 1.sw,
                  height: 240.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                      image: DecorationImage(
                          image: AssetImage('assets/images/bg_message.png'),
                          fit: BoxFit.fill)),
                  child: Container(
                    margin: EdgeInsets.only(
                        left: 20, right: 20, top: 34.w, bottom: 25.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/images/ic_file_down.webp',
                                width: 20.w,
                                height: 20.h,
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                              Expanded(
                                  child: Container(
                                alignment: Alignment.centerLeft,
                                child: RichText(
                                    textAlign: TextAlign.left,
                                    text: TextSpan(
                                        text: 'Downloading',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16.sp,
                                        ),
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: widget.fileName,
                                            style: TextStyle(
                                              color: Color(0xFF00FFFF),
                                              fontSize: 16.sp,
                                            ),
                                          ),
                                        ])),
                              ))
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 18.h,
                        ),
                        Container(
                          child: Text(
                            "Please wait until the download is completed!",
                            style: TextStyle(
                              color: Color(0xFF988B9A),
                              fontSize: 14.sp,
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 18.h,
                        ),
                        Obx(() {
                          return _createProgressBar(controller.progress());
                        }),
                        SizedBox(
                          height: 6.h,
                        ),
                        Obx(() {
                          return Container(
                              child: Text(
                                  "${(controller.progress() * 100).toStringAsFixed(2)}% · ${controller.time()} seconds",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Color(0xFF988B9A),
                                  )));
                        }),
                        Expanded(child: SizedBox()),
                        Container(
                          child: Row(
                            children: [
                              Expanded(child: SizedBox()),
                              SizedBox(
                                width: 20.w,
                              ),
                              Expanded(
                                  child: TapHigh(
                                      onTap: () {
                                        _cancelDownload();
                                      },
                                      child: ContainerBox(
                                          padding: EdgeInsets.only(
                                              left: 30.w,
                                              right: 30.w,
                                              top: 12.w,
                                              bottom: 12.w),
                                          boxColor: Colors.transparent,
                                          borderColor: Color(0xFFFF3BDF),
                                          borderWith: 1.w,
                                          radius: 10,
                                          alignment: Alignment.center,
                                          child: Text(
                                            S.of(Get.context!).cancel,
                                            style: TextStyle(
                                                color: Color(0xFFFF3BDF),
                                                fontSize: 16.sp),
                                          ))))
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _createProgressBar(double progress) {
    return LinearProgressIndicator(
      backgroundColor: Color(0x78733D7D),
      minHeight: 8.w,
      value: progress == 1.0 ? 1.0 : progress,
      borderRadius: BorderRadius.circular(10),
      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00FFFF)),
    );
  }

  void _cancelDownload() {
    controller.cancelTime();
    Navigator.pop(context);
    ChatDownloadFileUtils.cancelDownload(downloadId);
  }

  @override
  void dispose() {
    super.dispose();
    controller.cancelTime();
    ChatDownloadFileUtils.cancelDownload(downloadId);
  }
}
