/// <AUTHOR>
/// @Date 2025/8/18 17:36
///
/// @Description TODO
import 'dart:collection';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'dart:async';

import '../../utils/inAppLogUtil.dart';

class ThrottledMessageSender {
  // 限制：每秒最多发送3条消息（可根据实际情况调整）
  final Duration _throttleDuration = const Duration(milliseconds: 300);
  DateTime? _lastSendTime;
  final Queue<ChatMessage> _messageQueue = Queue();
  bool _isProcessing = false;

  // 发送消息（带节流控制）
  Future<void> sendMessage(ChatMessage message) async {
    // 加入消息队列
    _messageQueue.add(message);

    // 处理队列（确保同一时间只有一个处理流程）
    if (!_isProcessing) {
      _processQueue();
    }
  }

  // 处理消息队列，控制发送频率
  Future<void> _processQueue() async {
    _isProcessing = true;

    while (_messageQueue.isNotEmpty) {
      final message = _messageQueue.first;

      try {
        // 计算需要等待的时间
        final now = DateTime.now();
        if (_lastSendTime != null) {
          final elapsed = now.difference(_lastSendTime!);
          if (elapsed < _throttleDuration) {
            // 等待剩余时间，避免频率超限
            await Future.delayed(_throttleDuration - elapsed);
          }
        }

        // 发送消息
        await ChatClient.getInstance.chatManager.sendMessage(message);
        _lastSendTime = DateTime.now();
        _messageQueue.removeFirst();
        print("消息发送成功: ${message.msgId}");
        logError("发送成功${message.toJson()}");
      } catch (e) {
        if (e.toString().contains("error: 508")) {
          // 仍触发508，延长等待时间后重试
          print("触发频率限制，延长等待时间...");
          await Future.delayed(const Duration(seconds: 1));
        } else {
          // 其他错误，移除消息并不再重试
          print("消息发送失败: $e");
          _messageQueue.removeFirst();
          rethrow;
        }
      }
    }

    _isProcessing = false;
  }

  // 取消队列中所有消息
  void cancelAllMessages() {
    _messageQueue.clear();
    _isProcessing = false;
  }
}
