import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:new_agnes/api/StorageService.dart';
import 'package:new_agnes/api/stream/stream_ai_enum.dart';
import 'package:new_agnes/api/stream/stream_message_type_enum.dart';
import 'package:new_agnes/module/chat/logic.dart';
import 'package:new_agnes/module/chat/widget/chat_ai_doc_widget.dart';
import 'package:new_agnes/module/chat/widget/chat_ai_error_widget.dart';
import 'package:new_agnes/module/chat/widget/chat_ai_slides_ppt_widget.dart';
import 'package:new_agnes/module/chat/widget/chat_ai_slides_search_web_widget.dart';
import 'package:new_agnes/module/chat/widget/pdf_preview_widget.dart';
import 'package:new_agnes/module/chat/widget/search_chat_item_widget.dart';
import 'package:new_agnes/module/chat/widget/chat_using_tool_tip_widget.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/dialog_utils.dart';
import 'package:new_agnes/utils/event_bus.dart';
import 'package:new_agnes/utils/stt/AzureSpeechManager.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import 'package:new_agnes/widget/TapHigh.dart';
import 'package:new_agnes/widget/message_long_tap_tools.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:vibration/vibration.dart';
import '../../api/Api.dart';
import '../../api/stream/render/RenderManagerController.dart';
import '../../api/stream/render/entity.dart';
import '../../data/caseAndQuestion.dart';
import '../../dialog/message_dialog.dart';
import '../../generated/l10n.dart';
import '../../utils/bgCover.dart';
import '../../utils/toastUtil.dart';
import '../../widget/ComAppbar.dart';
import '../../widget/HuiTan.dart';
import '../../widget/message_input_widget.dart';
import '../home/<USER>/input_widgets/audio_wave_widget.dart';
import '../home/<USER>/input_widgets/home_drawer.dart';
import '../home/<USER>/input_widgets/home_task_input.dart';
import '../home/<USER>/logic.dart';
import '../../widget/gradient_popover_selector.dart';
import 'widget/chat_ai_images_video_widget.dart';
import 'widget/chat_ai_slides_message_widget.dart';
import 'widget/chat_ai_slides_search_image_widget.dart';
import 'widget/chat_think_widget.dart';
import 'widget/deep_research_lay.dart';
import 'widget/search_urls_widget.dart';

class ChatPage extends StatefulWidget {
  final String content;
  final String conversationId;
  final String model;
  final SearchType searchType;
  final bool favoured;
  final List<MessageUploadModel> uploadFiles;

  ChatPage(this.content,
      {this.conversationId = '',
      this.model = '0',
      this.searchType = SearchType.tools,
      this.favoured = false,
      this.uploadFiles = const []});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with WidgetsBindingObserver {
  late ChatLogic chatLogic;
  late HomeTaskInputLogic taskInputLogic;
  final RolesLogic rolesLogic = Get.find<RolesLogic>();
  final HomeDrawerLogic homeDrawerLogic = Get.find<HomeDrawerLogic>();
  final focusNode = FocusNode();
  ScrollController _scrollController = ScrollController();
  final TextEditingController _taskController = TextEditingController();
  double _lastKeyboardHeight = 0;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  AppLifecycleState? _lastLifecycleState = AppLifecycleState.resumed;

  @override
  void initState() {
    super.initState();

    // WidgetsBinding.instance.addPostFrameCallback((_) async {
    //   final status = await Permission.systemAlertWindow.status.isGranted;
    //   if (!status) {
    //     DialogUtils.showRequestFloatingDialog(()async{
    //       await Permission.systemAlertWindow.request();
    //       Get.back();
    //     }, (){
    //       Get.back();
    //     });
    //   }
    // });

    taskInputLogic = rolesLogic.create(() {
      if (taskInputLogic.task.value.isNotEmpty) {
        //取消上一个会话
        //taskInputLogic.stopChatting(chatLogic.conversationId);
        //开始新的
        focusNode.unfocus();
        chatLogic!.model = taskInputLogic.searchType.value.param;
        chatLogic!.goTask(taskInputLogic.task.value);
        chatLogic!.sendStreamChat(taskInputLogic.task.value);
      }
    });
    rolesLogic.currentType = 2;
    chatLogic = Get.put(ChatLogic());
    chatLogic!.title.value = widget.content;
    chatLogic!.favoured.value = widget.favoured;
    chatLogic!.isShowError = true;
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scaffoldKey.currentState?.isDrawerOpen;
      taskInputLogic.searchType.value = widget.searchType;
      // taskInputLogic.isShowQuestions.value = true;
      // taskInputLogic.searchType.value = widget.searchType;
    });
    if (widget.conversationId.isNotEmpty) {
      chatLogic!.conversationId = widget.conversationId;
      chatLogic!.historySelectedId = widget.conversationId;
      taskInputLogic.conversationId = widget.conversationId;
      print(
          '历史数据=${chatLogic.renderManager.workflowRx.value.renderList.length}');
      WidgetsBinding.instance.addPostFrameCallback((callback) {
        chatLogic!.historyConversation(widget.conversationId, onResult: () {
          scrollToNoAnimateBottom();
        });
      });
    } else {
      chatLogic.renderManager.workflowRx.value.renderList.value = [];
      chatLogic!.goTask(widget.content, uploadFiles: widget.uploadFiles);
      print(
          '新数据=${chatLogic.renderManager.workflowRx.value.renderList.length}');
      WidgetsBinding.instance.addPostFrameCallback((callback) {
        chatLogic!.model = widget.model;
        chatLogic!.setConversations(widget.content, widget.uploadFiles,
            (result) {
          taskInputLogic.conversationId = result;
        });
      });
    }
    initDataListener();
    initScrollControllerListener();
    initFocusNodeListener();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    print('App state changed: $state');
    if (_lastLifecycleState == AppLifecycleState.resumed &&
        state == AppLifecycleState.inactive) {
      print('App 切换到后台');
      // _handleAppToBackground();
    } else if (_lastLifecycleState == AppLifecycleState.inactive &&
        state == AppLifecycleState.resumed) {
      print('App 切换到前台');
      _handleAppToForeground();
    }
    _lastLifecycleState = state;
  }

  void initFocusNodeListener() {
    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        double height = _scrollController.position.maxScrollExtent -
            _scrollController.position.pixels;
        if (height < 35.w) {
          Future.delayed(const Duration(milliseconds: 300), () {
            scrollGoToBottom(isCloseFocus: false);
          });
        }
      }
    });
  }

  Future<void> _handleAppToBackground() async {
    print('执行后台操作');
    final status = await FlutterOverlayWindow.isPermissionGranted();
    if (!status) {
      return;
    }
    var bool = await FlutterOverlayWindow.isActive();
    print("isActive:${bool}");
    if (!bool) {
      //FlutterOverlayWindow.shareData({"type": "init"});
      await FlutterOverlayWindow.showOverlay(
        enableDrag: true,
        overlayType: OverlayType.chat,
        overlayTitle: "agens",
        overlayContent: 'searching..',
        flag: OverlayFlag.defaultFlag,
        alignment: OverlayAlignment.centerRight,
        visibility: NotificationVisibility.visibilityPublic,
        positionGravity: PositionGravity.auto,
        height: 200.w.toInt(),
        width: 200.w.toInt(),
        startPosition: const OverlayPosition(0, 0),
      );
    }
  }

  Future<void> _handleAppToForeground() async {
    StreamStatus status =
        chatLogic.streamStatus[taskInputLogic.conversationId] ??
            StreamStatus.waiting;
    if (chatLogic.isEnd || status != StreamStatus.error) {
      print('已渲染结束，不执行前台操作');
      return;
    }
    print('执行前台操作');
    // scrollGoToBottom();
    chatLogic.streamStatus[taskInputLogic.conversationId] =
        StreamStatus.waiting;
    chatLogic!.historyConversation(taskInputLogic.conversationId, onResult: () {
      scrollToNoAnimateBottom();
    }, isLoading: false);
    // closeOverlay();
  }

  Future<void> closeOverlay() async {
    if (await FlutterOverlayWindow.isActive()) {
      await FlutterOverlayWindow.closeOverlay();
    }
  }

  void initScrollControllerListener() {
    // 监听用户滚动
    _scrollController.addListener(() {
      // 检测是否在滑动
      if (_scrollController.position.userScrollDirection !=
          ScrollDirection.idle) {
        chatLogic.isScrolling.value = true;
      } else {
        chatLogic.isScrolling.value = false;
      }
      // 检测是否到达顶部
      chatLogic.isAtTop.value = _scrollController.position.pixels == 0;

      // 检测是否到达底部
      chatLogic.isAtBottom.value = _scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent;
      if (chatLogic.isAtBottom.value) {
        chatLogic!.isTop.value = true;
        chatLogic.isAutoScrolling.value = true;
        if (chatLogic.isHistory.value ) {
          chatLogic.isHistory.value = false;
        }
      } else {
        chatLogic!.isTop.value = false;
      }
      if (_scrollController.position.userScrollDirection ==
              ScrollDirection.reverse ||
          _scrollController.position.userScrollDirection ==
              ScrollDirection.forward) {
        chatLogic.isAutoScrolling.value = false;
      }
    });
  }

  void initDataListener() {
    WidgetsBinding.instance!.addPostFrameCallback((_) {
      chatLogic.renderManager.workflowRx.value.renderList.listen((think) {
        if (chatLogic.isHistory.value) {
          return;
        }
        if (chatLogic.isAutoScrolling.value) {
          String type = 'llm';
          if (think.length > 0) {
            type = think[think.length - 1].sender.value;
          }
          scrollGoToBottom(type: type);
        }
      });
    });
  }

  // 添加一个防抖变量，防止短时间内多次触发滚动
  DateTime _lastScrollTime = DateTime.now();

  void scrollGoToBottom({type = 'llm', bool isCloseFocus = true}) {
    if (isCloseFocus) {
      focusNode.unfocus();
    }
    WidgetsBinding.instance.addPostFrameCallback((callback) {
      Future.delayed(Duration(milliseconds: type == 'llm' ? 100 : 200), () {
        if (DateTime.now().difference(_lastScrollTime).inMilliseconds < 100) {
          return;
        }
        _lastScrollTime = DateTime.now();
        if (_scrollController.positions.isNotEmpty) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: Duration(milliseconds: 250),
            curve: Curves.linear,
          );
        }
      });
    });
  }

  void scrollToNoAnimateBottom() {
    WidgetsBinding.instance.addPostFrameCallback((callback) {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  @override
  void didChangeMetrics() {
    final currentHeight = WidgetsBinding
        .instance.platformDispatcher.views.first.viewInsets.bottom;
    if (_lastKeyboardHeight > 0 && currentHeight == 0) {
      focusNode.unfocus();
    }
    _lastKeyboardHeight = currentHeight;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    chatLogic.renderManager.workflowRx.value.renderList.value = [];
    chatLogic?.streamRequest?.cancelRequest();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      taskInputLogic.chatting.value = false;
      if (AzureSpeechManager.isFromBle || taskInputLogic.speeching.value) {
        taskInputLogic.isRecognizingAni.value = false;
        print("stopContinuousRecognition");
        AzureSpeechManager.stopContinuousRecognition();
        taskInputLogic.speeching.value = false;
      }
      var audioWaveLogic = Get.find<AudioWaveLogic>();
      audioWaveLogic.isPressedFinished.value = true;
      audioWaveLogic.isLoading.value = false;
      taskInputLogic.isRecognizingAni.value = false;
    });

    rolesLogic.currentType = 1;
    chatLogic.renderManager.workflowRx.value.renderList.clear();
    chatLogic.conversationId = '';
    print('清数据=${chatLogic.renderManager.workflowRx.value.renderList.length}');
    Get.delete<ChatLogic>();
    Get.delete<RenderManagerController>();
    // taskInputLogic.dispose();
    chatLogic.streamStatus.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (isPop, result) async {
        if (isPop) {
          return;
        }
        Get.back();
      },
      child: buildMineBg(
        child: Scaffold(
          onDrawerChanged: (isDrawerOpen) async {
            if (isDrawerOpen) {
              focusNode.unfocus();
              await homeDrawerLogic.loadFavouredSearchHistory();
              await homeDrawerLogic.loadNonFavouredSearchHistory();
              await homeDrawerLogic.getQuotaRequest();
            } else {
              if(chatLogic.isSwitch){
                _scrollController.jumpTo(0);
              }
              chatLogic.isSwitch = false;
            }
          },
          key: _scaffoldKey,
          appBar: PreferredSize(
            preferredSize: Size.fromHeight(kToolbarHeight),
            child: Obx(() {
              return ComAppBar(
                context,
                "", // 2025.9.17，产品要求去除会话标题
                backgroundColor: Colors.transparent,
                icBack: 'assets/images/icon_drawer.webp',
                onPop: () {
                  CmUtils.zhenDong();
                  _scaffoldKey.currentState?.openDrawer();
                },
                actions: [
                  Container(
                      margin: EdgeInsets.only(right: 16.w),
                    child: GestureDetector(
                        onTap: () {
                          rolesLogic.homeTaskInputLogic.searchType.value =
                              SearchType.tools;
                          rolesLogic.homeTaskInputLogic.selectedItem.value = {};
                          chatLogic.stopLoading();
                          Get.back();
                          Future.delayed(Duration(milliseconds: 80), () {
                            eventBus.fire("toSearchPage");
                          });
                        },
                        child: Image.asset(
                          "assets/icon/icon_new_task.webp",
                          width: 24.w,
                          height: 24.h,
                        )),
                  ),
                  GradientPopoverSelector(
                    items: [
                      {
                        'icon': chatLogic!.favoured.value
                            ? 'assets/images/ic_chary_top_shoucang_yes.webp'
                            : 'assets/images/ic_chart_top_shoucang_no.webp',
                        'text': S.of(context).collect
                      },
                      {
                        'icon': 'assets/images/ic_chart_top_share.png',
                        'text': S.of(context).share
                      },
                      {
                        'icon': 'assets/images/ic_chart_top_edit.webp',
                        'text': S.of(context).rename
                      }
                    ],
                    selectedIndex: 0,
                    onSelected: (index) {
                      if (index == 0) {
                        chatLogic!.collectionOrEdit(1);
                      } else if (index == 1) {
                        String shareUrl =
                            "${Api.baseUrl}/share?conversationid=${chatLogic.conversationId}";
                        SharePlus.instance.share(
                          ShareParams(
                            title: chatLogic.title.value,
                            uri: Uri.parse(shareUrl),
                          ),
                        );
                      } else if (index == 2) {
                        showDialog(
                          context: context,
                          builder: (context) => MessageDialog(
                            () {
                              Get.back();
                            },
                            (String data) {
                              if (data.isNotEmpty) {
                                Get.back();
                                chatLogic!.collectionOrEdit(2, editText: data);
                              } else {
                                showFailToast(
                                    S.of(Get.context!).writeSomething);
                              }
                            },
                            data: chatLogic!.title.value,
                            onLeftName: S.of(Get.context!).cancel,
                            onRightName: S.of(Get.context!).confirm,
                            height: 220,
                            isEdit: true,
                            title: S.of(Get.context!).edit,
                          ),
                        );
                      }
                    },
                    showDeleteItem: false,
                    // 适当向上提升弹出框，避免离图标过远；可根据需要微调像素值。
                    lift: 16,
                    coverAppBar: true,
                    autoClampSafeArea: false,
                    marginSize: 0,
                    showSelected: false,
                    child: Container(
                      margin: EdgeInsets.only(right: 16.w),
                      child: Image.asset(
                        "assets/images/ic_group_chat_more.png",
                        width: 24.w,
                        height: 24.h,
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
          backgroundColor: Colors.transparent,
          extendBodyBehindAppBar: false,
          extendBody: true,
          resizeToAvoidBottomInset: true,
          // 规避底部布局被软键盘顶起
          drawer: HomeDrawer(
            context,
            "chat",
            onScrollBottom: () {
              scrollToNoAnimateBottom();
            },
          ),
          body: SafeArea(
              child: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    child: Stack(
                      children: [
                        _listLay(),
                        // _topLay(),
                        Positioned(
                            bottom: 16.w,
                            right: 24.w,
                            child: Obx(() {
                              return chatLogic.renderManager.workflowRx.value
                                          .renderList.length >
                                      1
                                  ? TapHigh(
                                      onTap: () {
                                        focusNode.unfocus();
                                        if (chatLogic!.isTop.value) {
                                          chatLogic!.isTop.value = false;
                                          _scrollController.jumpTo(0);
                                        } else {
                                          chatLogic!.isTop.value = true;
                                          _scrollController.jumpTo(
                                              _scrollController
                                                  .position.maxScrollExtent);
                                        }
                                      },
                                      child: Image.asset(
                                        chatLogic!.isTop.value
                                            ? 'assets/images/ic_chart_go_top.webp'
                                            : 'assets/images/ic_chart_go_down.webp',
                                        width: 24.w,
                                        height: 24.w,
                                      ))
                                  : SizedBox();
                            })),
                      ],
                    ),
                  ),
                ),
                _bottomLay(),
              ],
            ),
          )),
        ),
      ),
    );
  }

  Widget _bottomLay() {
    return Obx(() => HomeTaskInput(
          focusNode,
          () {
            if (taskInputLogic.task.value.isNotEmpty) {
              focusNode.unfocus();
              chatLogic.isAutoScrolling.value = true;
              chatLogic!.model = taskInputLogic.searchType.value.param;
              List<MessageUploadModel> uploadFiles =
                  List.from(chatLogic.uploadFiles());
              chatLogic!
                  .goTask(taskInputLogic.task.value, uploadFiles: uploadFiles);
              chatLogic!.sendStreamChat(taskInputLogic.task.value,
                  uploadFiles: uploadFiles);
              chatLogic.uploadFiles.clear();
            }
          },
          taskInputLogic,
          type: 1,
          selectedImages: chatLogic.uploadFiles.value,
          onRemoveImage: (index) {
            if (chatLogic.uploadFiles.isEmpty) return;
            chatLogic.uploadFiles.removeAt(index);
          },
          onAddImage: (uploadFiles) {
            chatLogic.uploadFiles.addAll(uploadFiles);
          },
          onUpdateImage: (index, uploadFile) {
            chatLogic.uploadFiles[index] = uploadFile;
          },
        ));
  }

  Widget _listLay() {
    return Listener(
      onPointerMove: (event) {
        SystemChannels.textInput.invokeMethod('TextInput.hide');
      },
      child: Obx(() {
        print('是否是历史=${chatLogic.isHistory.value}');
        return Container(
            alignment: Alignment.topCenter,
            child: ScrollConfiguration(
                behavior: NoShadowScrollBehavior(),
                child: SingleChildScrollView(
                  controller: _scrollController,
                  physics: BouncingScrollPhysics(),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        chatLogic.isHistory.value
                            ? Container(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Obx(() {
                                      bool isThink = false;
                                      if (chatLogic.renderManager.workflow
                                          .renderList.isNotEmpty) {
                                        isThink = chatLogic.renderManager.workflow
                                                .renderList.last.type ==
                                            StreamMessageTypeEnum
                                                .message_thinking;
                                      }
                                      return (chatLogic
                                                  .isLoadingAnimation.value &&
                                              !isThink)
                                          ? Lottie.asset(
                                              'assets/json/loading.json',
                                              height: 48.h,
                                              width: 48.w)
                                          : SizedBox();
                                    }),
                                    Container(
                                      height: 1.sh,
                                    )
                                  ],
                                ),
                              )
                            : SizedBox(),
                        ListView.builder(
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: chatLogic!.renderManager!.workflowRx.value
                                .renderList.length,
                            itemBuilder: (context, index) {
                              RenderTask model = chatLogic!.renderManager!
                                  .workflowRx.value.renderList[index];
                              return Obx(() {
                                return _itemLay(model, index);
                              });
                            }),
                        Obx(() {
                          bool isThink = false;
                          if (chatLogic
                              .renderManager.workflow.renderList.isNotEmpty) {
                            RenderTask lastTask = chatLogic.renderManager.workflow.renderList
                                .last;
                            isThink = lastTask.type ==
                                StreamMessageTypeEnum.message_thinking;
                            if(lastTask.thinkTasks.isNotEmpty){
                              RenderTask lastThinkTask = lastTask.thinkTasks.last;
                              if(lastThinkTask.type() == StreamMessageTypeEnum.reporter){
                                isThink = false;
                              }
                            }
                          }
                          return chatLogic.isHistory.value
                              ? SizedBox()
                              : (chatLogic.isLoadingAnimation.value && !isThink)
                                  ? Lottie.asset('assets/json/loading.json',
                                      height: 48.h, width: 48.w)
                                  : SizedBox();
                        }),
                        SizedBox(
                          height: 20.w,
                        )
                      ],
                    ),
                  ),
                )));
      }),
    );
  }

  Widget _itemLay(RenderTask model, int index) {
    int lastIndex =
        chatLogic!.renderManager!.workflowRx.value.renderList.length - 1;
    Widget widget = Container();
    chatLogic.isEnd = model.isComplete.value;
    if (model!.sender.value == 'llm') {
      //ai
      switch (model.type.value) {
        case StreamMessageTypeEnum.message_go_button:
          widget = GoButton(onResult: () {
            if (chatLogic.renderManager.isTaskRunning) {
              //正在执行直接return
              return;
            }
            chatLogic!.renderManager!.workflowRx.value.renderList![index].type
                .value = StreamMessageTypeEnum.message_execute_now;
            chatLogic!.renderManager!.workflowRx.value.renderList![index].sender
                .value = 'user';
            chatLogic!.renderManager!.workflowRx.value.renderList![index]
                .content.value = 'execute now';
            chatLogic!.sendStreamChat('execute now', fromButton: true);
          });
          break;
        case StreamMessageTypeEnum.message_thinking:
          int renderLength = index;
          if (renderLength > 3) {
            for (int a = renderLength - 3; a < renderLength; a++) {
              RenderTask renderTask =
                  chatLogic!.renderManager!.workflowRx.value.renderList[a];
              if (renderTask.type == StreamMessageTypeEnum.message_go_button) {
                Future.delayed(Duration(milliseconds: 500), () {
                  chatLogic!.renderManager!.workflowRx.value.renderList![a].type
                      .value = StreamMessageTypeEnum.message_execute_now;
                  chatLogic!.renderManager!.workflowRx.value.renderList![a]
                      .sender.value = 'user';
                  chatLogic!.renderManager!.workflowRx.value.renderList![a]
                      .content.value = 'execute now';
                });
              }
            }
          }
          widget = ChatThinkWidget(index, () {});
          break;
        case StreamMessageTypeEnum.search_urls:
          RenderTask contentTask =
              chatLogic!.renderManager!.workflowRx.value.renderList[index - 1];
          widget = SearchUrlsWidget(model.content.value, model.isComplete(),
              contentTask.content.value, contentTask.agentName);
          break;
        case StreamMessageTypeEnum.message:
          newMessageZhenDong(index);
          widget = SearchChatItemWidget(
            content: model.content.value,
            isComplete: model.isComplete(),
            handOff: model.handOff,
            agentName: model.agentName,
            locationIndex: index,
            allIndex:
                chatLogic!.renderManager!.workflowRx.value.renderList.length,
            useAnimation: false,
            outerController: _scrollController,
            isHis: chatLogic.isHistory.value,
            appendixMode: model.appendixMode,
          );
          break;
        case StreamMessageTypeEnum.design_mentor:
        case StreamMessageTypeEnum.visual_principles:
        case StreamMessageTypeEnum.prompt_enhance:
        case StreamMessageTypeEnum.slide_design:
        case StreamMessageTypeEnum.plan_schedule:
          widget = ChatAiSlidesMessageWidget(
            isFold: model.fold(),
            content: model.content(),
            handOff: model.handOff,
            agentName: model.agentName,
            isComplete: model.isComplete(),
            type: model.type(),
            pptCompletedText: model.pptCompletedText,
            outerScrollController: _scrollController,
          );
          break;
        case StreamMessageTypeEnum.tag:
          newMessageZhenDong(index);
          widget = ChatUsingToolTipWidget(title: model.content(),isComplete: model.isComplete(),);
          break;
        case StreamMessageTypeEnum.search_web:
          widget = ChatAiSlidesSearchWebWidget(
            content: model.content(),
            isComplete: model.isComplete(),
            outerScrollController: _scrollController,
          );
          break;
        case StreamMessageTypeEnum.search_image:
          widget = ChatAiSlidesSearchImageWidget(
            content: model.content(),
            isComplete: model.isComplete(),
            outerScrollController: _scrollController,
          );
          break;
        case StreamMessageTypeEnum.ppt:
          widget = ChatAiSlidesPptWidget(
            content: model.content(),
            isComplete: model.isComplete(),
            conversationId: model.conversationId,
            slidesData: [],
          );
          break;
        case StreamMessageTypeEnum.error:
          widget = ChatAiErrorWidget(content: model.content());
          break;
        case StreamMessageTypeEnum.design_cache:
        case StreamMessageTypeEnum.visual:
          widget = ChatAiImagesVideoWidget(
            outerController: _scrollController,
            data: model.productImages ?? [],
            isComplete: model.isComplete(),
            content: model.content(),
            onImageExport: (imageExport) {
              chatLogic.uploadFiles.add(imageExport);
            },
          );
          break;
        default:
          break;
      }
    }
    if (model.sender.value == 'user') {
      //用户
      switch (model.type.value) {
        case StreamMessageTypeEnum.message_text:
          widget = Column(
            children: [
              if (model.docs != null) ...[
                SizedBox(height: 8.h),
                ChatAiDocWidget(docs: model.docs ?? [])
              ],
              SizedBox(
                height: model.docs != null ? 8.h : 20.h,
              ),
              UserRightTextLay(model.content.value),
            ],
          );
          break;
        case StreamMessageTypeEnum.message_execute_now:
          widget = UserGoSuccressLay();
          break;
        default:
          break;
      }
    }
    if (model.sender() == "sys") {
      if (model.type() == StreamMessageTypeEnum.error) {
        widget = ChatAiErrorWidget(content: model.content());
      }
    }
    return widget;
  }

  void newMessageZhenDong(int index) {
    if (!chatLogic.isHistory.value && index == chatLogic.newMessageIndex) {
      CmUtils.zhenDong();
      CmUtils.zhenDong();
      CmUtils.zhenDong();
      chatLogic.newMessageIndex = chatLogic.newMessageIndex + 5;
    }
  }
}
