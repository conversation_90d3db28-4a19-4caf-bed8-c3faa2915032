///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class HistoryDeepResearchModelWorkflowStepsTasksPayload {
/*
{
  "text": "```json\n{\n  \"thought\": \"用户需要一个详细的大理旅游计划，包括天气、交通、住宿、景点、行程规划、特色美食和行前准备等内容。我将分步骤安排研究员收集信息，然后由报告员整理成完整的旅游攻略。\",\n  \"title\": \"大理旅游计划\",\n  \"steps\": [\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"收集大理基本信息\",\n      \"description\": \"收集大理的天气情况、交通方式、住宿选择和主要景点信息。包括最佳旅游季节、如何到达大理、各类住宿的价格区间和位置、必去景点介绍等。\",\n      \"note\": \"注意区分淡旺季信息，收集最新数据\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"规划行程路线\",\n      \"description\": \"根据旅行时长(假设5-7天)和个人偏好(文化/自然风光)，设计详细的每日行程安排。包括景点游览顺序、交通方式和时间安排。\",\n      \"note\": \"要考虑景点之间的距离和开放时间\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色住宿\",\n      \"description\": \"收集大理古城、洱海周边等区域的特色民宿和酒店推荐，包括价格、位置、设施和用户评价等信息。\",\n      \"note\": \"重点关注有当地特色的住宿选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色美食\",\n      \"description\": \"收集大理特色美食和推荐餐厅，包括乳扇、三道茶、酸辣鱼等当地美食，以及口碑好的餐厅位置和人均消费。\",\n      \"note\": \"注意收集不同价位和环境的餐厅选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"准备行前清单\",\n      \"description\": \"根据大理的气候特点和旅游活动，整理必备物品清单，包括衣物、药品、证件、电子设备等。\",\n      \"note\": \"考虑不同季节的特殊需求\"\n    },\n    {\n      \"agent_name\": \"reporter\",\n      \"title\": \"生成完整攻略\",\n      \"description\": \"将前面收集的所有信息整合成一份结构清晰、实用的大理旅游攻略文档，包括文字描述和必要的数据表格。\",\n      \"note\": \"确保信息准确、排版美观易读\"\n    }\n  ]\n}\n```"
}
*/

  String? text;
  String? toolName;
  HistoryDeepResearchModelPayloadInput? input;
  String? output;

  HistoryDeepResearchModelWorkflowStepsTasksPayload({
    this.text,
    this.toolName,
    this.input,
    this.output,
  });
  HistoryDeepResearchModelWorkflowStepsTasksPayload.fromJson(Map<String, dynamic> json) {
    text = json['text']?.toString();
    toolName = json['toolName']?.toString();
    input = (json['input'] != null) ? HistoryDeepResearchModelPayloadInput.fromJson(json['input']) : null;
    output = json['output']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['text'] = text;
    data['toolName'] = toolName;
    if (input != null) {
      data['input'] = input!.toJson();
    }
    data['output'] = output;
    return data;
  }
}

class HistoryDeepResearchModelPayloadInput {
/*
{
  "query": "大理 最佳旅游季节"
}
*/

  String? query;

  HistoryDeepResearchModelPayloadInput({
    this.query,
  });
  HistoryDeepResearchModelPayloadInput.fromJson(Map<String, dynamic> json) {
    query = json['query']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['query'] = query;
    return data;
  }
}

class HistoryDeepResearchModelWorkflowStepsTasks {
/*
{
  "id": "52f1d253-5a13-477e-9728-0f5072c2b3ac",
  "type": "thinking",
  "state": "success",
  "payload": {
    "text": "```json\n{\n  \"thought\": \"用户需要一个详细的大理旅游计划，包括天气、交通、住宿、景点、行程规划、特色美食和行前准备等内容。我将分步骤安排研究员收集信息，然后由报告员整理成完整的旅游攻略。\",\n  \"title\": \"大理旅游计划\",\n  \"steps\": [\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"收集大理基本信息\",\n      \"description\": \"收集大理的天气情况、交通方式、住宿选择和主要景点信息。包括最佳旅游季节、如何到达大理、各类住宿的价格区间和位置、必去景点介绍等。\",\n      \"note\": \"注意区分淡旺季信息，收集最新数据\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"规划行程路线\",\n      \"description\": \"根据旅行时长(假设5-7天)和个人偏好(文化/自然风光)，设计详细的每日行程安排。包括景点游览顺序、交通方式和时间安排。\",\n      \"note\": \"要考虑景点之间的距离和开放时间\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色住宿\",\n      \"description\": \"收集大理古城、洱海周边等区域的特色民宿和酒店推荐，包括价格、位置、设施和用户评价等信息。\",\n      \"note\": \"重点关注有当地特色的住宿选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色美食\",\n      \"description\": \"收集大理特色美食和推荐餐厅，包括乳扇、三道茶、酸辣鱼等当地美食，以及口碑好的餐厅位置和人均消费。\",\n      \"note\": \"注意收集不同价位和环境的餐厅选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"准备行前清单\",\n      \"description\": \"根据大理的气候特点和旅游活动，整理必备物品清单，包括衣物、药品、证件、电子设备等。\",\n      \"note\": \"考虑不同季节的特殊需求\"\n    },\n    {\n      \"agent_name\": \"reporter\",\n      \"title\": \"生成完整攻略\",\n      \"description\": \"将前面收集的所有信息整合成一份结构清晰、实用的大理旅游攻略文档，包括文字描述和必要的数据表格。\",\n      \"note\": \"确保信息准确、排版美观易读\"\n    }\n  ]\n}\n```"
  }
}
*/

  String? id;
  String? type;
  String? state;
  HistoryDeepResearchModelWorkflowStepsTasksPayload? payload;

  HistoryDeepResearchModelWorkflowStepsTasks({
    this.id,
    this.type,
    this.state,
    this.payload,
  });
  HistoryDeepResearchModelWorkflowStepsTasks.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    type = json['type']?.toString();
    state = json['state']?.toString();
    payload = (json['payload'] != null) ? HistoryDeepResearchModelWorkflowStepsTasksPayload.fromJson(json['payload']) : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['type'] = type;
    data['state'] = state;
    if (payload != null) {
      data['payload'] = payload!.toJson();
    }
    return data;
  }
}

class HistoryDeepResearchModelWorkflowSteps {
/*
{
  "id": "6bc0d289-8004-4bbf-91aa-a465a0284b2e_planner_7",
  "agentId": "6bc0d289-8004-4bbf-91aa-a465a0284b2e_planner_7",
  "agentName": "planner",
  "type": "agentic",
  "tasks": [
    {
      "id": "52f1d253-5a13-477e-9728-0f5072c2b3ac",
      "type": "thinking",
      "state": "success",
      "payload": {
        "text": "```json\n{\n  \"thought\": \"用户需要一个详细的大理旅游计划，包括天气、交通、住宿、景点、行程规划、特色美食和行前准备等内容。我将分步骤安排研究员收集信息，然后由报告员整理成完整的旅游攻略。\",\n  \"title\": \"大理旅游计划\",\n  \"steps\": [\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"收集大理基本信息\",\n      \"description\": \"收集大理的天气情况、交通方式、住宿选择和主要景点信息。包括最佳旅游季节、如何到达大理、各类住宿的价格区间和位置、必去景点介绍等。\",\n      \"note\": \"注意区分淡旺季信息，收集最新数据\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"规划行程路线\",\n      \"description\": \"根据旅行时长(假设5-7天)和个人偏好(文化/自然风光)，设计详细的每日行程安排。包括景点游览顺序、交通方式和时间安排。\",\n      \"note\": \"要考虑景点之间的距离和开放时间\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色住宿\",\n      \"description\": \"收集大理古城、洱海周边等区域的特色民宿和酒店推荐，包括价格、位置、设施和用户评价等信息。\",\n      \"note\": \"重点关注有当地特色的住宿选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色美食\",\n      \"description\": \"收集大理特色美食和推荐餐厅，包括乳扇、三道茶、酸辣鱼等当地美食，以及口碑好的餐厅位置和人均消费。\",\n      \"note\": \"注意收集不同价位和环境的餐厅选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"准备行前清单\",\n      \"description\": \"根据大理的气候特点和旅游活动，整理必备物品清单，包括衣物、药品、证件、电子设备等。\",\n      \"note\": \"考虑不同季节的特殊需求\"\n    },\n    {\n      \"agent_name\": \"reporter\",\n      \"title\": \"生成完整攻略\",\n      \"description\": \"将前面收集的所有信息整合成一份结构清晰、实用的大理旅游攻略文档，包括文字描述和必要的数据表格。\",\n      \"note\": \"确保信息准确、排版美观易读\"\n    }\n  ]\n}\n```"
      }
    }
  ]
}
*/

  String? id;
  String? agentId;
  String? agentName;
  String? type;
  List<HistoryDeepResearchModelWorkflowStepsTasks?>? tasks;

  HistoryDeepResearchModelWorkflowSteps({
    this.id,
    this.agentId,
    this.agentName,
    this.type,
    this.tasks,
  });
  HistoryDeepResearchModelWorkflowSteps.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    agentId = json['agentId']?.toString();
    agentName = json['agentName']?.toString();
    type = json['type']?.toString();
    if (json['tasks'] != null) {
      final v = json['tasks'];
      final arr0 = <HistoryDeepResearchModelWorkflowStepsTasks>[];
      v.forEach((v) {
        arr0.add(HistoryDeepResearchModelWorkflowStepsTasks.fromJson(v));
      });
      tasks = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['agentId'] = agentId;
    data['agentName'] = agentName;
    data['type'] = type;
    if (tasks != null) {
      final v = tasks;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['tasks'] = arr0;
    }
    return data;
  }
}

class HistoryDeepResearchModelWorkflow {
/*
{
  "id": "cd1ee95c-f40b-40d8-a757-3768fca120c0",
  "name": "research",
  "steps": [
    {
      "id": "6bc0d289-8004-4bbf-91aa-a465a0284b2e_planner_7",
      "agentId": "6bc0d289-8004-4bbf-91aa-a465a0284b2e_planner_7",
      "agentName": "planner",
      "type": "agentic",
      "tasks": [
        {
          "id": "52f1d253-5a13-477e-9728-0f5072c2b3ac",
          "type": "thinking",
          "state": "success",
          "payload": {
            "text": "```json\n{\n  \"thought\": \"用户需要一个详细的大理旅游计划，包括天气、交通、住宿、景点、行程规划、特色美食和行前准备等内容。我将分步骤安排研究员收集信息，然后由报告员整理成完整的旅游攻略。\",\n  \"title\": \"大理旅游计划\",\n  \"steps\": [\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"收集大理基本信息\",\n      \"description\": \"收集大理的天气情况、交通方式、住宿选择和主要景点信息。包括最佳旅游季节、如何到达大理、各类住宿的价格区间和位置、必去景点介绍等。\",\n      \"note\": \"注意区分淡旺季信息，收集最新数据\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"规划行程路线\",\n      \"description\": \"根据旅行时长(假设5-7天)和个人偏好(文化/自然风光)，设计详细的每日行程安排。包括景点游览顺序、交通方式和时间安排。\",\n      \"note\": \"要考虑景点之间的距离和开放时间\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色住宿\",\n      \"description\": \"收集大理古城、洱海周边等区域的特色民宿和酒店推荐，包括价格、位置、设施和用户评价等信息。\",\n      \"note\": \"重点关注有当地特色的住宿选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色美食\",\n      \"description\": \"收集大理特色美食和推荐餐厅，包括乳扇、三道茶、酸辣鱼等当地美食，以及口碑好的餐厅位置和人均消费。\",\n      \"note\": \"注意收集不同价位和环境的餐厅选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"准备行前清单\",\n      \"description\": \"根据大理的气候特点和旅游活动，整理必备物品清单，包括衣物、药品、证件、电子设备等。\",\n      \"note\": \"考虑不同季节的特殊需求\"\n    },\n    {\n      \"agent_name\": \"reporter\",\n      \"title\": \"生成完整攻略\",\n      \"description\": \"将前面收集的所有信息整合成一份结构清晰、实用的大理旅游攻略文档，包括文字描述和必要的数据表格。\",\n      \"note\": \"确保信息准确、排版美观易读\"\n    }\n  ]\n}\n```"
          }
        }
      ]
    }
  ]
}
*/

  String? id;
  String? name;
  List<HistoryDeepResearchModelWorkflowSteps?>? steps;

  HistoryDeepResearchModelWorkflow({
    this.id,
    this.name,
    this.steps,
  });
  HistoryDeepResearchModelWorkflow.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    name = json['name']?.toString();
    if (json['steps'] != null) {
      final v = json['steps'];
      final arr0 = <HistoryDeepResearchModelWorkflowSteps>[];
      v.forEach((v) {
        arr0.add(HistoryDeepResearchModelWorkflowSteps.fromJson(v));
      });
      steps = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (steps != null) {
      final v = steps;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['steps'] = arr0;
    }
    return data;
  }
}

class HistoryDeepResearchModel {
/*
{
  "workflow": {
    "id": "cd1ee95c-f40b-40d8-a757-3768fca120c0",
    "name": "research",
    "steps": [
      {
        "id": "6bc0d289-8004-4bbf-91aa-a465a0284b2e_planner_7",
        "agentId": "6bc0d289-8004-4bbf-91aa-a465a0284b2e_planner_7",
        "agentName": "planner",
        "type": "agentic",
        "tasks": [
          {
            "id": "52f1d253-5a13-477e-9728-0f5072c2b3ac",
            "type": "thinking",
            "state": "success",
            "payload": {
              "text": "```json\n{\n  \"thought\": \"用户需要一个详细的大理旅游计划，包括天气、交通、住宿、景点、行程规划、特色美食和行前准备等内容。我将分步骤安排研究员收集信息，然后由报告员整理成完整的旅游攻略。\",\n  \"title\": \"大理旅游计划\",\n  \"steps\": [\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"收集大理基本信息\",\n      \"description\": \"收集大理的天气情况、交通方式、住宿选择和主要景点信息。包括最佳旅游季节、如何到达大理、各类住宿的价格区间和位置、必去景点介绍等。\",\n      \"note\": \"注意区分淡旺季信息，收集最新数据\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"规划行程路线\",\n      \"description\": \"根据旅行时长(假设5-7天)和个人偏好(文化/自然风光)，设计详细的每日行程安排。包括景点游览顺序、交通方式和时间安排。\",\n      \"note\": \"要考虑景点之间的距离和开放时间\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色住宿\",\n      \"description\": \"收集大理古城、洱海周边等区域的特色民宿和酒店推荐，包括价格、位置、设施和用户评价等信息。\",\n      \"note\": \"重点关注有当地特色的住宿选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"推荐特色美食\",\n      \"description\": \"收集大理特色美食和推荐餐厅，包括乳扇、三道茶、酸辣鱼等当地美食，以及口碑好的餐厅位置和人均消费。\",\n      \"note\": \"注意收集不同价位和环境的餐厅选择\"\n    },\n    {\n      \"agent_name\": \"researcher\",\n      \"title\": \"准备行前清单\",\n      \"description\": \"根据大理的气候特点和旅游活动，整理必备物品清单，包括衣物、药品、证件、电子设备等。\",\n      \"note\": \"考虑不同季节的特殊需求\"\n    },\n    {\n      \"agent_name\": \"reporter\",\n      \"title\": \"生成完整攻略\",\n      \"description\": \"将前面收集的所有信息整合成一份结构清晰、实用的大理旅游攻略文档，包括文字描述和必要的数据表格。\",\n      \"note\": \"确保信息准确、排版美观易读\"\n    }\n  ]\n}\n```"
            }
          }
        ]
      }
    ]
  }
}
*/

  HistoryDeepResearchModelWorkflow? workflow;

  HistoryDeepResearchModel({
    this.workflow,
  });
  HistoryDeepResearchModel.fromJson(Map<String, dynamic> json) {
    workflow = (json['workflow'] != null) ? HistoryDeepResearchModelWorkflow.fromJson(json['workflow']) : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (workflow != null) {
      data['workflow'] = workflow!.toJson();
    }
    return data;
  }
}
