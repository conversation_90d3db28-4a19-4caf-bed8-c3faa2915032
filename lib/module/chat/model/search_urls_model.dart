///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class SearchUrlsModel {
/*
{
  "type": "web",
  "title": "南京 - 中国气象局-天气预报-城市预报",
  "content": "时间 14:00 17:00 20:00 23:00 02:00 05:00 08:00 11:00\n天气Image 16Image 17Image 18Image 19Image 20Image 21Image 22Image 23\n气温 30.8℃27.8℃26.2℃25.2℃24.9℃23.4℃27.5℃30.6℃\n降水 无降水 1mm 无降水 无降水 无降水 无降水 无降水 无降水\n风速 9.7m/s 9.9m/s 8.8m/s 6.2m/s 6.5m/s 6.3m/s 7.9m/s 10.1m/s\n风向 西南风 西南风 西南风 西南风 西南风 西南风 西南风 西南风\n气压 995.7hPa 995.4hPa 996.4hPa 996.9hPa 996.8hPa 997.1hPa 998.5hPa 999.9hPa\n湿度 75.3%77.5%77.6%85.2%93.9%97.5%82.2%43.2%\n云量 84.8%90.3%92%79.9%79.9%10.1%10.1%0.1% [...] 时间 08:00 11:00 14:00 17:00 20:00 23:00 02:00 05:00\n天气Image 56Image 57Image 58Image 59Image 60Image 61Image 62Image 63\n气温 26℃28.3℃28.5℃28.1℃26.9℃26.7℃26.7℃25.5℃\n降水 10.2mm 2.5mm 14.4mm 4.2mm 14.1mm 6.3mm 6.5mm 2.4mm\n风速 7.8m/s 10m/s 10.7m/s 9.4m/s 9.3m/s 9.4m/s 10.5m/s 10.7m/s\n风向 西南风 西南风 西南风 西南风 东南风 东南风 东南风 西南风\n气压 1001.9hPa 1000.6hPa 999.4hPa 999.4hPa 999.4hPa 998.9hPa 998.5hPa 999.2hPa\n湿度 93.7%86.4%89.3%94.2%93.6%97.8%100%99%\n云量 100%99.8%99.6%99.4%99.2%99.5%99.8%99.6% [...] 时间 08:00 11:00 14:00 17:00 20:00 23:00 02:00 05:00\n天气Image 32Image 33Image 34Image 35Image 36Image 37Image 38Image 39\n气温 27℃31.2℃34.1℃33.7℃30.1℃28.3℃26.7℃24.8℃\n降水 无降水 无降水 无降水 无降水 无降水 无降水 无降水 无降水\n风速 7.9m/s 7.9m/s 7.7m/s 7.3m/s 6.2m/s 7m/s 6.2m/s 6.8m/s\n风向 西南风 西南风 西南风 东南风 东南风 东南风 东南风 东南风\n气压 1004hPa 1004.3hPa 1002.6hPa 1001.6hPa 1002.5hPa 1003.8hPa 1003.4hPa 1003.6hPa\n湿度 75.8%43.5%51.4%43.5%63.9%66.7%84.9%84.5%\n云量 0.4%10.1%10.1%10.1%10.1%10.1%75%79.9%",
  "url": "https://weather.cma.cn/web/weather/58238.html"
}
*/

  String? type;
  String? title;
  String? content;
  String? url;
  String? image;

  SearchUrlsModel({
    this.type,
    this.title,
    this.content,
    this.url,
    this.image,
  });
  SearchUrlsModel.fromJson(Map<String, dynamic> json) {
    type = json['type']?.toString();
    title = json['title']?.toString();
    content = json['content']?.toString();
    url = json['url']?.toString();
    image = '';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['title'] = title;
    data['content'] = content;
    data['url'] = url;
    return data;
  }
}
