///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PlanningModelSteps {
/*
{
  "agent_name": "researcher",
  "title": "收集大理旅游基本信息",
  "description": "研究员将收集大理的主要景点信息（如洱海、大理古城、崇圣寺三塔等）、特色活动（如环洱海骑行、白族美食体验）、最佳旅行季节和天气情况。",
  "note": "需要包括每个景点的开放时间、门票价格和推荐游玩时长。"
}
*/

  String? agentName;
  String? title;
  String? description;
  String? note;

  PlanningModelSteps({
    this.agentName,
    this.title,
    this.description,
    this.note,
  });
  PlanningModelSteps.fromJson(Map<String, dynamic> json) {
    agentName = json['agent_name']?.toString();
    title = json['title']?.toString();
    description = json['description']?.toString();
    note = json['note']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['agent_name'] = agentName;
    data['title'] = title;
    data['description'] = description;
    data['note'] = note;
    return data;
  }
}

class PlanningModel {
/*
{
  "thought": "用户需要一个详细的大理旅游计划，包括旅行日期、景点选择、住宿安排、交通方式和预算估算。我将分解这个任务为多个步骤，分别由研究员收集信息，最后由报告员整理成专业报告。",
  "title": "大理旅游计划制定",
  "steps": [
    {
      "agent_name": "researcher",
      "title": "收集大理旅游基本信息",
      "description": "研究员将收集大理的主要景点信息（如洱海、大理古城、崇圣寺三塔等）、特色活动（如环洱海骑行、白族美食体验）、最佳旅行季节和天气情况。",
      "note": "需要包括每个景点的开放时间、门票价格和推荐游玩时长。"
    }
  ]
}
*/

  String? thought;
  String? title;
  List<PlanningModelSteps?>? steps;

  PlanningModel({
    this.thought,
    this.title,
    this.steps,
  });
  PlanningModel.fromJson(Map<String, dynamic> json) {
    thought = json['thought']?.toString();
    title = json['title']?.toString();
    if (json['steps'] != null) {
      final v = json['steps'];
      final arr0 = <PlanningModelSteps>[];
      v.forEach((v) {
        arr0.add(PlanningModelSteps.fromJson(v));
      });
      steps = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['thought'] = thought;
    data['title'] = title;
    if (steps != null) {
      final v = steps;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['steps'] = arr0;
    }
    return data;
  }
}
