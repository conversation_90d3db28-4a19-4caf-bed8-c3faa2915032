class RoleInfoBean {
  String? role_id;
  String? role_name;
  String? role_code;
  int? type;
  String? type_name;
  int? status;
  bool? is_deleted;

  RoleInfoBean(
      {this.role_id,
      this.role_name,
      this.role_code,
      this.type,
      this.type_name,
      this.status,
      this.is_deleted});

  RoleInfoBean.fromJson(Map<String, dynamic> json) {
    role_id = json['role_id'];
    role_name = json['role_name'];
    role_code = json['role_code'];
    type = json['type'];
    type_name = json['type_name'];
    status = json['status'];
    is_deleted = json['is_deleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['role_id'] = this.role_id;
    data['role_name'] = this.role_name;
    data['role_code'] = this.role_code;
    data['type'] = this.type;
    data['type_name'] = this.type_name;
    data['status'] = this.status;
    data['is_deleted'] = this.is_deleted;

    return data;
  }
}
