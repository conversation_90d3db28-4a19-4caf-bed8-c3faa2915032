///
/// <AUTHOR>
/// @Date 2025/8/14 10:04
///
/// @Description 群成员信息

class SearchUserBean {
  String? user_id;
  String? user_name;
  String? registration_credential;
  String? auth_type;
  String? last_login;
  String? avatar_url;
  late bool is_member;
  late bool un_create;

  SearchUserBean({
    this.user_id,
    this.user_name,
    this.registration_credential,
    this.auth_type,
    this.last_login,
    this.avatar_url,
    bool? is_member,
    bool? un_create,
  }) {
    this.is_member = is_member ?? false;
    this.un_create = un_create ?? false;
  }

  SearchUserBean.fromJson(Map<String, dynamic> json) {
    user_id = json['user_id'];
    user_name = json['user_name'];
    registration_credential = json['registration_credential'];
    auth_type = json['auth_type'];
    last_login = json['last_login'];
    avatar_url = json['avatar_url'];
    is_member = json['is_member'] ?? false;
    un_create = false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = user_id;
    data['user_name'] = user_name;
    data['registration_credential'] = registration_credential;
    data['auth_type'] = auth_type;
    data['last_login'] = last_login;
    data['avatar_url'] = avatar_url;
    data['is_member'] = is_member;
    return data;
  }
}
