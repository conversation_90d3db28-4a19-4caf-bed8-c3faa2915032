import 'package:get/get.dart';

///
/// <AUTHOR>
/// @Date 2025/8/14 10:04
///
/// @Description 群成员信息

class SearchUserBean {
  String? user_id;
  String? user_name;
  String? registration_credential;
  String? auth_type;
  String? last_login;
  String? avatar_url;
  RxBool is_member = false.obs;
  late bool un_create;
  RxBool isRegister = false.obs;
  RxBool isSelected = false.obs;
  String? firstLetter;

  SearchUserBean({
    this.user_id,
    this.user_name,
    this.registration_credential,
    this.auth_type,
    this.last_login,
    this.avatar_url,
    bool? is_member,
    bool? un_create,
    bool isRegister = false,
    bool isSelected = false,
    this.firstLetter,
  }) {
    this.is_member = (is_member ?? false).obs;
    this.un_create = un_create ?? false;
    this.isRegister = isRegister.obs;
    this.isSelected = isSelected.obs;
  }

  SearchUserBean.fromJson(Map<String, dynamic> json) {
    user_id = json['user_id'];
    user_name = json['user_name'];
    registration_credential = json['registration_credential'];
    auth_type = json['auth_type'];
    last_login = json['last_login'];
    avatar_url = json['avatar_url'];
    is_member = (json['is_member'] ?? false).obs;
    un_create = false;
    firstLetter = "";
    isSelected = false.obs;
    isRegister = false.obs;
  }

  SearchUserBean copyWith({
    String? user_id,
    String? user_name,
    String? registration_credential,
    String? auth_type,
    String? last_login,
    String? avatar_url,
    bool? is_member,
    bool? un_create,
    bool? isRegister,
    bool? isSelected,
    String? firstLetter,
  }) => SearchUserBean(
    user_id: user_id ?? this.user_id,
    user_name: user_name ?? this.user_name,
    registration_credential: registration_credential ?? this.registration_credential,
    auth_type: auth_type ?? this.auth_type,
    last_login: last_login ?? this.last_login,
    avatar_url: avatar_url ?? this.avatar_url,
    is_member: is_member ?? this.is_member.value,
    un_create: un_create ?? this.un_create,
    isRegister: isRegister ?? this.isRegister.value,
    isSelected: isSelected ?? this.isSelected.value,
    firstLetter: firstLetter ?? this.firstLetter,
  );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = user_id;
    data['user_name'] = user_name;
    data['registration_credential'] = registration_credential;
    data['auth_type'] = auth_type;
    data['last_login'] = last_login;
    data['avatar_url'] = avatar_url;
    data['is_member'] = is_member;
    data['un_create'] = un_create;
    data['isRegister'] = isRegister.value;
    data['isSelected'] = isSelected.value;
    data['firstLetter'] = firstLetter;
    return data;
  }
}
