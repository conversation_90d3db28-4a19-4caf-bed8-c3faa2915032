import 'package:get/get.dart';

/// <AUTHOR>
/// @Date 2025/8/19
///
/// @Description 用户信息

class UserIdentity {
  int? thirdPlatformType;
  String? thirdUserId;

  UserIdentity({this.thirdPlatformType, this.thirdUserId});

  UserIdentity.fromJson(Map<String, dynamic> json) {
    thirdPlatformType = json['third_platform_type'];
    thirdUserId = json['third_user_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['third_platform_type'] = thirdPlatformType;
    data['third_user_id'] = thirdUserId;
    return data;
  }
}

class GroupUserBean {
  String? id;
  String? userPhone;
  String? email;
  String? avatarUrl;
  String? username;
  String? roleId;
  RxBool? isSelected;
  List<UserIdentity>? userIdentities;

  GroupUserBean({
    this.id,
    this.userPhone,
    this.email,
    this.avatarUrl,
    this.username,
    this.roleId,
    this.userIdentities,
    this.isSelected,
  });

  GroupUserBean.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userPhone = json['user_phone'];
    email = json['email'];
    avatarUrl = json['avatar_url'];
    username = json['username'];
    roleId = json['role_id'];
    isSelected = false.obs;
    if (json['user_identities'] != null) {
      userIdentities = <UserIdentity>[];
      json['user_identities'].forEach((v) {
        userIdentities!.add(UserIdentity.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['user_phone'] = userPhone;
    data['email'] = email;
    data['avatar_url'] = avatarUrl;
    data['username'] = username;
    data['isSelected'] = isSelected;
    data['role_id'] = roleId;
    if (userIdentities != null) {
      data['user_identities'] =
          userIdentities!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}