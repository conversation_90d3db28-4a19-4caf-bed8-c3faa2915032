class GroupChatFile {
  final String id;
  final String title;

  final String groupId;
  final String userId;
  final String objectId;
  final int objectType;
  final String artifactUrl;
  final int artifactIndex;
  final String createdAt;
  final String updatedAt;
  final bool isDeleted;

  GroupChatFile({
    required this.id,
    required this.groupId,
    required this.userId,
    required this.objectId,
    required this.objectType,
    required this.artifactUrl,
    required this.artifactIndex,
    required this.createdAt,
    required this.updatedAt,
    required this.isDeleted,
    required this.title
  });

  factory GroupChatFile.fromJson(Map<String, dynamic> json) {
    return GroupChatFile(
      id: json['id'] as String,
      groupId: json['group_id'] as String,
      userId: json['user_id'] as String,
      objectId: json['object_id'] as String,
      objectType: json['object_type'] as int,
      artifactUrl: json['artifact_url'] as String,
      artifactIndex: json['artifact_index'] as int,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
      isDeleted: json['is_deleted'] as bool,
      title: json['title'] as String,

    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'group_id': groupId,
      'user_id': userId,
      'object_id': objectId,
      'object_type': objectType,
      'artifact_url': artifactUrl,
      'artifact_index': artifactIndex,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'is_deleted': isDeleted,
      'title': title
    };
  }
}