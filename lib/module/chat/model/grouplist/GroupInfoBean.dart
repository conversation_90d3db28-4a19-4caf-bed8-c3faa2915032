import 'package:agora_chat_sdk/agora_chat_sdk.dart';

///
/// <AUTHOR>
/// @Date 2025/8/14 9:58
///
/// @Description 群聊信息

class GroupInfoBean {
  int? orderIndex;
  String? groupId;
  String? thirdGroupId;
  String? thirdChatGroupId;
  String? thirdAudioGroupId;
  String? name;
  List<String>? avatarUrls;
  String? timestamp;
  int? cardinality;
  String? content;
  //// 1、聊天  2、人员回复 3、系统消息 4、助手回复 5、@成员 6、@助手 7、Incoming call新増类型 8、In call 新増类型 9、Missed voice call 新增类型 10、工具调用新増类型
  int? messageType;
  int? source;
  String? role;

  ChatConversation? conversation;

  GroupInfoBean(
      {this.orderIndex,
        this.groupId,
        this.thirdGroupId,
        this.thirdChatGroupId,
        this.thirdAudioGroupId,
        this.name,
        this.avatarUrls,
        this.timestamp,
        this.cardinality,
        this.content,
        this.messageType,
        this.source,
        this.role});

  GroupInfoBean.fromJson(Map<String, dynamic> json) {
    orderIndex = json['order_index'];
    groupId = json['group_id'];
    thirdGroupId = json['third_group_id'];
    thirdChatGroupId = json['third_chat_group_id'];
    thirdAudioGroupId = json['third_audio_group_id'];
    name = json['name'];
    avatarUrls = json['avatar_urls'].cast<String>();
    timestamp = json['timestamp'];
    cardinality = json['cardinality'];
    content = json['content'];
    messageType = json['message_type'];
    source = json['source'];
    role = json['role'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['order_index'] = this.orderIndex;
    data['group_id'] = this.groupId;
    data['third_group_id'] = this.thirdGroupId;
    data['third_chat_group_id'] = this.thirdChatGroupId;
    data['third_audio_group_id'] = this.thirdAudioGroupId;
    data['name'] = this.name;
    data['avatar_urls'] = this.avatarUrls;
    data['timestamp'] = this.timestamp;
    data['cardinality'] = this.cardinality;
    data['content'] = this.content;
    data['message_type'] = this.messageType;
    data['source'] = this.source;
    data['role'] = this.role;
    return data;
  }
}
