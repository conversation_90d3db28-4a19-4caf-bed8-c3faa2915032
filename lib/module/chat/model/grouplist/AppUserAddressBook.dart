
import 'dart:typed_data';

import 'package:contacts_service_plus/contacts_service_plus.dart';
import 'package:lpinyin/lpinyin.dart';

import '../../../../utils/inAppLogUtil.dart';

/// 通讯录联系人数据模型
class AppUserAddressBook {
  final String id;
  final String name;
  final String displayName;
  final List<String> phoneNumbers;
  final List<String> emails;
  final Uint8List? avatar;
  final String firstLetter;
  final String pinyin;
  final String pinyinShort;
  bool isRegistered;
  late bool is_member;

  // 匹配状态
  bool isAppMember;
  bool isSelected;
  String? appUserId;
  String? appUserEmail;
  String? appUserPhone;
  String? appUserAvatar;

  AppUserAddressBook({
    required this.id,
    required this.name,
    required this.displayName,
    required this.phoneNumbers,
    required this.emails,
    this.avatar,
    required this.firstLetter,
    required this.pinyin,
    required this.pinyinShort,
    this.isAppMember = false,
    this.isSelected = false,
    this.appUserId,
    this.appUserEmail,
    this.appUserPhone,
    this.appUserAvatar,
    this.isRegistered = false,
    this.is_member = false
  });

  /// 从系统联系人创建
  factory AppUserAddressBook.fromContact(Contact contact) {
    final name = contact.displayName ?? contact.givenName ?? contact.familyName ?? '';
    final cleanName = name.trim();

    // 获取拼音和首字母
    String pinyin = '';
    String pinyinShort = '';
    String firstLetter = '#';

    if (cleanName.isNotEmpty) {
      try {
        pinyin = PinyinHelper.getPinyinE(cleanName, separator: '');
        pinyinShort = PinyinHelper.getShortPinyin(cleanName);

        // 获取首字母
        if (pinyinShort.isNotEmpty) {
          firstLetter = pinyinShort[0].toUpperCase();
          // 确保首字母是A-Z
          if (!RegExp(r'^[A-Z]$').hasMatch(firstLetter)) {
            firstLetter = '#';
          }
        } else if (cleanName.isNotEmpty) {
          final firstChar = cleanName[0].toUpperCase();
          if (RegExp(r'^[A-Z]$').hasMatch(firstChar)) {
            firstLetter = firstChar;
          }
        }
      } catch (e) {
        logError('拼音转换失败: $e');
        // 如果拼音转换失败，尝试直接获取首字母
        if (cleanName.isNotEmpty) {
          final firstChar = cleanName[0].toUpperCase();
          if (RegExp(r'^[A-Z]$').hasMatch(firstChar)) {
            firstLetter = firstChar;
          }
        }
      }
    }

    // 获取电话号码
    final phoneNumbers = contact.phones?.map((phone)=>phone.value != null && phone.value!.isNotEmpty ?phone.value!.trim() : '').where((phone){
      return phone.isNotEmpty;
    }).toList() ?? [];

    // 获取邮箱
    final emails = contact.emails?.map((email) => email.value != null && email.value!.isNotEmpty ? email.value!.trim()  : '').where((email) => email.isNotEmpty).toList() ?? [];

    return AppUserAddressBook(
      id: contact.identifier ?? '',
      name: cleanName,
      displayName: cleanName,
      phoneNumbers: phoneNumbers,
      emails: emails,
      avatar: contact.avatar?.isNotEmpty == true ? contact.avatar : null,
      firstLetter: firstLetter,
      pinyin: pinyin,
      pinyinShort: pinyinShort,
      isRegistered: false,
      is_member: false,
      appUserAvatar: "",
      appUserEmail: emails.isNotEmpty ? emails[0].trim() : '',
      appUserPhone: phoneNumbers.isNotEmpty ? phoneNumbers[0].trim() : '',
    );
  }

  /// 复制并更新匹配状态
  AppUserAddressBook copyWith({
    bool? isAppMember,
    bool? isSelected,
    String? appUserId,
    String? appUserEmail,
    String? appUserPhone,
    String? appUserAvatar,
    bool? isRegistered,
    bool? is_member
  }) {
    return AppUserAddressBook(
      id: id,
      name: name,
      displayName: displayName,
      phoneNumbers: phoneNumbers,
      emails: emails,
      avatar: avatar,
      firstLetter: firstLetter,
      pinyin: pinyin,
      pinyinShort: pinyinShort,
      isAppMember: isAppMember ?? this.isAppMember,
      isSelected: isSelected ?? this.isSelected,
      appUserId: appUserId ?? this.appUserId,
      appUserEmail: appUserEmail ?? this.appUserEmail,
      appUserPhone: appUserPhone ?? this.appUserPhone,
      appUserAvatar: appUserAvatar ?? this.appUserAvatar,
      isRegistered: isRegistered ?? this.isRegistered,
      is_member: is_member ?? this.is_member
    );
  }
}