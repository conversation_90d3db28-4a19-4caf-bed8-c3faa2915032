class ThirdSDKModel {
  String? AZURE_SPEECH_SERVICE_KEY;
  String? AZURE_SPEECH_SERVICE_REGION;
  String? AGORA_APP_ID;
  String? AGORA_APP_CERTIFICATE;
  String? AGORA_ORG_NAME;
  String? AGORA_APP_NAME;
  String? TENCENT_APP_ID;
  String? TENCENT_SECRET_ID;
  String? TENCENT_SECRET_KEY;

  ThirdSDKModel({
    this.AZURE_SPEECH_SERVICE_KEY,
    this.AZURE_SPEECH_SERVICE_REGION,
    this.AGORA_APP_ID,
    this.AGORA_APP_CERTIFICATE,
    this.AGORA_ORG_NAME,
    this.AGORA_APP_NAME,
    this.TENCENT_APP_ID,
    this.TENCENT_SECRET_ID,
    this.TENCENT_SECRET_KEY,
  });
  ThirdSDKModel.fromJson(Map<String, dynamic> json) {
    AZURE_SPEECH_SERVICE_KEY = json['AZURE_SPEECH_SERVICE_KEY']?.toString();
    AZURE_SPEECH_SERVICE_REGION =
        json['AZURE_SPEECH_SERVICE_REGION']?.toString();
    AGORA_APP_ID = json['AGORA_APP_ID']?.toString();
    AGORA_APP_CERTIFICATE = json['AGORA_APP_CERTIFICATE']?.toString();
    AGORA_ORG_NAME = json['AGORA_ORG_NAME']?.toString();
    AGORA_APP_NAME = json['AGORA_APP_NAME']?.toString();
    TENCENT_APP_ID = json['TENCENT_APP_ID']?.toString();
    TENCENT_SECRET_ID = json['TENCENT_SECRET_ID']?.toString();
    TENCENT_SECRET_KEY = json['TENCENT_SECRET_KEY']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['AZURE_SPEECH_SERVICE_KEY'] = AZURE_SPEECH_SERVICE_KEY;
    data['AZURE_SPEECH_SERVICE_REGION'] = AZURE_SPEECH_SERVICE_REGION;
    data['AGORA_APP_ID'] = AGORA_APP_ID;
    data['AGORA_APP_CERTIFICATE'] = AGORA_APP_CERTIFICATE;
    data['AGORA_ORG_NAME'] = AGORA_ORG_NAME;
    data['AGORA_APP_NAME'] = AGORA_APP_NAME;
    data['TENCENT_APP_ID'] = TENCENT_APP_ID;
    data['TENCENT_SECRET_ID'] = TENCENT_SECRET_ID;
    data['TENCENT_SECRET_KEY'] = TENCENT_SECRET_KEY;

    return data;
  }
}
