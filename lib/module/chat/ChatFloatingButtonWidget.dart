import 'dart:async';
import 'dart:isolate';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../widget/TapHigh.dart';

final String _kOverlayNameHome = 'overlay_port_chat';

class ChatFloatingButtonWidget extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _ChatFloatingButtonWidgetState();
}

class _ChatFloatingButtonWidgetState extends State<ChatFloatingButtonWidget> {
  String displayText = '搜索中...';
  final _receivePort = ReceivePort();
  bool flag = false;
  bool isShowCeiling = false;
  StreamSubscription? streamSubscription;
  Timer? shrinkTimer;

  @override
  void initState() {
    print("ChatFloatingButtonWidget init");
    super.initState();
    streamSubscription = FlutterOverlayWindow.overlayListener.listen((message) {
      print("receiver：${message.toString()}");
      if (message["type"] == "init") {
        setState(() {
          displayText = "搜索中...";
          isShowCeiling = false;
          initTimer();
          startTimer();
        });
      } else if (message["type"] == "message") {
        setState(() {
          displayText = message["content"];
        });
      } else if (message["type"] == "moveFinish") {
        if (message["x"] == 0) {
          //吸附了
          startTimer();
        }
      }
      if (message["type"] == "moveStart") {
        initTimer();
      }
    });
  }

  void startTimer() {
    if (shrinkTimer == null) {
      shrinkTimer = Timer(Duration(seconds: 3), () async {
        await FlutterOverlayWindow.resizeOverlay(32, 66, true);
        setState(() {
          isShowCeiling = true;
          initTimer();
        });
      });
    }
  }

  void initTimer() {
    if (shrinkTimer != null) {
      shrinkTimer!.cancel();
      shrinkTimer = null;
    }
  }

  void initServer() {
    IsolateNameServer.removePortNameMapping(_kOverlayNameHome);
    final res = IsolateNameServer.registerPortWithName(
      _receivePort.sendPort,
      _kOverlayNameHome,
    );
    print("$res: OVERLAY");
    _receivePort.listen((message) {
      print("message from OVERLAY: $message");
      setState(() {
        displayText = message;
      });
    });
  }

  @override
  void dispose() {
    streamSubscription?.cancel();
    isShowCeiling = false;
    if (shrinkTimer != null) {
      shrinkTimer!.cancel();
      shrinkTimer = null;
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return flag
        ? GestureDetector(
            onTap: () async {
              FlutterOverlayWindow.moveToFront();
            },
            child: AnimatedOpacity(
              opacity: 1.0, // 淡入淡出动画
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: AnimatedScale(
                  scale: 1.0, // 缩放动画
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  child: !isShowCeiling
                      ? Container(
                          width: 70,
                          height: 70,
                          decoration: BoxDecoration(
                            color: Color(0xFFFFFFE3),
                            borderRadius: BorderRadius.circular(14),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                "assets/icon/phone_call.png",
                                width: 24.w,
                                height: 24.w,
                                fit: BoxFit.cover,
                              ),
                              SizedBox(
                                height: 5.w,
                              ),
                              Text(
                                displayText,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        )
                      : TapHigh(
                          onTap: () async {
                            await FlutterOverlayWindow.resizeOverlay(
                                70, 70, true);
                            setState(() {
                              isShowCeiling = false;
                            });
                            startTimer();
                          },
                          child: Container(
                              decoration: BoxDecoration(
                                  color: Color(0x78733D7D),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(10.r),
                                    bottomLeft: Radius.circular(10.r),
                                  )),
                              child: Container(
                                  padding:
                                      EdgeInsets.only(left: 5.w, right: 5.w),
                                  child: Image.asset(
                                    'assets/icon/icon_arrow_white.png',
                                    width: 5.w,
                                    height: 10.w,
                                  ))))),
            ),
          )
        : Container(
            width: 1,
            height: 1,
          );
  }
}
