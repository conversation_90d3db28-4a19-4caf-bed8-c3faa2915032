import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/stream/render/RenderManagerController.dart';
import 'package:new_agnes/api/stream/stream_ai_enum.dart';
import 'package:new_agnes/api/stream/stream_message_type_enum.dart';
import 'package:new_agnes/api/stream/stream_request.dart';
import 'package:new_agnes/api/stream/stream_result_entity.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/message_input_widget.dart';

import '../../../api/Api.dart';
import '../../../api/ApiProvider.dart';
import '../../../utils/cmUtils.dart';
import '../../api/stream/render/JsonChunkFinder.dart';
import '../../api/stream/render/entity.dart';
import '../../generated/l10n.dart';
import '../home/<USER>/input_widgets/home_task_input.dart';
import '../home/<USER>/logic.dart';
import 'model/planning_model.dart';

class ChatLogic extends GetxController {
  final renderManager = RenderManagerController();
  var conversationId = '';
  var favoured = false.obs;
  var title = "".obs;
  String model = '0';
  Rx<bool> isTop = true.obs;
  Rx<bool> isLoadingAnimation = false.obs;
  Rx<bool> isHistory = true.obs; // 默认是历史进入，不触发页面自动到底的滚动
  Rx<bool> isAutoScrolling = true.obs;
  Rx<bool> isScrolling = false.obs;
  Rx<bool> isAtTop = false.obs;
  Rx<bool> isAtBottom = false.obs;
  bool isEnd = false;
  var historySelectedId = "";
  int newMessageIndex = 0;
  StreamRequest? streamRequest;
  final uploadFiles = <MessageUploadModel>[].obs;
  final Map<String,StreamStatus> streamStatus = {};
  bool isShowError=true;//是否显示错误布局 默认是显示的，但是当在chat页面切换历史的时候会，断掉流，这个时候不显示错误布局
  bool isSwitch=false;


  //创建话题获取id
  Future<void> setConversations(title,List<MessageUploadModel> uploadFiles, Function(String result) callback) async {
    late Response res;
    res = await Get.find<ApiProvider>().post(
      Api.conversations,
      {'title': title, 'folder_id': ''},
    );
    isShowError=true;
    if (CmUtils.isSuccess(res.statusCode!)) {
      conversationId = '${res.body['id']}';
      print('AAA新数据ID=${conversationId}');
      favoured.value = res.body['favoured'];
      renderManager.conversationId = conversationId;
      callback(conversationId);
      sendStreamChat(title,uploadFiles: uploadFiles);
    } else {
      renderManager.dispatchRender(StreamResultEntity(
          event: "error", data: jsonEncode({"message": res.statusText})));
    }
  }

  /**
   * 发送会话
   */
  void sendStreamChat(
    String content, {
    type = 'text',
    fromButton = false,
    List<MessageUploadModel> uploadFiles = const [],
  }) {
    isShowError=true;
    startLoading();
    getDocIdAndMaskImages(uploadFiles, (docIds,maskImages){
      streamRequest?.cancelRequest();
      streamRequest = StreamRequest(url: Api.chatStream, id: conversationId,status: streamStatus, body: {
        "messages": [
          {"id": "", "role": "user", "type": type, "content": "${content}"}
        ],
        "docs": docIds,
        "conversation_id": "${conversationId}",
        "mode": model,
        "is_doc_changed": false,
        "is_cooperation": false,
        "cooperation_comment_ids": [],
        "debug": false,
        "from_button": fromButton,
        "mask_images": maskImages
      })
        ..sendRequest(
            onSuccess: (StreamResultEntity resultData) {
              //解析数据
              renderManager.dispatchRender(resultData);
            },
            onDone: () {
              debugPrint("onDone: $conversationId");
              stopLoading();
            },
            onError: (String error) {
              if(isShowError) {
                handleErrorConnection(error);
              }
              debugPrint("onError: $error");
            },
            model: model);
    });
  }

  void getDocIdAndMaskImages(List<MessageUploadModel> uploadFiles,Function(List<String> docIds,Map<String,dynamic> maskImages) callBack) async{
    if(uploadFiles.isEmpty){
      callBack([],{});
      return;
    }
    Map<String,dynamic> maskImages = {};
    List<Map> params = uploadFiles.map((e){
      if(e.export){
        maskImages[e.file_url??""] = "";
        return null;
      }
      return e.toRequestDocIdJson();
    }).whereType<Map>().toList();
    Response response = await Get.find<ApiProvider>()
        .post(Api.uploadRag, {"files": params});
    if(response.statusCode == 200){
      List result = jsonDecode(response.bodyString??"[]");
      List<String> docIds =
          result.map((e) => e["doc_id"]).whereType<String>().toList();
      callBack(docIds,maskImages);
    }else {
      handleErrorConnection(response.statusText ??
          S.of(Get.context!).networkConnectionFailedPleaseTryAgain);
    }
  }

  //获取历史消息
  Future<void> historyConversation(String id, {onResult,bool isLoading = true}) async {
    isAutoScrolling.value=false;
    renderManager.workflowRx.value.renderList.clear();
    conversationId = id;
    print('AAA历史数据ID=${conversationId}');
    renderManager.conversationId = id;
    if(isLoading){
      startLoading();
    }
    isHistory.value = true; // 确认是历史，关闭页面自动到底的滚动
    Get.find<RolesLogic>().chatInputLogic.conversationId=id;
    Response res =
        await Get.find<ApiProvider>().get(Api.conversations + "/$id/dialogue");
    isAutoScrolling.value=true;
    if (res.statusCode == 200) {
      isShowError=true;
      // var data = jsonDecode(res.bodyString?? "");
      // 自定义一个历史的StreamResultEntity对象
      bool isEnd = true;
      if (res.body is Map) {
        isEnd = res.body["is_end"] ?? true;
      }
      this.isEnd = isEnd;
      if (isEnd) {
        var entity = StreamResultEntity(
            event: "sse_history",
            data: jsonEncode(res.body),
            conversationId: id);
        renderManager.dispatchRender(entity);
        if (onResult != null) {
          onResult();
        }
        // 解析历史数据
        stopLoading();
      } else {
        streamRequest?.cancelRequest();
        startLoading();
        //重新发起请求
        Map body = res.body;
        List message = body["messages"] ?? [];
        String curModel = model;
        for (int i = message.length - 1; i >= 0; i--) {
          String? appendix = message[i]["appendix"]?.toString();
          if (appendix != null) {
            dynamic json = jsonDecode(appendix);
            if(json is Map && json.containsKey("mode")){
              curModel = jsonDecode(appendix)["mode"]?.toString() ?? "";
              break;
            }
          }
        }
        if (curModel == "2") {
          message.insert(0, {
            "id": "0",
            "message": "",
            "sender": "llm",
            "type": "message_thinking",
            "idx": 0,
            "docs": [],
            "appendix": null,
            "refId": null,
          });
        }
        body["messages"] = message;
        var entity = StreamResultEntity(
            event: "sse_history", data: jsonEncode(body), conversationId: id);
        renderManager.dispatchRender(entity);
        streamRequest?.cancelRequest();
        streamRequest = StreamRequest(url: Api.chatResumeStream + "/$id",id: id,status: streamStatus)..sendRequest(
            onSuccess: (StreamResultEntity resultData) {
              //解析数据
              renderManager.dispatchRender(resultData);
            },
            onDone: () {
              isHistory.value=false;
              stopLoading();
            },
            onError: (String error) {
              if(isShowError) {
                handleErrorConnection(error);
              }
            },
            model: curModel);
        if (onResult != null) {
          onResult();
        }
      }
    }
  }

  //发送信息
  void goTask(String content, {List<MessageUploadModel>? uploadFiles}) {
    isShowError=true;
    if (renderManager!.workflowRx.value.renderList.length>1) {
      RenderTask lastTask = renderManager!.workflowRx.value
          .renderList[renderManager!.workflowRx.value.renderList.length - 1];
      if (lastTask.type == StreamMessageTypeEnum.message_go_button) {
        renderManager!.workflowRx.value.renderList
            .removeAt(renderManager!.workflowRx.value.renderList.length - 1);
      }
    }
    isHistory.value= false; // 确认是SSE之后，开启页面自动到底的滚动
    newMessageIndex = renderManager!.workflowRx.value.renderList.length + 1;
    // 初始化事件处理器,并添加到GetX框架
    RenderTask renderTask = new RenderTask();
    renderTask.sender.value = 'user';
    if (content == 'execute now') {
      renderTask.type.value = StreamMessageTypeEnum.message_execute_now;
    } else {
      renderTask.type.value = StreamMessageTypeEnum.message_text;
    }
    renderTask.content = content.obs;
    renderTask.conversationId = conversationId;
    renderTask.docs = uploadFiles?.map((e) => e.toDocJson()).toList() ?? [];
    renderManager!.workflowRx.value.renderList.add(renderTask);
  }

  void addLoadingPlanner() {
    RenderTask renderTask = new RenderTask();
    renderTask.sender.value = 'llm';
    renderTask.type.value = StreamMessageTypeEnum.message_thinking;
    renderTask.content.value = 'deepResearch for 0 seconds';
    renderTask.conversationId = conversationId;
    renderManager!.workflowRx.value.renderList.add(renderTask);

    // getPlaningData();
  }

  //1:收藏 2：编辑
  Future<void> collectionOrEdit(int type, {editText}) async {
    bool favouredParm = type == 1 ? !favoured.value : favoured.value;
    String titleParm = title.value;
    Response res = await Get.find<ApiProvider>().patch(
        Api.conversations + "/" + conversationId,
        {"favour": favouredParm, "title": type == 1 ? titleParm : editText});
    if (res.statusCode == 200) {
      if (res.bodyString == null) {
        return;
      }
      favoured.value = favouredParm;
      if (type == 2) {
        title.value = editText;
      }else {
        showSuccessToast(S.of(Get.context!).operationSuccessful);
      }
    }
  }

  void handleErrorConnection(String error) {
    renderManager.dispatchRender(StreamResultEntity(
        event: "error", data: jsonEncode({"message": error})));
    stopLoading();
  }

  void startLoading(){
    if (!isLoadingAnimation()) {
      isLoadingAnimation.value = true;
    }
    bool isRegistered = Get.isRegistered<HomeTaskInputLogic>();
    if(isRegistered){
      HomeTaskInputLogic homeTaskInputLogic = Get.find<HomeTaskInputLogic>();
      if(!homeTaskInputLogic.chatting()){
        homeTaskInputLogic.chatting.value = true;
      }
    }
  }

  void stopLoading(){
    if (isLoadingAnimation()) {
      isLoadingAnimation.value = false;
    }
    bool isRegistered = Get.isRegistered<HomeTaskInputLogic>();
    if(isRegistered){
      HomeTaskInputLogic homeTaskInputLogic = Get.find<HomeTaskInputLogic>();
      if(homeTaskInputLogic.chatting()){
        homeTaskInputLogic.chatting.value = false;
      }
    }
  }
}
