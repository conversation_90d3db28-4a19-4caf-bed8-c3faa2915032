import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../api/Api.dart';
import '../../utils/file_utils.dart';

class DownloadLogic extends GetxController {
  RxDouble progress = 0.0.obs;
  FileUtils fileUtils= new FileUtils();
  void downFile({
    required id,
    required fileType,
    required content,
    required fileName,
    required type, //0 exportFile接口  1 exportFilePPT 接口
    String pathUrl='',
    onReuslt,
  }) {
    DateTime dateTime = new DateTime.now();
    String time =
        '${dateTime.year}${dateTime.month}${dateTime.day}${dateTime.hour}${dateTime.minute}${dateTime.second}';
    String postUrl = Api.exportFile;
    var query = {
      'content': '${content}',
      'conversation_id': '${id}',
      'format': '${fileType}',
    };
    if (type == 1) {
      postUrl = Api.exportFilePPT;
      query = {
        'conversation_id': '${id}',
        'format': '${fileType}',
      };
    }

    fileUtils.downloadFile(
        url: pathUrl.isNotEmpty?pathUrl:postUrl,
        fileName: '${fileName}_${time}.${fileType}',
        query: query,
        onProgress: (double pro) {
          progress.value = pro;
          debugPrint('下载进度=${pro}');
        },
        onSavePath: (savePath) {
          if (onReuslt != null) {
            onReuslt(savePath);
          }
        });
  }
}
