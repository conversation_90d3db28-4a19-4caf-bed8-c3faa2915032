import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../widget/HuiTan.dart';
import '../logic.dart';

class ScrollTextLay extends StatefulWidget {
  double maxHeight;
  String content;
  int index;
  Function onBottomResult;

  ScrollTextLay(this.content, this.onBottomResult, this.index,
      {this.maxHeight = 40});

  @override
  State<ScrollTextLay> createState() => _ScrollTextLayState();
}

class _ScrollTextLayState extends State<ScrollTextLay> {
  int textLines = 0;
  ChatLogic chatLogic = Get.find<ChatLogic>();
  ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance!.addPostFrameCallback((_) {
      ever(
          chatLogic.renderManager.workflowRx.value.renderList
              .value[widget.index].content, (data) {
        if (scrollController.hasClients) {
          Future.delayed(Duration(seconds: 1), () {
            if (scrollController.hasClients) {
              if (scrollController != null &&
                  scrollController.position != null) {
                scrollController.animateTo(
                  scrollController.position.maxScrollExtent,
                  duration: Duration(milliseconds: 2000),
                  curve: Curves.easeOut,
                );
              }
            } else {
              return;
            }
          });
        }
      });
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 5.w,
      ),
      child: ShaderMask(
        shaderCallback: (bounds) {
          return LinearGradient(
            colors: [
              Color(0xFF0D0D0D).withValues(alpha: 0.1),
              Color(0xFF0D0D0D),
              Color(0xFF0D0D0D).withValues(alpha: 0.2),
              // Color(0xFF0D0D0D).withValues(alpha: 0.2),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ).createShader(Rect.fromLTWH(0, 0, bounds.width, bounds.height));
        },
        blendMode: BlendMode.srcIn,
        child: Container(
          padding: EdgeInsets.only(bottom: 10.w),
          decoration: new BoxDecoration(
              color: Colors.transparent,
              // border: Border(
              //   bottom: BorderSide(
              //     color: Color(0xFFEFB5E8),
              //     width: 0,
              //   ),
              // ),
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Color(0xFF9BFBC6),
                  Color(0xFF87C0F5),
                  Color(0xFFC8BFFF),
                  Color(0xFFEFB5E8)
                ],
              )),
          constraints: BoxConstraints(
            maxHeight: 50.w, // 最多展示 4 行高度
          ),
          child: Obx(() {
            return SingleChildScrollView(
              controller: scrollController,
              child: Text(
                chatLogic.renderManager.workflowRx.value.renderList
                    .value[widget.index].content.value,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.white,
                ),
              ),
            );
          }),
        ),
      ),
    );
  }
}

/// 获取文字信息
TextPainter getTextInfo(
  String? text,
  TextStyle style,
) {
  TextSpan span = TextSpan(text: text, style: style);
  return TextPainter(text: span, textDirection: TextDirection.ltr)
    ..layout(maxWidth: 1.sw - 200.w);
}
