import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:markdown_widget/markdown_widget.dart';
import 'package:new_agnes/data/caseAndQuestion.dart';
import 'package:new_agnes/module/chat/widget/chat_ai_design_outline_widget.dart';
import 'package:new_agnes/module/chat/widget/chat_plan_image_widget.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import 'package:new_agnes/widget/TapHigh.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/gestures.dart';
import 'package:markdown/markdown.dart' as m;

import '../../../api/stream/stream_message_type_enum.dart';
import '../../../generated/l10n.dart';
import '../../../widget/GradientBorderContainer.dart';
import 'chat_bottom_tip_widget.dart';
import 'deep_research_lay.dart';
import 'deep_research_share.dart';
import 'ppt_lay.dart';

class SearchChatItemWidget extends StatefulWidget {
  final String content;
  final bool isComplete; //是否结束,结束需要展示点赞，踩，复制，分享
  final double fontSize;
  final double verticalSize;
  final int showType; //自定义markDown的布局  1 ppt  2 deepResearch的 toolCallResult布局
  final String handOff;
  final String agentName;
  final bool useAnimation; // 新增参数，控制是否使用动画
  final bool isHis; // 是否是历史数据
  int locationIndex = 0;
  int allIndex = 0;
  final ScrollController? outerController;
  final String appendixMode;

  SearchChatItemWidget({
    super.key,
    required this.content,
    this.isComplete = false,
    this.fontSize = 16,
    this.verticalSize = 8,
    this.handOff = '',
    this.agentName = '',
    this.showType = 1,
    this.useAnimation = false, // 默认不使用动画
    this.locationIndex = 0, // 下表位置
    this.allIndex = 0, // 总长度
    this.isHis = false, //
    this.outerController,
    this.appendixMode = '',
  });

  @override
  State<SearchChatItemWidget> createState() => _SearchChatItemWidgetState();
}

class _SearchChatItemWidgetState extends State<SearchChatItemWidget>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  late Animation<int> _textLengthAnimation;
  String _displayedContent = '';
  final FeedbackLogic feedbackLogic = FeedbackLogic();

  @override
  void initState() {
    super.initState();
    if (widget.useAnimation) {
      if (widget.locationIndex == widget.allIndex - 1) {
        _controller = AnimationController(vsync: this);
        _updateAnimation();
      } else {
        _displayedContent = widget.content;
        if (_controller != null) {
          _controller!.dispose();
        }
      }
    } else {
      _displayedContent = widget.content;
    }
  }

  @override
  void didUpdateWidget(covariant SearchChatItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.content != widget.content && widget.useAnimation) {
      if (widget.locationIndex == widget.allIndex - 1) {
        _updateAnimation();
      } else {
        _displayedContent = widget.content;
        if (_controller != null) {
          _controller?.dispose();
          _controller = null;
        }
      }
    } else if (!widget.useAnimation) {
      _displayedContent = widget.content;
    }
  }

  void _updateAnimation() {
    // 计算新增字符数
    int startLength = _displayedContent.length;
    int endLength = widget.content.length;

    // 如果没有新增内容，直接显示
    if (startLength >= endLength) {
      _displayedContent = widget.content;
      return;
    }

    _controller!.duration = Duration(milliseconds: 996);
    _textLengthAnimation = IntTween(
      begin: startLength,
      end: endLength,
    ).animate(
        CurvedAnimation(parent: _controller!, curve: Curves.linearToEaseOut))
      ..addListener(() {
        setState(() {
          _displayedContent =
              widget.content.substring(0, _textLengthAnimation.value);
        });
      });

    _controller!.forward(from: 0);
  }

  @override
  void dispose() {
    if (widget.useAnimation) {
      if (_controller != null) {
        _controller!.dispose();
      }
    }
    super.dispose();
  }

  MarkdownConfig getMarkConfig(bool handler) {
    return MarkdownConfig(configs: [
      HrConfig.darkConfig,
      H1Config.darkConfig,
      H2Config.darkConfig,
      H3Config.darkConfig,
      H4Config.darkConfig,
      H5Config.darkConfig,
      H6Config.darkConfig,
      PreConfig.darkConfig,
      PreConfig(
        builder: handler ? customCodeBuilder : null,
        copyCallback: (content) async{
          await Clipboard.setData(ClipboardData(text: content));
          showSuccessToast(S.of(context).copied);
        }
      ),
      PConfig(
          textStyle: TextStyle(
              fontSize: widget.fontSize.sp,
              color: Colors.white,
              height: 1.4,
              backgroundColor: Colors.transparent)),
      CodeConfig(
          style: TextStyle(
              fontSize: widget.fontSize.sp,
              color: Colors.white,
              height: 1.4,
              backgroundColor: Colors.transparent)),
      BlockquoteConfig(
          textColor: const Color(0xffd0d7de),
          sideColor: Colors.transparent,
          sideWith: 0,
          padding: EdgeInsets.zero,
          margin: EdgeInsets.zero),
      TableConfig(
        // 保持默认列宽为自适应，但允许表格根据内容扩展
        defaultColumnWidth: LimitedColumnWidth(),
        // 使用内在宽度，根据内容自适应
        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
        headerStyle: TextStyle(
          fontSize: 16.sp,
          color: Color(0xFFFF3BDF),
          fontWeight: FontWeight.w500,
        ),
        bodyStyle: TextStyle(
            fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.w400),
        headPadding: EdgeInsets.symmetric(vertical: 16),
        headerRowDecoration: BoxDecoration(
            color: Color(0x8F604276),
            border: Border.all(color: Colors.transparent),
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10), topRight: Radius.circular(10))),
        border: TableBorder(
          borderRadius: BorderRadius.circular(10),
          horizontalInside: BorderSide(color: Color(0xFF988B9A)),
          verticalInside: BorderSide(color: Color(0xFF988B9A)),
        ),
        // 优化wrapper，确保表格可以水平滚动
        wrapper: (child) {
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.w),
            ),
            child: GradientBorderContainer.single(
              strokeWidth: 1,
              gradient: LinearGradient(
                colors: [
                  Color(0xFF7253FA),
                  Color(0xFF00FFFF),
                  Color(0xFFFF3BDF),
                ],
                stops: const [0.0, 0.32, 1.0],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
                tileMode: TileMode.decal,
              ),
              borderRadius: BorderRadius.circular(10.w),
              child: ClipRRect(
                // 优化表格左上角右上角超出部分
                borderRadius: BorderRadius.circular(10.w),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: IntrinsicWidth(
                    child: child,
                  ),
                ),
              ),
            ),
          );
        },
      ),
      ListConfig(
          marginLeft: 16.w,
          marginBottom: 4.h,
          marker: (a, b, c) {
            return Padding(
              padding: EdgeInsets.only(top: 10.h),
              child: Container(
                alignment: Alignment.center,
                width: 4.w,
                height: 4.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
              ),
            );
          })
    ]);
  }

  CodeBuilder get customCodeBuilder => (String? code, String? language) {
        if (language == 'json' && code != null) {
          if (widget.appendixMode == SearchType.aiDesign.param) {
            return ChatAiDesignOutlineWidget(
              content: code,
              outerController: widget.outerController,
            );
          }
          if (code is Map) {
            //历史记录点击进来的
            return _lay(code);
          }
          try {
            final jsonData = json.decode(code); //会话详情
            return _lay(jsonData);
          } catch (e) {
            return SizedBox();
          }
        } else if (code != null && code.contains("```json")) {
          return Container();
        } else {
          return MarkdownBlock(
            data: "```$language\n${code ?? ""}\n```",
            config: getMarkConfig(false),
          );
        }
      };

  //自定义markDown布局显示的地方
  Widget _lay(jsonData) {
    Widget lay = Container();
    switch (widget.showType!) {
      case 1: //ppt
        lay = JsonPPtLay(context, jsonData,
            outerScrollController: widget.outerController);
        break;
      case 2: //deepResearch的 toolCallResult布局
        lay = RecommendLay(jsonData);
        break;
      default:
        break;
    }
    return lay;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 10.w,
        ),
        MarkdownWidget(
          data: widget.useAnimation ? _displayedContent : widget.content,
          shrinkWrap: true,
          config: getMarkConfig(true),
          padding: EdgeInsets.zero,
        ),
        // widget.isHis &&
        //         widget.handOff == 'handoff_to_deep_research()' &&
        //         widget.agentName == 'reporter'
        //     ? ChatPlanImageWidget(
        //         shareContent: widget.content,
        //       )
        //     : widget.isComplete &&
        //             widget.handOff == 'handoff_to_deep_research()' &&
        //             widget.agentName == 'reporter'
        //         ? ChatPlanImageWidget(
        //             shareContent: widget.content,
        //           )
        //         : SizedBox(),
        widget.isComplete
            ? ChatBottomTipWidget(
                isLike: true,
                isStepOn: false,
                copyContent: widget.content,
                agentName: widget.agentName,
                feedbackLogic: feedbackLogic,
              )
            : SizedBox(),
      ],
    );
  }
}

/// 限制列宽
class LimitedColumnWidth extends TableColumnWidth {
  final double minWidth;
  final double maxWidth;

  const LimitedColumnWidth({this.minWidth = 80, this.maxWidth = 200});

  @override
  double minIntrinsicWidth(Iterable<RenderBox> cells, double containerWidth) {
    // 最小宽度
    double intrinsic = 0;
    for (final cell in cells) {
      intrinsic = intrinsic > cell.getMinIntrinsicWidth(double.infinity)
          ? intrinsic
          : cell.getMinIntrinsicWidth(double.infinity);
    }
    return intrinsic.clamp(minWidth, maxWidth);
  }

  @override
  double maxIntrinsicWidth(Iterable<RenderBox> cells, double containerWidth) {
    // 最大宽度
    double intrinsic = 0;
    for (final cell in cells) {
      intrinsic = intrinsic > cell.getMaxIntrinsicWidth(double.infinity)
          ? intrinsic
          : cell.getMaxIntrinsicWidth(double.infinity);
    }
    return intrinsic.clamp(minWidth, maxWidth);
  }

  @override
  double? flex(Iterable<RenderBox> cells) => null;
}

