import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'chat_bottom_tip_widget.dart';

class ChatUsingToolTipWidget extends StatelessWidget {
  final String title;
  final bool isComplete;
  const ChatUsingToolTipWidget({super.key, required this.title,required this.isComplete});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 38.h,
          margin: EdgeInsets.only(top: 16.h),
          child: CustomPaint(
            painter: GradientBorderPainter(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xBAFFFFFF), // #FFFFFFBA (白色带透明度)
                  Color(0xA1988B9A),   // #988B9AA1 (灰色带透明度)
                ],
              ),
              strokeWidth: 1,
              radius: 30,
            ),
            child: Container(
              height: 38.h,
              decoration: BoxDecoration(
                color: const Color(0xFFFF3BDF).withValues(alpha: 0.42),
                borderRadius: BorderRadius.circular(30),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  const Text(
                    "Using Tool | ",
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.white
                    ),
                  ),
                  Expanded(
                    child: Text(
                      title,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        color: Color(0xFFC9A8C9),
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
        if(isComplete)
          ChatBottomTipWidget(
            isLike: true,
            isStepOn: false,
            feedbackLogic: FeedbackLogic(),
          ),
      ],
    );
  }
}

class GradientBorderPainter extends CustomPainter {
  final Gradient gradient;
  final double strokeWidth;
  final double radius;

  GradientBorderPainter({
    required this.gradient,
    required this.strokeWidth,
    required this.radius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 调整绘制区域，避免边框被裁掉
    final rect = Rect.fromLTWH(
      strokeWidth / 2, 
      strokeWidth / 2, 
      size.width - strokeWidth, 
      size.height - strokeWidth
    );
    
    final paint = Paint()
      ..shader = gradient.createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(radius));
    canvas.drawRRect(rrect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}


