import 'dart:io';

import 'package:android_path_provider/android_path_provider.dart';
import 'package:better_player_plus/better_player_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:new_agnes/api/stream/render/entity.dart';
import 'package:new_agnes/module/chat/widget/rotating_loading_image.dart';
import 'package:new_agnes/utils/image_upload_util.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/message_input_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:new_agnes/utils/video_thumbnail_generator.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:new_agnes/widget/image_viewer_widget.dart';

import '../../../dialog/message_auto_dialog.dart';
import '../../../generated/l10n.dart';
import '../../../widget/GradientBorderContainer.dart';
import 'chat_bottom_tip_widget.dart';
import 'ppt_lay.dart';

/// 媒体下载功能 Mixin
mixin MediaDownloadMixin {
  /// 通用的媒体下载方法
  Future<void> downloadMedia({
    required BuildContext context,
    required ProductImageModel item,
    required bool isVideo,
  }) async {
    if (item.url == null || item.url!.isEmpty) {
      showFailToast(S.of(context).networkConnectionFailedPleaseTryAgain);
      return;
    }

    // 检查存储权限
    if (!await _checkStoragePermission(context)) {
      return;
    }

    try {
      EasyLoading.show(status: S.of(context).saving);
      
      // 生成唯一文件名
      final String fileName = _generateUniqueFileName(item.title, isVideo);
      
      if (isVideo) {
        await _downloadVideoFile(item.url!, fileName);
      } else {
        await _downloadImageFile(item.url!, fileName);
      }
      
      EasyLoading.dismiss();
      showSuccessToast(S.of(context).saved);
      
    } catch (e) {
      EasyLoading.dismiss();
      print('下载失败: $e');
      showFailToast(S.of(context).networkConnectionFailedPleaseTryAgain);
    }
  }

  /// 检查存储权限
  Future<bool> _checkStoragePermission(BuildContext context) async {
    try {
      // Android 13+ 需要特定的媒体权限
      Permission permission;
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        if (androidInfo.version.sdkInt >= 33) {
          // Android 13+ 使用细分权限
          permission = Permission.photos;
        } else {
          // Android 10 及以下使用传统存储权限
          permission = Permission.storage;
        }
      } else {
        // iOS 使用照片权限
        permission = Permission.photos;
      }

      final status = await permission.status;
      
      if (status.isGranted || status.isLimited) {
        return true;
      }
      
      if (status.isDenied) {
        final result = await permission.request();
        if(result.isDenied){
          await _showPermissionDialog(context);
          // showFailToast("请先开启允许访问存储文件的权限");
          return false;
        }
        return result.isGranted;
      }
      
      if (status.isPermanentlyDenied) {
        await _showPermissionDialog(context);
        return false;
      }
      
      return false;
    } catch (e) {
      print('权限检查失败: $e');
      showFailToast(S.of(context).networkConnectionFailedPleaseTryAgain);
      return false;
    }
  }

  /// 显示权限设置对话框
  Future<void> _showPermissionDialog(BuildContext context) async {
    return showDialog(
        context: Get.context!,
        barrierDismissible:true,
        builder: (context) {
          return MessageAutoDialog(
              title: S.of(context).album,
              isTitleCenter: true,
              data: S.of(context).weNeedAccessToSaveDownloadedImagesOrVideosToYourPhotoLibrary,
              onLeftName: S.of(context).cancel,
              onRightName: S.of(context).setNow,
              height: 350.w,
                  (){
                    Navigator.of(context).pop();
              }, (){
            openAppSettings();
            Navigator.of(context).pop();
          });
        });
  }

  /// 下载图片文件
  Future<void> _downloadImageFile(String url, String fileName) async {
    final dio = Dio();
    final response = await dio.get(
      url,
      options: Options(responseType: ResponseType.bytes),
      onReceiveProgress: (received, total) {
        if (total > 0) {
          final progress = (received / total * 100).toInt();
          EasyLoading.showProgress(progress / 100.0, status: "${S.of(Get.context!).saving} $progress%");
        }
      },
    );

    if (response.statusCode == 200 && response.data != null) {
      final result = await ImageGallerySaverPlus.saveImage(
        response.data,
        quality: 100,
        name: fileName,
      );
      
      if (result == null || result["isSuccess"] != true) {
        throw Exception("保存图片失败");
      }
    } else {
      throw Exception("下载图片失败，状态码: ${response.statusCode}");
    }
  }

  /// 下载视频文件
  Future<void> _downloadVideoFile(String url, String fileName) async {
    final dio = Dio();
    
    // 获取Movies目录路径
    final appDocDir = await AndroidPathProvider.moviesPath;
    final savePath = "$appDocDir/$fileName.mp4";
    
    try {
      // 下载到临时位置
      await dio.download(
        url,
        savePath,
        onReceiveProgress: (received, total) {
          if (total > 0) {
            final progress = (received / total * 100).toInt();
            EasyLoading.showProgress(progress / 100.0, status: "${S.of(Get.context!).saving} $progress%");
          }
        },
      );

      // 保存到相册
      final result = await ImageGallerySaverPlus.saveFile(savePath);
      
      if (result == null || result["isSuccess"] != true) {
        throw Exception("保存视频失败");
      }
      
    } finally {
      // 清理临时文件
      try {
        final file = File(savePath);
        if (await file.exists()) {
          await file.delete();
        }
      } catch (e) {
        print('清理临时文件失败: $e');
      }
    }
  }

  /// 生成唯一文件名
  String _generateUniqueFileName(String? title, bool isVideo) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = isVideo ? 'mp4' : 'jpg';
    
    if (title != null && title.isNotEmpty) {
      // 清理文件名中的非法字符
      final cleanTitle = title.replaceAll(RegExp(r'[^\w\s-]'), '').trim();
      if (cleanTitle.isNotEmpty) {
        return "${cleanTitle}_$timestamp";
      }
    }
    
    return "agnes_$timestamp";
  }
}

class ChatAiImagesVideoWidget extends StatelessWidget {
  final ScrollController? outerController;
  final List<ProductImageModel> data;
  final bool isComplete;
  final String content;
  final  FeedbackLogic feedbackLogic = FeedbackLogic();
  final ValueChanged<MessageUploadModel>? onImageExport;
  ChatAiImagesVideoWidget(
      {super.key,
      this.outerController,
      required this.data,
      required this.isComplete,
      required this.content,
      this.onImageExport});

  /// 获取GridView的列数
  int _getCrossAxisCount() {
    if (data.isEmpty) return 3;
    // 当图片数量少于3个时，使用实际图片数量作为列数
    // 这样可以保持每个图片的标准尺寸
    return data.length > 3 ? 3 : data.length;
  }

  /// 计算容器的合适宽度
  double _calculateContainerWidth() {
    // 屏幕宽度
    final screenWidth = Get.width;
    
    // 如果图片数量大于等于3个，或者数据为空，使用屏幕宽度
    if (data.isEmpty || data.length >= 3) {
      return screenWidth;
    }
    
    // GridView的内边距：左右各16，总共32
    const double gridPadding = 32.0;
    
    // 图片之间的间距
    const double crossAxisSpacing = 8.0;
    
    // 计算当3张图片时单个图片的宽度（作为标准尺寸）
    final double standardItemWidth = (screenWidth - gridPadding - (2 * crossAxisSpacing)) / 3;
    
    // 计算当前行显示的图片数量
    final int itemsPerRow = data.length > 3 ? 3 : data.length;
    
    // 使用标准图片宽度计算实际需要的容器宽度
    double requiredWidth = gridPadding + (itemsPerRow * standardItemWidth);
    if (itemsPerRow > 1) {
      requiredWidth += (itemsPerRow - 1) * crossAxisSpacing;
    }
    
    // 确保不超过屏幕宽度
    return requiredWidth.clamp(0.0, screenWidth);
  }

  @override
  Widget build(BuildContext context) {
    if(data.isEmpty){
      return SizedBox.shrink();
    }
    // 计算容器的合适宽度
    double containerWidth = _calculateContainerWidth();
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: containerWidth,
          child: GradientBorderContainer.single(
            strokeWidth: 1,
            gradient: LinearGradient(
              colors: [
                Color(0xFF7253FA),
                Color(0xFFFF3BDF),
                Color(0xFF5E57FE),
              ],
              stops: [0.0, 0.32, 1.0],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
              tileMode: TileMode.decal,
            ),
            borderRadius: BorderRadius.circular(10),
            child: ConstrainedBox(
              constraints: BoxConstraints(maxHeight: 508.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: wrapWithEdgeHandoff(
                    GridView.builder(
                      shrinkWrap: true,
                      itemCount: data.length,
                      padding: EdgeInsets.all(16),
                      physics: ClampingScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: _getCrossAxisCount(), // 动态调整列数
                        crossAxisSpacing: 8, // 水平间距
                        mainAxisSpacing: 8, // 垂直间距
                        childAspectRatio: 1 / 1, // 宽高比 1:1
                      ),
                      itemBuilder: (context, index) {
                        final slideItem = data[index];
                        return _buildMediaItem(context, slideItem, index);
                      },
                    ),
                        outerController),
                  )
                ],
              ),
            ),
          ),
        ),
        if(isComplete)...[
          ChatBottomTipWidget(
            isLike: true,
            isStepOn: false,
            copyContent: content,
            feedbackLogic: feedbackLogic,
          )
        ]
      ],
    );
  }

  Widget _buildMediaItem(
      BuildContext context, ProductImageModel item, int index) {
    final bool isVideo = item.type?.toLowerCase() == 'video';

    return GestureDetector(
      onTap: () => isVideo
          ? _handleVideoTap(context, item)
          : _showFullScreenImage(context, item, index),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Color(0x4D424B76),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              // 背景图片/缩略图
              Positioned.fill(
                child: item.thumbnail != null || item.url != null
                    ? _buildMediaThumbnail(item, isVideo)
                    : Container(
                        color: Color(0x4D424B76),
                        child: Center(
                          child: Icon(
                            isVideo ? Icons.videocam : Icons.image,
                            color: Color(0xFF988B9A),
                            size: 24,
                          ),
                        ),
                      ),
              ),

              // 视频播放图标覆盖层
              if (isVideo)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Container(
                        width: 20.w,
                        height: 20.h,
                        child: Image.asset(
                          'assets/images/ic_design_video.png',
                          width: 20.w,
                          height: 20.h,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleVideoTap(BuildContext context, ProductImageModel item) {
    // 过滤出所有视频
    final videoItems = data.where((item) => item.type?.toLowerCase() == 'video').toList();
    final videoIndex = videoItems.indexOf(item);
    
    if (videoItems.length > 1) {
      // 多个视频，使用新的视频浏览器
      final videos = videoItems.map((item) => VideoItem.fromProductImage(item)).toList();
      
      VideoViewerDialog.show(
        context: context,
        videos: videos,
        initialIndex: videoIndex >= 0 ? videoIndex : 0,
        showDownloadButton: true,
        showPageIndicator: false,
        onDownload: (videoItem) => _downloadVideoFromVideoItem(context, videoItem),
      );
    } else {
      // 单个视频，使用原有的底部弹窗
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        isDismissible: true,
        builder: (context) => _VideoOptionsBottomSheet(videoItem: item),
      );
    }
  }
  
  /// 从VideoItem下载视频
  void _downloadVideoFromVideoItem(BuildContext context, VideoItem videoItem) async {
    final productImageModel = ProductImageModel(
      videoItem.url,        // url
      null,                 // media_info
      videoItem.thumbnail,  // thumbnail
      null,                 // id
      videoItem.type,       // type
      videoItem.title,      // title
      null,                 // version
      null,                 // index
    );
    
    final downloadMixin = _VideoDownloadMixin();
    await downloadMixin.downloadMedia(
      context: context,
      item: productImageModel,
      isVideo: true,
    );
  }

  /// 构建媒体缩略图（图片或视频第一帧）
  Widget _buildMediaThumbnail(ProductImageModel item, bool isVideo) {
    if (isVideo && item.url != null) {
      // 对于视频，使用VideoThumbnailWidget生成第一帧作为缩略图
      return VideoThumbnailWidget(
        videoUrl: item.url!,
        fit: BoxFit.cover,
        placeholder: Container(
          color: Color(0x4D424B76),
          child: Center(
            child: RotatingLoadingImage(
              imagePath: "assets/images/icon_load.webp",
              width: 30.w,
              height: 30.h,
            ),
          ),
        ),
        errorWidget: Container(
          color: Color(0x4D424B76),
          child: Center(
            child: Icon(
              Icons.videocam_off,
              color: Color(0xFF988B9A),
              size: 24,
            ),
          ),
        ),
      );
    } else {
      // 对于图片，使用CachedNetworkImage
      return CachedNetworkImage(
        imageUrl: item.thumbnail ?? item.url!,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Color(0x4D424B76),
          child: Center(
            child: RotatingLoadingImage(
              imagePath: "assets/images/icon_load.webp",
              width: 30.w,
              height: 30.h,
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: Color(0x4D424B76),
          child: Center(
            child: Icon(
              Icons.image_not_supported,
              color: Color(0xFF988B9A),
              size: 24,
            ),
          ),
        ),
      );
    }
  }

  void _showFullScreenImage(
      BuildContext context, ProductImageModel item, int currentIndex) {
    // 过滤出所有图片（非视频）
    final imageItems = data.where((item) => item.type?.toLowerCase() != 'video').toList();
    final imageIndex = imageItems.indexOf(item);
    
    // 转换为通用图片数据模型
    final images = imageItems.map((item) => ImageItem.network(
      item.url ?? item.thumbnail ?? '',
      title: item.title,
      type: item.type,
      thumbnail: item.thumbnail
    )).toList();
    
    ImageViewerDialog.show(
      context: context,
      images: images,
      initialIndex: imageIndex >= 0 ? imageIndex : 0,
      showDownloadButton: true,
      showExportButton: onImageExport != null,
      showBottomButtons: true,
      showPageIndicator: false,
      onDownload: (index) => _downloadCurrentImage(context, imageItems[index >= 0 ? index : 0]),
      onExport: onImageExport != null ? (index) => _exportCurrentImage(context, imageItems[index >= 0 ? index : 0]) : null,
    );
  }

  void _downloadCurrentImage(BuildContext context, ProductImageModel item) async {
    // 使用现有的下载逻辑
    final downloadMixin = _ImageDownloadMixin();
    await downloadMixin.downloadMedia(
      context: context,
      item: item,
      isVideo: false,
    );
  }

  void _exportCurrentImage(BuildContext context, ProductImageModel item) {
    if (onImageExport != null) {
      onImageExport?.call(MessageUploadModel(
        file_url: item.url,
        file_title: item.title,
        mime_type: item.type,
        uploadStatus: UploadStatus.success,
        export: true
      ));
      Get.back();
    }
  }
}

/// 图片下载功能Mixin
class _ImageDownloadMixin with MediaDownloadMixin {
  // 继承MediaDownloadMixin的所有下载功能
}

/// 视频下载功能Mixin
class _VideoDownloadMixin with MediaDownloadMixin {
  // 继承MediaDownloadMixin的所有下载功能
}

class _VideoOptionsBottomSheet extends StatefulWidget {
  final ProductImageModel videoItem;

  const _VideoOptionsBottomSheet({required this.videoItem});

  @override
  State<_VideoOptionsBottomSheet> createState() =>
      _VideoOptionsBottomSheetState();
}

class _VideoOptionsBottomSheetState extends State<_VideoOptionsBottomSheet>
    with AutomaticKeepAliveClientMixin, MediaDownloadMixin {
  late final Widget _videoPlayer;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // 在initState中创建视频播放器，避免重复创建
    _videoPlayer = BetterPlayer.network(
      widget.videoItem.url!,
      betterPlayerConfiguration: BetterPlayerConfiguration(
          fit: BoxFit.cover,
          autoPlay: true,
          subtitlesConfiguration:
              BetterPlayerSubtitlesConfiguration(outlineEnabled: false),
          controlsConfiguration: BetterPlayerControlsConfiguration(
            backgroundColor: Colors.transparent,
            controlBarColor: Colors.transparent,
            enableProgressText: false,
            enablePlayPause: false,
            enableOverflowMenu: false,
            enableMute: false,
            enableSkips: false,
            enableQualities: false,
            enableFullscreen: false,
            enablePip: false,
            enableSubtitles: false,
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持状态
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: Color(0xFF000A19),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          // 顶部拖拽指示器（居中）
          Container(
            padding: EdgeInsets.all(16),
            child: Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          ),

          // 视频播放区域
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: _videoPlayer, // 使用预创建的视频播放器实例
                ),
              ),
            ),
          ),

          // 底部下载按钮区域
          Container(
            margin: EdgeInsets.only(bottom: 32, top: 36),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 下载视频按钮
                GestureDetector(
                  onTap: () => _downloadVideo(context, widget.videoItem),
                  child: Container(
                    width: 40.w,
                    height: 40.h,
                    child: Image.asset("assets/icon/ic_icon_download.png"),
                  ),
                ),
              ],
            ),
          ),

          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  void _downloadVideo(BuildContext context, ProductImageModel item) async {
    await downloadMedia(
      context: context,
      item: item,
      isVideo: true,
    );
  }
}

/// 视频数据模型
class VideoItem {
  /// 视频URL
  final String? url;
  
  /// 视频标题
  final String? title;
  
  /// 视频类型
  final String? type;

  /// 缩略图
  final String? thumbnail;

  const VideoItem({
    this.url,
    this.title,
    this.type,
    this.thumbnail
  });

  /// 从ProductImageModel创建
  factory VideoItem.fromProductImage(ProductImageModel productImage) {
    return VideoItem(
      url: productImage.url,
      title: productImage.title,
      type: productImage.type,
      thumbnail: productImage.thumbnail,
    );
  }
}

/// 视频浏览器组件
/// 支持侧滑浏览、下载等功能
class VideoViewerWidget extends StatefulWidget {
  /// 视频列表
  final List<VideoItem> videos;
  
  /// 当前显示的视频索引
  final int initialIndex;
  
  /// 是否显示下载按钮
  final bool showDownloadButton;
  
  /// 下载按钮点击回调
  final ValueChanged<VideoItem>? onDownload;
  
  /// 视频点击回调
  final ValueChanged<int>? onVideoTap;
  
  /// 页面切换回调
  final ValueChanged<int>? onPageChanged;
  
  /// 是否显示页面指示器
  final bool showPageIndicator;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 是否显示顶部拖拽指示器
  final bool showDragIndicator;

  const VideoViewerWidget({
    Key? key,
    required this.videos,
    this.initialIndex = 0,
    this.showDownloadButton = true,
    this.onDownload,
    this.onVideoTap,
    this.onPageChanged,
    this.showPageIndicator = true,
    this.backgroundColor,
    this.showDragIndicator = true,
  }) : super(key: key);

  @override
  State<VideoViewerWidget> createState() => _VideoViewerWidgetState();
}

class _VideoViewerWidgetState extends State<VideoViewerWidget> with AutomaticKeepAliveClientMixin, MediaDownloadMixin {
  late PageController _pageController;
  late int _currentIndex;
  
  // 视频播放器管理
  final Map<int, Widget> _videoPlayers = {};
  final Map<int, BetterPlayerController> _playerControllers = {};
  final Set<int> _disposedControllers = {}; // 跟踪已释放的控制器

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
    
    // 预创建当前和相邻视频播放器
    _createVideoPlayer(_currentIndex);
    if (_currentIndex > 0) _createVideoPlayer(_currentIndex - 1);
    if (_currentIndex < widget.videos.length - 1) _createVideoPlayer(_currentIndex + 1);
  }

  @override
  void dispose() {
    _pageController.dispose();
    // 安全地清理所有视频播放器
    _disposeAllPlayers();
    super.dispose();
  }
  
  /// 安全地释放所有播放器
  void _disposeAllPlayers() {
    for (var entry in _playerControllers.entries) {
      final index = entry.key;
      final controller = entry.value;
      if (!_disposedControllers.contains(index)) {
        try {
          controller.dispose();
          _disposedControllers.add(index);
        } catch (e) {
          print('释放播放器 $index 时出错: $e');
        }
      }
    }
    _playerControllers.clear();
    _videoPlayers.clear();
    _disposedControllers.clear();
  }
  
  /// 创建视频播放器
  void _createVideoPlayer(int index) {
    if (index < 0 || index >= widget.videos.length) return;
    
    final videoItem = widget.videos[index];
    if (videoItem.url == null || _videoPlayers.containsKey(index)) return;
    
    // 清理该索引的旧标记（如果存在）
    _disposedControllers.remove(index);
    
    try {
      final controller = BetterPlayerController(
        BetterPlayerConfiguration(
          fit: BoxFit.cover,
          autoPlay: index == _currentIndex, // 只有当前视频自动播放
          looping: false,
          subtitlesConfiguration: BetterPlayerSubtitlesConfiguration(outlineEnabled: false),
          controlsConfiguration: BetterPlayerControlsConfiguration(
            backgroundColor: Colors.transparent,
            controlBarColor: Colors.transparent,
            enableProgressText: false,
            enablePlayPause: false,
            enableOverflowMenu: false,
            enableMute: false,
            enableSkips: false,
            enableQualities: false,
            enableFullscreen: false,
            enablePip: false,
            enableSubtitles: false,
          ),
        ),
      );
      
      controller.setupDataSource(BetterPlayerDataSource(
        BetterPlayerDataSourceType.network,
        videoItem.url!,
      ));
      
      _playerControllers[index] = controller;
      _videoPlayers[index] = BetterPlayer(controller: controller);
    } catch (e) {
      print('创建视频播放器 $index 时出错: $e');
      // 只有在创建失败时才标记为已释放
      _disposedControllers.add(index);
    }
  }
  
  /// 暂停所有视频播放器
  void _pauseAllPlayers() {
    for (var entry in _playerControllers.entries) {
      final index = entry.key;
      final controller = entry.value;
      if (!_disposedControllers.contains(index)) {
        try {
          controller.pause();
        } catch (e) {
          print('暂停播放器 $index 时出错: $e');
        }
      }
    }
  }
  
  /// 播放指定索引的视频
  void _playVideo(int index) {
    final controller = _playerControllers[index];
    if (controller != null && !_disposedControllers.contains(index)) {
      try {
        controller.play();
      } catch (e) {
        print('播放视频 $index 时出错: $e');
      }
    }
  }
  
  /// 智能管理视频播放器
  void _manageVideoPlayers(int currentIndex) {
    // 需要保留的播放器索引（当前和相邻的）
    final Set<int> keepIndices = {};
    
    // 当前视频
    keepIndices.add(currentIndex);
    
    // 前一个视频
    if (currentIndex > 0) {
      keepIndices.add(currentIndex - 1);
    }
    
    // 后一个视频
    if (currentIndex < widget.videos.length - 1) {
      keepIndices.add(currentIndex + 1);
    }
    
    // 释放不需要的播放器（距离当前位置超过1的）
    final toRemove = <int>[];
    for (var index in _playerControllers.keys) {
      if (!keepIndices.contains(index)) {
        toRemove.add(index);
      }
    }
    
    // 安全地释放远离的播放器
    for (var index in toRemove) {
      _disposePlayerAtIndex(index);
    }
    
    // 创建需要的播放器
    for (var index in keepIndices) {
      _createVideoPlayer(index);
    }
  }
  
  /// 释放指定索引的播放器
  void _disposePlayerAtIndex(int index) {
    final controller = _playerControllers[index];
    if (controller != null) {
      try {
        controller.dispose();
      } catch (e) {
        print('释放播放器 $index 时出错: $e');
      }
    }
    _playerControllers.remove(index);
    _videoPlayers.remove(index);
    // 不要永久标记为已释放，允许后续重新创建
    // _disposedControllers.add(index); // 移除这行
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持状态
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Color(0xFF000A19),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          // 顶部拖拽指示器
          if (widget.showDragIndicator)
            Container(
              padding: EdgeInsets.all(16),
              child: Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            ),

          // 页面指示器
          if (widget.showPageIndicator && widget.videos.length > 1)
            Container(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Text(
                '${_currentIndex + 1} / ${widget.videos.length}',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14.sp,
                ),
              ),
            ),

          // 视频查看区域
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              physics: BouncingScrollPhysics(),
              scrollDirection: Axis.horizontal,
              allowImplicitScrolling: true,
              padEnds: false,
              onPageChanged: (index) {
                final oldIndex = _currentIndex;
                
                // 暂停上一个视频
                _pauseAllPlayers();
                
                setState(() {
                  _currentIndex = index;
                });
                
                // 智能管理播放器：保留当前和相邻的，清理远离的
                _manageVideoPlayers(index);
                
                // 播放当前视频
                _playVideo(index);
                
                widget.onPageChanged?.call(index);
              },
              itemCount: widget.videos.length,
              itemBuilder: (context, index) {
                return _buildVideoPage(index);
              },
            ),
          ),

          // 底部下载按钮区域
          if (widget.showDownloadButton)
            Container(
              margin: EdgeInsets.only(bottom: 32, top: 36),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 下载视频按钮
                  GestureDetector(
                    onTap: () => _downloadCurrentVideo(),
                    child: Container(
                      width: 40.w,
                      height: 40.h,
                      child: Image.asset("assets/icon/ic_icon_download.png"),
                    ),
                  ),
                ],
              ),
            ),
          
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  /// 构建视频页面
  Widget _buildVideoPage(int index) {
    final videoItem = widget.videos[index];
    
    return GestureDetector(
      onTap: () => widget.onVideoTap?.call(index),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16),
        child: Container(
          margin: EdgeInsets.symmetric(vertical: 8),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: _getVideoPlayerWidget(index, videoItem),
          ),
        ),
      ),
    );
  }
  
  /// 获取视频播放器组件
  Widget _getVideoPlayerWidget(int index, VideoItem videoItem) {
    // 如果播放器存在且未被释放，返回播放器
    if (_videoPlayers.containsKey(index) && 
        !_disposedControllers.contains(index) &&
        _playerControllers.containsKey(index)) {
      return _videoPlayers[index]!;
    }
    
    // 如果播放器不存在，尝试创建（适用于当前和相邻视频）
    if (_shouldCreatePlayer(index)) {
      _createVideoPlayer(index);
      
      // 如果创建成功，返回播放器
      if (_videoPlayers.containsKey(index) && 
          !_disposedControllers.contains(index)) {
        return _videoPlayers[index]!;
      }
    }
    
    // 否则返回占位符（带重试功能）
    return _buildVideoPlaceholder(videoItem, index);
  }
  
  /// 判断是否应该创建播放器
  bool _shouldCreatePlayer(int index) {
    // 只为当前视频和相邻视频创建播放器
    final distance = (index - _currentIndex).abs();
    return distance <= 1;
  }
  
  /// 强制刷新播放器（用于解决播放器状态异常的情况）
  void _refreshPlayer(int index) {
    if (index < 0 || index >= widget.videos.length) return;
    
    // 先释放现有播放器
    _disposePlayerAtIndex(index);
    
    // 等待一帧后重新创建
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _shouldCreatePlayer(index)) {
        _createVideoPlayer(index);
        if (mounted) {
          setState(() {});
        }
      }
    });
  }
  
  /// 构建视频占位符
  Widget _buildVideoPlaceholder(VideoItem videoItem, int index) {
    return GestureDetector(
      onTap: () {
        // 点击占位符时尝试刷新播放器
        if (_shouldCreatePlayer(index)) {
          _refreshPlayer(index);
        }
      },
      child: Container(
        color: Color(0x4D424B76),
        child: Stack(
          children: [
            // 缩略图背景
            if (videoItem.thumbnail != null)
              Positioned.fill(
                child: VideoThumbnailWidget(
                  videoUrl: videoItem.url!,
                  fit: BoxFit.cover,
                  placeholder: Container(
                    color: Color(0x4D424B76),
                    child: Center(
                      child: RotatingLoadingImage(
                        imagePath: "assets/images/icon_load.webp",
                        width: 30.w,
                        height: 30.h,
                      ),
                    ),
                  ),
                  errorWidget: Container(
                    color: Color(0x4D424B76),
                    child: Center(
                      child: Icon(
                        Icons.videocam_off,
                        color: Color(0xFF988B9A),
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),
            // 加载指示器和重试提示
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  RotatingLoadingImage(
                    imagePath: "assets/images/icon_load.webp",
                    width: 30.w,
                    height: 30.h,
                  ),
                  if (_shouldCreatePlayer(index)) ...[
                    SizedBox(height: 8),
                    Text(
                      '点击重试',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 下载当前视频
  void _downloadCurrentVideo() {
    if (_currentIndex >= 0 && _currentIndex < widget.videos.length) {
      final currentVideo = widget.videos[_currentIndex];
      widget.onDownload?.call(currentVideo);
      
      // 如果没有提供回调，使用默认下载逻辑
      if (widget.onDownload == null) {
        _downloadVideoWithMixin(currentVideo);
      }
    }
  }
  
  /// 使用Mixin下载视频
  void _downloadVideoWithMixin(VideoItem videoItem) async {
    final productImageModel = ProductImageModel(
      videoItem.url,        // url
      null,                 // media_info
      videoItem.thumbnail,  // thumbnail
      null,                 // id
      videoItem.type,       // type
      videoItem.title,      // title
      null,                 // version
      null,                 // index
    );
    
    await downloadMedia(
      context: context,
      item: productImageModel,
      isVideo: true,
    );
  }
}

/// 视频浏览弹窗工具类
class VideoViewerDialog {
  /// 显示视频浏览弹窗
  static Future<void> show({
    required BuildContext context,
    required List<VideoItem> videos,
    int initialIndex = 0,
    bool showDownloadButton = true,
    ValueChanged<VideoItem>? onDownload,
    ValueChanged<int>? onVideoTap,
    ValueChanged<int>? onPageChanged,
    bool showPageIndicator = true,
    Color? backgroundColor,
    bool showDragIndicator = true,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      builder: (context) => VideoViewerWidget(
        videos: videos,
        initialIndex: initialIndex,
        showDownloadButton: showDownloadButton,
        onDownload: onDownload,
        onVideoTap: onVideoTap,
        onPageChanged: onPageChanged,
        showPageIndicator: showPageIndicator,
        backgroundColor: backgroundColor,
        showDragIndicator: showDragIndicator,
      ),
    );
  }
}
