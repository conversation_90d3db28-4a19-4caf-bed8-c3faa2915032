import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../../api/Api.dart';
import '../../../api/ApiProvider.dart';
import '../../../generated/l10n.dart';
import '../../../widget/ContainerBox.dart';
import '../../../widget/GradientBorderContainer.dart';
import '../../../widget/TapHigh.dart';
import '../../mine/mine_info/logic.dart';
import '../logic.dart';
import 'deep_research_share.dart';
import 'package:image/image.dart' as img;
import 'package:get/get.dart' as getx;

class ChatPlanImageWidget extends StatefulWidget {
  String shareContent;

  ChatPlanImageWidget({super.key, this.shareContent = ""});

  @override
  State<ChatPlanImageWidget> createState() => _ChatPlanImageWidgetState();
}

class _ChatPlanImageWidgetState extends State<ChatPlanImageWidget> {
   ChatLogic chatLogic = Get.find<ChatLogic>();
   ChatPlanImagLogic chatPlanImageLogic = Get.put(ChatPlanImagLogic());
  int type = 0;
  bool showImageLay = false;
  bool isImage = false;
  bool isWeb = false;

  @override
  void initState() {
    super.initState();
    chatPlanImageLogic.initText(
        chatLogic.title.value, chatLogic.conversationId);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: _plannerShareLay(),
    );
  }

  Widget _plannerShareLay() {
    return Container(
      padding: EdgeInsets.only(left: 1.w, right: 1.w),
      margin: EdgeInsets.only(top: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(Get.context!).letAgnesHelpYouCreateABetterSharingFormat,
            style: TextStyle(
                color: Colors.white,
                fontSize: 20.sp,
                fontWeight: FontWeight.w600),
          ),
          SizedBox(
            height: 10.w,
          ),
          TapHigh(
            onTap: () {
              chatPlanImageLogic.getImageData(
                  widget.shareContent, chatLogic.conversationId, false);
              setState(() {
                type = 1;
                // if (isImage) {
                //   isImage = false;
                // } else {
                //   isImage = true;
                // }
              });
            },
            child: GradientBorderContainer.single(
              height: 40.h,
              strokeWidth: 0.5,
              gradient: LinearGradient(
                colors: [
                  type == 1 ? Color(0xFFFF3BDF) : Colors.transparent,
                  type == 1 ? Color(0xFF5E57FE) : Colors.transparent,
                  type == 1 ? Color(0xFF2DDAF2) : Colors.transparent,
                  type == 1 ? Color(0xFF4D84FA) : Colors.transparent,
                  type == 1 ? Color(0xFFFF3BDF) : Colors.transparent,
                ],
                stops: [0.0, 0.24, 0.51, 0.78, 1.0],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
                tileMode: TileMode.decal,
              ),
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: EdgeInsets.only(
                    left: 16.w, right: 16.w, top: 10.w, bottom: 10.w),
                decoration: BoxDecoration(
                  color: const Color(0x8F604276),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      type == 1
                          ? 'assets/images/ic_select_yes.webp'
                          : 'assets/images/ic_select_no.webp',
                      width: 16.w,
                      height: 16.w,
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                    Expanded(
                        child: Text(S.of(Get.context!).makeMeAShareableImagePost,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.white,
                            ))),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(
            height: 10.w,
          ),
          TapHigh(
              onTap: () {
                chatPlanImageLogic.getWebData(chatLogic.conversationId);
                setState(() {
                  type = 2;
                });
                // setState(() {
                //   if (isWeb) {
                //     isWeb = false;
                //   } else {
                //     isWeb = true;
                //   }
                // });
              },
              child: GradientBorderContainer.single(
                height: 40.h,
                strokeWidth: 0.5,
                gradient: LinearGradient(
                  colors: [
                    type == 2 ? Color(0xFFFF3BDF) : Colors.transparent,
                    type == 2 ? Color(0xFF5E57FE) : Colors.transparent,
                    type == 2 ? Color(0xFF2DDAF2) : Colors.transparent,
                    type == 2 ? Color(0xFF4D84FA) : Colors.transparent,
                    type == 2 ? Color(0xFFFF3BDF) : Colors.transparent,
                  ],
                  stops: [0.0, 0.24, 0.51, 0.78, 1.0],
                  begin: Alignment.bottomLeft,
                  end: Alignment.topRight,
                  tileMode: TileMode.decal,
                ),
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: EdgeInsets.only(
                      left: 16.w, right: 16.w, top: 10.w, bottom: 10.w),
                  decoration: BoxDecoration(
                    color: const Color(0x8F604276),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        type == 2
                            ? 'assets/images/ic_select_yes.webp'
                            : 'assets/images/ic_select_no.webp',
                        width: 16.w,
                        height: 16.w,
                      ),
                      SizedBox(
                        width: 8.w,
                      ),
                      Expanded(
                          child: Text(S.of(Get.context!).makeMeAnHtmlWebPage,
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.white,
                              ))),
                    ],
                  ),
                ),
              )),
          // TapHigh(
          //     onTap: () {
          //       if (isImage || isWeb) {
          //         showImageLay = true;
          //       } else {
          //         showImageLay = false;
          //       }
          //       if (isWeb && isImage) {
          //         type = 3;
          //       } else if (isImage) {
          //         type = 1;
          //       } else if (isWeb) {
          //         type = 2;
          //       } else {
          //         type = 0;
          //       }
          //       setState(() {});
          //     },
          //     child: ContainerBox(
          //         jBColors: [
          //           Color(0xFFFF91EE),
          //           Color(0xFFFF3BDF),
          //         ],
          //         radius: 10,
          //         margin: EdgeInsets.only(top: 16.w),
          //         padding: EdgeInsets.only(
          //             left: 14.w, right: 14.w, top: 8.w, bottom: 8.w),
          //         begin: Alignment.topCenter,
          //         end: Alignment.bottomCenter,
          //         child: Container(
          //           child: Text(
          //             'Confirm',
          //             style: TextStyle(color: Colors.white, fontSize: 14.sp),
          //           ),
          //         ))),
          type != 0 ? ShareWidget(type) : SizedBox(),
        ],
      ),
    );
  }
}

class ChatPlanImagLogic extends GetxController {
  var imageGenerateText = "".obs;
  var shareUrl = "";
  var imageData = "".obs;
  var webData = "".obs;
  var isEdit = false.obs;

  void initText(String title, String conversationId) {
    imageGenerateText.value =
        "Excited to share a new piece l created using Agnes-it's titled \"${title}\"Would loveyour thoughts!";
    shareUrl = "${Api.baseUrl}/share?conversationid=${conversationId}";
  }

  Future<void> share(String title, String shareUrl) async {
    print("shareUrl:" + shareUrl);
    SharePlus.instance.share(
      ShareParams(
        title: title,
        uri: Uri.parse(shareUrl),
      ),
    );
  }

  Future<void> downloadAndShareImage(String _imageUrl) async {
    final Dio _dio = Dio();
    if (await Permission.storage.request().isGranted &&
        await Permission.manageExternalStorage.request().isGranted) {
      try {
        final dir = await getApplicationDocumentsDirectory();
        final path = '${dir.path}/downloaded_image.jpg';
        await _dio.download(
          _imageUrl,
          path,
          onReceiveProgress: (received, total) {
            if (total != -1) {
              print('${(received / total * 100).toStringAsFixed(0)}%');
            }
          },
        );
        print('图片已保存到: $path');

        var file = await compressImage(File(path));
        final params = ShareParams(
          text: 'Great picture',
          files: [XFile(file.path)],
        );

        final result = await SharePlus.instance.share(params);
        if (result.status == ShareResultStatus.success) {
          print('Thank you for sharing the picture!');
        }
        await file.delete();
      } catch (e) {
        print('下载失败: $e');
      }
    } else {
      print('没有存储权限');
    }
  }

  Future<File> compressImage(File file) async {
    final bytes = await file.readAsBytes();
    final image = img.decodeImage(bytes);
    final smallerImage = img.copyResize(image!, width: 800);
    final compressedBytes = img.encodeJpg(smallerImage, quality: 80);
    final tempDir = await getTemporaryDirectory();
    final targetPath =
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
    final newFile = await File(targetPath).writeAsBytes(compressedBytes);
    return newFile;
  }

  Future<void> getImageData(
      String content, String conversation_id, bool is_regenerate) async {
    getx.Response res = await Get.find<ApiProvider>().post(Api.ugc, {
      "content": content,
      "conversation_id": conversation_id,
      "is_regenerate": false
    });
    if (res.statusCode == 200) {
      if (res.body == null) {
        return;
      }
      final Map<String, dynamic> responseBody = res.body;
      imageData.value = responseBody['image_url'];
    } else {
      imageData.value = "500";
    }
  }

  Future<void> getWebData(String conversation_id) async {
    Get.find<ApiProvider>().httpClient.timeout=Duration(seconds: 100);
    getx.Response res =
        await Get.find<ApiProvider>().post(getGenerate(conversation_id), {});
    if (res.statusCode == 200) {
      if (res.body == null) {
        return;
      }
      final Map<String, dynamic> responseBody = res.body;
      if (responseBody['success']) {
        if(webData.value!=responseBody['url']){
          webData.value = responseBody['url'];
        }
      }
    }
  }

  String getGenerate(String conversation_id) {
    return "/api/html/${conversation_id}/generate";
  }
}
