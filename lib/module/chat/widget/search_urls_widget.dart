import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/dialog/sources_dialog.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import 'package:new_agnes/widget/ImageUrl.dart';
import 'package:new_agnes/widget/TapHigh.dart';

import '../model/search_urls_model.dart';
import 'chat_bottom_tip_widget.dart';
import 'deep_research_lay.dart';

class SearchUrlsWidget extends StatefulWidget {
  String jsonData;
  String content;
  String agentName;
  bool isComplete;

  SearchUrlsWidget(
      this.jsonData, this.isComplete, this.content, this.agentName);

  @override
  State<SearchUrlsWidget> createState() => _SearchUrlsWidgetState();
}

class _SearchUrlsWidgetState extends State<SearchUrlsWidget> {
  List<SearchUrlsModel> searchList = [];
  final FeedbackLogic feedbackLogic = FeedbackLogic();

  @override
  void initState() {
    // TODO: implement initState
    List<dynamic> jsonArray = jsonDecode(widget.jsonData);
    jsonArray.forEach((data) {
      SearchUrlsModel model = new SearchUrlsModel();
      model.content = data['content'];
      model.type = data['type'];
      model.url = data['url'];
      model.title = data['title'];
      String image = model.url!;
      List<String> imageList1 = image.split('//');
      if (imageList1.length > 1 && imageList1[1].isNotEmpty) {
        List<String> imageList2 = imageList1[1].split('/');
        image = 'https://www.google.com/s2/favicons?sz=64&domain=${imageList2[0]}';
      }
      model.image = image;
      searchList.add(model);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.w),
      child: Column(
        children: [
          Container(
            child: Row(
              children: [
                TapHigh(
                    onTap: () {
                      Get.bottomSheet(SourcesDialog(searchList),
                          backgroundColor: Colors.transparent,
                          isScrollControlled: true,
                          elevation: 0,
                          barrierColor: Colors.transparent);
                    },
                    child: ContainerBox(
                      boxColor: Color(0xFF05294B),
                      borderColor: Color(0xFF7BD33A),
                      borderWith: 1.w,
                      radius: 14,
                      padding: EdgeInsets.only(
                          left: 10.w, right: 10.w, top: 4.w, bottom: 4.w),
                      child: Row(
                        children: [
                          Container(
                            width: searchList.length > 2
                                ? 40.w
                                : searchList.length > 1
                                    ? 30.w
                                    : searchList.length > 0
                                        ? 20.w
                                        : 0.w,
                            child: Stack(
                              children: [
                                searchList.length > 0
                                    ? Positioned(
                                        child:
                                            _imageLay('${searchList[0].image}'))
                                    : SizedBox(),
                                searchList.length > 1
                                    ? Positioned(
                                        left: 10.w,
                                        child:
                                            _imageLay('${searchList[1].image}'))
                                    : SizedBox(),
                                searchList.length > 2
                                    ? Positioned(
                                        left: 20.w,
                                        child:
                                            _imageLay('${searchList[2].image}'))
                                    : SizedBox(),
                              ],
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(left: 10.w),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  '+ ${searchList.length} sources',
                                  style: TextStyle(
                                      color: Color(0xFF7BD33A),
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w600),
                                ),
                                Image.asset(
                                  'assets/images/ic_chat_right_green.png',
                                  width: 20.w,
                                  height: 20.w,
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ))
              ],
            ),
          ),
          widget.isComplete
              ? ChatBottomTipWidget(
                  isLike: true,
                  isStepOn: false,
                  copyContent: widget.content,
                  agentName: widget.agentName,
                  feedbackLogic: feedbackLogic,
                )
              : SizedBox(),
        ],
      ),
    );
  }

  Widget _imageLay(image) {
    return ContainerBox(
      radius: 180,
      child: UrlImage('${image}',
          width: 20.w, height: 20.w, radius: 180, errorWidget: ErrorLay()),
    );
  }
}
