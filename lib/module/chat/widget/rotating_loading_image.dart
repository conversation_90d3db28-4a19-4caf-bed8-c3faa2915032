import 'package:flutter/material.dart';

class RotatingLoadingImage extends StatefulWidget {
  final String imagePath;
  final double width;
  final double height;

  const RotatingLoadingImage(
      {super.key,
      required this.imagePath,
      required this.width,
      required this.height});

  @override
  State<RotatingLoadingImage> createState() => _RotatingLoadingImageState();
}

class _RotatingLoadingImageState extends State<RotatingLoadingImage>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(); // 无限循环旋转
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: Image.asset(
        widget.imagePath,
        width: widget.width,
        height: widget.height,
      ), // 你的静态图片
    );
  }
}
