import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:new_agnes/dialog/download_dialog.dart';
import 'package:new_agnes/module/chat/logic.dart';
import 'package:new_agnes/module/home/<USER>/input_widgets/share_widget.dart';
import 'package:new_agnes/utils/dialog_utils.dart';
import 'package:new_agnes/widget/ContainerBox.dart';
import 'package:new_agnes/widget/TapHigh.dart';
import 'package:share_plus/share_plus.dart';

import '../../../api/Api.dart';
import '../../../api/ApiProvider.dart';
import '../../../generated/l10n.dart';
import '../../../utils/toastUtil.dart';
import '../../../widget/GradientBorderContainer.dart';

class ChatBottomTipWidget extends StatefulWidget {
  final bool isLike; //是否点赞
  final bool isStepOn; //是否踩
  bool isPPt;
  String copyContent;
  String agentName;
  FeedbackLogic feedbackLogic;

  ChatBottomTipWidget(
      {super.key,
      required this.isLike,
      required this.isStepOn,
      required this.feedbackLogic,
      this.isPPt = false,
      this.copyContent = "",
      this.agentName = ''});

  @override
  State<ChatBottomTipWidget> createState() => _ChatBottomTipWidgetState();
}

class _ChatBottomTipWidgetState extends State<ChatBottomTipWidget> {
  bool isShowDownLay = false;
  OverlayEntry? _overlayEntry;
  bool _isShowing = false;
  final GlobalKey _buttonKey = GlobalKey();
  var _selectedItem = "".obs;

  // var feedbackLogic = Get.put(FeedbackLogic());
  var chatLogic = Get.find<ChatLogic>();
  final List<Map<String, dynamic>> _menuItems = [
    {
      'icon': 'assets/icon/icon_pdf.webp',
      'text': "PDF",
    },
    {
      'icon': 'assets/icon/icon_markdown.webp',
      'text': "Markdown",
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 20.w),
      child: Column(
        children: [
          Container(
              child: Obx(
            () => Row(
              children: [
                Visibility(
                  visible: widget.feedbackLogic.feedbackType.value != "dislike",
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          if(widget.feedbackLogic.feedbackType.value.isEmpty){
                            widget.feedbackLogic
                                .feedback("like", chatLogic.conversationId);
                          }
                        },
                        child: Image.asset(
                          widget.feedbackLogic.feedbackType.value == "like"
                              ? 'assets/images/ic_chat_like.png'
                              : 'assets/images/ic_chat_un_like.png',
                          width: 20.w,
                          height: 20.w,
                          fit: BoxFit.fill,
                        ),
                      ),
                      SizedBox(
                        width: 16.w,
                      ),
                    ],
                  ),
                ),
                Visibility(
                    visible: widget.feedbackLogic.feedbackType.value != "like",
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            if(widget.feedbackLogic.feedbackType.value == "dislike"){
                              return;
                            }
                            DialogUtils.showFanKuiDialog(widget.feedbackLogic);
                          },
                          child: Image.asset(
                            widget.feedbackLogic.feedbackType.value == "dislike"
                                ? 'assets/images/ic_chat_step_on_select.png'
                                : 'assets/images/ic_chat_step_on.png',
                            width: 20.w,
                            height: 20.w,
                          ),
                        ),
                        SizedBox(
                          width: 16.w,
                        ),
                      ],
                    )),
                GestureDetector(
                  onTap: () {
                    //区分ppt分享
                    if (widget.isPPt) {
                      String pptUrl =
                          "${Api.baseUrl}/share/${chatLogic.conversationId}";
                      widget.feedbackLogic.copy(pptUrl);
                    } else {
                      widget.feedbackLogic.copy(widget.copyContent);
                    }
                  },
                  child: Image.asset(
                    'assets/images/ic_chat_copy.png',
                    width: 20.w,
                    height: 20.w,
                    color: Colors.white,
                  ),
                ),
                SizedBox(
                  width: 16.w,
                ),
                GestureDetector(
                  onTap: () {
                    String shareUrl =
                        "${Api.baseUrl}/share?conversationid=${chatLogic.conversationId}";
                    SharePlus.instance.share(
                      ShareParams(
                        title: chatLogic.title.value,
                        uri: Uri.parse(shareUrl),
                      ),
                    );
                    //showShareInfoDialog(context, "1754388078056614", "南京天气");
                  },
                  child: Image.asset(
                    'assets/images/ic_chat_share.png',
                    width: 20.w,
                    height: 20.w,
                    color: Colors.white,
                  ),
                ),
                SizedBox(
                  width: 16.w,
                ),
                widget.agentName == 'reporter'
                    ? GestureDetector(
                        key: _buttonKey,
                        onTap: () {
                          _showOverlay();
                        },
                        child: Image.asset(
                          'assets/images/ic_download_no.webp',
                          width: 20.w,
                          height: 20.w,
                          color: Colors.white,
                        ),
                      )
                    : SizedBox()
              ],
            ),
          )),
        ],
      ),
    );
  }

  void _showOverlay() {
    if (_isShowing) {
      _removeOverlay();
      return;
    }

    final RenderBox renderBox =
        _buttonKey.currentContext?.findRenderObject() as RenderBox;
    final offset = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 半透明背景层，点击可关闭
          Positioned.fill(
            child: GestureDetector(
              onTap: _removeOverlay,
              child: Container(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
          ),
          // 下拉框内容
          Positioned(
            left: offset.dx,
            top: offset.dy + size.height + 8,
            child: Material(
              elevation: 4,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                constraints: const BoxConstraints(
                  minWidth: 100,
                ),
                decoration: BoxDecoration(
                  border: Border.fromBorderSide(
                      BorderSide(color: Color(0xFFFF3BDF), width: 1)),
                  color: Color(0xE52E174F),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x40000000),
                      blurRadius: 4,
                      spreadRadius: 0,
                    )
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: _menuItems.map((item) {
                    return Material(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.transparent,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 6),
                        child: _buildItem(item),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    Overlay.of(_buttonKey.currentContext!).insert(_overlayEntry!);
    _isShowing = true;
  }

  Widget _buildItem(dynamic item) {
    final isSelected = _selectedItem.value.isEmpty
        ? false
        : item['text'] == _selectedItem.value;
    return InkWell(
        splashColor: Colors.purple.withOpacity(0.3),
        highlightColor: Colors.purple.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          // _selectedItem.value = item['text'];
          if (item['text'] == 'PDF') {
            DialogUtils.showDownLoadDialog(chatLogic.conversationId, 'pdf',
                widget.copyContent, chatLogic.title.value, 0);
          } else if (item['text'] == 'Markdown') {
            DialogUtils.showDownLoadDialog(chatLogic.conversationId, 'md',
                widget.copyContent, chatLogic.title.value, 0);
          }
          _overlayEntry!.markNeedsBuild();
          _removeOverlay();
        },
        child: GradientBorderContainer.single(
          strokeWidth: 1,
          gradient: LinearGradient(
            colors: isSelected
                ? [
                    Color(0xFF7253FA),
                    Color(0xFFFF3BDF),
                    Color(0xFF5E57FE),
                  ]
                : [
                    Colors.transparent,
                    Colors.transparent,
                    Colors.transparent,
                  ],
            stops: const [0.0, 0.32, 1.0],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
            tileMode: TileMode.decal,
          ),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 8.w),
            width: 120.w,
            child: Row(
              children: [
                SizedBox(width: 9.w),
                Image.asset(
                  item['icon'],
                  width: 16.w,
                  height: 16.w,
                ),
                SizedBox(width: 8.w),
                Text(
                  item['text'],
                  style: TextStyle(fontSize: 14.sp, color: Colors.white),
                ),
              ],
            ),
          ),
        ));
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry?.remove();
      _overlayEntry = null;
      _isShowing = false;
    }
  }
}

class FeedbackLogic extends GetxController {
  var feedbackType = "".obs;

  //feedback_type: dislike like
  Future<void> feedback(String feedback_type, String conversation_id) async {
    Response res = await Get.find<ApiProvider>().post(Api.feedback,
        {"conversation_id": conversation_id, "feedback_type": feedback_type});
    if (res.statusCode == 200) {
      if (res.body == null) {
        return;
      }
      final Map<String, dynamic> responseBody = res.body;
      if (responseBody['success']) {
        if (feedback_type == "like") {
          feedbackType.value = "like";
          // showSuccessToast('liked');
        } else {
          feedbackType.value = "dislike";
          // showSuccessToast('Feedback Sent');
        }
      }
    } else if (res.statusCode == 409) {
      showFailToast(res.statusText ?? S.of(Get.context!).networkConnectionLost);
    } else {
      showFailToast(S.of(Get.context!).systemBusyPleaseTryAgain);
    }
  }

  void copy(dynamic content) {
    Clipboard.setData(ClipboardData(text: content));
    showSuccessToast(S.of(Get.context!).copied);
  }
}
