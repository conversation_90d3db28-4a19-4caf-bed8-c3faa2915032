import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/module/chat/widget/chat_ai_slides_border_widget.dart';
/**
 * 数据格式
 * {
    "thought" : "我将为南京旅游创建一个专业的PPT演示文稿。首先需要收集南京的历史、景点、美食等旅游相关信息，然后根据收集的资料制作精美的幻灯片。整个过程分为信息收集和幻灯片制作两个主要阶段。",
    "title" : "南京旅游PPT制作计划",
    "steps" : [ {
    "agent_name" : "asset_preparer",
    "title" : "收集南京旅游资料",
    "description" : "收集南京的历史沿革、主要景点、特色美食、交通住宿等旅游相关信息，并确定适合南京旅游主题的配色方案。",
    "note" : "重点关注中山陵、夫子庙等著名景点，以及盐水鸭等特色美食的信息。"
    }, {
    "agent_name" : "slide_generator",
    "title" : "制作南京旅游PPT",
    "description" : "根据收集的资料和配色方案，制作包含封面、南京概览、必游景点、深度体验、美食推荐、旅游攻略和结束页的完整PPT。",
    "note" : "确保每张幻灯片内容精炼、图文并茂，突出南京的历史文化特色。"
    } ]
    }
 */

class ChatAiSlidesPlannerWidget extends StatefulWidget {
  final dynamic map;

  const ChatAiSlidesPlannerWidget({super.key, required this.map});

  @override
  State<ChatAiSlidesPlannerWidget> createState() =>
      _ChatAiSlidesPlannerWidgetState();
}

class _ChatAiSlidesPlannerWidgetState extends State<ChatAiSlidesPlannerWidget> {

  @override
  Widget build(BuildContext context) {
    final data = widget.map;
    final thought = data['thought'] ?? '';
    final title = data['title'] ?? '';
    final steps = data['steps'] ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题部分
        Container(
          margin:
          const EdgeInsets.only(left: 8, right: 8, top: 16, bottom: 12),
          alignment: Alignment.centerLeft,
          child: Text(
            title,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        // 思考过程部分
        if (thought.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Text(
              thought,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
              ),
            ),
          ),
        ],
        // 步骤列表部分
        if (steps.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...List.generate(steps.length, (index) {
                  final step = steps[index];
                  final stepTitle = step['title'] ?? '';
                  final stepDescription = step['description'] ?? '';
                  final stepNote = step['note'] ?? '';
                  final agentName = step['agent_name'] ?? '';

                  return Text(
                    "${index + 1}、${stepTitle} ${stepDescription} ${stepNote}",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
        // 底部间距，确保内容不被按钮遮挡
        SizedBox(height: 40.h),
      ],
    );
  }
}
