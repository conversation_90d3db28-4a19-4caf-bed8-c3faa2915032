import 'dart:collection';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../utils/bgCover.dart';
import '../../../utils/cmUtils.dart';
import '../../../widget/ComAppbar.dart';
import '../../../widget/ContainerBox.dart';
import '../../web/single_page_web/logic.dart';

class ChatWebviewWidget extends StatefulWidget {
  String? title;
  String? url;

  ChatWebviewWidget(this.url, {this.title = ''});

  @override
  State<ChatWebviewWidget> createState() => _ChatWebWidgetState();
}

class _ChatWebWidgetState extends State<ChatWebviewWidget> {
  final SinglePageWebLogic logic = Get.put(SinglePageWebLogic());

  InAppWebViewController? webViewController;
  InAppWebViewSettings settings = InAppWebViewSettings(
      isInspectable: false,
      mediaPlaybackRequiresUserGesture: false,
      transparentBackground: true,
      allowsInlineMediaPlayback: true,
      iframeAllow: "camera; microphone",
      iframeAllowFullscreen: true);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await InAppWebViewController.setWebContentsDebuggingEnabled(true);  // 启用调试
      //_initWebView();  // 初始化 WebView
    });
  }


  @override
  void dispose() {
    webViewController!.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false, // 规避底部布局被软键盘顶起
        body: Obx(() {
          return Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // _topLay(),
                logic.progress.value == 1
                    ? SizedBox()
                    : Container(
                  child: _createProgressBar(
                      logic.progress.value, context),
                ),
                Expanded(child: _webLay())
              ],
            ),
          );
        }));
  }

  Widget _topLay() {
    return ContainerBox(
      jBColors: [
        Color(0x99000000),
        Color(0x80000000),
        Color(0x4D000000),
        Color(0x33000000),
        Color(0x00000000),
        Color(0x00000000),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      height: CmUtils.getBarHeight(context) + 50.w,
      child: Container(),
    );
  }

  Widget _webLay() {
    return Container(
      color: Colors.white,
      child: InAppWebView(
        initialUrlRequest: URLRequest(url: WebUri('${widget.url}')),
        // initialUrlRequest: URLRequest(url: WebUri('https://www.baidu.com')),
        onReceivedServerTrustAuthRequest: (controller, challenge) async {
          //解决 handshake failed问题
          return ServerTrustAuthResponse(
              action: ServerTrustAuthResponseAction.PROCEED);
        },
        initialSettings: settings,
        onWebViewCreated: (InAppWebViewController controller) {
          webViewController = controller;
        },
        onDownloadStartRequest: (controller, url) async {},
        onScrollChanged: (InAppWebViewController controller, int x, int y) {},
        onLoadStop: (InAppWebViewController controller, WebUri? url) {},
        onProgressChanged: (InAppWebViewController controller, int progress) {
          logic.progress.value = progress / 100.0;
          setState(() {});
        },
        gestureRecognizers: [
          Factory(() => VerticalDragGestureRecognizer()),
        ].toSet(),
      ),
    );
  }

  /// 生成进度条组件，进度从0 ~ 1
  _createProgressBar(double progress, BuildContext context) {
    return LinearProgressIndicator(
      backgroundColor: Colors.white70.withOpacity(0),
      minHeight: 2.w,
      value: progress == 1.0 ? 0 : progress,
      valueColor: AlwaysStoppedAnimation<Color>(
          Color(0xFFB65CC5).withAlpha((255.0 * 0.9).round())),
    );
  }

  //关闭监听
  Future<bool> _goBack(BuildContext context) async {
    if (webViewController != null && await webViewController!.canGoBack()) {
      webViewController!.goBack();
      return false;
    }
    return true;
  }

  Future<bool> canBack() async {
    bool isBack = true;
    if (webViewController != null) {
      isBack = await _goBack(context);
    }
    if (isBack) {
      Get.back();
    }
    return false;
  }
}
