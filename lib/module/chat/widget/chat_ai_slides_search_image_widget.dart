import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/api/stream/stream_message_type_enum.dart';
import 'package:new_agnes/module/chat/widget/chat_bottom_tip_widget.dart';
import 'package:new_agnes/widget/image_viewer_widget.dart';

import '../mixin/chat_ai_slides_item_mixin.dart';

/**
 * 图片信息模型
 */
class ImageInfo {
  final String title;
  final String url;

  ImageInfo({required this.title, required this.url});
}

/**
 * Markdown图片解析器
 */
class MarkdownImageParser {
  static List<ImageInfo> parseImages(String markdownContent) {
    List<ImageInfo> images = [];

    // 匹配markdown图片格式: ![alt text](url)
    RegExp imageRegex = RegExp(r'!\[([^\]]*)\]\(([^)]+)\)');
    Iterable<RegExpMatch> matches = imageRegex.allMatches(markdownContent);

    for (RegExpMatch match in matches) {
      String title = match.group(1) ?? '';
      String url = match.group(2) ?? '';

      if (url.isNotEmpty) {
        images.add(ImageInfo(title: title, url: url));
      }
    }

    return images;
  }
}

/**
 * 主组件 - 解析markdown图片并显示为宫格布局
 */
class ChatAiSlidesSearchImageWidget extends StatefulWidget {
  final String content;
  final bool isComplete;
  final ScrollController? outerScrollController;

  const ChatAiSlidesSearchImageWidget(
      {super.key, required this.content, required this.isComplete, this.outerScrollController});

  @override
  State<ChatAiSlidesSearchImageWidget> createState() =>
      _ChatAiSlidesSearchImageWidgetState();
}

class _ChatAiSlidesSearchImageWidgetState
    extends State<ChatAiSlidesSearchImageWidget> with ChatAiSlidesItemMixin {
  List<ImageInfo> _images = [];
  final  FeedbackLogic feedbackLogic = FeedbackLogic();

  @override
  void initState() {
    super.initState();
    _parseImages();
  }

  @override
  void didUpdateWidget(ChatAiSlidesSearchImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.content != widget.content) {
      _parseImages();
    }
  }

  void _parseImages() {
    _images = MarkdownImageParser.parseImages(widget.content);
  }

  /// 显示图片浏览器
  void _showImageBrowser(ImageInfo clickedImage) {
    // 将ImageInfo转换为ImageItem
    final images = _images.map((imageInfo) => ImageItem.network(
      imageInfo.url,
      title: imageInfo.title,
      type: 'image',
    )).toList();
    
    // 找到当前点击的图片索引
    final currentIndex = _images.indexOf(clickedImage);
    
    ImageViewerDialog.show(
      context: context,
      images: images,
      initialIndex: currentIndex >= 0 ? currentIndex : 0,
      showDownloadButton: false,
      showExportButton: false,
      showBottomButtons: false,
      showPageIndicator: false,
      showDragIndicator: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    bool hasMoreImages = _images.length > 4;
    double minHeight = _images.length > 4 ? 114.h : 60.h;
    return Column(
      children: [
        if (_images.isNotEmpty) ...[
          appendExpandButton(
              Container(
                margin: const EdgeInsets.only(top: 8, bottom: 10),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: getBackgroundColor(StreamMessageTypeEnum.search_image,
                        "search_image"),
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: appendMaskToContainer(
                    ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: minHeight, // 设置最小高度
                      ),
                      child: wrapWithEdgeHandoff(
                        GridView.builder(
                          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                          physics: physics,
                          shrinkWrap: true, // 让GridView高度自适应内容
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            crossAxisSpacing: 8.w,
                            mainAxisSpacing: 8.h,
                            childAspectRatio: 4.0 / 3.0,
                          ),
                          itemCount: _images.length,
                          itemBuilder: (context, index) {
                            return _buildImageItem(_images[index]);
                          },
                        ),
                        widget.outerScrollController,
                      ),
                    ),
                    EdgeInsets.zero,
                    isMask: hasMoreImages
                ),
              ), () {
            changeExpandState(() {
              if (mounted) setState(() {});
            });
          },isExpand: hasMoreImages,minHeight: minHeight),
        ],
        if (widget.isComplete)
          ChatBottomTipWidget(
            isLike: true,
            isStepOn: false,
            feedbackLogic: feedbackLogic,
          ),
      ],
    );
  }

  Widget _buildImageItem(ImageInfo imageInfo) {
    return GestureDetector(
      onTap: () => _showImageBrowser(imageInfo),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: Stack(
            children: [
              // 图片
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: imageInfo.url,
                  fit: BoxFit.cover,
                  placeholder: (context, url) {
                    return Container(
                      color: Colors.white,
                      child: Center(
                        child: CircularProgressIndicator(
                          color: Color(0xFFFF84F4),
                          strokeWidth: 2,
                        ),
                      ),
                    );
                  },
                  errorWidget: (context, error, stackTrace) {
                    return Container(
                      color: Colors.white,
                      child: const Icon(
                        Icons.error_outline,
                        color: Color(0xFFFF84F4),
                        size: 32,
                      ),
                    );
                  },
                ),
              ),
              // 标题遮罩
              // if (imageInfo.title.isNotEmpty)
              //   Positioned(
              //     bottom: 0,
              //     left: 0,
              //     right: 0,
              //     child: Container(
              //       decoration: BoxDecoration(
              //         gradient: LinearGradient(
              //           begin: Alignment.topCenter,
              //           end: Alignment.bottomCenter,
              //           colors: [
              //             Colors.transparent,
              //             Colors.black.withOpacity(0.7),
              //           ],
              //         ),
              //       ),
              //       padding: EdgeInsets.all(8.w),
              //       child: Text(
              //         imageInfo.title,
              //         style: TextStyle(
              //           color: Colors.white,
              //           fontSize: 12.sp,
              //           fontWeight: FontWeight.w500,
              //         ),
              //         maxLines: 2,
              //         overflow: TextOverflow.ellipsis,
              //       ),
              //     ),
              //   ),
            ],
          ),
        ),
      ),
    );
  }
}
