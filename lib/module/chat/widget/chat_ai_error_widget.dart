import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatAiErrorWidget extends StatelessWidget {
  final String content;

  const ChatAiErrorWidget({super.key, required this.content});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin:  EdgeInsets.only(top: 20.w),
      width: double.infinity,
      decoration: BoxDecoration(
        color: Color(0xFFFFD3D3),
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Image.asset(
            'assets/images/ic_chat_error.png',
            width: 32,
            height: 32,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              content,
              style: TextStyle(fontSize: 14, color: Color(0xFFFE4D4D)),
            ),
          ),
        ],
      ),
    );
  }
}
