import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/widget/TapHigh.dart';
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';

import '../../../utils/bgCover.dart';
import '../../../utils/cmUtils.dart';
import '../../../widget/ComAppbar.dart';
import '../../../widget/ContainerBox.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../../../widget/HuiTan.dart';
import 'search_chat_item_widget.dart';

class PdfPreviewWidget extends StatefulWidget {
  String filePath;
  int type; //1 pdf  2 markDown
  String content; //内容
  PdfPreviewWidget(this.filePath, {this.type = 1, this.content = ''});

  @override
  State<PdfPreviewWidget> createState() => _PdfPreviewWidgetState();
}

class _PdfPreviewWidgetState extends State<PdfPreviewWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return buildMineBg(
        child: Scaffold(
            backgroundColor: Colors.transparent,
            resizeToAvoidBottomInset: false, // 规避底部布局被软键盘顶起
            body: Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _topLay(),
                  widget.type == 1
                      ? Expanded(
                          child: Container(
                          child: SfPdfViewer.file(
                            File(widget.filePath),
                            canShowSignaturePadDialog: false,
                            canShowHyperlinkDialog: false,
                            canShowPaginationDialog: false,
                            canShowPasswordDialog: false,
                            canShowTextSelectionMenu: false,
                            canShowScrollHead: false,
                          ),
                        ))
                      : Expanded(
                          child: Container(
                              padding: EdgeInsets.only(
                                  left: 20.w, right: 20.w, bottom: 10.w),
                              child: ScrollConfiguration(
                                behavior: NoShadowScrollBehavior(),
                                child: SingleChildScrollView(
                                  child: Column(
                                    children: [
                                      SearchChatItemWidget(
                                          content: widget.content),
                                    ],
                                  ),
                                ),
                              )))
                ],
              ),
            )));
  }

  Widget _topLay() {
    return ContainerBox(
      jBColors: [
        Color(0x99000000),
        Color(0x80000000),
        Color(0x4D000000),
        Color(0x33000000),
        Color(0x00000000),
        Color(0x00000000),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      height: CmUtils.getBarHeight(context) + 50.w,
      child: ComAppBar(
        context,
        '',
        backgroundColor: Colors.transparent,
        onPop: () {
          Get.back();
        },
        actions: [
          GestureDetector(
            onTap: () async {
              List<String> split = widget.filePath.split('/');
              // 分享文件
              await Share.shareXFiles(
                [XFile(widget.filePath)], // 支持多个文件
                text: '${split[split.length - 1]}', // 可选：附加文本
                subject: '', // 可选：主题（邮件等）
              );
            },
            child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.only(right: 15.w, left: 15.w, top: 4.w),
              child: Image.asset(
                "assets/images/ic_ppt_share.png",
                width: 24.w,
                height: 24.h,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
