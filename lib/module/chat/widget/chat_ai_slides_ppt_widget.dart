import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/Api.dart';

import '../page/chat_ai_slides_ppt_page.dart';
import 'chat_bottom_tip_widget.dart';

class ChatAiSlidesPptWidget extends StatefulWidget {
  final String content;
  final bool isComplete;
  final List<dynamic>? slidesData; // 支持原始 JSON 数据
  final String conversationId;

  const ChatAiSlidesPptWidget({
    super.key, 
    required this.content, 
    required this.isComplete,
    required this.conversationId,
    this.slidesData,
  });

  @override
  State<ChatAiSlidesPptWidget> createState() => _ChatAiSlidesPptWidgetState();
}

class _ChatAiSlidesPptWidgetState extends State<ChatAiSlidesPptWidget> {
  final  FeedbackLogic feedbackLogic = FeedbackLogic();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        //TODO 应该是下个页面有什么东西导致页面闪了一下
        Get.to(ChatAiSlidesPptPage(
          url: "${Api.baseUrl}/share/${widget.conversationId}",
          title: widget.content,
          id: widget.conversationId,
        ));
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 160.w,
            height: 120.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Column(
              children: [
                Container(
                  height: 94.h,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                      color: Color(0xFFFF5555),
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10.r),
                          topRight: Radius.circular(10.r))),
                  child: Center(
                    child: Text(
                      widget.content,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white),
                    ),
                  ),
                ),
                Container(
                    height: 26.h,
                    alignment: Alignment.centerRight,
                    margin: EdgeInsets.only(right: 8.w),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(10.r),
                            bottomRight: Radius.circular(10.r))),
                    child: Text("Click to open",
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Color(0xFF838384),
                          fontWeight: FontWeight.w400,
                        )))
              ],
            ),
          ),
          if (widget.isComplete)
            ChatBottomTipWidget(
              isLike: true,
              isStepOn: false,
              isPPt: true,
              feedbackLogic: feedbackLogic,
            ),
        ],
      ),
    );
  }
}
