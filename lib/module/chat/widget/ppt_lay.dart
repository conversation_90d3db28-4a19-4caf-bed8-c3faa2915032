import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';

Widget JsonPPtLay(BuildContext context, dynamic jsonContent,
    {bool shrink = true,
    bool fullScreen = false,
    ScrollController? outerScrollController}) {
  if (jsonContent['slides'] == null) {
    return const SizedBox();
  }
  List<dynamic> slidesLists = jsonContent['slides'];
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 1),
    child: GradientBorderContainer.single(
      strokeWidth: 1,
      gradient: LinearGradient(
        colors: [
          Color(0xFF7253FA),
          Color(0xFFFF3BDF),
          Color(0xFF5E57FE),
        ],
        stops: [0.0, 0.32, 1.0],
        begin: Alignment.bottomLeft,
        end: Alignment.topRight,
        tileMode: TileMode.decal,
      ),
      borderRadius: BorderRadius.circular(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
                color: Color(0x8F604276),
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(10))),
            child: Row(
              children: [
                Text('VIEW',
                    style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 16.sp,
                        color: Color(0xFFFF3BDF))),
                Spacer(),
                Text(
                  "${slidesLists.length}  pages",
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
                SizedBox(
                  width: 16,
                ),
                GestureDetector(
                  onTap: () {
                    if (shrink) {
                      showUnfoldDialog(context, jsonContent);
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  child: Image.asset(
                      shrink
                          ? "assets/icon/icon_unfold.webp"
                          : "assets/icon/icon_shrink.webp",
                      width: 20,
                      height: 20),
                )
              ],
            ),
          ),
          Container(
            constraints: BoxConstraints(
              maxHeight:
                  fullScreen ? MediaQuery.of(context).size.height - 212 : 508,
            ),
            child: wrapWithEdgeHandoff(
                SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      children: [
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: slidesLists.length,
                          itemBuilder: (context, index) {
                            final slideItem = slidesLists[index];
                            if (index == 0) {
                              if (slideItem['index'] != 1) {
                                return _buildVChapterItem(
                                    context, slideItem, index);
                              }
                              return _buildCoverItem(context, slideItem, index,
                                  slidesLists.length);
                            } else {
                              return _buildVChapterItem(
                                  context, slideItem, index);
                            }
                          },
                        )
                      ],
                    ),
                  ),
                ),
                outerScrollController),
          )
        ],
      ),
    ),
  );
}

Widget _buildCoverItem(BuildContext context, Map item, int index, int length) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        children: [
          Image.asset(
            "assets/images/ic_process_right.webp",
            width: 16,
            height: 16,
          ),
          SizedBox(
            width: 12,
          ),
          Text(
            "P1  cover",
            style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white,
                fontWeight: FontWeight.w600),
          )
        ],
      ),
      SizedBox(
        height: 16,
      ),
      Padding(
        padding: EdgeInsets.only(left: 28),
        child: Text(
          item.containsKey("subtitle") ? item['subtitle'] : "",
          style: TextStyle(color: Colors.white, fontSize: 16.sp),
        ),
      ),
      SizedBox(
        height: 12,
      ),
      Padding(
        padding: EdgeInsets.only(left: 32),
        child: Text(
          "present:${item['presenter']}",
          style: TextStyle(color: Color(0xFF988B9A), fontSize: 16.sp),
        ),
      ),
      SizedBox(
        height: 16,
      ),
      if (length > 1) ...{
        Divider(
          color: Color(0xFF988B9A),
          height: 1,
        ),
        SizedBox(
          height: 16,
        ),
      }
    ],
  );
}

Widget _buildVChapterItem(BuildContext context, dynamic item, int index) {
  var sectionsList = item['sections'];
  // if(sectionsList == null){
  //   return const SizedBox();
  // }
  return Column(
    mainAxisSize: MainAxisSize.min,
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        children: [
          Image.asset(
            "assets/images/ic_process_right.webp",
            width: 16,
            height: 16,
          ),
          SizedBox(
            width: 12,
          ),
          Text(
            "P${item['index']}  chapter1",
            style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white,
                fontWeight: FontWeight.w600),
          )
        ],
      ),
      SizedBox(
        height: 16,
      ),
      Padding(
        padding: EdgeInsets.only(left: 28),
        child: Text(
          item['title'],
          style: TextStyle(color: Colors.white, fontSize: 16.sp),
        ),
      ),
      SizedBox(
        height: 12,
      ),
      sectionsList == null
          ? const SizedBox()
          : Column(
              children: [
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: sectionsList.length,
                  itemBuilder: (context, index) {
                    final sectionItem = sectionsList[index];
                    return _buildSectionItem(context, sectionItem, index);
                  },
                )
              ],
            ),
      // Padding(
      //   padding: EdgeInsets.only(left: 32),
      //   child: Text(
      //     item['sections'][0],
      //     style: TextStyle(color: Color(0xFF988B9A), fontSize: 16),
      //   ),
      // )
    ],
  );
}

Widget _buildSectionItem(BuildContext context, String item, int index) {
  return Container(
    padding: EdgeInsets.all(12),
    margin: EdgeInsets.only(bottom: 8, left: 28),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.all(Radius.circular(8)),
      color: Color(0x4D424B76),
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "content ${index + 1}",
          style: TextStyle(
              color: Color(0xFF8F6CFF),
              fontSize: 16.sp,
              fontWeight: FontWeight.w600),
        ),
        SizedBox(
          height: 8,
        ),
        Text(
          item,
          style: TextStyle(color: Colors.white, fontSize: 16.sp),
        ),
      ],
    ),
  );
}

void showUnfoldDialog(BuildContext context, dynamic jsonContent) {
  showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: "PPT Info",
    // 半透明遮罩
    pageBuilder: (context, animation, secondaryAnimation) {
      return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 0, sigmaY: 0), // 毛玻璃模糊
        child: Center(
          child: Material(
            color: Color(0xCD201034),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  JsonPPtLay(context, jsonContent,
                      shrink: false, fullScreen: true),
                ],
              ),
            ),
          ),
        ),
      );
    },
    transitionDuration: const Duration(milliseconds: 200),
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return FadeTransition(
        opacity: animation,
        child: ScaleTransition(
          scale: CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutBack,
          ),
          child: child,
        ),
      );
    },
  );
}

Widget wrapWithEdgeHandoff(
    Widget scrollable, ScrollController? outerScrollController) {
  return NotificationListener<ScrollNotification>(
    onNotification: (notification) {
      final outer = outerScrollController;
      if (outer == null || !outer.hasClients) return false;

      if (notification is OverscrollNotification) {
        final position = outer.position;
        final double target = (position.pixels + notification.overscroll)
            .clamp(position.minScrollExtent, position.maxScrollExtent);
        if (target != position.pixels) {
          position.jumpTo(target);
          return true;
        }
      }
      if (notification is ScrollUpdateNotification) {
        final delta = notification.scrollDelta;
        if (delta == null) return false;
        final metrics = notification.metrics;
        final bool atTop = metrics.pixels <= metrics.minScrollExtent;
        final bool atBottom = metrics.pixels >= metrics.maxScrollExtent;
        final bool draggingUpwards = delta < 0;
        final bool draggingDownwards = delta > 0;

        if ((atTop && draggingUpwards) || (atBottom && draggingDownwards)) {
          final position = outer.position;
          final double target = (position.pixels + delta)
              .clamp(position.minScrollExtent, position.maxScrollExtent);
          if (target != position.pixels) {
            position.jumpTo(target);
            return true;
          }
        }
      }
      return false;
    },
    child: scrollable,
  );
}
