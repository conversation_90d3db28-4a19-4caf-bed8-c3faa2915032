import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatAiSlidesBorderWidget extends StatefulWidget {
  final Widget child;

  const ChatAiSlidesBorderWidget({super.key, required this.child});

  @override
  State<ChatAiSlidesBorderWidget> createState() =>
      _ChatAiSlidesBorderWidgetState();
}

class _ChatAiSlidesBorderWidgetState extends State<ChatAiSlidesBorderWidget> {
  final Random _random = Random();
  bool _isExpanded = false; // 默认收起状态

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 8, bottom: 12),
      child: Stack(
        children: [
          // 主容器
          Container(
            constraints: BoxConstraints(
              minHeight: 96.h,
              maxHeight: _isExpanded ? 508.h : 96.h,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  completelyRandomColor,
                  completelyRandomColor.withOpacity(0.07),
                ],
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Stack(
              children: [
                // 内容区域
                SingleChildScrollView(
                  physics: _isExpanded
                      ? const AlwaysScrollableScrollPhysics()
                      : const NeverScrollableScrollPhysics(),
                  child: IntrinsicHeight(
                    child: widget.child,
                  ),
                ),
                // 底部渐变遮罩（收起状态时显示）
                if (!_isExpanded)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 40.h,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            completelyRandomColor.withOpacity(0.9),
                          ],
                        ),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(10),
                          bottomRight: Radius.circular(10),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // 展开收起按钮 - 位于边框上
          Positioned(
            left: 0.w,
            bottom: 0.h,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              child: Container(
                width: 24.w,
                height: 24.h,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  _isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.white,
                  size: 16.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color get completelyRandomColor {
    return Color.fromRGBO(
      _random.nextInt(256), // Red
      _random.nextInt(256), // Green
      _random.nextInt(256), // Blue
      1,
    );
  }
}
