import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/module/chat/mixin/chat_ai_slides_item_mixin.dart';

import '../../../api/stream/stream_message_type_enum.dart';
import 'chat_bottom_tip_widget.dart';

class ChatAiSlidesSearchWebWidget extends StatefulWidget {
  final String content;
  final bool isComplete;
  final ScrollController? outerScrollController;

  const ChatAiSlidesSearchWebWidget(
      {super.key, required this.content, required this.isComplete, this.outerScrollController});

  @override
  State<ChatAiSlidesSearchWebWidget> createState() =>
      _ChatAiSlidesSearchWebWidgetState();
}

class _ChatAiSlidesSearchWebWidgetState
    extends State<ChatAiSlidesSearchWebWidget> with ChatAiSlidesItemMixin {
  final  FeedbackLogic feedbackLogic = FeedbackLogic();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        appendExpandButton(
            Container(
              margin: const EdgeInsets.only(top: 8, bottom: 10),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: getBackgroundColor(
                      StreamMessageTypeEnum.search_web, "search_web"),
                ),
                border: Border.all(
                    color: getBorderColor(
                        StreamMessageTypeEnum.search_web, "search_web"),
                    width: 0.5),
                borderRadius: BorderRadius.circular(10),
              ),
              child: appendMaskToContainer(
                  wrapWithEdgeHandoff(
                    SingleChildScrollView(
                      primary: false,
                      physics: physics,
                      child: buildMarkdownWidget(widget.content, "",widget.outerScrollController),
                    ),
                    widget.outerScrollController,
                  ),
                  EdgeInsets.symmetric(horizontal: 8, vertical: 8)),
            ), () {
          changeExpandState(() {
            if (mounted) setState(() {});
          });
        }),
        if (widget.isComplete)
          ChatBottomTipWidget(
            isLike: true,
            isStepOn: false,
            feedbackLogic: feedbackLogic,
          ),
      ],
    );
  }
}
