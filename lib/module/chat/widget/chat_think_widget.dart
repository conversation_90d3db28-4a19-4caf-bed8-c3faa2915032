import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/stream/stream_message_type_enum.dart';
import 'package:new_agnes/module/chat/widget/scroll_text.dart';

import '../../../api/stream/render/JsonChunkFinder.dart';
import '../../../dialog/process_dialog.dart';
import '../../../generated/l10n.dart';
import '../../../widget/ContainerBox.dart';
import '../../../widget/TapHigh.dart';
import '../logic.dart';
import '../model/planning_model.dart';

class ChatThinkWidget extends StatefulWidget {
  int index;
  var onResult;
  ChatThinkWidget(
    this.index,
    this.onResult,
  );

  @override
  State<ChatThinkWidget> createState() => _ChatThinkWidgetState();
}

class _ChatThinkWidgetState extends State<ChatThinkWidget>
    with SingleTickerProviderStateMixin {
  ChatLogic chatLogic = Get.find<ChatLogic>();
  Timer? timer;
  Rx<int> time = 0.obs;
  RxString planContent = ''.obs;
  String planingData = '';
  Rx<bool> isShowLoading = true.obs;
  late AnimationController _controller;
  bool isShowTime = true;

  @override
  void dispose() {
    if (timer != null) {
      timer!.cancel();
    }
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 1500),
    )..repeat();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (chatLogic.renderManager.workflowRx.value.renderList
              .value[widget.index].isHistory) {
        isShowLoading.value = false;
        isShowTime = false;
      } else {
        isShowTime = true;
        timer = Timer.periodic(Duration(seconds: 1), (Timer timer) {
          time.value++;
        });
      }
      getPlaningData();
    });
    super.initState();
  }

  //获取deepResearch 的Planner 数据
  void getPlaningData() {
    chatLogic.renderManager.workflowRx.value.renderList.value[widget.index]
        .thinkTasks
        .listen((render) {
      if (render.length > 0 && render[0].content.value.isNotEmpty) {}
      if (planContent.value.isEmpty &&
          render.length > 0 &&
          render[0].agentName == 'planner') {
        String data = '${render[0].content.value}';
        List<String> split1 = []; //split1[0]  json数据
        if (data.contains(JsonChunkFinder.START_MARKER)) {
          List<String> split0 = data.split(JsonChunkFinder.START_MARKER);
          if (split0.length > 1 &&
              split0[1].contains(JsonChunkFinder.END_MARKER)) {
            split1 = split0[1].split(JsonChunkFinder.END_MARKER);
          }
        }
        if (split1.length > 0 && render[0].agentName == 'planner') {
          try {
            PlanningModel planningModel =
                PlanningModel.fromJson(jsonDecode(split1[0]));
            planingData =
                planingData + 'Step1：Planning' + '${planningModel.title}';
            if (planningModel.steps!.length > 0) {
              planningModel.steps!.forEach((stepsModel) {
                planingData = planingData +
                    '${stepsModel!.title}' +
                    '${stepsModel.description}';
              });
            }
            planContent.value = planingData;
          } catch (e) {
            debugPrint('出错了${e}');
          }
        }
      }
      render.forEach((renderData) {
        if (renderData.type == StreamMessageTypeEnum.reporter) {
          if (timer != null) {
            timer!.cancel();
          }
          isShowLoading.value = false;
          _controller.stop();
          _controller.dispose();
          if(widget.onResult!=null){
            widget.onResult();
          }
          setState(() {});
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: _thinkLay(),
    );
  }

  /**
   * deep research Think 布局
   */
  Widget _thinkLay({onResult}) {
    return TapHigh(onTap: () {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        // 允许全屏展开
        backgroundColor: Colors.transparent,
        barrierColor: Colors.transparent,
        builder: (context) {
          return DraggableScrollableSheet(
            expand: false,
            // 是否允许扩展到全屏
            initialChildSize: 0.6,
            // 初始高度（屏幕高度的50%）
            minChildSize: 0.2,
            // 最小高度（屏幕高度的25%）
            maxChildSize: 0.8,
            // 最大高度（屏幕高度的90%）
            builder: (context, scrollController) {
              return ProcessDialog(widget.index, scrollController);
            },
          );
        },
      );
    }, child: Obx(() {
      return ContainerBox(
          margin: EdgeInsets.only(top: 20.w),
          padding: EdgeInsets.only(
            left: 10.0.w,
            right: 16.0.w,
          ),
          jBColors: isShowLoading.value
              ? [
                  Color(0xFF9BFBC6),
                  Color(0xFF87C0F5),
                  Color(0xFFC8BFFF),
                  Color(0xFFEFB5E8)
                ]
              : null,
          borderColor: isShowLoading.value ? null : Color(0xFF988B9A),
          boxColor: isShowLoading.value ? null : Colors.transparent,
          borderWith: isShowLoading.value ? 0.w : 1.w,
          radius: 10,
          child: Container(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Obx(() {
                  return isShowLoading.value
                      ? _buildLoadingIndicator()
                      // Container(
                      //         margin: EdgeInsets.only(right: 15.w),
                      //         child: Image.asset(
                      //           'assets/images/ic_chart_zhuan.webp',
                      //           width: 20.w,
                      //           height: 20.w,
                      //         ),
                      //       )
                      : SizedBox();
                }),
                Expanded(
                    child: Container(
                  margin: EdgeInsets.only(left: 10.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 10.w,
                      ),
                      Obx(() {
                        return Text(
                          isShowTime
                              ? '${S.of(Get.context!).researchForZdSeconds(time.value)}'
                              : '${S.of(Get.context!).deepResearch}',
                          style: TextStyle(
                              color: isShowLoading.value
                                  ? Color(0xFF0D0D0D)
                                  : Colors.white,
                              fontSize: 16.sp,
                              height: 1.4),
                        );
                      }),
                      Obx(() {
                        return
                                isShowLoading.value
                            ? ScrollTextLay(
                                '${planContent.value}', () {}, widget.index)
                            : SizedBox(
                                height: 10.w,
                              );
                      }),
                    ],
                  ),
                )),
                Container(
                  margin: EdgeInsets.only(left: 4.w),
                  child: Image.asset(
                    isShowLoading.value
                        ? 'assets/images/ic_chart_right.webp'
                        : 'assets/images/ic_right_white.webp',
                    width: 20.w,
                    height: 20.w,
                  ),
                ),
              ],
            ),
          ));
    }));
  }

  // 过渡加载框
  Widget _buildLoadingIndicator() {
    return AnimatedBuilder(
      animation: _controller,
      builder: (_, __) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            RotationTransition(
              turns: _controller,
              child: Image.asset(
                'assets/images/ic_chart_zhuan.webp',
                width: 20.w,
                height: 20.w,
              ),
            ),
          ],
        );
      },
    );
  }
}
