import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:new_agnes/module/chat/widget/chat_plan_image_widget.dart';
import 'package:get/get.dart' as getx;
import 'package:new_agnes/module/chat/widget/webviewPage.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/widget/TapHigh.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../api/Api.dart';
import '../../../generated/l10n.dart';
import '../../../utils/toastUtil.dart';
import '../../mine/mine_info/logic.dart';
import '../../web/single_page_web/page.dart';
import '../logic.dart';

final List<Map<String, dynamic>> shareIcons = [
  {
    'icon': 'assets/icon/icon_x.webp',
    'type': "x_share",
  },
  {
    'icon': 'assets/icon/icon_in.webp',
    'type': "in_share",
  },
  {
    'icon': 'assets/icon/icon_f.webp',
    'type': "facebook",
  },
  {
    'icon': 'assets/icon/icon_p.webp',
    'type': "p_share",
  },
  {
    'icon': 'assets/icon/icon_tiktok.webp',
    'type': "tiktok",
  },
  {
    'icon': 'assets/icon/icon_share_m.webp',
    'type': "m_share",
  },
  {
    'icon': 'assets/icon/icon_sms.webp',
    'type': "sms",
  },
];

class ShareWidget extends StatefulWidget {
  final int shareType; //1:image 2:web
  String shareContent;

  ShareWidget(this.shareType, {this.shareContent = ""});

  @override
  State<ShareWidget> createState() => _ShareWidgetState();
}

class _ShareWidgetState extends State<ShareWidget>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  var chatPlanImageLogic = Get.find<ChatPlanImagLogic>();
  final ChatLogic chatLogic = Get.find<ChatLogic>();
  final MineInfoLogic mineInfoLogic = Get.find<MineInfoLogic>();

  final FocusNode _focusNode = FocusNode();
  final TextEditingController _imageGenerateController =
      TextEditingController();
  double _lastKeyboardHeight = 0;
  late AnimationController animationController;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    animationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 1200));
    // chatPlanImageLogic.imageGenerateText.value = "Excited to share a new piece l created using Agnes-it's titled \"${chatLogic.title}\"Would loveyour thoughts!";
    // if (widget.shareType == 1) {
    //   chatPlanImageLogic.getImageData(
    //       widget.shareContent, chatLogic.conversationId, false);
    // } else if (widget.shareType == 2) {
    //   chatPlanImageLogic.getWebData(chatLogic.conversationId);
    // }
  }

  @override
  void didChangeMetrics() {
    final currentHeight = WidgetsBinding
        .instance.platformDispatcher.views.first.viewInsets.bottom;
    if (_lastKeyboardHeight > 0 && currentHeight == 0) {
      _focusNode.unfocus();
      chatPlanImageLogic.isEdit.value = false;
    }
    _lastKeyboardHeight = currentHeight;
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top:  widget.shareType==2?0.h:30.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.shareType==2?SizedBox(): Text(
            widget.shareType == 2 ? 'web page' : 'Image Post',
            style: TextStyle(
                color: Colors.white,
                fontSize: 20.sp,
                fontWeight: FontWeight.w600),
          ),
          widget.shareType==2?SizedBox():SizedBox(
            height: 16.h,
          ),
          getShareWidget()
        ],
      ),
    );
  }

  Widget getShareWidget() {
    print("getShareWidget:" + widget.shareType.toString());
    if (widget.shareType == 1) {
      return imageShare();
    } else if (widget.shareType == 2) {
      return webShare();
    } else {
      return Column(
        children: [
          imageShare(),
          SizedBox(
            height: 64.h,
          ),
          Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'web page',
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w600),
                ),
                SizedBox(
                  height: 16.h,
                ),
                webShare()
              ],
            ),
          )
        ],
      );
    }
  }

  Widget imageShare() {
    _imageGenerateController.text = chatPlanImageLogic.imageGenerateText.value;
    _imageGenerateController.addListener(() {
      chatPlanImageLogic.imageGenerateText.value =
          _imageGenerateController.text;
    });
    return Container(
      height: 505.h,
      padding: EdgeInsets.all(15),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20)),
          color: Colors.white,
          image: DecorationImage(
            image: AssetImage("assets/images/bg_share.webp"),
            fit: BoxFit.cover,
          )),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(9),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    mineInfoLogic.userInfoModel.value.avatarUrl != null
                        ? ClipOval(
                            child: Image.network(
                              mineInfoLogic
                                  .userInfoModel.value.avatarUrl!.value,
                              width: 40.w,
                              height: 40.w,
                              fit: BoxFit.cover,
                            ),
                          )
                        : Image.asset(
                            "assets/images/ic_default_user_avatar.webp",
                            width: 40.w,
                            height: 40.w,
                          ),
                    SizedBox(
                      width: 16.w,
                    ),
                    Text(
                        mineInfoLogic.userInfoModel.value.username != null
                            ? mineInfoLogic.userInfoModel.value.username!.value
                                        .length >
                                    10
                                ? '${mineInfoLogic.userInfoModel.value.username!.value.substring(0, 10)}...'
                                : mineInfoLogic
                                    .userInfoModel.value.username!.value
                            : "   ",
                        style: TextStyle(
                            fontSize: 16.sp, fontWeight: FontWeight.w600)),
                  ],
                ),
                SizedBox(
                  height: 8.h,
                ),
                Obx(() => chatPlanImageLogic.isEdit.value
                    ? Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            border: Border.all(
                                width: 1.w, color: Color(0xFFDDDFDD))),
                        child: TextField(
                          strutStyle: StrutStyle(height: 1.5.h),
                          focusNode: _focusNode,
                          controller: _imageGenerateController,
                          textAlignVertical: TextAlignVertical.center,
                          style: TextStyle(
                              fontSize: 16.sp, color: Color(0xFF323431)),
                          maxLines: 3,
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(vertical: 0),
                            isDense: true,
                            hintStyle: TextStyle(
                                fontSize: 16.sp, color: Color(0xFF988B9A)),
                          ),
                        ),
                      )
                    : Padding(
                        padding: EdgeInsets.only(top: 10.h),
                        child: Text.rich(
                          TextSpan(
                            style: TextStyle(
                                color: Color(0xFF323431), fontSize: 16.sp),
                            children: [
                              TextSpan(
                                  text: chatPlanImageLogic
                                      .imageGenerateText.value),
                              WidgetSpan(child: SizedBox(width: 8.w)),
                              WidgetSpan(
                                child: GestureDetector(
                                  onTap: () {
                                    chatPlanImageLogic.isEdit.value = true;
                                    _focusNode.requestFocus();
                                  },
                                  child: Image.asset(
                                    "assets/icon/icon_edit.webp",
                                    width: 15.w,
                                    height: 14.h,
                                  ),
                                ),
                                alignment: PlaceholderAlignment.middle,
                              ),
                            ],
                          ),
                        ),
                      )),
                SizedBox(
                  height: 2.h,
                ),
                GestureDetector(
                  onTap: () {
                    Get.to(SingleWidgetWebWidget(
                      chatPlanImageLogic.shareUrl,
                      title: chatLogic.title.value,
                    ));
                  },
                  child: Text(
                    "${Api.baseUrl}/share?conversationid=${chatLogic.conversationId}",
                    style: TextStyle(color: Color(0xFF5E57FE), fontSize: 16.sp),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                Obx(
                  () => chatPlanImageLogic.imageData.value.isNotEmpty
                      ? Container(
                          height: 201.h,
                          decoration: BoxDecoration(
                              color: chatPlanImageLogic.imageData.value == "500"
                                  ? Colors.transparent
                                  : Color(0xFFF3EEEB),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(14))),
                          child: chatPlanImageLogic.imageData.value == "500"
                              ? Center(
                                  child: Image.asset(
                                    "assets/images/icon_share_empty.webp",
                                    height: 201.h,
                                    width: 201.w,
                                  ),
                                )
                              : ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: Image.network(
                                    chatPlanImageLogic.imageData.value,
                                    height: 201.h,
                                    width: 313.w,
                                    fit: BoxFit.fill,
                                  ),
                                ))
                      : Container(
                          height: 201,
                        ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 12,
          ),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: shareIcons.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    String shareContent =
                        chatPlanImageLogic.imageGenerateText.value;
                    String shareUrl =
                        "${Api.baseUrl}/share?conversationid=${chatLogic.conversationId}";
                    String encodeShareUrl = Uri.encodeQueryComponent(shareUrl);
                    String encodeShareContent = Uri.encodeFull(shareContent);
                    switch (index) {
                      case 0:
                        String twitterUrl =
                            "https://twitter.com/intent/tweet?text=";
                        String completeUrl = twitterUrl +
                            encodeShareContent +
                            "&url=" +
                            encodeShareUrl;
                        launchExternalBrowser(completeUrl);
                        break;
                      case 1:
                        String linkUrl =
                            "https://www.linkedin.com/shareArticle?forceDesktop=true&mini=true&url=";
                        String completeUrl = linkUrl +
                            encodeShareUrl +
                            "&title=" +
                            encodeShareContent;
                        launchExternalBrowser(completeUrl);
                        break;
                      case 2:
                        String facebookUrl =
                            "https://www.facebook.com/sharer/sharer.php?u=";
                        String completeUrl = facebookUrl + encodeShareUrl;
                        launchExternalBrowser(completeUrl);
                        break;
                      default:
                        Clipboard.setData(ClipboardData(
                            text: chatPlanImageLogic.imageGenerateText.value +
                                "\n" +
                                chatPlanImageLogic.shareUrl));
                        showSuccessToast(S.of(Get.context!).copied);
                        break;
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.only(right: 16.w),
                    child: Image.asset(
                      shareIcons[index]['icon'],
                      width: 32.w,
                      height: 32.w,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void launchExternalBrowser(String url) async {
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
    } else {
      throw Exception('无法打开链接: $url');
    }
  }

  Widget webShare() {
    return Obx((){
      return Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10.h,),
            chatPlanImageLogic.webData.value.isEmpty?Lottie.asset('assets/json/get_web_url_loading.json',
                height: 24.h, width: 24.w,fit: BoxFit.fill):
            Container(
              margin: EdgeInsets.only(top: 6.w),
              child: Row(
                children: [
                  Expanded(child:  Container(
                    child: Text(
                      'https://${chatPlanImageLogic.webData.value}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(color: Color(0xFF00FFFF), fontSize: 16.sp),
                    ),
                  )),
                  SizedBox(width: 26.w,),
                  TapHigh(
                      onTap: (){
                        CmUtils.fuZhi('https://${chatPlanImageLogic.webData.value}');
                      },
                      child: Image.asset('assets/images/web_url_copy.webp',width: 20.w,height: 20.w,)
                  ),

                ],
              ),
            ),
            SizedBox(height:chatPlanImageLogic.webData.value.isEmpty?0.w: 16.w,),
            chatPlanImageLogic.webData.value.isEmpty?SizedBox():Container(
                height: 36.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Colors.white,
                  gradient: LinearGradient(
                    colors: [
                      Color(0xFFFF91EE),
                      Color(0xFFFF3BDF),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 14.w),
                  child: TextButton(
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () {
                      launchExternalBrowser(
                          "https://${chatPlanImageLogic.webData.value}");
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          "Proceed to",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(
                          width: 6.w,
                        ),
                        Image.asset(
                          "assets/icon/icon_proceed.webp",
                          width: 12.w,
                          height: 12.w,
                        ),
                      ],
                    ),
                  ),
                ))
          ],
        ),
      );
    });

    return Obx((){
      return Container(
        height: 505.h,
        padding: EdgeInsets.all(24),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(20)),
            color: Colors.white,
            image: DecorationImage(
              image: AssetImage("assets/images/bg_share.webp"),
              fit: BoxFit.cover,
            )),
        child: chatPlanImageLogic.webData.value.isEmpty
            ? _buildLoadingIndicator():Stack(
          children: [
            ChatWebviewWidget("https:${chatPlanImageLogic.webData.value}"),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                  height: 36.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.white,
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFFFF91EE),
                        Color(0xFFFF3BDF),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 14.w),
                    child: TextButton(
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: () {
                        launchExternalBrowser(
                            "https:${chatPlanImageLogic.webData.value}");
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "Proceed to",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(
                            width: 6.w,
                          ),
                          Image.asset(
                            "assets/icon/icon_proceed.webp",
                            width: 12.w,
                            height: 12.w,
                          ),
                        ],
                      ),
                    ),
                  )),
            ),
          ],
        ),
      );
    });
  }

  // 过渡加载框
  Widget _buildLoadingIndicator() {
    return SpinKitCircle(
      color: Color(0xCFFC944E9),
      size: 60,
      controller: animationController,
    );
  }
}
