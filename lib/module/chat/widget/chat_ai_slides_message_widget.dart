import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:new_agnes/api/stream/stream_message_type_enum.dart';
import 'package:new_agnes/utils/logger_utils.dart';

import '../mixin/chat_ai_slides_item_mixin.dart';
import 'chat_bottom_tip_widget.dart';

class ChatAiSlidesMessageWidget extends StatefulWidget {
  final bool isFold;
  final String content;
  final String handOff;
  final String agentName;
  final bool isComplete;
  final StreamMessageTypeEnum type;
  final bool pptCompletedText;
  final ScrollController? outerScrollController;

  const ChatAiSlidesMessageWidget(
      {super.key,
      required this.isFold,
      required this.content,
      required this.handOff,
      required this.agentName,
      required this.isComplete,
      required this.pptCompletedText,
      required this.type,
      this.outerScrollController});

  @override
  State<ChatAiSlidesMessageWidget> createState() =>
      _ChatAiSlidesMessageWidgetState();
}

class _ChatAiSlidesMessageWidgetState extends State<ChatAiSlidesMessageWidget>
    with ChatAiSlidesItemMixin {
  final  FeedbackLogic feedbackLogic = FeedbackLogic();

  Widget _wrapWithEdgeHandoff(Widget scrollable) =>
      wrapWithEdgeHandoff(scrollable, widget.outerScrollController);

  @override
  Widget build(BuildContext context) {
    final List<Widget> children = [
      _buildMarkdownContent(),
    ];
    if (widget.isComplete) {
      children.add(_buildBottomTip());
    }
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children);
  }

  Widget _buildMarkdownContent() {
    if (widget.agentName == "coordinator" || widget.pptCompletedText || widget.isComplete) {
      return Container(
        margin: const EdgeInsets.only(top: 8, bottom: 12),
        child: buildMarkdownWidget(
            appendPlannerJsonStyle(widget.content), "",widget.outerScrollController),
      );
    } else {
      return appendExpandButton(
          Container(
            margin: const EdgeInsets.only(top: 8, bottom: 10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: getBackgroundColor(widget.type, widget.agentName),
              ),
              border: Border.all(
                  color: getBorderColor(widget.type, widget.agentName),
                  width: 0.5),
              borderRadius: BorderRadius.circular(10),
            ),
            child: appendMaskToContainer(
                _wrapWithEdgeHandoff(
                  SingleChildScrollView(
                    primary: false,
                    physics: physics,
                    child: buildMarkdownWidget(
                        appendPlannerJsonStyle(widget.content),
                        widget.agentName,widget.outerScrollController),
                  ),
                ),
                EdgeInsets.symmetric(horizontal: 8)),
          ), () {
        changeExpandState(() {
          if (mounted) setState(() {});
        });
      });
    }
  }

  Widget _buildBottomTip() {
    return ChatBottomTipWidget(
      isLike: true,
      isStepOn: false,
      copyContent: widget.content,
      feedbackLogic: feedbackLogic,
    );
  }

  String appendPlannerJsonStyle(String content) {
    if (widget.type == StreamMessageTypeEnum.plan_schedule &&
        !content.startsWith("```json")) {
      content = "```json\n$content\n```";
    }
    return content;
  }
}
