import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:markdown_widget/widget/all.dart';

import '../../../widget/image_viewer_widget.dart';

// 文档数据模型
class DocModel {
  final String? id;
  final String title;
  final String mimeType;
  final String url;

  DocModel({
    required this.id,
    required this.title,
    required this.mimeType,
    required this.url,
  });

  factory DocModel.fromJson(Map<String, dynamic> json) {
    return DocModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      mimeType: json['mime_type'] ?? '',
      url: json['url'] ?? '',
    );
  }

  // 判断是否为图片
  bool get isImage =>
      mimeType.startsWith('image/') || mimeType.startsWith('image');

  // 判断是否为PDF
  bool get isPdf => mimeType == 'application/pdf';

  // 判断是否为文档
  bool get isDocument =>
      mimeType.contains('document') ||
      mimeType.contains('text') ||
      mimeType.contains('word') ||
      mimeType.contains('excel') ||
      mimeType.contains('powerpoint');
}

class ChatAiDocWidget extends StatelessWidget {
  final List docs;
  final List<ImageItem> images = [];
  ChatAiDocWidget({super.key, required this.docs});

  // 解析文档数据
  List<DocModel> parsingDocs() {
    if (docs.isEmpty) return [];

    return docs
        .map((doc) {
          if (doc is Map<String, dynamic>) {
            return DocModel.fromJson(doc);
          }
          return null;
        })
        .where((doc) => doc != null)
        .cast<DocModel>()
        .toList();
  }

  // 分离图片和其他文档
  Map<String, List<DocModel>> _separateDocsByType(List<DocModel> docs) {
    final images = <DocModel>[];
    final others = <DocModel>[];

    for (final doc in docs) {
      if (doc.isImage) {
        images.add(doc);
      } else {
        others.add(doc);
      }
    }

    return {
      'images': images,
      'others': others,
    };
  }

  @override
  Widget build(BuildContext context) {
    final parsedDocs = parsingDocs();
    images.clear();
    images.addAll(parsedDocs.map((e){
      return ImageItem.network(e.url);
    }).toList());
    if (parsedDocs.isEmpty) {
      return const SizedBox.shrink();
    }

    // 不再区分图片和其他文档，统一按顺序展示
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // 使用水平滚动展示所有文档
        _buildHorizontalDocList(context, parsedDocs),
      ],
    );
  }

  // 构建水平滚动的文档列表
  Widget _buildHorizontalDocList(BuildContext context, List<DocModel> docs) {
    return Container(
      alignment: Alignment.centerRight,
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        scrollDirection: Axis.horizontal,
        child: Row(
          children: docs
              .map((doc) => Container(
                    margin: EdgeInsets.only(
                        left: docs.indexOf(doc) == 0 ? 0 : 4.w, right: docs.last == doc ? 0 : 4.w),
                    child: doc.isImage
                        ? _buildScrollableImageItem(context, doc)
                        : _buildDocItem(context, doc),
                  ))
              .toList(),
        ),
      ),
    );
  }

  // 构建可滚动的图片项
  Widget _buildScrollableImageItem(BuildContext context, DocModel image) {
    final imageWidth = 80.w;
    final imageHeight = 80.w;

    return SizedBox(
      width: imageWidth,
      height: imageHeight,
      child: _buildImageItem(context, image),
    );
  }

  // 构建单个图片项
  Widget _buildImageItem(BuildContext context, DocModel image) {

    return GestureDetector(
      onTap: () => _openDocument(context, image),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.w),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // 图片展示
            Image.network(
              image.url,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: Colors.grey[200],
                  child: Center(
                    child: CircularProgressIndicator(
                      color: Color(0xFFFF84F4),
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      strokeWidth: 2,
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[200],
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.broken_image,
                        color: Colors.grey,
                        size: 32,
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // 构建单个文档项
  Widget _buildDocItem(BuildContext context, DocModel doc) {
  return Container(
    width: 214.w,
    height: 80.w,
    child: Stack(
      children: [
        // 渐变边框背景
        Positioned.fill(
          child: Container(
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
              image: const DecorationImage(image: AssetImage('assets/images/bg_doc_item.webp')),
              borderRadius: BorderRadius.circular(8.w),
            ),
            child: ListTile(
              contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 8.w),
              visualDensity: VisualDensity.compact,
              horizontalTitleGap: 8.w,
              leading: _buildDocIcon(doc),
              title: Text(
                doc.title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              onTap: () => _openDocument(context, doc),
            ),
          ),
        ),
      ],
    ),
  );
  }

  // 构建文档图标
  Widget _buildDocIcon(DocModel doc) {
    var iconAsset = 'assets/groupChat/icon_file.webp';

    if (doc.isPdf) {
      iconAsset = 'assets/groupChat/icon_pdf.webp';
    } else if (doc.isDocument) {
      if (doc.mimeType.contains('word')) {
        iconAsset = 'assets/groupChat/icon_doc.webp';
      } else if (doc.mimeType.contains('excel')) {
        iconAsset = 'assets/groupChat/icon_xlsx.webp';
      } else if (doc.mimeType.contains('powerpoint')) {
        iconAsset = 'assets/groupChat/icon_pptx.webp';
      } else {
        iconAsset = 'assets/groupChat/icon_file.webp';
      }
    }

    return Container(
      width: 40.w,
      height: 40.w,
      child: Image.asset(
        iconAsset,
        fit: BoxFit.contain,
      ),
    );
  }

  // 打开文档
  void _openDocument(BuildContext context, DocModel doc) {
    if (!doc.isImage) {
      // 仅允许图片
      return;
    }
    int index = images.indexWhere((image) => image.url == doc.url);
    if(index == -1){
      index = 0;
    }
    ImageViewerDialog.show(
      context: context,
      images: images,
      initialIndex: index,
      showDownloadButton: false,
      showBottomButtons: false,
      showPageIndicator: false,
    );
  }
}
