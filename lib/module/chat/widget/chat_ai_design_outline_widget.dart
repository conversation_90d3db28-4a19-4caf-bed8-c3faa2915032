import 'dart:convert';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:markdown_widget/markdown_widget.dart';
import 'package:new_agnes/module/chat/widget/ppt_lay.dart';
import 'package:new_agnes/module/chat/widget/rotating_loading_image.dart';

import '../../../widget/GradientBorderContainer.dart';

class ChatAiDesignOutlineWidget extends StatelessWidget {
  final String? content;
  final ScrollController? outerController;
  final bool fullScreen;
  const ChatAiDesignOutlineWidget({super.key, this.content,this.outerController,this.fullScreen = false});

  @override
  Widget build(BuildContext context) {
    if ((content?.length ?? 0) == 0) {
      return SizedBox.shrink();
    }
    String jsonContent = content!;
    try {
      Map<String, dynamic> jsonContentMap = jsonDecode(jsonContent);
      List<AiDesignCustomModel> data = getData(jsonContentMap);
      return GradientBorderContainer.single(
        strokeWidth: 1,
        gradient: LinearGradient(
          colors: [
            Color(0xFF7253FA),
            Color(0xFFFF3BDF),
            Color(0xFF5E57FE),
          ],
          stops: [0.0, 0.32, 1.0],
          begin: Alignment.bottomLeft,
          end: Alignment.topRight,
          tileMode: TileMode.decal,
        ),
        borderRadius: BorderRadius.circular(10),
        child: ConstrainedBox(
          constraints: BoxConstraints(maxHeight: fullScreen ? MediaQuery.of(context).size.height - 100 : 508.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                    color: Color(0x8F604276),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(10))),
                child: Row(
                  children: [
                    Text('VIEW',
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 16.sp,
                            color: Color(0xFFFF3BDF))),
                    Spacer(),
                    SizedBox(
                      width: 16,
                    ),
                    GestureDetector(
                      onTap: () {
                        if(fullScreen){
                          Navigator.of(context).pop();
                        }else {
                          showUnfoldDialog(context, jsonContent);
                        }
                      },
                      child: Image.asset(
                          fullScreen
                              ? "assets/icon/icon_shrink.webp"
                              : "assets/icon/icon_unfold.webp",
                          width: 20,
                          height: 20),
                    )
                  ],
                ),
              ),
              Flexible(
                child: wrapWithEdgeHandoff(ListView.builder(
                  shrinkWrap: true,
                  itemCount: data.length,
                  padding: EdgeInsets.zero,
                  physics: ClampingScrollPhysics(),
                  itemBuilder: (context, index) {
                    final slideItem = data[index];
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 8.h,
                        ),
                        Row(
                          children: [
                            SizedBox(
                              width: 16.w,
                            ),
                            Image.asset(
                              "assets/groupChat/ic_group_product_arrow.png",
                              width: 16.w,
                              height: 16.h,
                            ),
                            SizedBox(
                              width: 12.w,
                            ),
                            Text(
                              slideItem.title,
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold),
                            )
                          ],
                        ),
                        if (slideItem.type.isEmpty) ...[
                          Container(
                            margin: EdgeInsets.only(top: 16.h,left: 44.w,right: 16.w),
                            child: Text(
                              slideItem.content,
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.white,
                              ),
                            ),
                          )
                        ],
                        if (slideItem.type.isNotEmpty) ...[
                          Container(
                            margin: EdgeInsets.only(top: 16.h,left: 44.w,right: 16.w),
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Color(0xFF4B764D).withValues(alpha: 0.3),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  slideItem.content,
                                  style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xFFDBFF71)),
                                ),
                                SizedBox(
                                  height: 8.h,
                                ),
                                Text(
                                  slideItem.note,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.white,
                                  ),
                                )
                              ],
                            ),
                          )
                        ],
                        SizedBox(
                          height: 16.h,
                        ),
                        if(index != data.length - 1)...[
                          Container(
                            color: Color(0xFF988B9A),
                            height: 1.h,
                          )
                        ],
                      ],
                    );
                  },
                ), outerController),
              )
            ],
          ),
        ),
      );
    } catch (e) {
      return Container(
        alignment: Alignment.topRight,
        child: RotatingLoadingImage(
            imagePath: "assets/images/icon_load.webp",
            width: 20.w,
            height: 20.h),
      );
    }
  }

  MarkdownConfig _getMarkdownConfig() {
    return MarkdownConfig(configs: [
      HrConfig.darkConfig,
      H1Config.darkConfig,
      H2Config.darkConfig,
      H3Config.darkConfig,
      H4Config.darkConfig,
      H5Config.darkConfig,
      H6Config.darkConfig,
      PreConfig.darkConfig,
      PConfig(
          textStyle: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              height: 1,
              backgroundColor: Colors.transparent)),
      CodeConfig(
          style: TextStyle(
              backgroundColor: Colors.transparent, color: Colors.white)),
      BlockquoteConfig.darkConfig,
      ListConfig(
          marginLeft: 16.w,
          marginBottom: 4.h,
          marker: (a, b, c) {
            return Padding(
              padding: EdgeInsets.only(top: 10.h),
              child: Container(
                alignment: Alignment.center,
                width: 4.w,
                height: 4.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
              ),
            );
          })
    ]);
  }

  List<AiDesignCustomModel> getData(Map<String, dynamic> map) {
    AiDesignOutLineModel aiDesignOutLineModel =
        AiDesignOutLineModel.fromJson(map);
    List<AiDesignCustomModel> models = [];
    models.add(AiDesignCustomModel(
        title: "Knowledge",
        content: aiDesignOutLineModel.guidance ?? "",
        note: "",
        type: ""));
    models.addAll(aiDesignOutLineModel.visuals
            ?.map((e) => AiDesignCustomModel(
                type: e.type ?? "",
                title: "${e.type}${e.index ?? ""}",
                content: e.title ?? "",
                note: e.note ?? ""))
            .toList() ??
        []);
    return models;
  }

  void showUnfoldDialog(BuildContext context, dynamic jsonContent) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: "",
      // 半透明遮罩
      pageBuilder: (context, animation, secondaryAnimation) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 0, sigmaY: 0), // 毛玻璃模糊
          child: Center(
            child: Material(
              color: Color(0xCD201034),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    ChatAiDesignOutlineWidget(content: jsonContent,fullScreen: true,)
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutBack,
            ),
            child: child,
          ),
        );
      },
    );
  }
}

///
/// {
//   "visuals" : [ {
//     "type" : "image",
//     "title" : "可爱的猫咪特写",
//     "note" : "一张小猫咪的可爱特写，突出它水汪汪的大眼睛和柔软的毛发。",
//     "index" : 1
//   } ],
//   "guidance" : "设计一张可爱的猫咪图片，突出其萌态。",
//   "deep_mode" : false
// }
class AiDesignOutLineModel {
  final String? guidance;
  final bool? deepMode;
  final List<AiDesignOutLineVisualModel>? visuals;

  AiDesignOutLineModel({this.guidance, this.deepMode, this.visuals});

  factory AiDesignOutLineModel.fromJson(Map<String, dynamic> json) {
    return AiDesignOutLineModel(
      guidance: json['guidance'] as String?,
      deepMode: json['deep_mode'] as bool?,
      visuals: (json['visuals'] as List?)
          ?.map((e) =>
              AiDesignOutLineVisualModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class AiDesignOutLineVisualModel {
  final String? type;
  final String? title;
  final String? note;
  final int? index;

  AiDesignOutLineVisualModel({this.type, this.title, this.note, this.index});

  factory AiDesignOutLineVisualModel.fromJson(Map<String, dynamic> json) {
    return AiDesignOutLineVisualModel(
      type: json['type'] as String?,
      title: json['title'] as String?,
      note: json['note'] as String?,
      index: json['index'] as int?,
    );
  }
}

class AiDesignCustomModel {
  final String title;
  final String content;
  final String note;
  final String type;

  AiDesignCustomModel(
      {required this.title,
      required this.content,
      required this.note,
      required this.type});
}
