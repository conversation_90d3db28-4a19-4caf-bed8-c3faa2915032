import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/widget/ImageUrl.dart';
import 'package:new_agnes/widget/TapHigh.dart';

import '../../../dialog/sources_dialog.dart';
import '../../../generated/l10n.dart';
import '../../../widget/ContainerBox.dart';
import '../../../widget/message_long_tap_tools.dart';
import '../../home/<USER>/widgets/roles_bottom_navigation_bar.dart';
import '../../web/single_page_web/page.dart';
import '../logic.dart';
import '../model/search_urls_model.dart';
import '../model/tool_call_model.dart';
import 'scroll_text.dart';

/**
 * go_button
 */
Widget GoButton({onResult}) {
  return Container(
    margin: EdgeInsets.only(top: 34.w),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TapHigh(
            onTap: () {
              if (onResult != null) {
                CmUtils.twoTap(Get.context!, () {
                  onResult();
                });
              }
            },
            child: Image.asset(
              'assets/images/icon_go_button.webp',
              width: 48.w,
              height: 32.w,
            )),
        SizedBox(
          width: 12.w,
        ),
        Expanded(
            child: Text(
          S.of(Get.context!).orLetMeKnowIfYouHaveAnyQuestions,
          style: TextStyle(color: Colors.white, fontSize: 14.sp, height: 1.3),
        ))
      ],
    ),
  );
}

/**
 * 用户发送的文本消息布局
 */
Widget UserRightTextLay(content) {
  final logic = Get.find<ChatLogic>();
  Timer? _longPressTimer;
  double _scale = 1.0;
  return StatefulBuilder(builder: (context, state) {
    return GestureDetector(
      onLongPressStart: (details) {
        state(() {
          _scale = 1.05;
        });
        _longPressTimer = Timer(const Duration(milliseconds: 250), () {
          logic.isAutoScrolling.value = false;
          MessageLongTapTools.showCustomOverlay(context,
              (MessageItemType type) {
            switch (type) {
              case MessageItemType.quote:
                break;
              case MessageItemType.copy:
                CmUtils.fuZhi(content);
                break;
            }
          }, true, isOnlyCopy: true);
        });
      },
      onLongPressEnd: (details) {
        state(() {
          _scale = 1.0;
        });
        _longPressTimer?.cancel();
      },
      child: AnimatedScale(
        duration: Duration(milliseconds: 200),
        scale: _scale,
        child: Row(
          children: [
            Spacer(),
            ContainerBox(
                borderColor: Color(0xFF988B9A),
                borderWith: 1.0,
                radius: 10,
                boxColor: Color(0x8F604276),
                padding: EdgeInsets.only(
                    left: 16.w, right: 16.w, top: 14.w, bottom: 14.w),
                alignment: Alignment.centerRight,
                child: ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 1.sw - 86.w),
                  child: Text(
                    content,
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500),
                  ),
                ))
          ],
        ),
      ),
    );
  });
}

/**
 * 用户点击Go 后显示的成功图标
 */
Widget UserGoSuccressLay() {
  return Container(
    margin: EdgeInsets.only(top: 20.w, bottom: 16.h),
    alignment: Alignment.centerRight,
    child: Image.asset(
      'assets/images/ic_chart_success_go.webp',
      width: 52.w,
      height: 48.w,
    ),
  );
}

/**
 * deepResearch 的 toolCallResult 的布局
 */
Widget RecommendLay(jsonContent) {
  ToolCallModel toolCallModel = ToolCallModel.fromJson(jsonContent);
  List<dynamic> toolResultList = [];
  List<Widget> wrapList = [];
  if (toolCallModel.toolName == 'tavily_search' &&
      toolCallModel.toolResult!.isNotEmpty) {
    toolResultList = jsonDecode(toolCallModel.toolResult!);

    if (toolResultList.length > 3) {
      for (int i = 0; i < 3; i++) {
        var data = toolResultList[i];
        getToolsList(data, wrapList);
      }
      wrapList.add(_toolsMoreLay(toolResultList));
    } else {
      for (int i = 0; i < toolResultList.length; i++) {
        var data = toolResultList[i];
        getToolsList(data, wrapList);
      }
    }
  }
  return toolCallModel.toolName == 'tavily_search'
      ? Container(
          child: Column(
            children: [
              Container(
                child: Row(
                  children: [
                    Expanded(
                        child: Container(
                      alignment: Alignment.centerLeft,
                      child: RichText(
                          textAlign: TextAlign.left,
                          text: TextSpan(
                              text: 'Searching for  ',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14.sp,
                              ),
                              children: <TextSpan>[
                                TextSpan(
                                  text: '"${toolCallModel.toolInput!.query}"',
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.bold),
                                ),
                              ])),
                    ))
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 5.w),
                width: 1.sw,
                child: Wrap(
                    direction: Axis.horizontal,
                    spacing: 8.w,
                    // textDirection: TextDirection.ltr,
                    children: wrapList),
              ),
            ],
          ),
        )
      : Container();
}

void getToolsList(data, wrapList) {
  String image = data['url'];
  List<String> imageList1 = image.split('//');
  if (imageList1.length > 1 && imageList1[1].isNotEmpty) {
    List<String> imageList2 = imageList1[1].split('/');
    image = '${imageList1[0]}//${imageList2[0]}/favicon.ico';
    wrapList.add(_toolsLay(data, image));
  }
}

Widget _toolsLay(data, image) {
  return TapHigh(
      onTap: () {
        Get.to(SingleWidgetWebWidget(
          data['url'],
          title: '${data['title']}',
        ));
      },
      child: ContainerBox(
        boxColor: Color(0xFF05294B),
        borderColor: Color(0xFF7BD33A),
        radius: 180,
        borderWith: 1.w,
        padding: EdgeInsets.only(top: 4.w, bottom: 4.w, left: 6.w, right: 6.w),
        margin: EdgeInsets.only(top: 8.w),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ContainerBox(
                radius: 180,
                child: ContainerBox(
                    radius: 180,
                    child: UrlImage('${image}',
                        width: 16.w,
                        height: 16.w,
                        radius: 180,
                        errorWidget: ErrorLay()))),
            SizedBox(
              width: 6.w,
            ),
            Container(
              constraints: BoxConstraints(
                maxWidth: 1.sw - 115.w,
              ),
              child: Text(
                '${data['title']}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: Color(0xFF7BD33A),
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600),
              ),
            )
          ],
        ),
      ));
}

Widget _toolsMoreLay(toolResultList) {
  return TapHigh(
      onTap: () {
        List<SearchUrlsModel> searchList = [];
        toolResultList.forEach((toolData) {
          SearchUrlsModel searchUrlsModel = new SearchUrlsModel();
          String toolDataImage = toolData['url'];
          String toolDataContent = '';
          List<String> imageList1 = toolDataImage.split('//');
          if (imageList1.length > 1 && imageList1[1].isNotEmpty) {
            List<String> imageList2 = imageList1[1].split('/');
            toolDataImage = '${imageList1[0]}//${imageList2[0]}/favicon.ico';
            toolDataContent = imageList2[0];
          }
          searchUrlsModel.image = toolDataImage;
          searchUrlsModel.type = '';
          searchUrlsModel.title = toolData['title'];
          searchUrlsModel.url = toolData['url'];
          searchUrlsModel.content = toolDataContent;
          searchList.add(searchUrlsModel);
        });
        Get.bottomSheet(SourcesDialog(searchList),
            backgroundColor: Colors.transparent,
            isScrollControlled: true,
            elevation: 0,
            barrierColor: Colors.transparent);
      },
      child: ContainerBox(
        boxColor: Color(0xFF05294B),
        borderColor: Color(0xFFFF3BDF),
        radius: 180,
        borderWith: 1.w,
        padding: EdgeInsets.only(top: 4.w, bottom: 4.w, left: 6.w, right: 6.w),
        margin: EdgeInsets.only(top: 8.w),
        child: Container(
          child: Text(
            '+ ${toolResultList.length} sources',
            textAlign: TextAlign.center,
            style: TextStyle(
                color: Color(0xFFFF3BDF),
                fontSize: 14.sp,
                fontWeight: FontWeight.w600),
          ),
        ),
      ));
}

Widget ErrorLay() {
  return Container(
    color: Colors.black,
    // padding: EdgeInsets.all(1.w),
    child: Image.asset(
      'assets/images/ic_defult_icon.webp',
      fit: BoxFit.fill,
    ),
  );
}

Widget DefaultUserAvatar() {
  return Container(
    color: Colors.transparent,
    child: Image.asset(
      'assets/images/ic_default_user_avatar.webp',
    ),
  );
}

Widget BottomHeightLay(context, {height = 0}) {
  return SizedBox(
    height: height == 0 ? CmUtils.bottomBarHeight.value : height,
  );
}
