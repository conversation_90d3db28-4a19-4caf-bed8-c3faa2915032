import 'dart:collection';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/Api.dart';
import 'package:new_agnes/api/ApiProvider.dart';
import 'package:share_plus/share_plus.dart';

import '../../../utils/bgCover.dart';
import '../../../utils/cmUtils.dart';
import '../../../widget/ComAppbar.dart';
import '../../../widget/ContainerBox.dart';
import 'logic.dart';

class SingleWidgetWebWidget extends StatefulWidget {
  final String? title;
  final String? url;
  final bool hasShare;
  SingleWidgetWebWidget(this.url, {this.title = '',this.hasShare = false});

  @override
  State<SingleWidgetWebWidget> createState() => _SingleWidgetWebWidgetState();
}

class _SingleWidgetWebWidgetState extends State<SingleWidgetWebWidget> {
  final SinglePageWebLogic logic = Get.put(SinglePageWebLogic());

  InAppWebViewController? webViewController;
  InAppWebViewSettings settings = InAppWebViewSettings(
      isInspectable: false,
      mediaPlaybackRequiresUserGesture: false,
      transparentBackground: true,
      supportZoom: false,
      javaScriptEnabled:  true,
      useShouldOverrideUrlLoading:  true,
      allowsInlineMediaPlayback: true,
      iframeAllow: "camera; microphone",
      iframeAllowFullscreen: true,
      // 添加Stripe特定的配置
      domStorageEnabled: true,
      databaseEnabled: true,
      cacheMode: CacheMode.LOAD_DEFAULT,
      mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
      useWideViewPort: true,
      loadWithOverviewMode: true,

      // 启用第三方cookies（Stripe可能需要）
      thirdPartyCookiesEnabled: true,

      // 启用更多WebView功能
      supportMultipleWindows: true,
      javaScriptCanOpenWindowsAutomatically: true,
  );

  @override
  Widget build(BuildContext context) {
    return buildMineBg(
        child: Scaffold(
            backgroundColor: Colors.transparent,
            resizeToAvoidBottomInset: false, // 规避底部布局被软键盘顶起
            body: Obx(() {
              return Container(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _topLay(),
                    logic.progress.value == 1
                        ? SizedBox()
                        : Container(
                            child: _createProgressBar(
                                logic.progress.value, context),
                          ),
                    Expanded(child: _webLay())
                  ],
                ),
              );
            })));
  }

  @override
  void dispose() {
    webViewController?.stopLoading();
    super.dispose();
  }

  Widget _topLay() {
    return ContainerBox(
      jBColors: [
        Color(0x99000000),
        Color(0x80000000),
        Color(0x4D000000),
        Color(0x33000000),
        Color(0x00000000),
        Color(0x00000000),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      height: CmUtils.getBarHeight(context) + 50.w,
      child: ComAppBar(
        context,
        '${widget.title}',
        backgroundColor: Colors.transparent,
        onPop: () {
          Get.back();
        },
        actions: [
          SizedBox(width: 40.w,),
          if(widget.hasShare)...[
            GestureDetector(
              onTap: () {
                SharePlus.instance.share(
                  ShareParams(
                    title: widget.title,
                    uri: Uri.parse(widget.url ?? ""),
                  ),
                );
              },
              child: Image.asset(
                "assets/images/ic_ppt_share.png",
                width: 24.w,
                height: 24.h,
              ),
            ),
            SizedBox(width: 16.w)
          ]
        ],
      ),
    );
  }

  Widget _webLay() {
    return Container(
      color: Colors.transparent,
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
      child: InAppWebView(
        initialUrlRequest: URLRequest(url: WebUri('${widget.url}')),
        onReceivedServerTrustAuthRequest: (controller, challenge) async {
          //解决 handshake failed问题
          return ServerTrustAuthResponse(
              action: ServerTrustAuthResponseAction.PROCEED);
        },
        initialSettings: settings,
        onWebViewCreated: (InAppWebViewController controller) {
          webViewController = controller;
        },
        onDownloadStartRequest: (controller, url) async {},
        onScrollChanged: (InAppWebViewController controller, int x, int y) {},
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          debugPrint("onLoadStart: ${navigationAction.request.url}");
          // 判断是否是返回首页
          if ("${Api.baseUrl}/" == navigationAction.request.url.toString()) {
            Get.until((route) => route.isFirst);
            Get.back();
            return null;
          }
          return NavigationActionPolicy.ALLOW;
        },
        onLoadStart: (InAppWebViewController controller, WebUri? url) async {
          var res = await controller.evaluateJavascript(
              source:
                  "localStorage.setItem('token', '${ApiProvider.token!.substring(7)}');");
          debugPrint("evaluateJavascript result: $res");
        },
        onLoadStop: (InAppWebViewController controller, WebUri? url) {},
        onProgressChanged: (InAppWebViewController controller, int progress) {
          logic.progress.value = progress / 100.0;
          setState(() {});
        },
        // 修复后的渲染进程崩溃处理
        onRenderProcessGone: (controller, detail) async {
          // 处理渲染进程崩溃
          debugPrint("WebView渲染进程已崩溃: ${detail.didCrash}, 退出时优先级: ${detail.rendererPriorityAtExit}");

          // 重置进度状态
          logic.progress.value = 0.0;

          // 显示错误提示
          if (mounted) {
            // 延迟一小段时间后重新加载
            Future.delayed(Duration(milliseconds: 800), () async {
              try {
                await controller.reload();
              } catch (e) {
                debugPrint("重新加载失败: $e");
                // 如果重新加载失败，可以考虑重建整个WebView
              }
            });
          }

          // 返回true表示已经处理了该异常
          return;
        },

        // 添加WebView崩溃处理
        onWebContentProcessDidTerminate: (controller) async {
          debugPrint("WebView内容进程已终止");

          if (mounted) {
            // 尝试重新加载页面
            await controller.reload();
          }
        },
        gestureRecognizers: [
          Factory(() => VerticalDragGestureRecognizer()),
        ].toSet(),
      ),
    );
  }

  /// 生成进度条组件，进度从0 ~ 1
  _createProgressBar(double progress, BuildContext context) {
    return LinearProgressIndicator(
      backgroundColor: Colors.white70.withOpacity(0),
      minHeight: 2.w,
      value: progress == 1.0 ? 0 : progress,
      valueColor: AlwaysStoppedAnimation<Color>(
          Color(0xFFB65CC5).withAlpha((255.0 * 0.9).round())),
    );
  }

  //关闭监听
  Future<bool> _goBack(BuildContext context) async {
    if (webViewController != null && await webViewController!.canGoBack()) {
      webViewController!.goBack();
      return false;
    }
    return true;
  }

  Future<bool> canBack() async {
    bool isBack = true;
    if (webViewController != null) {
      isBack = await _goBack(context);
    }
    if (isBack) {
      Get.back();
    }
    return false;
  }
}
