import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

class IapDemoPage extends StatefulWidget {
  const IapDemoPage({super.key});

  @override
  State<IapDemoPage> createState() => _IapDemoPageState();
}

class _IapDemoPageState extends State<IapDemoPage> {
  final InAppPurchase _iap = InAppPurchase.instance;
  StreamSubscription<List<PurchaseDetails>>? _subscription;
  bool _available = false;
  bool _loading = false;
  final List<ProductDetails> _products = [];
  final List<PurchaseDetails> _purchases = [];

  // TODO: 替换为你在 Google Play Console 配置的商品ID
  final Set<String> _productIds = <String>{
    'test_product',
  };

  @override
  void initState() {
    super.initState();
    final Stream<List<PurchaseDetails>> purchaseUpdated =
        _iap.purchaseStream;
    _subscription = purchaseUpdated.listen(_onPurchaseUpdated, onDone: () {
      _subscription?.cancel();
    }, onError: (Object error) {
      // handle error here.
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('购买更新出错: $error')),
      );
    });
    _initStoreInfo();
  }

  Future<void> _initStoreInfo() async {
    setState(() => _loading = true);
    final bool isAvailable = await _iap.isAvailable();
    if (!isAvailable) {
      setState(() {
        _available = false;
        _loading = false;
      });
      return;
    }

    final ProductDetailsResponse response =
        await _iap.queryProductDetails(_productIds);
    if (response.error != null) {
      setState(() {
        _available = true;
        _loading = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('查询商品失败: ${response.error!.message}')),
      );
      return;
    }

    setState(() {
      _available = true;
      _products
        ..clear()
        ..addAll(response.productDetails);
      _loading = false;
    });
  }

  void _onPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) async {
    for (final purchaseDetails in purchaseDetailsList) {
      setState(() {
        _purchases.removeWhere((p) => p.productID == purchaseDetails.productID);
        _purchases.add(purchaseDetails);
      });
      if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        // 在这里进行服务器验单等逻辑
      }
      if (purchaseDetails.pendingCompletePurchase) {
        await _iap.completePurchase(purchaseDetails);
      }
    }
  }

  Future<void> _buy(ProductDetails product) async {
    final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
    await _iap.buyNonConsumable(purchaseParam: purchaseParam);
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('IAP Demo (Google Play)'),
      ),
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    _available ? '商店可用' : '商店不可用',
                    style: TextStyle(
                      color: _available ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _loading ? null : _initStoreInfo,
                  icon: const Icon(Icons.refresh),
                  tooltip: '刷新商品',
                )
              ],
            ),
            const SizedBox(height: 12),
            if (_loading) const LinearProgressIndicator(),
            const SizedBox(height: 12),
            if (_products.isEmpty && !_loading)
              const Text('无可购买商品，请在 Google Play Console 创建商品并同步'),
            Expanded(
              child: ListView.separated(
                itemCount: _products.length,
                separatorBuilder: (_, __) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final product = _products[index];
                  return ListTile(
                    title: Text(product.title),
                    subtitle: Text(product.description),
                    trailing: Text(product.price),
                    onTap: () => _buy(product),
                  );
                },
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  final bool available = await _iap.isAvailable();
                  if (!mounted) return;
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Billing 可用: $available')),
                  );
                },
                child: const Text('检查 Billing 可用性'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

