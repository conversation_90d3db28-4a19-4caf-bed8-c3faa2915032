import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/ble/blueToothManager.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';

class VibePodsLogic extends GetxController {
  var isConnected = (-1).obs;//-1未连接  0连接中  1连接失败 2连接成功(isConnected=true)
  late BlueToothManager blueToothManager;
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    blueToothManager = Get.find<BlueToothManager>();
    if(blueToothManager.connectedDevice?.isConnected == true){
      isConnected.value = 2;
    }
    // 监听连接状态
    blueToothManager.connectionStream.listen((device) {
      if (device != null) {
        print('已连接到设备: ${device.platformName}');
        isConnected.value = 2;
      } else {
        print('未连接：设备已断开连接');
        isConnected.value = -1;
      }
    }, onError: (error) {
      print('logic error: $error');
      if(error is int){
        isConnected.value = error;
      }
    });
  }

  void connectBlueTooth() {
    // 手动重新扫描连接
    blueToothManager.startScanAfterPermissionCheck();

  }
}
