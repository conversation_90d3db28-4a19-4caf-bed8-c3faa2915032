import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:new_agnes/utils/bgCover.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/widget/ComAppbar.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';

import '../../../api/Api.dart';
import '../../../generated/l10n.dart';
import '../../web/single_page_web/page.dart';
import 'logic.dart';

class VibePodsPage extends StatelessWidget {
  VibePodsPage({super.key});

  final VibePodsLogic logic = Get.put(VibePodsLogic());

  connectBle() async {
    if (FlutterBluePlus.adapterStateNow == BluetoothAdapterState.off) {
      // 打开蓝牙
      FlutterBluePlus.turnOn().then((value) {});
    } else {
      if (!logic.blueToothManager.isScanning &&
          (logic.isConnected == -1 || logic.isConnected == 1)) {
        logError("进入页面就开始连接蓝牙${FlutterBluePlus.adapterStateNow}");
        logic.connectBlueTooth();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    connectBle();
    return buildMineBg(
      child: Scaffold(
        extendBody: true,
        extendBodyBehindAppBar: true,
        backgroundColor: Colors.transparent,
        appBar: ComAppBar(context, S.of(context).agnesVibepods),
        // appBar: AppBar(
        //   backgroundColor: Colors.transparent,
        //   elevation: 0,
        //   leading: IconButton(
        //     onPressed: () => Get.back(),
        //     icon: Image.asset(
        //       "assets/images/icon_back.webp",
        //       width: 24.w,
        //       height: 24.w,
        //       color: Colors.white,
        //     ),
        //   ),
        //   centerTitle: true,
        //   title: Text(
        //     S.of(context).agnesVibePods,
        //     style: TextStyle(fontSize: 18, color: Colors.white),
        //     textAlign: TextAlign.center,
        //   ),
        // ),
        body: Stack(
          children: [
            Positioned(
              top: 0,
              right: 0,
              child: Image.asset(
                "assets/images/img_circle_right_top.png",
                width: 88.h,
                height: 102.w,
              ),
            ),
            Positioned(
              right: 0,
              bottom: 116,
              child: Image.asset(
                "assets/images/img_circle_right_mid.png",
                width: 132.h,
                height: 310.w,
              ),
            ),
            Positioned(
              bottom: 185,
              left: 0,
              child: Image.asset(
                "assets/images/img_circle_left_mid.png",
                width: 111.h,
                height: 305.w,
              ),
            ),
            Obx(() {
              var tip = logic.isConnected.value == 2
                  ? S.of(context).connected
                  : S.of(context).connectToAgnesVibepodsAndChatAnytime;
              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: 160.h),
                  Center(
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        SizedBox(
                          width: 330.h,
                          height: 330.w,
                          child: Visibility(
                            visible: logic.isConnected.value == 0,
                            child: Lottie.asset(
                                "assets/json/ble_connecting.json",
                                width: 330.h,
                                height: 330.w,
                                fit: BoxFit.fill),
                          ),
                        ),
                        Image.asset(
                          "assets/images/img_blue_set.png",
                          width: 280.h,
                          height: 200.w,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 79.h),
                  if (logic.isConnected.value == -1 ||
                      logic.isConnected.value == 1) ...{
                    GradientBorderContainer(
                      width: 361.w,
                      height: 60.h,
                      strokeWidth: 1,
                      borderRadius: BorderRadius.circular(10.w),
                      gradients: [
                        LinearGradient(
                          colors: [
                            Color(0x00EDB5E9),
                            Color(0x80DAA0D6),
                          ],
                          stops: [0, 1],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        LinearGradient(
                          colors: [
                            Color(0x00000000),
                            Color(0x2D000000),
                          ],
                          stops: [0, 1],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        LinearGradient(
                          colors: [
                            Color(0x28FFFFFF),
                            Color(0x00FFFFFF),
                          ],
                          stops: [0, 1],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ],
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.w),
                          gradient: LinearGradient(
                            colors: [
                              Color(0x26FFFFFF),
                              Color(0x10FFFFFF),
                            ],
                            stops: [0, 1],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              logic.isConnected.value == 1
                                  ? S.of(context).connectionFailed
                                  : S.of(context).notConnected,
                              style:
                                  TextStyle(fontSize: 16, color: Colors.white),
                            ),
                            Container(
                              height: 44,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: Colors.white,
                                gradient: LinearGradient(
                                  colors: [
                                    Color(0xFFFF91EE),
                                    Color(0xFFFF3BDF),
                                  ],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                ),
                              ),
                              child: TextButton(
                                style: TextButton.styleFrom(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 14.w),
                                  foregroundColor: Colors.white,
                                ),
                                onPressed: () => logic.connectBlueTooth(),
                                child: Center(
                                  child: Text(
                                    S.of(context).connect,
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Color((0xFF0D0D0D)),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 16.h),
                  },
                  if (logic.isConnected == 0) ...{
                    Text(
                      "Agnes VibePods",
                      style: TextStyle(fontSize: 22, color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                  },
                  SizedBox(height: 16.h),
                  Text(
                    tip,
                    style: TextStyle(fontSize: 16, color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                  GestureDetector(
                    onTap: () {
                      CmUtils.launchExternalBrowser(Api.knowMore);
                    },
                    child: Container(
                      margin: EdgeInsets.only(top: 16.h),
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 13.h),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(10.w),
                        border: Border.all(
                          color: Color(0xFFFF3BDF),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        S.of(context).knowMore,
                        style:
                            TextStyle(fontSize: 16, color: Color(0xFFFF3BDF)),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                ],
              );
            }),
          ],
        ),
      ),
    );
  }
}
