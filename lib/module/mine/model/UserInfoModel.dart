import 'package:get/get.dart';

///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class UserInfoModel {
/*
{
  "id": "6b090002-6aa4-458f-91aa-991c3550579f",
  "username": "2668617368",
  "email": "<EMAIL>",
  "user_phone": null,
  "avatar_url": "https://agnes-sg.s3.ap-southeast-1.amazonaws.com/avatars/default/default-avatar-0606.png",
  "is_active": true,
  "created_at": "2025-07-29T16:05:05"
}
*/

  String? id;
  RxString? username=''.obs;
  String? email;
  RxString? userPhone;
  RxString? avatarUrl=''.obs;
  bool? isActive;
  String? createdAt;

  UserInfoModel({
    this.id,
    this.username,
    this.email,
    this.userPhone,
    this.avatarUrl,
    this.isActive,
    this.createdAt,
  });
  UserInfoModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    username = json['username']!=null?json['username']?.toString().obs:''.obs;
    email = json['email']!=null?json['email']?.toString():'';
    userPhone = json['user_phone']!=null?json['user_phone']?.toString().obs:''.obs;
    avatarUrl = json['avatar_url']!=null?json['avatar_url']?.toString().obs:''.obs;
    isActive = json['is_active'];
    createdAt = json['created_at']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['username'] = username?.value;
    data['email'] = email;
    data['user_phone'] = userPhone;
    data['avatar_url'] = avatarUrl?.value;
    data['is_active'] = isActive;
    data['created_at'] = createdAt;
    return data;
  }
}
