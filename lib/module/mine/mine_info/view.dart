import 'dart:convert';
import 'dart:io';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/group_chat/agora_logic.dart';
import 'package:new_agnes/module/debug/debug_page.dart';
import 'package:new_agnes/module/debug/global.dart';
import 'package:new_agnes/utils/event_bus.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/photo_utils.dart';
import 'package:new_agnes/utils/stt/AzureSpeechManager.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/ImageUrl.dart';
import 'package:new_agnes/widget/TapHigh.dart';

import '../../../api/Api.dart';
import '../../../api/ApiProvider.dart';
import '../../../api/StorageService.dart';
import '../../../dialog/message_dialog.dart';
import '../../../generated/l10n.dart';
import '../../../utils/bgCover.dart';
import '../../../widget/GradientBorderContainer.dart';
import '../../../widget/ComAppbar.dart';
import '../../account/login/page.dart';
import '../../chat/widget/deep_research_lay.dart';
import '../model/UserInfoModel.dart';
import 'logic.dart';

class MineInfoPage extends StatelessWidget {
  MineInfoPage({Key? key}) : super(key: key);

  final MineInfoLogic mineInfoLogic = Get.find<MineInfoLogic>();
  List menuList = [0, 1, 2, 3, 4, 5];

  @override
  Widget build(BuildContext context) {
    String currentLanguageCode = Get.find<StorageService>().getLanguage() ??
        Localizations.localeOf(context).languageCode;
    //final currentLocale = Localizations.localeOf(context);
    mineInfoLogic.language.value =
        _getLanguageName(Locale(currentLanguageCode));

    return WillPopScope(
      onWillPop: () async {
        return true;
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        resizeToAvoidBottomInset: false, // 规避底部布局被软键盘顶起
        body: buildMineBg(
          child: Stack(
            children: [
              Container(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ComAppBar(
                        context,
                        S.of(Get.context!).account,
                        onPop: () {
                          Get.back();
                        },
                      ),
                      Container(
                        padding: EdgeInsets.only(left: 22, right: 22),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              '${S.of(Get.context!).avatar}',
                              style:
                                  TextStyle(color: Colors.white, fontSize: 24),
                            ),
                            Container(
                              width: 1.sw,
                              margin: EdgeInsets.only(top: 30),
                              alignment: Alignment.center,
                              child: Stack(
                                children: [
                                  Obx(() {
                                    return Center(
                                      child: UrlImage(
                                        '${mineInfoLogic.userInfoModel.value.avatarUrl}',
                                        width: 145.w,
                                        height: 145.w,
                                        radius: 180,
                                        errorWidget:DefaultUserAvatar()
                                      ),
                                    );
                                  }),
                                  Positioned(
                                      right: 1.sw / 2 - 70,
                                      bottom: 0,
                                      child: TapHigh(
                                          onTap: () {
                                            PhotoUtils.goToPhotoDialog(
                                                context, 230.w, 2,
                                                (List<File> fileList) {
                                              if (fileList.length == 1) {
                                                mineInfoLogic.setUserAvatar(
                                                    fileList[0].path,
                                                    onResult: (data) {
                                                  UserInfoModel userInfoModel =
                                                      Get.find<StorageService>()
                                                          .getUserInfoData();
                                                  userInfoModel
                                                          .avatarUrl?.value =
                                                      data['avatar_url'];
                                                  Get.find<StorageService>()
                                                      .setUserInfoData(
                                                          jsonEncode(
                                                              userInfoModel
                                                                  .toJson()));
                                                  mineInfoLogic
                                                          .userInfoModel
                                                          .value
                                                          .avatarUrl!
                                                          .value =
                                                      data['avatar_url'];
                                                  eventBus.fire(AgoraAvatarEvent());
                                                });
                                              }
                                            });
                                          },
                                          child: Image.asset(
                                            'assets/images/icon_edit.webp',
                                            width: 24,
                                            height: 24,
                                          )))
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 16,
                            ),
                            Obx(() {
                              return mineInfoLogic.userInfoModel.value.username!
                                      .value.isNotEmpty
                                  ? _editLay(S.of(Get.context!).username,
                                      '${mineInfoLogic.userInfoModel.value.username!.value}',hasIcon: true,
                                      () {
                                      showDialog(
                                          context: Get.context!,
                                          builder: (context) {
                                            return MessageDialog(
                                              () {
                                                Get.back();
                                              },
                                              (String data) {
                                                if (data.isNotEmpty) {
                                                  Get.back();
                                                  mineInfoLogic.setUserInfo(
                                                      data, onResult: () {
                                                    mineInfoLogic
                                                        .userInfoModel
                                                        .value
                                                        .username!
                                                        .value = data;
                                                  });
                                                } else {
                                                  showFailToast(S
                                                      .of(Get.context!)
                                                      .writeSomething);
                                                }
                                              },
                                              data: mineInfoLogic.userInfoModel
                                                  .value.username!.value,
                                              onLeftName:
                                                  S.of(Get.context!).cancel,
                                              onRightName:
                                                  S.of(Get.context!).confirm,
                                              height: 220,
                                              isEdit: true,
                                              title: S.of(Get.context!).edit,
                                            );
                                          });
                                    })
                                  : SizedBox();
                            }),
                            mineInfoLogic.userInfoModel.value.email!.isNotEmpty
                                ? _editLay(
                                    S.of(Get.context!).email,
                                    '${mineInfoLogic.userInfoModel.value.email}',
                                    () {})
                                : SizedBox(),
                            mineInfoLogic
                                    .userInfoModel.value.userPhone!.isNotEmpty
                                ? _editLay(
                                    S.of(Get.context!).phone,
                                    '${mineInfoLogic.userInfoModel.value.userPhone}',
                                    () {})
                                : SizedBox(),
                            _yuYanLay(),
                            SizedBox(
                              height: 60,
                            ),
                            _bottonLay(),
                            SizedBox(
                              height: 20,
                            ),
                            // Container(
                            //   width: 1.sw,
                            //   margin: EdgeInsets.only(top: 40.w),
                            //   alignment: Alignment.centerRight,
                            //   child: Text(
                            //     '版本：v${mineInfoLogic.version}',
                            //     style: TextStyle(
                            //       color: Color(0xFFB65CC5)
                            //           .withAlpha((255.0 * 0.2).round()),
                            //       fontSize: 12.sp,
                            //     ),
                            //   ),
                            // ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: MediaQuery.of(context).padding.bottom,
                child: Text(
                  mineInfoLogic.version,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _editLay(name, data, onTap,{bool hasIcon = false}) {
    return TapHigh(
        onTap: () {
          if (onTap != null) {
            onTap();
          }
        },
        child: Container(
          margin: EdgeInsets.only(top: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              SizedBox(
                height: 10,
              ),
              Container(
                  height: 44,
                  padding:
                      EdgeInsets.only(left: 10, right: 10, top: 5, bottom: 5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10), // 圆角边框
                    border: Border.all(color: Color(0xFF8F8F8F)), // 外层统一边框
                  ),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      Expanded(child: Text(
                        data,
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      )),
                      if(hasIcon)...[
                        Image.asset(
                          'assets/images/ic_edit_user_name.png',
                          width: 24.w,
                          height: 24.h,
                        )
                      ]
                    ],
                  ))
            ],
          ),
        ));
  }

  //语言切换
  Widget _yuYanLay() {
    return Container(
      margin: EdgeInsets.only(top: 24),
      child: Row(
        children: [
          Expanded(
              child: Text(
            S.of(Get.context!).interfaceLanguage,
            style: TextStyle(color: Colors.white, fontSize: 16),
          )),
          PopupMenuButton(
            onSelected: (Locale data) {
              mineInfoLogic.language.value = _getLanguageName(data);
              Get.find<StorageService>().setLanguage(data.languageCode);
              Get.updateLocale(data);
              AzureSpeechManager.reInit(data);
            },
            color: Color(0xE52E174F),
            elevation: 1,
            // position: PopupMenuPosition.over,
            splashRadius: 2,
            shadowColor: Color.fromRGBO(46, 23, 79, 0.9),
            surfaceTintColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              // 设置菜单形状
              borderRadius: BorderRadius.circular(10),
              side: BorderSide(color: Color(0xFFFF3BDF)),
            ),
            child: Container(
                width: 120.w,
                padding: EdgeInsets.only(
                    left: 10.w, right: 10.w, top: 8.w, bottom: 10.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Color(0xFFFF3BDF)),
                ),
                alignment: Alignment.centerLeft,
                child: Row(
                  children: [
                    Expanded(child: Obx(() {
                      return Container(
                        alignment: Alignment.center,
                        child: Text('${mineInfoLogic.language.value}',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: Color(0xFFFF3BDF), fontSize: 14)),
                      );
                    })),
                    SizedBox(
                      width: 4,
                    ),
                    Image.asset(
                      'assets/images/icon_down.webp',
                      width: 16,
                      height: 16,
                    )
                  ],
                )),
            offset: Offset(0, -315),
            constraints: BoxConstraints(maxHeight: 320, maxWidth: 120),
            itemBuilder: (context) =>
                S.delegate.supportedLocales.map((Locale data) {
              return PopupMenuItem(
                value: data,
                mouseCursor: MouseCursor.defer,
                onTap: () {},
                height: 40,
                padding:
                    EdgeInsets.only(left: 10, right: 10, top: 1, bottom: 1),
                child: Container(
                  width: 120,
                  height: 40,
                  child: Container(
                    height: 35,
                    width: 100,
                    decoration: BoxDecoration(
                      color:
                          mineInfoLogic.language.value == _getLanguageName(data)
                              ? Color(0x78733D7D)
                              : Colors.transparent,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      '${_getLanguageName(data)}',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  //底部按钮
  Widget _bottonLay() {
    return Container(
      child: Row(
        children: [
          Expanded(
              child: TapHigh(
                  onTap: () {
                    showDialog(
                        context: Get.context!,
                        builder: (context) {
                          return MessageDialog(
                            () {
                              Get.back();
                            },
                            () {
                              mineInfoLogic.getUserDelete();
                            },
                            data: S.of(Get.context!).areYouSureYouWantToDeleteYourAccount,
                            onLeftName: S.of(Get.context!).cancel,
                            onRightName: S.of(Get.context!).confirmDelete,
                            height: 180,
                          );
                        });
                  },
                  child: Container(
                      constraints: BoxConstraints(minHeight: 48.h),
                      padding: EdgeInsets.only(top: 5, bottom: 5),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        // 圆角边框
                        border: Border.all(color: Color(0xFFFF3BDF)), // 外层统一边框
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        S.of(Get.context!).deleteAccount,
                        textAlign: TextAlign.center,
                        style:
                            TextStyle(color: Color(0xFFFF3BDF), fontSize: 16),
                      )))),
          SizedBox(
            width: 24.w,
          ),
          // Container(
          //   color: Color(0X19000000),
          //   width: 30,
          //   height: 30,
          // ),
          Expanded(
              child: TapHigh(
                  onTap: () {
                    showDialog(
                        context: Get.context!,
                        builder: (context) {
                          return MessageDialog(
                            () {
                              Get.back();
                            },
                            () async {
                              Get.find<ApiProvider>().clearToken();
                              await mineInfoLogic.releaseCache();
                              Get.offAll(() => LoginPage());
                            },
                            data: S.of(Get.context!).areYouSureYouWantToSignOutYourAccount,
                            // title: S.of(Get.context!).confirmSignOut,
                            onLeftName: S.of(Get.context!).cancel,
                            onRightName: S.of(Get.context!).signOut,
                            height: 210.h,
                            isTitleCenter: false,
                          );
                        });
                  },
                  child: Container(
                      constraints: BoxConstraints(minHeight: 48.h),
                      padding: EdgeInsets.only(top: 5, bottom: 5),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFFF91EE),
                            Color(0xFFFF3BDF),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        S.of(Get.context!).signOut,
                        style: TextStyle(color: Colors.black, fontSize: 16),
                      ))))
        ],
      ),
    );
  }

  String _getLanguageName(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return 'English';
      case 'es':
        return 'Español';
      case 'fil':
        return 'Filipino';
      case 'fr':
        return 'Français';
      case 'id':
        return 'Bahasa\nIndonesia';
      case 'ja':
        return '日本語';
      case 'ko':
        return '한국어';
      case 'ms':
        return 'Bahasa Melayu';
      case 'pt':
        return 'Português';
      case 'th':
        return 'ภาษาไทย';
      case 'vi':
        return 'Tiếng Việt';
      case 'zh':
        return '简体中文';
      default:
        return 'English'; // 默认语言名称，防止返回 null
    }
  }
}
