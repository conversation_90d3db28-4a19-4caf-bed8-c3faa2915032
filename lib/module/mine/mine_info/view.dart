import 'dart:convert';
import 'dart:io';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/group_chat/agora_logic.dart';
import 'package:new_agnes/module/debug/debug_page.dart';
import 'package:new_agnes/module/debug/global.dart';
import 'package:new_agnes/utils/event_bus.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/photo_utils.dart';
import 'package:new_agnes/utils/stt/AzureSpeechManager.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/EditText.dart';
import 'package:new_agnes/widget/ImageUrl.dart';
import 'package:new_agnes/widget/TapHigh.dart';

import '../../../analytics/AnalyticsService.dart';
import '../../../api/Api.dart';
import '../../../api/ApiProvider.dart';
import '../../../api/StorageService.dart';
import '../../../data/countries.dart';
import '../../../dialog/message_dialog.dart';
import '../../../generated/l10n.dart';
import '../../../utils/bgCover.dart';
import '../../../widget/CountryPickerBottomSheet.dart';
import '../../../widget/GradientBorderContainer.dart';
import '../../../widget/ComAppbar.dart';
import '../../account/login/input_widgets/login_phone_input.dart';
import '../../account/login/page.dart';
import '../../chat/widget/deep_research_lay.dart';
import '../model/UserInfoModel.dart';
import 'logic.dart';

class MineInfoPage extends StatefulWidget {
  MineInfoPage({Key? key}) : super(key: key);

  @override
  State<MineInfoPage> createState() => _MineInfoPageState();
}

class _MineInfoPageState extends State<MineInfoPage> {
  final MineInfoLogic mineInfoLogic = Get.find<MineInfoLogic>();

  List menuList = [0, 1, 2, 3, 4, 5];

  final PhoneInputLogic logic = Get.isRegistered<PhoneInputLogic>()
      ? Get.find<PhoneInputLogic>()
      : Get.put(PhoneInputLogic());

  TextEditingController phoneController = TextEditingController();

  TextEditingController smsController = TextEditingController();

  @override
  void initState() {
    logic.reset();
    mineInfoLogic.reset();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      String currentLanguageCode = Get.find<StorageService>().getLanguage() ??
          Localizations.localeOf(context).languageCode;
      //final currentLocale = Localizations.localeOf(context);
      mineInfoLogic.language.value =
          _getLanguageName(Locale(currentLanguageCode));
      // 初始化发送按钮文字
      logic.send.value = S.of(context).send;
      phoneController.addListener(() {
        logic.phone.value = phoneController.text;
        if (phoneController.text.trim().isNotEmpty) {
          logic.verifyPhone();
        } else {
          logic.isValidPhoneNumber.value = true;
        }
      });
      smsController.addListener(() {
        if (smsController.text.trim().isNotEmpty) {
          mineInfoLogic.isHighlight.value = true;
        } else {
          mineInfoLogic.isHighlight.value = false;
        }
      });
    });

    super.initState();
  }

  @override
  void dispose() {
    Get.delete<PhoneInputLogic>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return true;
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        resizeToAvoidBottomInset: true, // 规避底部布局被软键盘顶起
        body: buildMineBg(
          child: GestureDetector(
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: Column(
              children: [
                ComAppBar(
                  context,
                  S.of(Get.context!).account,
                  onPop: () {
                    Get.back();
                  },
                ),
                Expanded(
                    child: Container(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: EdgeInsets.only(left: 22, right: 22),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 10,
                              ),
                              Text(
                                '${S.of(Get.context!).avatar}',
                                style: TextStyle(
                                    color: Colors.white, fontSize: 24),
                              ),
                              Container(
                                width: 1.sw,
                                margin: EdgeInsets.only(top: 30),
                                alignment: Alignment.center,
                                child: Stack(
                                  children: [
                                    Obx(() {
                                      return Center(
                                        child: UrlImage(
                                            '${mineInfoLogic.userInfoModel.value.avatarUrl}',
                                            width: 145.w,
                                            height: 145.w,
                                            radius: 180,
                                            errorWidget: DefaultUserAvatar()),
                                      );
                                    }),
                                    Positioned(
                                        right: 1.sw / 2 - 70,
                                        bottom: 0,
                                        child: TapHigh(
                                            onTap: () {
                                              FocusScope.of(context)
                                                  .requestFocus(FocusNode());
                                              PhotoUtils.goToPhotoDialog(
                                                  context, 230.w, 2,
                                                  (List<File> fileList) {
                                                if (fileList.length == 1) {
                                                  mineInfoLogic.setUserAvatar(
                                                      fileList[0].path,
                                                      onResult: (data) {
                                                    UserInfoModel
                                                        userInfoModel =
                                                        Get.find<
                                                                StorageService>()
                                                            .getUserInfoData();
                                                    userInfoModel
                                                            .avatarUrl?.value =
                                                        data['avatar_url'];
                                                    Get.find<StorageService>()
                                                        .setUserInfoData(
                                                            jsonEncode(
                                                                userInfoModel
                                                                    .toJson()));
                                                    mineInfoLogic
                                                            .userInfoModel
                                                            .value
                                                            .avatarUrl!
                                                            .value =
                                                        data['avatar_url'];
                                                    eventBus.fire(
                                                        AgoraAvatarEvent());
                                                  });
                                                }
                                              });
                                            },
                                            child: Image.asset(
                                              'assets/images/icon_edit.webp',
                                              width: 24,
                                              height: 24,
                                            )))
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 16,
                              ),
                              Obx(() {
                                return mineInfoLogic.userInfoModel.value
                                        .username!.value.isNotEmpty
                                    ? _textLay(S.of(Get.context!).username,
                                        '${mineInfoLogic.userInfoModel.value.username!.value}',
                                        hasIcon: true, () {
                                        FocusScope.of(context)
                                            .requestFocus(FocusNode());
                                        showDialog(
                                            context: Get.context!,
                                            builder: (context) {
                                              return MessageDialog(
                                                () {
                                                  Get.back();
                                                },
                                                (String data) {
                                                  if (data.isNotEmpty) {
                                                    Get.back();
                                                    mineInfoLogic.setUserInfo(
                                                        data, onResult: () {
                                                      mineInfoLogic
                                                          .userInfoModel
                                                          .value
                                                          .username!
                                                          .value = data;
                                                    });
                                                  } else {
                                                    showFailToast(S
                                                        .of(Get.context!)
                                                        .writeSomething);
                                                  }
                                                },
                                                data: mineInfoLogic
                                                    .userInfoModel
                                                    .value
                                                    .username!
                                                    .value,
                                                onLeftName:
                                                    S.of(Get.context!).cancel,
                                                onRightName:
                                                    S.of(Get.context!).confirm,
                                                height: 220,
                                                isEdit: true,
                                                title: S.of(Get.context!).edit,
                                              );
                                            });
                                      })
                                    : SizedBox();
                              }),
                              mineInfoLogic
                                      .userInfoModel.value.email!.isNotEmpty
                                  ? _textLay(
                                      S.of(Get.context!).email,
                                      '${mineInfoLogic.userInfoModel.value.email}',
                                      () {})
                                  : SizedBox(),
                              Obx(() => mineInfoLogic.userInfoModel.value
                                      .userPhone!.value!.isNotEmpty
                                  ? _textLay(
                                      S.of(Get.context!).phone,
                                      '${mineInfoLogic.userInfoModel.value.userPhone!.value}',
                                      () {}):SizedBox(),
                                  // : _editPhoneLay(S.of(Get.context!).phone)
                              ),
                              _yuYanLay(),
                              SizedBox(
                                height: 60,
                              ),
                              _bottonLay(),
                              SizedBox(
                                height: 20,
                              ),
                              Container(
                                margin:
                                    EdgeInsets.only(top: 40.w, bottom: 40.w),
                                alignment: Alignment.center,
                                child: Text(
                                  mineInfoLogic.version,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Colors.grey,
                                  ),
                                ),
                              )
                              // Container(
                              //   width: 1.sw,
                              //   margin: EdgeInsets.only(top: 40.w),
                              //   alignment: Alignment.centerRight,
                              //   child: Text(
                              //     '版本：v${mineInfoLogic.version}',
                              //     style: TextStyle(
                              //       color: Color(0xFFB65CC5)
                              //           .withAlpha((255.0 * 0.2).round()),
                              //       fontSize: 12.sp,
                              //     ),
                              //   ),
                              // ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                )),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _textLay(name, data, onTap, {bool hasIcon = false}) {
    return TapHigh(
        onTap: () {
          if (onTap != null) {
            onTap();
          }
        },
        child: Container(
          margin: EdgeInsets.only(top: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              SizedBox(
                height: 10,
              ),
              Container(
                  height: 44,
                  padding: EdgeInsets.only(
                      left: 16.w, right: 16.w, top: 5, bottom: 5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10), // 圆角边框
                    border: Border.all(color: Color(0xFF8F8F8F)), // 外层统一边框
                  ),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      Expanded(
                          child: Text(
                        data,
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      )),
                      if (hasIcon) ...[
                        Image.asset(
                          'assets/images/ic_edit_user_name.png',
                          width: 24.w,
                          height: 24.h,
                        )
                      ]
                    ],
                  ))
            ],
          ),
        ));
  }

  Widget _editPhoneLay(name) {
    return Container(
      margin: EdgeInsets.only(top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            style: TextStyle(color: Colors.white, fontSize: 16),
          ),
          SizedBox(
            height: 10,
          ),
          Container(
            height: 44,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10), // 圆角边框
              border: Border.all(color: Color(0xFF8F8F8F)), // 外层统一边框
            ),
            child: Row(
              children: [
                SizedBox(width: 16.w),
                // 国家编码部分
                GestureDetector(
                  onTap: () {
                    FocusScope.of(context).requestFocus(FocusNode());
                    // 选择国家编码
                    CountryPickerDialogX.show(
                      Get.context!,
                      countryList: countryList,
                      selectedCountry: countryList.firstWhere(
                          (item) => item.number == logic.countryCode.value),
                      onCountrySelected: (country) {
                        logic.countryCode.value = country.number;
                        logic.verifyPhone();
                        Get.find<StorageService>()
                            .setUserCountryCode(country.number);
                      },
                      searchText: S.of(Get.context!).searchForACountry,
                      style: CountryPickerStyle(
                        backgroundColor: const Color(0xFF000A19),
                        countryNameStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                        countryCodeStyle: const TextStyle(
                          color: Color(0xFF988B9A),
                          fontSize: 14,
                        ),
                        searchFieldInputDecoration: InputDecoration(
                          hintText: S.of(Get.context!).searchForACountry,
                          hintStyle: const TextStyle(color: Colors.grey),
                          suffixIcon: const Icon(Icons.search),
                        ),
                      ),
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 0, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius:
                          BorderRadius.horizontal(left: Radius.circular(10)),
                    ),
                    child: Obx(() {
                      return Text(
                        "+${logic.countryCode.value}",
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      );
                    }),
                  ),
                ),
                SizedBox(width: 12),
                // 竖线分隔符
                Container(
                  width: 1,
                  height: 27,
                  color: Color(0xFFCADEE3),
                ),

                SizedBox(width: 12),
                // 手机号输入框（占主要空间）
                Expanded(
                  child: TextField(
                    controller: phoneController,
                    style: TextStyle(fontSize: 16, color: Colors.white),
                    keyboardType: TextInputType.phone,
                    textAlignVertical: TextAlignVertical.center,
                    cursorColor: Colors.white,
                    decoration: InputDecoration(
                      hintText: S.of(Get.context!).phoneNumber,
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      isDense: true,
                      hintStyle:
                          TextStyle(fontSize: 16, color: Color(0xFF988B9A)),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                // 倒计时按钮
                GestureDetector(
                  onTap: () {
                    FocusScope.of(context).requestFocus(FocusNode());
                    if (logic.phoneNumberValid &&
                        logic.isValidPhoneNumber.value &&
                        !logic.isCounting) {
                      mineInfoLogic.isShowSms.value = true;
                      // logic.startCountdown(Get.context!, onResult: () {
                      //   //todo 弹出验证码校验弹框
                      //   mineInfoLogic.isShowSms.value = true;
                      // });
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    alignment: Alignment.center,
                    child: Obx(() => Text(
                          "${logic.send.value}",
                          style: TextStyle(
                            fontSize: 16,
                            color: !logic.phoneNumberValid ||
                                    logic.isCounting ||
                                    !logic.isValidPhoneNumber.value
                                ? Colors.grey
                                : Colors.white,
                          ),
                        )),
                  ),
                ),
              ],
            ),
          ),
          Obx(() => Visibility(
              visible: !logic.isValidPhoneNumber.value,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 12),
                  Container(
                    margin: EdgeInsets.only(left: 12),
                    child: Text(
                      S.of(Get.context!).thePhoneNumberIsInvalid,
                      style:
                          TextStyle(fontSize: 12.sp, color: Color(0xFFFE4D4D)),
                    ),
                  ),
                ],
              ))),
          Obx(() => Visibility(
              visible: mineInfoLogic.isShowSms.value,
              child: Container(
                  height: 44,
                  margin: EdgeInsets.only(top: 16.w),
                  padding: EdgeInsets.only(
                      left: 16.w, right: 16.w, top: 5, bottom: 5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10), // 圆角边框
                    border: Border.all(color: Color(0xFF8F8F8F)), // 外层统一边框
                  ),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      Expanded(
                          child: EditText(
                        smsController,
                        size: 16,
                        hint: S.of(Get.context!).verificationCode,
                        textColor: Colors.white,
                        maxLine: 1,
                        hintColor: Color(0xFF988B9A),
                      )),
                      SizedBox(width: 10),
                      TapHigh(
                          onTap: () {
                            if (logic.phoneNumberValid &&
                                logic.isValidPhoneNumber.value &&
                                !logic.isCounting &&
                                mineInfoLogic.isHighlight.value) {
                              mineInfoLogic.getBindPhone(
                                  '+${logic.countryCode.value} ${phoneController.text}',
                                  mineInfoLogic.userInfoModel.value.email,
                                  smsController.text);
                            }
                          },
                          child: Container(
                            child: Text(
                              S.of(Get.context!).confirm,
                              style: TextStyle(
                                  color: mineInfoLogic.isHighlight.value
                                      ? Color(0xFFFF3BDF)
                                      : Color(0xFF988B9A)),
                            ),
                          ))
                    ],
                  ))))
        ],
      ),
    );
  }

  //语言切换
  Widget _yuYanLay() {
    return Container(
      margin: EdgeInsets.only(top: 24),
      child: Row(
        children: [
          Expanded(
              child: Text(
            S.of(Get.context!).interfaceLanguage,
            style: TextStyle(color: Colors.white, fontSize: 16),
          )),
          PopupMenuButton(
            onSelected: (Locale data) {
              mineInfoLogic.language.value = _getLanguageName(data);
              Get.find<StorageService>().setLanguage(data.languageCode);
              Get.updateLocale(data);
              AzureSpeechManager.reInit(data);
            },
            color: Color(0xE52E174F),
            elevation: 1,
            // position: PopupMenuPosition.over,
            splashRadius: 2,
            shadowColor: Color.fromRGBO(46, 23, 79, 0.9),
            surfaceTintColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              // 设置菜单形状
              borderRadius: BorderRadius.circular(10),
              side: BorderSide(color: Color(0xFFFF3BDF)),
            ),
            child: Container(
                width: 120.w,
                padding: EdgeInsets.only(
                    left: 10.w, right: 10.w, top: 8.w, bottom: 10.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Color(0xFFFF3BDF)),
                ),
                alignment: Alignment.centerLeft,
                child: Row(
                  children: [
                    Expanded(child: Obx(() {
                      return Container(
                        alignment: Alignment.center,
                        child: Text('${mineInfoLogic.language.value}',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: Color(0xFFFF3BDF), fontSize: 14)),
                      );
                    })),
                    SizedBox(
                      width: 4,
                    ),
                    Image.asset(
                      'assets/images/icon_down.webp',
                      width: 16,
                      height: 16,
                    )
                  ],
                )),
            offset: Offset(0, -325),
            constraints: BoxConstraints(maxHeight: 320, maxWidth: 120.w),
            itemBuilder: (context) =>
                S.delegate.supportedLocales.map((Locale data) {
              return PopupMenuItem(
                value: data,
                mouseCursor: MouseCursor.defer,
                onTap: () {},
                height: 40,
                padding:
                    EdgeInsets.only(left: 10, right: 10, top: 1, bottom: 1),
                child: Container(
                  width: 120.w,
                  height: 40,
                  child: Container(
                    height: 35,
                    width: 100,
                    decoration: BoxDecoration(
                      color:
                          mineInfoLogic.language.value == _getLanguageName(data)
                              ? Color(0x78733D7D)
                              : Colors.transparent,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      '${_getLanguageName(data)}',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  //底部按钮
  Widget _bottonLay() {
    return Container(
      child: Row(
        children: [
          Expanded(
              child: TapHigh(
                  onTap: () {
                    showDialog(
                        context: Get.context!,
                        builder: (context) {
                          return MessageDialog(
                            () {
                              Get.back();
                            },
                            () {
                              mineInfoLogic.getUserDelete();
                            },
                            data: S
                                .of(Get.context!)
                                .areYouSureYouWantToDeleteYourAccount,
                            onLeftName: S.of(Get.context!).cancel,
                            onRightName: S.of(Get.context!).confirmDelete,
                            height: 180,
                          );
                        });
                  },
                  child: Container(
                      constraints: BoxConstraints(minHeight: 48.h),
                      padding: EdgeInsets.only(top: 5, bottom: 5),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        // 圆角边框
                        border: Border.all(color: Color(0xFFFF3BDF)), // 外层统一边框
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        S.of(Get.context!).deleteAccount,
                        textAlign: TextAlign.center,
                        style:
                            TextStyle(color: Color(0xFFFF3BDF), fontSize: 16),
                      )))),
          SizedBox(
            width: 24.w,
          ),
          // Container(
          //   color: Color(0X19000000),
          //   width: 30,
          //   height: 30,
          // ),
          Expanded(
              child: TapHigh(
                  onTap: () {
                    showDialog(
                        context: Get.context!,
                        builder: (context) {
                          return MessageDialog(
                            () {
                              Get.back();
                            },
                            () async {
                              Get.find<ApiProvider>().clearToken();
                              await mineInfoLogic.releaseCache();
                              Get.offAll(() => LoginPage());
                            },
                            data: S
                                .of(Get.context!)
                                .areYouSureYouWantToSignOutYourAccount,
                            // title: S.of(Get.context!).confirmSignOut,
                            onLeftName: S.of(Get.context!).cancel,
                            onRightName: S.of(Get.context!).signOut,
                            height: 210.h,
                            isTitleCenter: false,
                          );
                        });
                  },
                  child: Container(
                      constraints: BoxConstraints(minHeight: 48.h),
                      padding: EdgeInsets.only(top: 5, bottom: 5),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFFF91EE),
                            Color(0xFFFF3BDF),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        S.of(Get.context!).signOut,
                        style: TextStyle(color: Colors.black, fontSize: 16),
                      ))))
        ],
      ),
    );
  }

  String _getLanguageName(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return 'English';
      case 'es':
        return 'Español';
      case 'fil':
        return 'Filipino';
      case 'fr':
        return 'Français';
      case 'id':
        return 'Bahasa\nIndonesia';
      case 'ja':
        return '日本語';
      case 'ko':
        return '한국어';
      case 'ms':
        return 'Bahasa Melayu';
      case 'pt':
        return 'Português';
      case 'th':
        return 'ภาษาไทย';
      case 'vi':
        return 'Tiếng Việt';
      case 'zh':
        return '简体中文';
      default:
        return 'English'; // 默认语言名称，防止返回 null
    }
  }
}
