import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:mime/mime.dart';
import 'package:new_agnes/module/account/login/logic.dart';
import 'package:new_agnes/utils/NetworkStatusManager.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/loading_util.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart' as path;

import '../../../api/Api.dart';
import '../../../api/ApiProvider.dart';
import '../../../api/StorageService.dart';
import '../../../utils/inAppLogUtil.dart';
import '../../../utils/toastUtil.dart';
import '../../account/login/page.dart';
import '../../chat/group/group_chat/agora_logic.dart';
import '../model/UserInfoModel.dart';

class MineInfoLogic extends GetxController {
  late RxString language = ''.obs;
  late Rx<UserInfoModel> userInfoModel = new UserInfoModel().obs;
  String version = '';
  @override
  void onInit() {
    // TODO: implement onInit
    getUserInfo();
    getVersion();
    super.onInit();
  }

  // 获取用户详情接口
  Future<void> getUserInfo() async {
    if (Get.find<StorageService>().getUserInfoData().username != null) {
      userInfoModel.value = Get.find<StorageService>().getUserInfoData();
    }
    Response res = await Get.find<ApiProvider>().get(
      Api.userInfo,
    );
    if (res.statusCode == 200) {
      final Map<String, dynamic> responseBody = res.body; // 自动解析 JSON
      UserInfoModel model = UserInfoModel.fromJson(responseBody);
      if (model != null) {
        userInfoModel.value = model;
        Get.find<StorageService>().setUserInfoData(jsonEncode(responseBody));
        print('获取用户信息成功');
      }
    }
  }

  // 删除用户信息
  Future<void> getUserDelete() async {
    late Response res;
    res = await Get.find<ApiProvider>().delete(
      Api.userInfo,
    );
    if (res.statusCode == 200) {
      Get.find<ApiProvider>().clearToken();
      await releaseCache();
      Get.offAll(() => LoginPage());
    }
  }

  Future<void> releaseCache() async {
    try {
      try{LoginLogic.cleanLoginInfo();} catch (e) {}
      Get.find<StorageService>()
          .removeHasSync(Get.find<AgoraLogic>().agoraUserId.value);
      Get.find<StorageService>().removeAppKey();
      Get.find<StorageService>().removeDeleteConversation();
      Get.find<StorageService>().removeAgoraInfo();
      Get.find<StorageService>().removeThirdSDKData();
      await ChatClient.getInstance.chatManager
          .deleteAllMessageAndConversation();
      if (Get.find<AgoraLogic>().isJoined.value) {
        try {await ChatClient.getInstance.logout(true);} catch (e) {logError("未登录,无需退出登陆${e.toString()}");}
      }
      Get.delete<NetworkStatusManager>();
      await Get.delete<AgoraLogic>();
    } catch (e) {
      logError("退出登录${e.toString()}");
    }
  }

  //用户信息修改
  Future<void> setUserInfo(newName, {onResult}) async {
    late Response res;
    res = await Get.find<ApiProvider>().post(
      Api.updateUserName,
      {'new_name': newName},
    );
    if (res.statusCode == 200) {
      showSuccessToast(res.body['message']);
      if (onResult != null) {
        onResult();
      }
    }
  }

  //用户头像修改
  Future<void> setUserAvatar(filePath, {onResult}) async {
    LoadingUtil.show();
    String fileName = path.basename(filePath);
    final mimeType = lookupMimeType(fileName) ?? 'image/jpeg';
    int maxSizeKB = 500; // 限制最大500KB
    Uint8List imageBytes = await compressImageIfNeeded(filePath, maxSizeKB);
    String? token = Get.find<StorageService>().getLoginData().accessToken ?? '';
    final form = FormData({
      'file':
          MultipartFile(imageBytes, filename: fileName, contentType: mimeType),
    });
    late Response res;
    res = await Get.find<ApiProvider>().post(
      Api.updateUserAvatar,
      headers: {'Authorization': "Bearer $token"},
      form,
      contentType: "multipart/form-data;boundary=${form.boundary}",
    );
    LoadingUtil.dismiss();
    if (res.statusCode == 200) {
      showSuccessToast(res.body['message']);
      if (res.body != null) {
        if (onResult != null) {
          onResult(res.body);
        }
      }
    }
  }

  Future<Uint8List> compressImageIfNeeded(
      String imagePath, int maxSizeKB) async {
    // 读取图片文件
    File imageFile = File(imagePath);
    Uint8List imageBytes = await imageFile.readAsBytes();

    // 判断图片的大小
    int imageSizeKB = imageBytes.lengthInBytes ~/ 1024; // 转换为 KB
    if (imageSizeKB <= maxSizeKB) {
      // 如果图片小于等于限制大小，直接返回原始字节流
      return imageBytes;
    } else {
      // 如果图片大于限制大小，进行压缩
      img.Image? image = img.decodeImage(imageBytes);
      if (image != null) {
        // 压缩图片，调整为最大宽度和高度为1000px（可以根据需求调整）
        img.Image resizedImage = img.copyResize(image, width: 150);
        // 转换成压缩后的字节流
        return Uint8List.fromList(
            img.encodeJpg(resizedImage, quality: 85)); // 设置压缩质量
      }
      throw Exception('Unable to compress image');
    }
  }

  Future<void> getVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    version = packageInfo.version; //版本号
  }
}
