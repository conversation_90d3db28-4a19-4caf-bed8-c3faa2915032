import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../widget/ComAppbar.dart';

class PermissionPage extends StatefulWidget {
  String quanXianData;
  List<Permission> quanxianList;

  PermissionPage(this.quanXianData, this.quanxianList);

  @override
  State<PermissionPage> createState() => _PermissionPageState();
}

class _PermissionPageState extends State<PermissionPage> {
  @override
  void initState() {

    WidgetsBinding.instance.addPostFrameCallback((callback) {
      getPemission();
    });
    super.initState();
  }

  Future<void> getPemission() async {
    Map<Permission, PermissionStatus> statuses =
        await widget.quanxianList.request();
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: ComAppBar(
          context,
          '权限说明',
          textColor: Color(0xFF333333),
          backgroundColor: Colors.white,
        ),
        resizeToAvoidBottomInset: true, // 规避底部布局被软键盘顶起
        body: MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: Container(
                color: Colors.white,
                padding: EdgeInsets.all(20),
                height: 1.sh,
                width: 1.sw,
                child: Column(
                  children: [
                    SizedBox(
                      height: 100,
                    ),
                    Text(
                      '${widget.quanXianData}',
                      style: TextStyle(fontSize: 20, height: 2.0),
                    ),
                  ],
                ))));
  }
}
