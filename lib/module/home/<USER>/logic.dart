import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/view.dart';
import 'package:new_agnes/utils/AESCryptoUtil.dart';
import 'package:new_agnes/utils/stt/AzureSpeechManager.dart';

import '../../../api/Api.dart';
import '../../../api/ApiProvider.dart';
import '../../../api/StorageService.dart';
import '../../../data/caseAndQuestion.dart';
import '../../../utils/cmUtils.dart';
import '../../../widget/message_input_widget.dart';
import '../../chat/model/third_sdk_model.dart';
import '../../mine/mine_info/logic.dart';
import 'input_widgets/audio_wave_widget.dart';
import 'input_widgets/home_task_input.dart';

class RolesLogic extends GetxController {
  final homeTaskInputLogic = Get.put(HomeTaskInputLogic());
  HomeTaskInputLogic get chatInputLogic => homeTaskInputLogic;
  final mineInfoLogic = Get.put(MineInfoLogic());
  var showCallAgnesPopup = false.obs;
  var completedText = "";
  var speechingText = "";
  var currentType = 1;
  late VoidCallback onNavigate;
  var isRecognizing = true;

  // 添加防抖定时器
  Timer? _navigationTimer;
  final uploadFiles = <MessageUploadModel>[].obs;

  HomeTaskInputLogic create(VoidCallback callback) {
    onNavigate = callback;
    return homeTaskInputLogic;
  }

  Future<void> initSpeech(String subscriptionKey, String region) async {
    if (await AzureSpeechManager.initializeRecognizer(
      subscriptionKey: subscriptionKey,
      region: region,
    )) {
      print("语音服务初始化成功");
    } else {
      print("用户拒绝麦克风权限");
    }

    setSpeechEventListener();
  }

  void setSpeechEventListener(){
    AzureSpeechManager.setSpeechEventListener(this.hashCode,(event) {
      print("收到语音事件: $event");
      switch (event['event']) {
        case 'recognizing':
          debugPrint("正在识别: ${event['text']}");
          // 取消待处理的导航
          _cancelPendingNavigation();
          if (!isRecognizing) {
            return;
          }
          speechingText = event['text'].toString();
          // if (speechingText.trim().isNotEmpty) {
          //   if (currentType == 1) {
          //     homeTaskInputLogic.isRecognizingAni.value = false;
          //   } else {
          //     chatInputLogic.isRecognizingAni.value = false;
          //   }
          // }

          if (currentType == 1) {
            //homeTaskInputLogic.scroll();
            //homeTaskInputLogic.taskController.text = completedText + speechingText;
            homeTaskInputLogic.speeching.value = true;
          } else {
            //chatInputLogic.scroll();
            //chatInputLogic.taskController.text = completedText + speechingText;
            chatInputLogic.speeching.value = true;
          }
          break;
        case 'recognized':
          debugPrint("识别完成: ${event['text']}");
          if (!isRecognizing) {
            return;
          }
          completedText += event['text'].toString();
          if (completedText.isEmpty) {
            completedText = speechingText;
          }
          completedText = completedText.replaceAll("。", ".");
          // if (currentType == 1) {
          //   homeTaskInputLogic.taskController.text = completedText;
          // } else {
          //   chatInputLogic.taskController.text = completedText;
          // }
          if (showCallAgnesPopup.value) {
            showCallAgnesPopup.value = false;
          }

          var isRegistered = Get.isRegistered<AudioWaveLogic>();
          if (isRegistered) {
            var audioWaveLogic = Get.find<AudioWaveLogic>();
            if(audioWaveLogic.isPressedFinished.value){
              if (currentType == 1) {
                print("completedText:${completedText}");
                homeTaskInputLogic.taskController.text = completedText;
                homeTaskInputLogic.speeching.value = false;
              } else {
                print("completedText:${completedText}");
                chatInputLogic.taskController.text = completedText;
                homeTaskInputLogic.speeching.value = false;
              }
              AzureSpeechManager.stopContinuousRecognition();
            }else{
              if (AzureSpeechManager.isFromBle) {
                _scheduleNavigation(completedText);
              }
            }
          }

          // 启动延迟导航，2秒后执行
          // if (currentType == 1) {
          //   _scheduleNavigation(homeTaskInputLogic.taskController.text);
          // } else {
          //   _scheduleNavigation(chatInputLogic.taskController.text);
          // }
          break;
        case 'sessionStarted':
          debugPrint("开始识别");
          var isRegistered = Get.isRegistered<AudioWaveLogic>();
          if (isRegistered) {
            var audioWaveLogic = Get.find<AudioWaveLogic>();
            audioWaveLogic.isPressedFinished.value = false;
          }
          if (currentType == 1) {
            homeTaskInputLogic.isRecognizingAni.value = true;
          } else {
            chatInputLogic.isRecognizingAni.value = true;
          }

          // 新的识别会话开始时取消待处理的导航
          _cancelPendingNavigation();

          if (AzureSpeechManager.isFromBle) {
            completedText = "";
            speechingText = "";
          } else {
            if (currentType == 1) {
              completedText = homeTaskInputLogic.taskController.text;
            } else {
              completedText = chatInputLogic.taskController.text;
            }
            speechingText = "";
          }
          if (currentType == 1) {
            homeTaskInputLogic.speeching.value = true;
          } else {
            chatInputLogic.speeching.value = true;
          }
          break;
        case 'sessionStopped':
          debugPrint("识别结束");

          if (currentType == 1) {
            homeTaskInputLogic.speeching.value = false;
            homeTaskInputLogic.isRecognizingAni.value = false;
            homeTaskInputLogic.isRecognizeEnd.value = true;
          } else {
            chatInputLogic.speeching.value = false;
            chatInputLogic.isRecognizingAni.value = false;
            chatInputLogic.isRecognizeEnd.value = true;
          }

          var isRegistered = Get.isRegistered<AudioWaveLogic>();
          if (isRegistered) {
            var audioWaveLogic = Get.find<AudioWaveLogic>();
            audioWaveLogic.isLoading.value = false;
            if(audioWaveLogic.isPressedFinished.value){
              if (currentType == 1) {
                Future.delayed(Duration(milliseconds: 200),(){
                  homeTaskInputLogic.scroll();
                });
              } else {
                Future.delayed(Duration(milliseconds: 200),(){
                  chatInputLogic.scroll();
                });
              }
            }
          }
          break;
        case 'canceled':
          debugPrint("取消识别: ${event['details'] ?? '未知原因'}");
          if (currentType == 1) {
            homeTaskInputLogic.speeching.value = false;
            homeTaskInputLogic.isRecognizingAni.value = false;
            homeTaskInputLogic.isRecognizeEnd.value = true;
          } else {
            chatInputLogic.speeching.value = false;
            chatInputLogic.isRecognizingAni.value = false;
            chatInputLogic.isRecognizeEnd.value = true;
          }
          // 取消识别时也取消待处理的导航
          _cancelPendingNavigation();
          var isRegistered = Get.isRegistered<AudioWaveLogic>();
          if (isRegistered) {
            var audioWaveLogic = Get.find<AudioWaveLogic>();
            audioWaveLogic.isLoading.value = false;
            if(audioWaveLogic.isPressedFinished.value){
              if (currentType == 1) {
                homeTaskInputLogic.scroll();
              } else {
                chatInputLogic.scroll();
              }
            }
          }
          break;
        default:
          debugPrint("未知事件类型: ${event['event']}");
      }
    });
  }

  // 安排延迟导航
  void _scheduleNavigation(String text) {
    // 取消之前的导航定时器
    _cancelPendingNavigation();

    // 设置新的2秒延迟导航
    _navigationTimer = Timer(Duration(seconds: 1), () {
      if (AzureSpeechManager.isFromBle) {
        // 不发送空消息
        if (text.trim().isEmpty) {
          return;
        }
        var audioWaveLogic = Get.find<AudioWaveLogic>();
        audioWaveLogic.isPressedFinished.value = true;
        if (currentType == 1) {
          print("isFromBle：${completedText}");
          homeTaskInputLogic.isRecognizingAni.value = false;
          // homeTaskInputLogic.taskController.text = completedText;
        } else {
          chatInputLogic.isRecognizingAni.value = false;
          chatInputLogic.taskController.text = completedText;
        }

        _navigateToChatPage(text);

        if (currentType == 1) {
          homeTaskInputLogic.taskController.text = "";
          homeTaskInputLogic.speeching.value = false;
        } else {
          chatInputLogic.taskController.text = "";
          chatInputLogic.speeching.value = false;
        }
        completedText = "";
        speechingText = "";
      }
      AzureSpeechManager.stopContinuousRecognition();
    });
  }

  // 取消待处理的导航
  void _cancelPendingNavigation() {
    _navigationTimer?.cancel();
    _navigationTimer = null;
  }

  // 执行实际的页面导航
  void _navigateToChatPage(String text) {
    if (currentType == 1) {
      Get.to(() => ChatPage(
            text,
            model: homeTaskInputLogic.searchType.value.param,
            searchType: homeTaskInputLogic.searchType.value,
          ));
      completedText = "";
      homeTaskInputLogic.taskController.clear();
      homeTaskInputLogic.task.value = "";
    } else {
      onNavigate.call();
    }
  }

  Future<void> getOtherServiceToken({Function(String appKey)? thirdSDKCallback,Function(String err)? errorCallback}) async {
    var res = await Get.find<ApiProvider>().post(
      Api.fetchThirdPartySDKParameter,
      {"public_key": AESCryptoUtil.getCachedRandomKey()},
    );
    debugPrint("===================解密后数据: ${res.isOk}");

    if (res.isOk) {
      if (res.body['encrypted_data'] != null) {
        var decryptedData =
            AESCryptoUtil.decryptAES256CBCSeparate(res.body['encrypted_data']);
        var data = jsonDecode(decryptedData);
        Get.find<StorageService>().setThirdSDKData(jsonEncode(data));
        ThirdSDKModel thirdSDKModel = ThirdSDKModel.fromJson(data);
        if((thirdSDKModel.AGORA_ORG_NAME??"").isNotEmpty && (thirdSDKModel.AGORA_APP_NAME??"").isNotEmpty){
          String appKey = "${thirdSDKModel.AGORA_ORG_NAME}#${thirdSDKModel.AGORA_APP_NAME}";
          Get.find<StorageService>().setAgoraAppKey(appKey);
          thirdSDKCallback?.call(appKey);
        } else {
          errorCallback?.call("");
        }
        debugPrint("解密后数据: $data");

        // {
        //   "AZURE_SPEECH_SERVICE_KEY": "",
        //   "AZURE_SPEECH_SERVICE_REGION": "",
        //   "AGORA_APP_ID": "3d4824793bcd48fe904a019b30e54c3f"
        // }

        if (data['AZURE_SPEECH_SERVICE_KEY'] == null ||
            data['AZURE_SPEECH_SERVICE_REGION'] == null) {
          debugPrint("获取数据 null 无法初始化数据");
          return;
        }
        initSpeech(data['AZURE_SPEECH_SERVICE_KEY'],
            data['AZURE_SPEECH_SERVICE_REGION']);
      } else {
        errorCallback?.call("cannot get data");
        debugPrint("无法获取数据");
      }
    } else {
      errorCallback?.call("get data err ${res.statusCode}");
      debugPrint("获取数据失败: ${res.statusCode}");
    }
  }

  Future<void> getHotNews() async {
    Response res = await Get.find<ApiProvider>().get(Api.getHotNews);

    if (res.statusCode == 200) {
      if (res.bodyString == null) {
        return;
      }
      final List<dynamic> jsonData = jsonDecode(res.bodyString!);
      List<String> hotQuestions = [];
      for (var item in jsonData) {
        hotQuestions.add(item);
      }
      setHotQuestions(hotQuestions);
    }
  }

  @override
  void onClose() {
    // 释放定时器资源
    _cancelPendingNavigation();
    uploadFiles.clear();
    super.onClose();
  }

  @override
  void onInit() {
    CmUtils.getDeviceData(); //获取设备信息
    uploadFiles.clear();
    super.onInit();
  }

  void releaseCache() {
    completedText = "";
    speechingText = "";
    uploadFiles.clear();
    if (currentType == 1) {
      homeTaskInputLogic.taskController.text = "";
      homeTaskInputLogic.task.value = "";
    } else {
      chatInputLogic.taskController.text = "";
      chatInputLogic.task.value = "";
    }
  }
}
