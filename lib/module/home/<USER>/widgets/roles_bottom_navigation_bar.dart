import 'dart:ui' as ui;
import 'package:blurrycontainer/blurrycontainer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:new_agnes/utils/logger_utils.dart';

import '../../../../widget/message_new_tag.dart';

class RolesBottomNavigationBar extends StatefulWidget {
  final List<RolesBarItem> rolesItems;
  final int curIndex;
  final TextStyle selectTextStyle;
  final TextStyle unSelectTextStyle;
  final double height;
  final ValueChanged<int>? onChangeTab;

  const RolesBottomNavigationBar({
    super.key,
    required this.rolesItems,
    required this.curIndex,
    required this.selectTextStyle,
    required this.unSelectTextStyle,
    required this.height,
    this.onChangeTab,
  });

  @override
  State<RolesBottomNavigationBar> createState() =>
      _RolesBottomNavigationBarState();

  static double getNavigationBarHeight(BuildContext context) {
    return kBottomNavigationBarHeight;
  }
}

class _RolesBottomNavigationBarState extends State<RolesBottomNavigationBar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: widget.height,
      child: Column(
        children: [
          widget.rolesItems[widget.curIndex].text == 'CoVibe'
              ? Container(
                  width: double.infinity,
                  height: 1.w,
                  color: Color(0x3328072D),
                )
              : SizedBox(),
          Expanded(
              child: widget.rolesItems[widget.curIndex].text == 'CoVibe'
                  ? BlurryContainer(
                      elevation: 0,
                      borderRadius: BorderRadius.all(Radius.circular(0)),
                      padding: EdgeInsets.all(0),
                      child: Container(
                          alignment: Alignment.topCenter,
                          child: Row(
                            children: List.generate(widget.rolesItems.length,
                                (index) => _buildItem(index)),
                          )),
                    )
                  : Container(
                      alignment: Alignment.topCenter,
                      child: Row(
                        children: List.generate(widget.rolesItems.length,
                            (index) => _buildItem(index)),
                      )))
        ],
      ),
    );
  }

  Widget _buildItem(int index) {
    RolesBarItem rolesBarItem = widget.rolesItems[index];
    String? iconUrl = widget.curIndex == index
        ? rolesBarItem.selectIconName
        : rolesBarItem.unSelectIconName;

    IconData? icon = rolesBarItem.iconData;
    bool isSelected = widget.curIndex == index;
    bool showBadge = rolesBarItem.text == 'CoVibe';

    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          widget.onChangeTab?.call(index);
        },
        child: Container(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(height: 8.h),
              // Icon with optional top-right red dot badge for CoVibe
              Builder(builder: (_) {
                Widget baseIcon;
                if (iconUrl?.isNotEmpty == true) {
                  baseIcon = Image.asset(
                    iconUrl!,
                    width: 24.w,
                    height: 24.w,
                  );
                } else if (icon != null) {
                  baseIcon = Icon(
                    icon,
                    size: 24.w,
                  );
                } else {
                  baseIcon = SizedBox(
                    width: 24.w,
                    height: 24.w,
                  );
                }

                if (!showBadge) return baseIcon;

                return Stack(
                  clipBehavior: Clip.none,
                  children: [
                    baseIcon,
                    Positioned(
                      right: -1.w,
                      top: -1.h,
                      child: MessageNewTag(),
                    ),
                  ],
                );
              }),
              SizedBox(height: 2.h),
              Text(
                rolesBarItem.text,
                style: isSelected
                    ? widget.selectTextStyle
                    : widget.unSelectTextStyle,
              ),
              SizedBox(height: 4.h),
              // 选中时显示“坐标”图片
              isSelected
                  ? Image.asset(
                      "assets/groupChat/ic_location_indicator.webp",
                      width: 32.w,
                      height: 2.w,
                    )
                  : SizedBox(height: 2.w),
              // const SizedBox(height: 8),
            ],
          ),
        ),
      ),
    );
  }
}

class RolesBarItem {
  final String text;
  final String? selectIconName;
  final String? unSelectIconName;
  final IconData? iconData;

  RolesBarItem({
    required this.text,
    this.selectIconName,
    this.unSelectIconName,
    this.iconData,
  });
}
