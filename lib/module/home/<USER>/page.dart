import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_tilt/flutter_tilt.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:marquee/marquee.dart';
import 'package:new_agnes/module/chat/group/group_chat/agora_logic.dart';
import 'package:new_agnes/module/chat/widget/deep_research_lay.dart';
import 'package:new_agnes/module/home/<USER>/input_widgets/home_drawer.dart';
import 'package:new_agnes/module/mine/mine_info/logic.dart';
import 'package:new_agnes/module/version/manager.dart';
import 'package:new_agnes/utils/ble/BleCallAgnesBottomView.dart';
import 'package:new_agnes/utils/ble/blueToothManager.dart';
import 'package:new_agnes/utils/stt/AzureSpeechManager.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import '../../../data/caseAndQuestion.dart';
import '../../../generated/l10n.dart';
import '../../../utils/cmUtils.dart';
import '../../../utils/event_bus.dart';
import '../../../widget/GradientBorderContainer.dart';
import '../../../widget/MarqueeText.dart';
import '../../../widget/message_new_tag.dart';
import '../../chat/view.dart';
import 'input_widgets/home_task_input.dart';
import 'logic.dart';
import 'widgets/roles_bottom_navigation_bar.dart';

class RolesPage extends StatefulWidget {
  var onBarTap;

  @override
  _RolesPageState createState() => _RolesPageState();
}

class _RolesPageState extends State<RolesPage> with WidgetsBindingObserver {
  final RolesLogic logic = Get.isRegistered<RolesLogic>()
      ? Get.find<RolesLogic>()
      : Get.put(RolesLogic());
  final HomeDrawerLogic homeDrawerLogic = Get.isRegistered<HomeDrawerLogic>()
      ? Get.find<HomeDrawerLogic>()
      : Get.put(HomeDrawerLogic());
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final focusNode = FocusNode();
  double _lastKeyboardHeight = 0;
  List<String> questLists = [];
  late Animation<double> scaleAnimation;
  late AnimationController scaleAnimationController;
  double opacity = 0;

  @override
  void dispose() {
    focusNode.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    const channel =
        String.fromEnvironment('APP_CHANNEL', defaultValue: 'googleplay');
    if (channel == "other") {
      print("checkForUpdate");
      AppUpdateManager().checkForUpdate(context);
    }
    if (shouldRefreshHotQuestions()) {
      logic.getHotNews();
    }
    // logic.getOtherServiceToken();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scaffoldKey.currentState?.isDrawerOpen;
    });

    WidgetsBinding.instance.addObserver(this);

    eventBus.on<String>().listen((data) {
      if (data == "toSearchPage") {
        print("toSearchPage");
        focusNode.requestFocus();
        // logic.homeTaskInputLogic.isShowQuestions.value = true;
        // questLists = getQuestions();
      }
    });

    // Get.find<BlueToothManager>().init();
    // Get.find<BlueToothManager>().setNotificationCallback((data) async {
    //   debugPrint("收到蓝牙数据: $data");
    //   if ((data["type"] ?? "") == "double_click") {
    //     // 检查是否已经初始化，否则弹窗提醒
    //     if (AzureSpeechManager.isInitialized.value) {
    //       // 启动语音识别
    //       AzureSpeechManager.startContinuousRecognition(true);
    //     } else {
    //       AzureSpeechManager.showUnInitializingDialog();
    //       return;
    //     }
    //     if (logic.currentType == 1) {
    //       // 返回到首页
    //       Get.until((route) => route.isFirst);
    //
    //       // 关闭侧边栏
    //       if (_scaffoldKey.currentState?.isDrawerOpen == true) {
    //         Navigator.of(Get.context!).pop();
    //       }
    //
    //       // 不展示底部弹窗
    //       // logic.showCallAgnesPopup.value = true;
    //     }
    //   }
    // });
  }

  @override
  void didChangeMetrics() {
    final currentHeight = WidgetsBinding
        .instance.platformDispatcher.views.first.viewInsets.bottom;
    if (_lastKeyboardHeight > 0 &&
        currentHeight <= 200 &&
        logic.homeTaskInputLogic.isShowQuestions.value) {
      logic.homeTaskInputLogic.isShowQuestions.value = false;
      focusNode.unfocus();
    } else if (_lastKeyboardHeight > 210 &&
        currentHeight > 210 &&
        !logic.homeTaskInputLogic.isShowQuestions.value) {
      logic.homeTaskInputLogic.isShowQuestions.value = true;
      questLists = getQuestions();
    }
    _lastKeyboardHeight = currentHeight;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (logic.homeTaskInputLogic.isShowQuestions.value) {
          //logic.homeTaskInputLogic.isShowQuestions.value = false;
          focusNode.unfocus();
        }
      },
      child: Container(
        child: Container(
          color: Colors.transparent,
          child: Scaffold(
            onDrawerChanged: (isDrawerOpen) async {
              if (isDrawerOpen) {
                focusNode.unfocus();
                await homeDrawerLogic.loadFavouredSearchHistory();
                await homeDrawerLogic.loadNonFavouredSearchHistory();
                await homeDrawerLogic.getQuotaRequest();
              }
            },
            resizeToAvoidBottomInset: true,
            key: _scaffoldKey,
            appBar: AppBar(
              leading: Builder(builder: (BuildContext context) {
                return IconButton(
                  icon: SizedBox(
                    width: 24,
                    height: 24,
                    child: Stack(
                      children: [
                        Center(
                          child: Image.asset(
                            "assets/images/icon_drawer.webp",
                            width: 24,
                            height: 24,
                          ),
                        ),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: MessageNewTag(),
                        ),
                      ],
                    ),
                  ),
                  onPressed: () {
                    CmUtils.zhenDong();
                    eventBus.fire(TabEvent(type: 1));
                    // Scaffold.of(context).openDrawer();
                  },
                );
              }),
              elevation: 0,
              backgroundColor: Colors.transparent,
            ),
            body: SafeArea(child: Obx(() {
              return Container(
                child: Column(
                  children: [
                    Expanded(
                      child: Stack(children: [
                        Visibility(
                          visible:
                              !logic.homeTaskInputLogic.isShowQuestions.value,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Lottie.asset("assets/json/eye.json",
                                      width: 53, height: 57, fit: BoxFit.fill),
                                  Text(
                                    "ello,${S.of(context).imAgnes}.",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 24.sp,
                                      height: 1,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 21.h),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 24.w),
                                child: Text(
                                  S
                                      .of(Get.context!)
                                      .iAssistWithSearchWritingAndAnswers,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16.sp,
                                  ),
                                ),
                              ),
                              // SizedBox(height: 2.h),
                              Text(
                                S.of(Get.context!).assignATaskToBegin,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 16.sp,
                                ),
                              ),
                              SizedBox(height: 28.h),
                            ],
                          ),
                        ),
                        Visibility(
                            visible:
                                logic.homeTaskInputLogic.isShowQuestions.value,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Expanded(
                                    child: Container(
                                        margin: EdgeInsets.only(
                                            top: logic.uploadFiles.length > 0
                                                ? 0.h
                                                : 44.h,
                                            bottom: 1.h),
                                        child: SingleChildScrollView(
                                          child: _buildQuestListInput(),
                                        )))
                              ],
                            )),
                      ]),
                    ),
                    Obx(() => Container(
                          margin: EdgeInsets.only(bottom: 20.h, top: 20.h),
                          child: HomeTaskInput(
                              focusNode,
                              () {
                                String tempValue =
                                    logic.homeTaskInputLogic.task.value;
                                focusNode.unfocus();
                                logic.homeTaskInputLogic.isShowQuestions.value =
                                    false;
                                Future.delayed(Duration(milliseconds: 200), () {
                                  Get.to(ChatPage(
                                    tempValue,
                                    model: logic.homeTaskInputLogic.searchType
                                        .value.param,
                                    searchType: logic
                                        .homeTaskInputLogic.searchType.value,
                                    uploadFiles: List.from(logic.uploadFiles()),
                                  ));
                                  logic.uploadFiles.clear();
                                });
                              },
                              selectedImages: logic.uploadFiles.value,
                              onRemoveImage: (index) {
                                if (logic.uploadFiles.isEmpty) return;
                                logic.uploadFiles.removeAt(index);
                              },
                              onAddImage: (uploadFiles) {
                                logic.uploadFiles.addAll(uploadFiles);
                              },
                              onUpdateImage: (index, uploadFile) {
                                logic.uploadFiles[index] = uploadFile;
                              },
                              logic.homeTaskInputLogic),
                        )),
                  ],
                ),
              );
            })),
          ),
        ),
      ),
    );
  }

  Widget _buildQuestListInput() {
    return GestureDetector(
      onTap: () {
        //FocusScope.of(context).unfocus();
      },
      child: SizedBox(
        child: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: questLists.length,
          itemBuilder: (context, index) {
            final text = questLists[index];
            return _buildPrettyButton(context, text, Color(0xFFFAFFFE), index);
          },
        ),
      ),
    );
  }

  Widget _buildPrettyButton(
      BuildContext context, String text, Color backgroundColor, index) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          splashColor: Colors.teal.shade100.withOpacity(0.2),
          highlightColor: Colors.transparent,
          // 避免高亮遮盖背景
          onTap: () async {
            focusNode.unfocus();
            logic.homeTaskInputLogic.isShowQuestions.value = false;
            Future.delayed(Duration(milliseconds: 200), () {
              Get.to(() => ChatPage(
                    text,
                    model: logic.homeTaskInputLogic.searchType.value.param,
                    searchType: logic.homeTaskInputLogic.searchType.value,
                  ));
            });
          },
          child: GradientBorderContainer.single(
            strokeWidth: 0.5,
            gradient: const LinearGradient(
              colors: [
                Color(0xFF7253FA),
                Color(0xFFFF3BDF),
                Color(0xFF5E57FE),
              ],
              stops: [0.0, 0.32, 1.0],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0x802E174F),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    Container(
                        width: MediaQuery.of(context).size.width - 96,
                        height: 20,
                        child: MarqueeText(
                          containerWidth:
                              MediaQuery.of(context).size.width - 110,
                          text: text,
                          textStyle: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                          marqueeBuilder: (context, text, textStyle) => Marquee(
                            text: text,
                            style: textStyle,
                            scrollAxis: Axis.horizontal,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            blankSpace: 100.0,
                            velocity: 80.0,
                            pauseAfterRound: Duration(milliseconds: 2000),
                            startAfter: Duration(milliseconds: 2000),
                            startPadding: 0.0,
                            accelerationDuration: Duration(milliseconds: 100),
                            accelerationCurve: Curves.linear,
                            decelerationDuration: Duration(milliseconds: 100),
                            decelerationCurve: Curves.easeOut,
                            textDirection: TextDirection.ltr,
                          ),
                          textBuilder: (context, text, textStyle) => Text(
                            text,
                            style: textStyle,
                          ),
                        )),
                    SizedBox(
                      width: 8,
                    ),
                    Image.asset(
                      'assets/images/icon_arrow_in_question.png',
                      width: 20,
                      height: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
