import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/generated/l10n.dart';
import 'package:new_agnes/module/home/<USER>/input_widgets/home_drawer.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/logger_utils.dart';
import 'package:new_agnes/utils/toastUtil.dart';

import '../../../../dialog/message_dialog.dart';

class HistoryItemLong {
  static OverlayEntry? _overlayEntry;

  static void showCustomOverlay(BuildContext context, String title,
      bool favoured, String conversationId, String pageType,
      {Function? onDismiss}) {
    final overlay = Overlay.of(context);
    Widget child = context.widget;
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final offset = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    final screenHeight = MediaQuery.of(context).size.height;

    final double maxHeight = screenHeight -
        MediaQuery.of(context).padding.bottom -
        kBottomNavigationBarHeight -
        kToolbarHeight -
        84.w;
    final showAbove = offset.dy + size.height > maxHeight;
    LoggerUtils.e("showAbove:$showAbove");
    LoggerUtils.e("maxHeight-----${maxHeight}");
    LoggerUtils.e("offset.dy------${offset.dy}");
    _overlayEntry = OverlayEntry(builder: (ctx) {
      return AnimatedScaleOverlay(
        offset: offset,
        size: size,
        title: title,
        favoured: favoured,
        showAbove: showAbove,
        conversationId: conversationId,
        pageType: pageType,
        onDismiss: () {
          hideCustomOverlay();
          onDismiss?.call();
        },
        child: child,
      );
    });

    overlay.insert(_overlayEntry!);
  }

  static void hideCustomOverlay() {
    if (_overlayEntry == null) return;
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

class AnimatedScaleOverlay extends StatefulWidget {
  final Offset offset;
  final Size size;
  final Widget child;
  final VoidCallback onDismiss;
  final String title;
  final bool favoured;
  final bool showAbove;
  final String conversationId;
  final String pageType;

  const AnimatedScaleOverlay({
    super.key,
    required this.offset,
    required this.size,
    required this.child,
    required this.onDismiss,
    required this.title,
    required this.favoured,
    required this.showAbove,
    required this.conversationId,
    required this.pageType,
  });

  @override
  State<AnimatedScaleOverlay> createState() => _AnimatedScaleOverlayState();
}

class _AnimatedScaleOverlayState extends State<AnimatedScaleOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  double sizeWidth = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.05,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    // 启动进入动画
    _controller.forward();
    CmUtils.zhenDong();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // 添加退出动画方法
  Future<void> animateOut() async {
    await _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        // 先播放退出动画，再执行回调
        await animateOut();
        widget.onDismiss();
      },
      child: Stack(
        children: [
          Positioned.fill(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
              child: ColoredBox(color: const Color(0xCC1D1236)),
            ),
          ),
          Positioned(
            left: widget.offset.dx,
            top: widget.offset.dy,
            child: AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Opacity(
                      opacity: _opacityAnimation.value,
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxWidth: 1.sw,
                        ),
                        child: IgnorePointer(
                          child: Padding(
                            padding: EdgeInsets.only(right: 32.w),
                            child: Container(
                              width: widget.size.width,
                              height: widget.size.height,
                              decoration: BoxDecoration(
                                color: Color(0x78733D7D),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: widget.child,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }),
          ),
          _buildPositionedMenu(),
        ],
      ),
    );
  }

  Widget _buildPositionedMenu() {
    double? left, right, top;
    right = MediaQuery.of(context).size.width -
        (widget.offset.dx + widget.size.width);
    // right = widget.offset.dx + widget.size.width;

    if (widget.showAbove) {
      top = widget.offset.dy - 166.h;
    } else {
      top = widget.offset.dy + 36.w + 8.w;
    }
    return Positioned(
      left: left,
      right: right,
      top: top,
      child: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          constraints: const BoxConstraints(
              // minWidth: 100,
              ),
          decoration: BoxDecoration(
            border: Border.fromBorderSide(
                BorderSide(color: Color(0xFFFF3BDF), width: 1.w)),
            color: Color(0xE52E174F),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Color(0x40000000),
                blurRadius: 4,
                spreadRadius: 0,
              )
            ],
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 8.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _buildMenuItemsWithDividers(widget.title,
                  widget.favoured, widget.conversationId, widget.pageType),
            ),
          ),
        ),
      ),
    );
  }
}

final List<Map<String, dynamic>> _menuItems = [
  {
    'icon': 'assets/images/ic_chart_top_shoucang_no.webp',
    'text': S.of(Get.context!).collect
  },
  {
    'icon': 'assets/images/ic_chart_top_share.png',
    'text': S.of(Get.context!).share
  },
  {
    'icon': 'assets/images/ic_chart_top_edit.webp',
    'text': S.of(Get.context!).rename
  },
  {
    'icon': 'assets/icon/icon_drawer_delete.webp',
    'text': S.of(Get.context!).delete
  }
];

List<Widget> _buildMenuItemsWithDividers(
    String title, bool favoured, String conversationId, String pageType) {
  List<Widget> itemsWithDividers = [];

  for (int i = 0; i < _menuItems.length; i++) {
    // 添加菜单项
    itemsWithDividers.add(
      Material(
        borderRadius: BorderRadius.circular(8),
        color: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6),
          child: _buildItem(
              _menuItems[i], i, title, favoured, conversationId, pageType),
        ),
      ),
    );

    // 如果不是最后一个项，则添加分割线
    if (i < _menuItems.length - 1) {
      itemsWithDividers.add(Container(
        height: 1.h,
        decoration: BoxDecoration(
          color: Colors.white,
        ),
      ));
    }
  }

  return itemsWithDividers;
}

OverlayEntry? _deleteEntry;

void hideDeleteOverlay() {
  if (_deleteEntry == null) return;
  _deleteEntry?.remove();
  _deleteEntry = null;
}

showDeleteDialog(String conversationId, String pageType, String title) {
  _deleteEntry = OverlayEntry(
    builder: (context) => MessageDialog(
      () async {
        hideDeleteOverlay();
      },
      () async {
        var homeDrawerLogic = Get.find<HomeDrawerLogic>();
        homeDrawerLogic.delete(conversationId, pageType);
        hideDeleteOverlay();
        HistoryItemLong.hideCustomOverlay();
      },
      data: title,
      title: S.of(Get.context!).deleteChat,
      isTitleCenter: true,
      onLeftName: S.of(Get.context!).cancel,
      onRightName: S.of(Get.context!).delete,
      height: 208.h,
    ),
  );
  Overlay.of(Get.context!).insert(_deleteEntry!);
}

OverlayEntry? _renameEntry;

void hideRenameOverlay() {
  if (_renameEntry == null) return;
  _renameEntry?.remove();
  _renameEntry = null;
}

showRenameDialog(bool favoured, String title, String conversationId) {
  _renameEntry = OverlayEntry(
    builder: (context) => MessageDialog(
      () {
        hideRenameOverlay();
      },
      (String data) {
        if (data.isNotEmpty) {
          var homeDrawerLogic = Get.find<HomeDrawerLogic>();
          homeDrawerLogic.collectionOrEdit(2, favoured, title, conversationId,
              editText: data);
          hideRenameOverlay();
          HistoryItemLong.hideCustomOverlay();
        } else {
          showFailToast(S.of(Get.context!).writeSomething);
        }
      },
      data: title,
      onLeftName: S.of(Get.context!).cancel,
      onRightName: S.of(Get.context!).confirm,
      height: 220,
      isEdit: true,
      title: S.of(Get.context!).edit,
    ),
  );
  Overlay.of(Get.context!).insert(_renameEntry!);
}

Widget _buildItem(dynamic item, int index, String title, bool favoured,
    String conversationId, String pageType) {
  return InkWell(
      splashColor: Colors.purple.withOpacity(0.3),
      highlightColor: Colors.purple.withOpacity(0.1),
      borderRadius: BorderRadius.circular(8),
      onTap: () {
        var homeDrawerLogic = Get.find<HomeDrawerLogic>();
        if (index == 0) {
          HistoryItemLong.hideCustomOverlay();
          homeDrawerLogic.collectionOrEdit(1, favoured, title, conversationId);
        } else if (index == 1) {
          HistoryItemLong.hideCustomOverlay();
          homeDrawerLogic.share(conversationId, title);
        } else if (index == 2) {
          //HistoryItemLong.hideCustomOverlay();
          showRenameDialog(
            favoured,
            title,
            conversationId,
          );
        } else if (index == 3) {
          // HistoryItemLong.hideCustomOverlay();
          showDeleteDialog(conversationId, pageType, title);
        }
      },
      child: Container(
        height: 36.h,
        padding: EdgeInsets.only(left: 10.w, right: 10.w),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(10),
        ),
        constraints: BoxConstraints(
            // minWidth: 151.w,
            ),
        child: Row(
          children: [
            index == 0
                ? Image.asset(
                    favoured
                        ? "assets/images/ic_chary_top_shoucang_yes.webp"
                        : item['icon'],
                    width: 20.w,
                    height: 20.h,
                  )
                : Image.asset(
                    item['icon'],
                    width: 20.w,
                    height: 20.h,
                  ),
            SizedBox(width: 8.w),
            Text(
              item['text'],
              style: TextStyle(
                fontSize: 16.sp,
                color: index == 3 ? Color(0xFFE52626) : Colors.white,
              ),
            ),
          ],
        ),
      ));
}
