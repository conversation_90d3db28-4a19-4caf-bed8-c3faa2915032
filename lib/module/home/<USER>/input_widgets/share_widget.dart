import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../api/Api.dart';
import '../../../../generated/l10n.dart';

final List<Map<String, dynamic>> shareIcons = [
  {
    'icon': 'assets/icon/icon_google.webp',
    'type': "google",
  },
  {
    'icon': 'assets/icon/icon_apple.webp',
    'type': "apple",
  },
  {
    'icon': 'assets/icon/icon_f.webp',
    'type': "facebook",
  },
  {
    'icon': 'assets/icon/icon_p.webp',
    'type': "p_share",
  },
  {
    'icon': 'assets/icon/icon_in.webp',
    'type': "in_share",
  },
  {
    'icon': 'assets/icon/icon_x.webp',
    'type': "x_share",
  },
];

void showShareInfoDialog(BuildContext context, String conversationId, String title) {
  String shareUrl = "${Api.baseUrl}/share?conversationid=$conversationId";
  var shareLogic = Get.put(ShareLogic());
  showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: "Share Info",
    barrierColor: Colors.black.withOpacity(0.2),
    // 半透明遮罩
    pageBuilder: (context, animation, secondaryAnimation) {
      return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8), // 毛玻璃模糊
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.delete<ShareLogic>();
                      Navigator.of(context).pop();
                    },
                    child: Image.asset(
                      "assets/images/icon_dialog_close.webp",
                      width: 24,
                      height: 24,
                    ),
                  ),
                  SizedBox(height: 24),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
                    child: GradientBorderContainer(
                      borderRadius: BorderRadius.all(Radius.circular(21)),
                      strokeWidth: 2,
                      gradients: [
                        LinearGradient(
                          colors: [
                            Color(0xFFFF3BDF),
                            Color(0x08FF3BDF),
                            Color(0xFF00FFFF),
                            Color(0xFF543C86),
                          ],
                          stops: [0, 0.34, 0.76, 1],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ],
                      child: Container(
                        height: 368,
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                            image:
                                AssetImage('assets/images/bg_message.png'),
                            // 背景图路径
                            fit: BoxFit.fill,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "share",
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.w600),
                            ),
                            SizedBox(
                              height: 16,
                            ),
                            Container(
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                      color: Color(0xFF8F8F8F), width: 1)),
                              child: Padding(
                                padding: EdgeInsets.all(16),
                                child: Text(shareUrl,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400)),
                              ),
                            ),
                            SizedBox(
                              height: 48,
                            ),
                            Text("Share your work on the following platforms.",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600)),
                            SizedBox(
                              height: 12,
                            ),

                            Expanded(
                                child: Padding(
                              padding: EdgeInsets.only(left: 16),
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: shareIcons.length,
                                itemBuilder: (context, index) {
                                  return GestureDetector(
                                    onTap: () {
                                      // showSuccessToast(
                                      //     "share ${shareIcons[index]['type']}");
                                      shareLogic.share(title, shareUrl);
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.only(right: 16.w),
                                      child: Image.asset(
                                        shareIcons[index]['icon'],
                                        width: 32.w,
                                        height: 32.h,
                                        fit: BoxFit.fitWidth,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            )),
                            SizedBox(
                              height: 42,
                            ),
                            GestureDetector(
                              onTap: () {
                                Clipboard.setData(ClipboardData(text: shareUrl));
                                showSuccessToast(S.of(Get.context!).copied);
                                shareLogic.isCopyFinished.value = true;
                              },
                              child: Container(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 6, horizontal: 14),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                          color: Color(0xFFFF3BDF), width: 1)),
                                  child: Obx(
                                    () => Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Visibility(
                                          visible: !shareLogic.isCopyFinished.value,
                                          child: Image.asset(
                                            "assets/images/icon_link.webp",
                                            width: 16,
                                            height: 16,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 6,
                                        ),
                                        Text("copy link",
                                            style: TextStyle(
                                                color: Color(0xFFFF3BDF),
                                                fontSize: 16,
                                                fontWeight: FontWeight.w400)),
                                      ],
                                    ),
                                  )),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
    transitionDuration: const Duration(milliseconds: 200),
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return FadeTransition(
        opacity: animation,
        child: ScaleTransition(
          scale: CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutBack,
          ),
          child: child,
        ),
      );
    },
  );
}

class ShareLogic extends GetxController {
  var isCopyFinished = false.obs;

  Future<void> share(String title, String shareUrl) async {
    print("shareUrl:" + shareUrl);
    SharePlus.instance.share(
      ShareParams(
        title: title,
        uri: Uri.parse(shareUrl),
      ),
    );
  }

  Future<String?> getClipboardText() async {
    final clipboardData = await Clipboard.getData('text/plain');
    return clipboardData?.text;
  }
}
