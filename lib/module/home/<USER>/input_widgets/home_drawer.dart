import 'dart:convert';
import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:new_agnes/main_tab_logic.dart';
import 'package:new_agnes/main_tab_page.dart';
import 'package:new_agnes/module/chat/group/notifications/notifications_view.dart';
import 'package:new_agnes/module/chat/view.dart';
import 'package:new_agnes/module/home/<USER>/input_widgets/audio_wave_widget.dart';
import 'package:new_agnes/module/home/<USER>/page.dart';
import 'package:new_agnes/module/mine/mine_info/logic.dart';
import 'package:new_agnes/module/mine/mine_info/view.dart';
import 'package:new_agnes/module/mine/vibe_pods/view.dart';
import 'package:new_agnes/module/web/single_page_web/page.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/event_bus.dart';
import 'package:new_agnes/utils/stt/AzureSpeechManager.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../api/Api.dart';
import '../../../../api/ApiProvider.dart';
import '../../../../api/StorageService.dart';
import '../../../../data/caseAndQuestion.dart';
import '../../../../generated/l10n.dart';
import '../../../../widget/GradientBorderContainer.dart';
import '../../../../widget/ImageUrl.dart';
import '../../../../widget/dashedUnderlineText.dart';
import '../../../../widget/message_new_tag.dart';
import '../../../chat/logic.dart';
import '../logic.dart';
import 'history_item_long.dart';

class HomeDrawer extends StatefulWidget {
  final String pageType;
  final BuildContext context;
  var onScrollBottom;

  HomeDrawer(this.context, this.pageType, {super.key, this.onScrollBottom});

  @override
  State<HomeDrawer> createState() => _HomeDrawerState();
}

class _HomeDrawerState extends State<HomeDrawer>
    with SingleTickerProviderStateMixin {
  final HomeDrawerLogic logic = Get.find<HomeDrawerLogic>();
  final MineInfoLogic mineInfoLogic = Get.find<MineInfoLogic>();
  final ScrollController _drawerScrollController = ScrollController();
  late AnimationController animationController;
  late List<dynamic> combinedList = [];
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  var historySelectedId = "";
  final MainTabLogic mainTabLogic = Get.isRegistered<MainTabLogic>()
      ? Get.find<MainTabLogic>()
      : Get.put(MainTabLogic());

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.72,
      color: Color(0xFF1A031E),
      child: Obx(
        () => Column(
          children: [
            SizedBox(height: 64),
            logic.isHistoryLoadEnd.value
                ? logic.historyItems.isEmpty
                    ? Expanded(
                        child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Align(
                            child: Container(
                                margin: EdgeInsets.only(right: 16.w),
                                height: 36,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.white,
                                  gradient: LinearGradient(
                                    colors: [
                                      Color(0xFFFF91EE),
                                      Color(0xFFFF3BDF),
                                    ],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                  ),
                                ),
                                child: Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 14),
                                  child: TextButton(
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      foregroundColor: Colors.white,
                                    ),
                                    onPressed: () {
                                      if (widget.pageType == "home") {
                                        var rolesLogic = Get.find<RolesLogic>();
                                        if (rolesLogic.homeTaskInputLogic
                                            .isRecognizingAni.value) {
                                          WidgetsBinding.instance
                                              .addPostFrameCallback((_) {
                                            var audioWaveLogic =
                                                Get.find<AudioWaveLogic>();
                                            audioWaveLogic.init();
                                            rolesLogic.homeTaskInputLogic
                                                .isRecognizingAni.value = false;
                                            AzureSpeechManager
                                                .stopContinuousRecognition();
                                          });
                                        }
                                        Navigator.of(context).pop();
                                        Future.delayed(
                                            Duration(milliseconds: 80), () {
                                          eventBus.fire("toSearchPage");
                                        });
                                      } else {
                                        var rolesLogic = Get.find<RolesLogic>();
                                        // if(rolesLogic.chatInputLogic.isRecognizingAni.value) {
                                        //   WidgetsBinding.instance
                                        //       .addPostFrameCallback((_) {
                                        //     var audioWaveLogic = Get.find<
                                        //         AudioWaveLogic>();
                                        //     audioWaveLogic.init();
                                        //   });
                                        // }
                                        rolesLogic.homeTaskInputLogic.searchType
                                            .value = SearchType.tools;
                                        rolesLogic.homeTaskInputLogic
                                            .selectedItem.value = {};
                                        Get.until((route) => route.isFirst);
                                        Future.delayed(
                                            Duration(milliseconds: 160), () {
                                          eventBus.fire("toSearchPage");
                                        });
                                        if (AzureSpeechManager.isFromBle) {
                                          AzureSpeechManager
                                              .stopContinuousRecognition();
                                        }
                                      }
                                    },
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Image.asset(
                                          "assets/images/icon_drawer_add.webp",
                                          width: 20,
                                          height: 20,
                                        ),
                                        SizedBox(
                                          width: 6,
                                        ),
                                        Text(
                                          S.of(Get.context!).newTask,
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Color((0xFF0D0D0D)),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )),
                            alignment: Alignment.centerRight,
                          ),
                          Visibility(
                              visible: mainTabLogic.isFeatureGroup.value,
                              child: Padding(
                                padding: EdgeInsets.only(left: 24.w, top: 24.h),
                                child: GestureDetector(
                                  onTap: () {
                                    if (widget.pageType == "home") {
                                      var rolesLogic = Get.find<RolesLogic>();
                                      if (rolesLogic.homeTaskInputLogic
                                          .isRecognizingAni.value) {
                                        WidgetsBinding.instance
                                            .addPostFrameCallback((_) {
                                          var audioWaveLogic =
                                              Get.find<AudioWaveLogic>();
                                          audioWaveLogic.init();
                                          rolesLogic.homeTaskInputLogic
                                              .isRecognizingAni.value = false;
                                          AzureSpeechManager
                                              .stopContinuousRecognition();
                                        });
                                      }
                                      Get.back();
                                      var mainTabLogic =
                                          Get.find<MainTabLogic>();
                                      mainTabLogic.curIndex.value = 1;
                                    } else {
                                      var rolesLogic = Get.find<RolesLogic>();
                                      rolesLogic.homeTaskInputLogic.searchType
                                          .value = SearchType.tools;
                                      rolesLogic.homeTaskInputLogic.selectedItem
                                          .value = {};
                                      Get.back();
                                      Get.back();
                                      var mainTabLogic =
                                          Get.find<MainTabLogic>();
                                      mainTabLogic.curIndex.value = 1;
                                    }
                                  },
                                  child: Row(
                                    children: [
                                      Image.asset(
                                        "assets/icon/icon_coVibe.webp",
                                        width: 20.w,
                                        height: 20.h,
                                      ),
                                      SizedBox(width: 8.w),
                                      Text(
                                        "CoVibe",
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.white,
                                            fontWeight: FontWeight.w500),
                                      )
                                    ],
                                  ),
                                ),
                              )),
                          SizedBox(height: 32.h),
                          Padding(
                            padding: EdgeInsets.only(left: 24.w),
                            child: InkWell(
                              onTap: () {
                                if (widget.pageType == "home") {
                                  Get.back();
                                } else {
                                  Get.until((route) => route.isFirst);
                                }
                              },
                              child: DashedUnderlineText(
                                text: S.of(context).chat,
                                style: TextStyle(
                                    fontSize: 14, color: Color(0xFF988B9A)),
                                dashColor: Colors.grey,
                                dashHeight: 2,
                              ),
                            ),
                          ),
                          SizedBox(height: 133.h),
                          Align(
                            alignment: Alignment.center,
                            child: Stack(
                              alignment: AlignmentDirectional.centerStart,
                              children: [
                                Image.asset(
                                  "assets/images/bg_create.webp",
                                  width: 61.w,
                                  height: 37.h,
                                  fit: BoxFit.fill,
                                ),
                                Padding(
                                  padding: EdgeInsets.only(left: 11),
                                  child: Text(
                                    S.of(context).createANewTaskToGetStarted,
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 16),
                                  ),
                                )
                              ],
                            ),
                          )
                        ],
                      ))
                    : Expanded(
                        child: Padding(
                        padding: EdgeInsets.only(left: 16.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Align(
                              alignment: Alignment.centerRight,
                              child: Container(
                                  margin: EdgeInsets.only(right: 16.w),
                                  height: 36,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    color: Colors.white,
                                    gradient: LinearGradient(
                                      colors: [
                                        Color(0xFFFF91EE),
                                        Color(0xFFFF3BDF),
                                      ],
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                    ),
                                  ),
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 14),
                                    child: TextButton(
                                      style: TextButton.styleFrom(
                                        padding: EdgeInsets.zero,
                                        foregroundColor: Colors.white,
                                      ),
                                      onPressed: () {
                                        if (widget.pageType == "home") {
                                          var rolesLogic =
                                              Get.find<RolesLogic>();
                                          if (rolesLogic.homeTaskInputLogic
                                              .isRecognizingAni.value) {
                                            WidgetsBinding.instance
                                                .addPostFrameCallback((_) {
                                              var audioWaveLogic =
                                                  Get.find<AudioWaveLogic>();
                                              audioWaveLogic.init();
                                              rolesLogic
                                                  .homeTaskInputLogic
                                                  .isRecognizingAni
                                                  .value = false;
                                              AzureSpeechManager
                                                  .stopContinuousRecognition();
                                            });
                                          }
                                          Navigator.of(context).pop();
                                          Future.delayed(
                                              Duration(milliseconds: 80), () {
                                            eventBus.fire("toSearchPage");
                                          });
                                        } else {
                                          var rolesLogic =
                                              Get.find<RolesLogic>();
                                          // if(rolesLogic.chatInputLogic.isRecognizingAni.value) {
                                          //   WidgetsBinding.instance.addPostFrameCallback((_) {
                                          //     var audioWaveLogic = Get.find<AudioWaveLogic>();
                                          //     audioWaveLogic.init();
                                          //   });
                                          // }
                                          rolesLogic
                                              .homeTaskInputLogic
                                              .searchType
                                              .value = SearchType.tools;
                                          rolesLogic.homeTaskInputLogic
                                              .selectedItem.value = {};
                                          Get.until((route) => route.isFirst);
                                          Future.delayed(
                                              Duration(milliseconds: 160), () {
                                            eventBus.fire("toSearchPage");
                                          });
                                        }
                                      },
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Image.asset(
                                            "assets/images/icon_drawer_add.webp",
                                            width: 20,
                                            height: 20,
                                          ),
                                          SizedBox(
                                            width: 6,
                                          ),
                                          Text(
                                            S.of(Get.context!).newTask,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Color((0xFF0D0D0D)),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )),
                            ),
                            Visibility(
                                visible: mainTabLogic.isFeatureGroup.value,
                                child: Padding(
                                  padding:
                                      EdgeInsets.only(left: 8.w, top: 26.h),
                                  child: GestureDetector(
                                    onTap: () {
                                      if (widget.pageType == "home") {
                                        var rolesLogic = Get.find<RolesLogic>();
                                        if (rolesLogic.homeTaskInputLogic
                                            .isRecognizingAni.value) {
                                          WidgetsBinding.instance
                                              .addPostFrameCallback((_) {
                                            var audioWaveLogic =
                                                Get.find<AudioWaveLogic>();
                                            audioWaveLogic.init();
                                            rolesLogic.homeTaskInputLogic
                                                .isRecognizingAni.value = false;
                                            AzureSpeechManager
                                                .stopContinuousRecognition();
                                          });
                                        }
                                        Get.back();
                                        var mainTabLogic =
                                            Get.find<MainTabLogic>();
                                        mainTabLogic.curIndex.value = 1;
                                      } else {
                                        var rolesLogic = Get.find<RolesLogic>();
                                        rolesLogic.homeTaskInputLogic.searchType
                                            .value = SearchType.tools;
                                        rolesLogic.homeTaskInputLogic
                                            .selectedItem.value = {};
                                        Get.back();
                                        Get.back();
                                        var mainTabLogic =
                                            Get.find<MainTabLogic>();
                                        mainTabLogic.curIndex.value = 1;
                                      }
                                    },
                                    child: Row(
                                      children: [
                                        Stack(
                                          children: [
                                            Container(
                                              width: 24,
                                              height: 24,
                                              padding: EdgeInsets.all(2),
                                              child: Image.asset(
                                                "assets/icon/icon_coVibe.webp",
                                                width: 20.w,
                                                height: 20.h,
                                              ),
                                            ),
                                            Positioned(
                                              right: 0,
                                              top: 0,
                                              child: MessageNewTag(),
                                            ),
                                          ],
                                        ),
                                        SizedBox(width: 8.w),
                                        Text(
                                          "CoVibe",
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w500),
                                        )
                                      ],
                                    ),
                                  ),
                                )),
                            SizedBox(height: 32.h),
                            Padding(
                              padding: EdgeInsets.only(left: 8.w),
                              child: InkWell(
                                onTap: () {
                                  if (widget.pageType == "home") {
                                    Get.back();
                                  } else {
                                    Get.until((route) => route.isFirst);
                                  }
                                },
                                child: DashedUnderlineText(
                                  text: S.of(context).chat,
                                  style: TextStyle(
                                      fontSize: 14, color: Color(0xFF988B9A)),
                                  dashColor: Colors.grey,
                                  dashHeight: 2,
                                ),
                              ),
                            ),
                            SizedBox(height: 12.w),
                            Expanded(
                              child: SmartRefresher(
                                header: ClassicHeader(
                                  idleText: '',
                                  releaseText: '',
                                  refreshingText: '',
                                  completeText: '',
                                  failedText: '',
                                  refreshingIcon: SpinKitFadingCircle(
                                    color: Color(0xCFFC944E9),
                                    size: 25.w,
                                  ),
                                  textStyle: TextStyle(
                                      fontSize: 14, color: Colors.grey),
                                ),
                                footer: ClassicFooter(
                                  idleText: '',
                                  loadingText: S.of(context).loading,
                                  noDataText: S.of(context).youveReachedTheEnd,
                                  failedText: '',
                                  canLoadingText: '',
                                  textStyle: TextStyle(
                                      fontSize: 14, color: Colors.grey),
                                ),
                                controller: _refreshController,
                                enablePullUp: true,
                                enablePullDown: true,
                                onRefresh: _onRefresh,
                                onLoading: _onLoading,
                                child: ListView.builder(
                                  controller: _drawerScrollController,
                                  shrinkWrap: true,
                                  padding: EdgeInsets.only(right: 24.w),
                                  itemCount: getConformityLists().length,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    final item = combinedList.isNotEmpty
                                        ? combinedList[index]
                                        : getConformityLists()[index];
                                    if (item == 'divider') {
                                      return Divider(
                                        height: 20.h,
                                        thickness: 1,
                                        color: Color(0x78733D7D), // 分割线颜色
                                      );
                                    } else if (item is Map<String, dynamic>) {
                                      var isSelected;
                                      if (historySelectedId.isEmpty) {
                                        var registered =
                                            Get.isRegistered<ChatLogic>();
                                        if (registered) {
                                          historySelectedId =
                                              Get.find<ChatLogic>()
                                                  .historySelectedId;
                                          isSelected =
                                              historySelectedId == item['id'];
                                        } else {
                                          isSelected = false;
                                        }
                                      } else {
                                        isSelected =
                                            historySelectedId == item['id'];
                                      }
                                      return StatefulBuilder(
                                          builder: (context, state) {
                                        return Material(
                                          color: Colors.transparent,
                                          child: InkWell(
                                            borderRadius:
                                                BorderRadius.circular(8.r),
                                            splashColor: Color(0xFFB95CC9)
                                                .withOpacity(0.2),
                                            highlightColor: Color(0xFFB95CC9)
                                                .withOpacity(0.2),
                                            onLongPress: () {
                                              HistoryItemLong.showCustomOverlay(
                                                  context,
                                                  item['title'],
                                                  item['favoured'],
                                                  item['id'],
                                                  widget.pageType);
                                            },
                                            onTap: () {
                                              if (widget.pageType == "home") {
                                                var logic =
                                                    Get.find<RolesLogic>();
                                                if (logic.homeTaskInputLogic
                                                    .speeching.value) {
                                                  WidgetsBinding.instance
                                                      .addPostFrameCallback(
                                                          (_) {
                                                    var audioWaveLogic = Get
                                                        .find<AudioWaveLogic>();
                                                    audioWaveLogic.init();
                                                    logic
                                                        .homeTaskInputLogic
                                                        .isRecognizingAni
                                                        .value = false;
                                                    logic
                                                        .homeTaskInputLogic
                                                        .speeching
                                                        .value = false;
                                                    AzureSpeechManager
                                                        .stopContinuousRecognition();
                                                  });
                                                  // logic.homeTaskInputLogic.isRecognizingAni.value = false;
                                                  // print(
                                                  //     "stopContinuousRecognition");
                                                  // AzureSpeechManager.stopContinuousRecognition();
                                                  // logic.homeTaskInputLogic.speeching.value = false;
                                                }
                                                logic.releaseCache();
                                                Get.back();
                                                Get.to(() => ChatPage(
                                                      item['title'],
                                                      conversationId:
                                                          item["id"],
                                                      favoured:
                                                          item["favoured"],
                                                    ));
                                              } else {
                                                Get.back();
                                                Get.find<ChatLogic>()
                                                        .historySelectedId =
                                                    item["id"];
                                                var rolesLogic =
                                                    Get.find<RolesLogic>();
                                                rolesLogic
                                                    .chatInputLogic
                                                    .searchType
                                                    .value = SearchType.tools;
                                                rolesLogic.chatInputLogic
                                                    .selectedItem.value = {};
                                                ChatLogic chatLogic =
                                                    Get.find<ChatLogic>();
                                                if (chatLogic.conversationId !=
                                                    '${item['id']}') {
                                                  if (rolesLogic.chatInputLogic
                                                      .isRecognizingAni.value) {
                                                    WidgetsBinding.instance
                                                        .addPostFrameCallback(
                                                            (_) {
                                                      var audioWaveLogic =
                                                          Get.find<
                                                              AudioWaveLogic>();
                                                      audioWaveLogic.init();
                                                      AzureSpeechManager
                                                          .stopContinuousRecognition();
                                                    });
                                                    rolesLogic
                                                        .chatInputLogic
                                                        .isRecognizingAni
                                                        .value = false;
                                                    rolesLogic
                                                        .chatInputLogic
                                                        .speeching
                                                        .value = false;
                                                  }
                                                  rolesLogic.releaseCache();
                                                  chatLogic.isShowError = false;
                                                  chatLogic.streamRequest
                                                      ?.cancelRequest();
                                                  chatLogic.conversationId =
                                                      '${item["id"]}';
                                                  chatLogic.title.value =
                                                      '${item["title"]}';
                                                  chatLogic.favoured.value =
                                                      item["favoured"];
                                                  chatLogic.isSwitch = true;
                                                  chatLogic.historyConversation(
                                                      '${item["id"]}',
                                                      onResult: () {
                                                    if (widget.onScrollBottom !=
                                                        null) {
                                                      widget.onScrollBottom!();
                                                    }
                                                  });
                                                }
                                              }
                                            },
                                            child: Container(
                                              decoration: isSelected
                                                  ? BoxDecoration(
                                                      color: Color(0x78733D7D),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8.r),
                                                    )
                                                  : null,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 8.w,
                                                  vertical: 8.h),
                                              // 控制整体内边距
                                              child: Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                // 垂直居中
                                                children: [
                                                  Image.asset(
                                                    _getIconByType(
                                                        item['type']),
                                                    width: 20.w,
                                                    height: 20.h,
                                                  ),
                                                  SizedBox(width: 8.w),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      // 靠左对齐
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      // 垂直居中
                                                      children: [
                                                        Text(
                                                          item['title']
                                                                  .toString()
                                                                  .trim() ??
                                                              '',
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          maxLines: 1,
                                                          style: TextStyle(
                                                            fontSize: 16.sp,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      });
                                    }
                                    return const SizedBox.shrink();
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ))
                : Expanded(child: _buildLoadingIndicator()),
            // 底部设置按钮
            SizedBox(height: 24.h),
            Container(
              padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                  bottom: MediaQuery.of(context).padding.bottom + 30.w),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      showModalBottomSheet(
                        context: context,
                        backgroundColor: Colors.transparent,
                        barrierColor: Color(0x4D000A19),
                        builder: (context) {
                          return Container(
                            padding: EdgeInsets.only(
                                bottom: MediaQuery.of(context).padding.bottom),
                            decoration: BoxDecoration(
                              color: Color(0xFF000A19),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(30),
                                topRight: Radius.circular(30),
                              ),
                              border: Border.all(
                                color: Color(0xFF000A19),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0xFF000A19),
                                  offset: Offset(0, 2),
                                  blurRadius: 10,
                                ),
                              ],
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                // BottomSheet 内容
                                Container(
                                  margin: EdgeInsets.all(24.w),
                                  height: 4.h,
                                  width: 53.w,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(4)),
                                  ),
                                ),
                                Container(
                                  width: double.infinity,
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 24.w),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        S.of(context).general,
                                        style: TextStyle(
                                            color: Color(0xFF988B9A),
                                            fontSize: 16),
                                      ),
                                      SizedBox(height: 14.h),
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.of(context).pop();
                                          Navigator.of(context).pop();
                                          Get.to(VibePodsPage());
                                        },
                                        child: Container(
                                          color: Colors.transparent,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 12),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Image.asset(
                                                  "assets/icon/icon_vibe_pods.webp",
                                                  width: 20.w,
                                                  height: 20.h,
                                                ),
                                                SizedBox(
                                                  width: 8.w,
                                                ),
                                                Text(
                                                  S
                                                      .of(Get.context!)
                                                      .agnesVibepods,
                                                  style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 16),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.of(context).pop();
                                          Navigator.of(context).pop();
                                          Get.to(MineInfoPage());
                                        },
                                        child: Container(
                                          color: Colors.transparent,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 12.0),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Image.asset(
                                                  "assets/icon/icon_setting.webp",
                                                  width: 20.w,
                                                  height: 20.h,
                                                ),
                                                SizedBox(
                                                  width: 8.w,
                                                ),
                                                Text(
                                                  S.of(Get.context!).settings,
                                                  style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 16),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      Obx(() => mainTabLogic
                                              .isFeatureGroup.value
                                          ? GestureDetector(
                                              onTap: () {
                                                Navigator.of(context).pop();
                                                Navigator.of(context).pop();
                                                Get.to(NotificationsPage());
                                              },
                                              child: Container(
                                                color: Colors.transparent,
                                                child: Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      vertical: 12.0),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Image.asset(
                                                        "assets/icon/icon_general_notifications.webp",
                                                        width: 20.w,
                                                        height: 20.h,
                                                      ),
                                                      SizedBox(
                                                        width: 8.w,
                                                      ),
                                                      Text(
                                                        S
                                                            .of(Get.context!)
                                                            .notifications,
                                                        style: TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 16),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            )
                                          : SizedBox()),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 30.w,
                                )
                              ],
                            ),
                          );
                        },
                      );
                    },
                    child: _buildHeadWithName(32, 8, 16, Colors.white),
                  ),
                  SizedBox(width: 8.w),
                  GestureDetector(
                    onTap: () async {
                      // showDiamondInfoDialog(context);
                      Get.to(() => SingleWidgetWebWidget(Api.subscriptionUrl));
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 1, horizontal: 6),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Color(0x78733D7D),
                      ),
                      child: Row(
                        children: [
                          Image.asset(
                            'assets/images/icon_drawer_diamond.webp',
                            width: 16,
                            height: 16,
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Obx(() => Container(
                                constraints: BoxConstraints(maxWidth: 60.w),
                                child: Text(logic.score.value.toString(),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 12)),
                              ))
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeadWithName(
      double headSize, double departSize, double nameSize, Color nameColor) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        mineInfoLogic.userInfoModel.value.avatarUrl != null
            ? ClipOval(
                child: Image.network(
                  mineInfoLogic.userInfoModel.value.avatarUrl!.value,
                  width: headSize,
                  height: headSize,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Image.asset(
                      "assets/images/ic_default_user_avatar.webp",
                      width: headSize,
                      height: headSize,
                    );
                  },
                ),
              )
            : Image.asset(
                "assets/images/ic_default_user_avatar.webp",
                width: headSize,
                height: headSize,
              ),
        SizedBox(width: departSize),
        SizedBox(width: departSize),
        Container(
          color: Colors.transparent,
          constraints: BoxConstraints(maxWidth: 180.w),
          child: Text(
              mineInfoLogic.userInfoModel.value.username != null
                  ? mineInfoLogic.userInfoModel.value.username!.value.length >
                          10
                      ? '${mineInfoLogic.userInfoModel.value.username!.value.substring(0, 9)}...'
                      : mineInfoLogic.userInfoModel.value.username!.value
                  : "   ",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: nameColor, fontSize: nameSize)),
        ),
      ],
    );
  }

  List<dynamic> getConformityLists() {
    final List<dynamic> favouredItems =
        logic.historyItems.where((item) => item['favoured'] == true).toList();
    final List<dynamic> nonFavouredItems =
        logic.historyItems.where((item) => item['favoured'] != true).toList();
    final bool hasDivider =
        favouredItems.isNotEmpty && nonFavouredItems.isNotEmpty;
    combinedList = [
      ...favouredItems,
      if (hasDivider) 'divider',
      ...nonFavouredItems,
    ];
    print("combinedList:::" + combinedList.toList().toString());
    return combinedList;
  }

  // 根据类型返回图标路径
  String _getIconByType(String? type) {
    switch (type) {
      case 'image':
        return 'assets/icon/img_type.png';
      case 'psd':
        return 'assets/icon/ps_type.png';
      case 'word':
        return 'assets/icon/word_type.png';
      case 'done':
        return 'assets/icon/ok_type.png';
      default:
        return 'assets/icon/icon_search.webp';
    }
  }

  // 过渡加载框
  Widget _buildLoadingIndicator() {
    return SpinKitCircle(
      color: Color(0xCFFC944E9),
      size: 60,
      controller: animationController,
    );
  }

  void showDiamondInfoDialog(BuildContext context) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: "Diamond Info",
      // 半透明遮罩
      pageBuilder: (context, animation, secondaryAnimation) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 0, sigmaY: 0), // 毛玻璃模糊
          child: Center(
            child: Material(
              color: Color(0xCD201034),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Image.asset(
                        "assets/images/icon_dialog_close.webp",
                        width: 24,
                        height: 24,
                      ),
                    ),
                    SizedBox(height: 24),
                    GradientBorderContainer(
                      borderRadius: BorderRadius.all(Radius.circular(21)),
                      strokeWidth: 2,
                      gradients: [
                        LinearGradient(
                          colors: [
                            Color(0xFFFF3BDF),
                            Color(0x08FF3BDF),
                            Color(0xFF00FFFF),
                            Color(0xFF543C86),
                          ],
                          stops: [0, 0.34, 0.76, 1],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ],
                      child: Container(
                        padding: const EdgeInsets.all(24),
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage('assets/images/bg_message.png'),
                            // 背景图路径
                            fit: BoxFit.fill,
                          ),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Image.asset(
                                  "assets/images/icon_drawer_diamond.webp",
                                  width: 40,
                                  height: 40,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  S.of(context).aboutDiamond,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              "${S.of(context).eachNewTaskConsumesOneDiamond}\n${S.of(context).theDiamondCountUpdatesOnThe1stOfEveryMonth}\n${S.of(context).eachUserReceivesDiamondsPerMonth}",
                              style: const TextStyle(
                                fontSize: 15,
                                height: 1.5,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutBack,
            ),
            child: child,
          ),
        );
      },
    );
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    await logic.loadFavouredSearchHistory();
    await logic.loadNonFavouredSearchHistory();
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  // 上拉加载更多
  Future<void> _onLoading() async {
    if (!logic.hasMoreFavoured && !logic.hasMoreNoFavoured) {
      _refreshController.loadNoData();
      return;
    }

    if (logic.hasMoreFavoured) {
      await logic.loadFavouredSearchHistory(append: true);
    }
    if (logic.hasMoreNoFavoured) {
      await logic.loadNonFavouredSearchHistory(append: true);
    }
    _refreshController.loadComplete();
  }

  @override
  void initState() {
    super.initState();
    var registered = Get.isRegistered<ChatLogic>();
    if (registered) {
      historySelectedId = Get.find<ChatLogic>().historySelectedId;
    }
    animationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 1200));

    eventBus.on<String>().listen((data) {
      if (data == "toRefresh") {
        print("toRefresh");
        _onRefresh();
      }
    });
  }

  @override
  void dispose() {
    animationController.dispose();
    _refreshController.dispose();
    //logic.isHistoryLoadEnd.value = false;
    //logic.historyItems.value = [];
    super.dispose();
  }
}

class HomeDrawerLogic extends GetxController {
  var isHistoryLoadEnd = false.obs;
  var score = ''.obs;
  var historyItems = [].obs;
  static const int pageSize = 50;
  var favouredSkip = 0;
  var nonFavouredSkip = 0;
  var hasMoreFavoured = false;
  var hasMoreNoFavoured = false;

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  Future<void> loadFavouredSearchHistory({bool append = false}) async {
    Response res = await Get.find<ApiProvider>().get(Api.searchHistory, query: {
      "is_favoured": "1",
      "skip": (append ? favouredSkip : 0).toString(),
      "limit": (HomeDrawerLogic.pageSize).toString()
    });
    if (res.statusCode == 200) {
      if (res.bodyString == null) {
        hasMoreFavoured = false;
        if (favouredSkip == 0) {
          historyItems.value = [];
        }
        return;
      }
      final List<dynamic> decodedList = jsonDecode(res.bodyString!);
      if (decodedList.isEmpty) {
        hasMoreFavoured = false;
        //return;
      }

      final List<Map<String, dynamic>> dataList =
          decodedList.map((item) => Map<String, dynamic>.from(item)).toList();

      // 更新分页状态
      bool _hasMoreFavoured = dataList.length == HomeDrawerLogic.pageSize;

      int newSkip = append ? favouredSkip + dataList.length : dataList.length;

      /// 移除所有包含 知识库及协作 的项
      dataList.removeWhere((item) {
        return (item.containsKey('is_cooperation') &&
                item['is_cooperation'] == true) // 协作
            ||
            (item.containsKey('folder_id') &&
                (item['folder_id'] != null &&
                    item['folder_id'].toString().trim().isNotEmpty)); // 知识库
      });

      if (append) {
        // 追加数据
        final currentData = List<Map<String, dynamic>>.from(historyItems);
        final favouredItems =
            currentData.where((item) => item['favoured'] == true).toList();

        final nonFavouredItems =
            currentData.where((item) => item['favoured'] != true).toList();

        final updatedFavouredItems =
            List<Map<String, dynamic>>.from(favouredItems)..addAll(dataList);
        final updatedData = <Map<String, dynamic>>[
          ...updatedFavouredItems,
          ...nonFavouredItems
        ];

        historyItems.value = updatedData;
        favouredSkip = newSkip;
        hasMoreFavoured = _hasMoreFavoured;
      } else {
        // 替换数据
        final currentData = List<Map<String, dynamic>>.from(historyItems);
        final nonFavouredItems =
            currentData.where((item) => item['favoured'] != true).toList();

        // 从 nonFavouredItems 中移除与 dataList 有相同 id 的项
        final dataListIds = dataList
            .where((item) => item.containsKey('id'))
            .map((item) => item['id'])
            .toSet();

        final filteredFavouredItems = nonFavouredItems.where((item) {
          if (!item.containsKey('id')) return true;
          return !dataListIds.contains(item['id']);
        }).toList();

        final updatedData = <Map<String, dynamic>>[
          ...dataList,
          ...filteredFavouredItems
        ];

        historyItems.value = updatedData;
        favouredSkip = newSkip;
        hasMoreFavoured = _hasMoreFavoured;

        print("loadHistory----yes:" + historyItems.toList().toString());
      }
    } else {
      hasMoreFavoured = false;
    }
  }

  Future<void> loadNonFavouredSearchHistory({bool append = false}) async {
    Response res = await Get.find<ApiProvider>().get(Api.searchHistory, query: {
      "is_favoured": "0",
      "skip": (append ? nonFavouredSkip : 0).toString(),
      "limit": (HomeDrawerLogic.pageSize).toString()
    });

    if (res.statusCode == 200) {
      if (res.bodyString == null) {
        hasMoreNoFavoured = false;
        isHistoryLoadEnd.value = true;
        return;
      }
      final List<dynamic> decodedList = jsonDecode(res.bodyString!);
      if (decodedList.isEmpty) {
        hasMoreNoFavoured = false;
        isHistoryLoadEnd.value = true;
        //return;
      }

      final List<Map<String, dynamic>> dataList =
          decodedList.map((item) => Map<String, dynamic>.from(item)).toList();

      // 更新分页状态
      bool _hasMoreNoFavoured = dataList.length == HomeDrawerLogic.pageSize;

      int newSkip =
          append ? nonFavouredSkip + dataList.length : dataList.length;

      /// 移除所有包含 知识库及协作 的项
      dataList.removeWhere((item) {
        return (item.containsKey('is_cooperation') &&
                item['is_cooperation'] == true) // 协作
            ||
            (item.containsKey('folder_id') &&
                (item['folder_id'] != null &&
                    item['folder_id'].toString().trim().isNotEmpty)); // 知识库
      });

      if (append) {
        // 追加数据
        final currentData = List<Map<String, dynamic>>.from(historyItems);
        final favouredItems =
            currentData.where((item) => item['favoured'] == true).toList();
        final nonFavouredItems =
            currentData.where((item) => item['favoured'] != true).toList();

        final updatedFavouredItems =
            List<Map<String, dynamic>>.from(nonFavouredItems)..addAll(dataList);
        final updatedData = <Map<String, dynamic>>[
          ...favouredItems,
          ...updatedFavouredItems
        ];

        historyItems.value = updatedData;
        nonFavouredSkip = newSkip;
        hasMoreNoFavoured = _hasMoreNoFavoured;
      } else {
        // 替换数据
        final currentData = List<Map<String, dynamic>>.from(historyItems);
        final favouredItems =
            currentData.where((item) => item['favoured'] == true).toList();

        // 从 favouredItems 中移除与 dataList 有相同 id 的项
        final dataListIds = dataList
            .where((item) => item.containsKey('id'))
            .map((item) => item['id'])
            .toSet();

        final filteredFavouredItems = favouredItems.where((item) {
          if (!item.containsKey('id')) return true;
          return !dataListIds.contains(item['id']);
        }).toList();

        final updatedData = <Map<String, dynamic>>[
          ...filteredFavouredItems,
          ...dataList
        ];

        historyItems.value = updatedData;
        nonFavouredSkip = newSkip;
        hasMoreNoFavoured = _hasMoreNoFavoured;

        print("loadHistory----no:" + historyItems.toList().toString());
      }
      isHistoryLoadEnd.value = true;
    } else {
      hasMoreNoFavoured = false;
      isHistoryLoadEnd.value = true;
    }
  }

  Future<void> getQuotaRequest() async {
    Response res = await Get.find<ApiProvider>().get(Api.plansPersonal);
    if (res.statusCode == 200) {
      if (res.body == null) {
        return;
      }
      final Map<String, dynamic> responseBody = res.body;
      score.value = CmUtils.firstLetterBig(responseBody["user_plan"] == null
          ? "free"
          : responseBody["user_plan"]["name"]);
    } else {
      Response res = await Get.find<ApiProvider>().get(Api.quotaRequest);
      if (res.statusCode == 200) {
        if (res.body == null) {
          return;
        }
        final Map<String, dynamic> responseBody = res.body;
        score.value = responseBody["remaining_count"].toString() ?? "0";
      }
    }
  }

  //1:收藏 2：编辑
  Future<void> collectionOrEdit(
      int type, bool favoured, String title, String conversationId,
      {editText}) async {
    bool favouredParm = type == 1 ? !favoured : favoured;
    String titleParm = title;
    Response res = await Get.find<ApiProvider>().patch(
        Api.conversations + "/" + conversationId,
        {"favour": favouredParm, "title": type == 1 ? titleParm : editText});
    if (res.statusCode == 200) {
      if (res.bodyString == null) {
        return;
      }
      favoured = favouredParm;
      if (type == 2) {
        //showSuccessToast(S.of(Get.context!).operationSuccessful);
        eventBus.fire("toRefresh");
      } else {
        //showSuccessToast(S.of(Get.context!).operationSuccessful);
        eventBus.fire("toRefresh");
      }
    }
  }

  void share(String conversationId, String title) {
    String shareUrl = "${Api.baseUrl}/share?conversationid=${conversationId}";
    SharePlus.instance.share(
      ShareParams(
        title: title,
        uri: Uri.parse(shareUrl),
      ),
    );
  }

  Future<void> delete(String conversationId, String pageType) async {
    //EasyLoading.show();
    Response res = await Get.find<ApiProvider>()
        .delete(Api.conversations + "/" + conversationId);
    if (res.statusCode == 204) {
      //EasyLoading.dismiss();
      // showSuccessToast(S.of(Get.context!).operationSuccessful);
      eventBus.fire("toRefresh");
      if ("chat" == pageType) {
        var chatLogic = Get.find<ChatLogic>();
        if (conversationId == chatLogic.conversationId) {
          Future.delayed(Duration(milliseconds: 30), () {
            Get.until((route) => route.isFirst);
          });
        }
      }
    }
  }
}
