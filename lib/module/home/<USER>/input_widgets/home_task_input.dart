import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:mime/mime.dart';
import 'package:new_agnes/api/Api.dart';
import 'package:new_agnes/module/home/<USER>/logic.dart';
import 'package:new_agnes/utils/stt/AzureSpeechManager.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/image_viewer_widget.dart';
import '../../../../api/ApiProvider.dart';
import '../../../../api/stream/render/RenderManagerController.dart';
import '../../../../data/caseAndQuestion.dart';
import '../../../../generated/l10n.dart';
import '../../../../utils/image_upload_util.dart';
import '../../../../utils/photo_utils.dart';
import '../../../../widget/GradientBorderContainer.dart';
import '../../../../widget/message_input_widget.dart';
import '../../../chat/logic.dart';
import 'audio_wave_widget.dart';
import 'package:path/path.dart' as path;

class HomeTaskInput extends StatefulWidget {
  final FocusNode focusNode;
  final VoidCallback onGoPressed;
  final HomeTaskInputLogic logic;
  final int type; //0 首页 1 chat
  final List<MessageUploadModel> selectedImages; // 外部传入的图片数据
  final Function(int)? onRemoveImage; // 删除图片的回调
  final Function(List<MessageUploadModel>)? onAddImage;
  final Function(int, MessageUploadModel)? onUpdateImage; // 更新图片状态的回调

  const HomeTaskInput(this.focusNode, this.onGoPressed, this.logic,
      {super.key,
      this.type = 0,
      this.selectedImages = const [],
      this.onRemoveImage,
      this.onAddImage,
      this.onUpdateImage});

  @override
  State<HomeTaskInput> createState() => _HomeTaskInputState();
}

class _HomeTaskInputState extends State<HomeTaskInput>
    with TickerProviderStateMixin {
  //final logic = Get.find<HomeTaskInputLogic>();
  // final ScrollController _scrollController = ScrollController();
  final GlobalKey _buttonKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  
  // 图片上传工具
  final ImageUploadUtil _uploadUtil = ImageUploadUtil.instance;
  bool _isShowing = false;
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  final int rippleCount = 3;
  var audioWaveLogic = Get.put(AudioWaveLogic());

  final List<Map<String, dynamic>> _menuItems = [
    {
      'icon': 'assets/icon/icon_search.svg',
      'text': SearchType.search,
    },
    {
      'icon': 'assets/icon/icon_research.svg',
      'text': SearchType.research,
    },
    {
      'icon': SearchType.aiDesign.icon,
      'text': SearchType.aiDesign,
    },
    {
      'icon': 'assets/icon/icon_ai_slides.svg',
      'text': SearchType.aiSlides,
    },
  ];

  @override
  void initState() {
    super.initState();
    var searchType = widget.logic.searchType.value;

    if (searchType != SearchType.tools) {
      var whereList =
          _menuItems.where((item) => searchType == item['text']).toList();
      widget.logic.selectedItem.value = whereList[0];
    }
    widget.focusNode.addListener(() {
      if (widget.focusNode.hasFocus) {
        // print('TextField 获得焦点');
        // logic.isShowQuestions.value = true;
      } else {
        //logic.isShowQuestions.value = false;
        _removeOverlay();
      }
    });
    _controllers = List.generate(rippleCount, (index) {
      return AnimationController(
        duration: Duration(milliseconds: 2000 + index * 200),
        vsync: this,
      )..repeat();
    });

    _animations = List.generate(rippleCount, (index) {
      return Tween(begin: 0.0, end: 1.0).animate(_controllers[index]);
    });
  }

  @override
  Widget build(BuildContext context) {
    widget.logic.taskController.addListener(() {
      widget.logic.task.value = widget.logic.taskController.text;
    });
    return Obx(() => Container(
          color: widget.type == 0
              ? Colors.transparent
              : !widget.logic.isRecognizingAni.value &&
                      audioWaveLogic.isPressedFinished.value
                  ? Colors.transparent
                  : Color(0x7A321138),
          padding: EdgeInsets.only(
              left: 16, right: 16, bottom: widget.type == 1 ? 20.w : 0),
          child: !widget.logic.isRecognizingAni.value &&
                  audioWaveLogic.isPressedFinished.value
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GradientBorderContainer.single(
                      height: _getContainerHeight().h,
                      strokeWidth: 2,
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFFFF3BDF),
                          Color(0xFF5E57FE),
                          Color(0xFF2DDAF2),
                          Color(0xFF4D84FA),
                          Color(0xFFFF3BDF),
                        ],
                        stops: [0.0, 0.24, 0.51, 0.78, 1.0],
                        begin: Alignment.bottomLeft,
                        end: Alignment.topRight,
                        tileMode: TileMode.decal,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        // padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                        padding: EdgeInsets.only(
                            left: 8, right: 8,bottom: 2),
                        decoration: BoxDecoration(
                          color: const Color(0x802E174F),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            SizedBox(height: 8.5.h,),
                            if(widget.selectedImages.isEmpty)
                              SizedBox(height: 3.5.h,),
                            // 图片展示区域
                            if (widget.selectedImages.isNotEmpty)
                              Container(
                                width: double.infinity,
                                constraints: BoxConstraints(
                                  maxHeight: 200.h, // 最大高度限制
                                ),
                                margin: EdgeInsets.only(bottom: 8),
                                child: _buildImageDisplay(),
                              ),
                            Expanded(
                              child: TextField(
                                focusNode: widget.focusNode,
                                scrollController: widget.type==1?widget.logic.chatScrollController:widget.logic.scrollController,
                                controller: widget.logic.taskController,
                                textAlignVertical: TextAlignVertical.center,
                                style: TextStyle(
                                    fontSize: 16, color: Colors.white),
                                maxLines: 5,
                                decoration: InputDecoration(
                                  hintText: S.of(Get.context!).giveMeATaskToWorkOn,
                                  border: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                  contentPadding:
                                      EdgeInsets.symmetric(vertical: 0),
                                  isDense: true,
                                  hintStyle: TextStyle(
                                      fontSize: 16, color: Color(0xFF988B9A)),
                                ),
                              ),
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    PhotoUtils.goToPhotoDialog(context, 230.h,title: S.of(context).chooseImage, 2, (files) {
                                      _addAndUploadImages(files);
                                    }, isMulti: true);
                                  },
                                  child: Image.asset(
                                    'assets/images/icon_add.webp',
                                    width: 24,
                                    height: 24,
                                  ),
                                ),
                                SizedBox(
                                  width: 8,
                                ),
                                Expanded(child: LayoutBuilder(builder: (context,box){
                                  return Row(
                                    children: [
                                      GestureDetector(
                                        key: _buttonKey,
                                        onTap: () {
                                          widget.logic.isPressed.value = true;
                                          _showOverlay();
                                        },
                                        child: GradientBorderContainer.single(
                                            strokeWidth: 1,
                                            gradient: LinearGradient(
                                              colors:
                                              widget.logic.searchType.value !=
                                                  SearchType.tools
                                                  ? [
                                                Color(0xFF7253FA),
                                                Color(0xFFFF3BDF),
                                                Color(0xFF5E57FE),
                                              ]
                                                  : [
                                                Colors.transparent,
                                                Colors.transparent,
                                                Colors.transparent,
                                              ],
                                              stops: const [0.0, 0.32, 1.0],
                                              begin: Alignment.bottomLeft,
                                              end: Alignment.topRight,
                                              tileMode: TileMode.decal,
                                            ),
                                            borderRadius: BorderRadius.circular(20),
                                            child: Container(
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                  BorderRadius.circular(20),
                                                  color: widget.logic.searchType
                                                      .value !=
                                                      SearchType.tools
                                                      ? Color(0x8F604276)
                                                      : Colors.transparent),
                                              child: Padding(
                                                padding:
                                                widget.logic.searchType.value !=
                                                    SearchType.tools
                                                    ? EdgeInsets.symmetric(
                                                    horizontal: 8,
                                                    vertical: 2)
                                                    : EdgeInsets.symmetric(
                                                    horizontal: 0,
                                                    vertical: 0),
                                                child: Row(
                                                  children: [
                                                    Image.asset(
                                                      !widget.logic.isPressed.value
                                                          ? 'assets/images/icon_search_type.webp'
                                                          : 'assets/images/icon_search.webp',
                                                      width: 24,
                                                      height: 24,
                                                    ),
                                                    if (widget.logic.searchType
                                                        .value !=
                                                        SearchType.tools) ...{
                                                      SizedBox(
                                                        width: 8,
                                                      ),
                                                      Text(
                                                        "|",
                                                        style: TextStyle(
                                                            fontSize: 12,
                                                            color: Colors.white),
                                                      ),
                                                      SizedBox(
                                                        width: 8,
                                                      ),
                                                      SvgPicture.asset(
                                                        widget.logic
                                                            .selectedItem['icon'],
                                                        width: 20,
                                                        height: 20,
                                                      ),
                                                      SizedBox(
                                                        width: 4,
                                                      ),
                                                      Container(
                                                        constraints: BoxConstraints(
                                                          maxWidth: (box.maxWidth - 16 -24 -8 -8 - 20 - 4 - 32).clamp(0, 300),
                                                        ),
                                                        child: Text(
                                                          maxLines: 1,
                                                          overflow:
                                                          TextOverflow.ellipsis,
                                                          widget.logic
                                                              .selectedItem["text"]
                                                              .name(context),
                                                          style: TextStyle(
                                                              fontSize: 14,
                                                              color: Colors.white),
                                                        ),
                                                      ),
                                                      GestureDetector(
                                                          onTap: () {
                                                            setState(() {
                                                              widget
                                                                  .logic
                                                                  .selectedItem
                                                                  .value = {};
                                                              widget
                                                                  .logic
                                                                  .searchType
                                                                  .value =
                                                                  SearchType.tools;
                                                            });
                                                          },
                                                          child: Container(
                                                            child: Padding(
                                                              padding:
                                                              EdgeInsets.only(
                                                                  left: 8.w,
                                                                  right: 8.w,
                                                                  top: 5.h,
                                                                  bottom: 5.h),
                                                              child: Image.asset(
                                                                "assets/images/icon_home_delete.webp",
                                                                width: 12.w,
                                                                height: 12.h,
                                                              ),
                                                            ),
                                                          ))
                                                    }
                                                  ],
                                                ),
                                              ),
                                            )),
                                      ),
                                      Spacer(),
                                    ],
                                  );
                                })),
                                GestureDetector(
                                  onTap: () async {
                                    debugPrint("准备语音识别");
                                    try {
                                      if (!AzureSpeechManager.isFromBle) {
                                        // 非耳机模式下，按键语音识别，耳机模式下，禁止点击按钮触发
                                        if (widget.logic.speeching.value) {
                                          // widget.logic.isRecognizingAni
                                          //     .value = false;
                                          // // 延迟一小段时间再执行，避免在构建过程中触发状态更新
                                          // WidgetsBinding.instance
                                          //     .addPostFrameCallback((_) {
                                          //   AzureSpeechManager
                                          //       .stopContinuousRecognition();
                                          // });
                                        } else {
                                          // 延迟一小段时间再执行，避免在构建过程中触发状态更新
                                          WidgetsBinding.instance
                                              .addPostFrameCallback((_) {
                                            if (AzureSpeechManager
                                                .eventCallback ==
                                                null) {
                                              print("eventCallback is null");
                                              var rolesLogic =
                                              Get.find<RolesLogic>();
                                              rolesLogic
                                                  .setSpeechEventListener();
                                            }
                                            AzureSpeechManager
                                                .startContinuousRecognition(
                                                false);
                                          });
                                        }
                                      }
                                    } catch (e) {
                                      debugPrint("语音识别操作失败: $e");
                                    }
                                  },
                                  child:
                                  AzureSpeechManager.isInitialized.value
                                      ? Container(
                                    padding: EdgeInsets.all(12.h),
                                    child: Image.asset(
                                      'assets/icon/icon_mic.webp',
                                      width: 24.w,
                                      height: 24.h,
                                    ),
                                  )
                                      : Container(
                                    width: 48.w,
                                    height: 48.h,
                                  ),
                                ),
                                Visibility(
                                  visible: !widget.logic.chatting.value,
                                  child: GestureDetector(
                                    onTap: widget.logic.getGoValid(widget.selectedImages)
                                        ? () {
                                      widget.onGoPressed.call();
                                      widget.logic.taskController.text =
                                      "";
                                      widget.logic.task.value = "";
                                      if (!AzureSpeechManager
                                          .isFromBle &&
                                          widget
                                              .logic.speeching.value) {
                                        widget.logic.speeching.value =
                                        false;
                                        WidgetsBinding.instance
                                            .addPostFrameCallback((_) {
                                          AzureSpeechManager
                                              .stopContinuousRecognition();
                                        });
                                      }
                                    }
                                        : null,
                                    child: Image.asset(
                                      widget.logic.getGoValid(widget.selectedImages)
                                          ? 'assets/images/icon_go_yes.webp'
                                          : 'assets/images/icon_go_no.webp',
                                      width: 70,
                                      height: 33,
                                    ),
                                  ),
                                ),

                                Visibility(
                                    visible: widget.logic.chatting.value,
                                    child: GestureDetector(
                                        onTap: widget.logic.chatting.value
                                            ? () {
                                          widget.logic.stopChatting(
                                              widget.logic
                                                  .conversationId);
                                        }
                                            : null,
                                        child: Container(
                                          width: 32,
                                          height: 32,
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              ...List.generate(rippleCount,
                                                      (index) {
                                                    return AnimatedBuilder(
                                                      animation:
                                                      _animations[index],
                                                      builder: (context, child) {
                                                        return CustomPaint(
                                                          painter: _RipplePainter(
                                                            progress:
                                                            _animations[index]
                                                                .value,
                                                            color:
                                                            Color(0x80FAC6F2),
                                                            childSize: 5,
                                                          ),
                                                          size: Size(32, 32),
                                                        );
                                                      },
                                                    );
                                                  }),
                                              Center(
                                                child: Image.asset(
                                                  'assets/icon/icon_chat.webp',
                                                  width: 32,
                                                  height: 32,
                                                ),
                                              ),
                                            ],
                                          ),
                                        )))
                              ],
                            )
                          ],
                        ),
                      ),
                    )
                  ],
                )
              : AudioWaveWidget(
                  onCancel: () {
                    audioWaveLogic.isLoading.value = false;
                    if (widget.logic.speeching.value) {
                      AzureSpeechManager.stopContinuousRecognition();
                    }
                    widget.logic.isRecognizingAni.value = false;
                  },
                  onDone: () {
                    audioWaveLogic.isPressedFinished.value = true;
                    if (!widget.logic.isRecognizeEnd.value) {
                      audioWaveLogic.isLoading.value = true;
                    }
                  },
                  type: widget.type,
                ),
        ));
  }

  void _showOverlay() {
    if (_isShowing) {
      _removeOverlay();
      return;
    }

    final RenderBox renderBox =
        _buttonKey.currentContext?.findRenderObject() as RenderBox;
    final offset = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 半透明背景层，点击可关闭
          Positioned.fill(
            child: GestureDetector(
              onTap: _removeOverlay,
              child: Container(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
          ),
          // 下拉框内容
          Positioned(
            left: offset.dx,
            top: offset.dy - 166.h,
            child: Material(
              elevation: 4,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                constraints: const BoxConstraints(
                  minWidth: 100,
                ),
                decoration: BoxDecoration(
                  border: Border.fromBorderSide(
                      BorderSide(color: Color(0xFFFF3BDF), width: 1.w)),
                  color: Color(0xE52E174F),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x40000000),
                      blurRadius: 4,
                      spreadRadius: 0,
                    )
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _menuItems.map((item) {
                      return Material(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.transparent,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 6),
                          child: _buildItem(item),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    Overlay.of(_buttonKey.currentContext!).insert(_overlayEntry!);
    _isShowing = true;
  }

  Widget _buildItem(dynamic item) {
    final isSelected = widget.logic.selectedItem.isEmpty
        ? false
        : item['text'] == widget.logic.selectedItem['text'];
    return InkWell(
        splashColor: Colors.purple.withOpacity(0.3),
        highlightColor: Colors.purple.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          setState(() {
            widget.logic.selectedItem.value = item;
            widget.logic.searchType.value = item["text"];
          });
          _overlayEntry!.markNeedsBuild();
          _removeOverlay();
        },
        child: Container(
          height: 36.h,
          padding: EdgeInsets.only(left: 10.w, right: 10.w),
          decoration: BoxDecoration(
            color: isSelected ? Color(0x78733D7D) : Colors.transparent,
            borderRadius: BorderRadius.circular(10),
          ),
          constraints: BoxConstraints(
            minWidth: 151.w,
          ),
          child: Row(
            children: [
              SvgPicture.asset(
                item['icon'],
                width: 20.w,
                height: 20.h,
              ),
              SizedBox(width: 8.w),
              Text(
                item['text'].name(context),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ));
  }

  void _removeOverlay() {
    widget.logic.isPressed.value = false;
    if (_overlayEntry != null) {
      _overlayEntry?.remove();
      _overlayEntry = null;
      _isShowing = false;
    }
  }

  void _removeFocus() {
    FocusScope.of(context).unfocus();
  }

  @override
  void dispose() {
    _removeOverlay();
    for (var controller in _controllers) {
      controller.dispose();
    }
    // 清理上传工具资源
    _uploadUtil.dispose();
    super.dispose();
  }

  // 构建图片展示布局
  Widget _buildImageDisplay() {
    // 图片高度为57，但需要考虑删除图标超出的部分
    double imageHeight = 57.h;
    double containerHeight = imageHeight + 5.h; // 额外5像素给删除图标超出的部分
    
    return Container(
      height: containerHeight,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.selectedImages.length,
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) {
          // 只显示已选择的图片
          return _buildImageItem(widget.selectedImages[index], index);
        },
      ),
    );
  }


  // 构建图片项
  Widget _buildImageItem(MessageUploadModel imageModel, int index) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      child: Stack(
        clipBehavior: Clip.none, // 允许子组件超出边界
        children: [
          Container(
            width: 43.w,
            height: 57.h,
            margin: EdgeInsets.only(right: 3.5.w,top: 3.5.h),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _buildImageWidget(imageModel,index),
            ),
          ),
          // 删除按钮 - 右上角超出一半距离
          Positioned(
            top: 0, // 向上超出5像素（10x10图标的一半）
            right: 0, // 向右超出5像素（10x10图标的一半）
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => _removeImage(index),
              child: Container(
                width: 16,  // 增大容器尺寸
                height: 16, // 增大容器尺寸
                alignment: Alignment.topRight,
                child: Image.asset("assets/images/ic_message_input_close.png",
                    width: 10.w, height: 10.h),
              ),
            ),
          ),
        ],
      ),
    );
  }


  // 构建图片Widget，根据MessageUploadModel选择合适的显示方式
  Widget _buildImageWidget(MessageUploadModel imageModel,int index) {
    // 根据上传状态和可用的图片源选择显示方式
    String? imageUrl = _getImageUrl(imageModel);
    
    if (imageUrl == null) {
      // 没有可用的图片源，显示错误状态
      return _buildErrorWidget();
    }

    // 根据上传状态添加加载指示器
    Widget imageWidget = GestureDetector(
        onTap: () {
          List<ImageItem> images = widget.selectedImages
              .map((e) {
                if (e.uploadStatus != UploadStatus.success) {
                  return null;
                }
                return ImageItem(
                    url: e.file_url,
                    assetPath: e.localFilePath,
                    thumbnail: e.localFilePath);
              })
              .whereType<ImageItem>()
              .toList();
          int clickIndex = images.indexWhere((e)=> e.url == imageModel.file_url);
          if (clickIndex == -1) {
            clickIndex = index;
          }
          ImageViewerDialog.show(
            context: context,
            images: images,
            initialIndex: index,
            showDownloadButton: false,
            showBottomButtons: false,
            showPageIndicator: false,
          );
        },
        child: _buildImageByUrl(imageUrl));

    // 如果正在上传，添加上传状态覆盖层
    if (imageModel.uploadStatus == UploadStatus.loading) {
      imageWidget = Stack(
        children: [
          imageWidget,
          Container(
            width: 43.w,
            height: 57.h,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFCB53BA)),
                ),
              ),
            ),
          ),
        ],
      );
    } else if (imageModel.uploadStatus == UploadStatus.failed) {
      // 上传失败，添加失败指示器和点击重试功能
      imageWidget = GestureDetector(
        onTap: () => _retryUploadImage(imageModel),
        child: Stack(
          children: [
            imageWidget,
            Container(
              width: 43.w,
              height: 57.h,
              child: Center(
                child: Container(
                  width: 20.w,
                  height: 20.h,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.refresh,
                    color: Colors.white,
                    size: 14.w,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }
    
    return imageWidget;
  }

  // 获取图片URL，优先级：localFilePath > file_url
  String? _getImageUrl(MessageUploadModel imageModel) {
    if (imageModel.localFilePath != null && imageModel.localFilePath!.isNotEmpty) {
      return imageModel.localFilePath;
    }
    if (imageModel.file_url != null && imageModel.file_url!.isNotEmpty) {
      return imageModel.file_url;
    }
    return null;
  }

  // 根据URL构建图片Widget
  Widget _buildImageByUrl(String imageUrl) {
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      // 网络图片 - 使用CachedNetworkImage
      return CachedNetworkImage(
        imageUrl: imageUrl,
        width: 43.w,
        height: 57.h,
        fit: BoxFit.cover,
        placeholder: (context, url) => Center(
          child: SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFCB53BA)),
            ),
          ),
        ),
        errorWidget: (context, url, error) => _buildErrorWidget(),
        // 缓存配置
        memCacheWidth: (43.w * 2).toInt(), // 2倍分辨率缓存
        memCacheHeight: (57.h * 2).toInt(),
        maxWidthDiskCache: (43.w * 3).toInt(), // 3倍分辨率磁盘缓存
        maxHeightDiskCache: (57.h * 3).toInt(),
      );
    } else if (imageUrl.startsWith('/') || imageUrl.startsWith('file://')) {
      // 本地文件图片
      String filePath = imageUrl.startsWith('file://') 
          ? imageUrl.substring(7) // 移除 'file://' 前缀
          : imageUrl;
      return Image.file(
        File(filePath),
        width: 43.w,
        height: 57.h,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
      );
    } else {
      // Assets 资源图片
      return Image.asset(
        imageUrl,
        width: 43.w,
        height: 57.h,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
      );
    }
  }

  // 构建错误状态Widget
  Widget _buildErrorWidget() {
    return Container(
      width: 43.w,
      height: 57.h,
      color: Colors.grey.withOpacity(0.3),
      child: Icon(
        Icons.broken_image,
        color: Colors.white.withOpacity(0.6),
        size: 20,
      ),
    );
  }

  // 计算容器高度
  double _getContainerHeight() {
    double baseHeight = 100.0;
    if (widget.selectedImages.isNotEmpty) {
      // 有图片时，基础高度 + 图片高度 + 间距
      double imageHeight = 57.0;
      double extraSpace = 5.0; // 删除图标超出的空间
      double margin = 8.0; // 图片区域的底部边距
      return baseHeight + imageHeight + extraSpace + margin;
    }
    return baseHeight;
  }

  // 移除图片方法
  void _removeImage(int index) {
    if (widget.onRemoveImage != null) {
      widget.onRemoveImage!(index);
    }
  }

  // 添加并上传图片
  void _addAndUploadImages(List<File> files) {
    List<MessageUploadModel> newImages = [];
    
    for (var file in files) {
      String fileName = path.basename(file.path);
      final mimeType = lookupMimeType(fileName) ?? 'image/jpeg';
      newImages.add(MessageUploadModel(
          file_title: fileName,
          mime_type: mimeType,
          localFilePath: file.path,
          uploadStatus: UploadStatus.loading));
    }
    
    // 通知外部组件添加图片
    widget.onAddImage?.call(newImages);
    
    // 开始上传图片
    _startUploadingImages(files, newImages);
  }

  // 开始上传图片
  void _startUploadingImages(List<File> files, List<MessageUploadModel> imageModels) {
    _uploadUtil.uploadMultipleFiles(
      files: files,
      uploadMode: UploadMode.sequential,
      onSingleSuccess: (index, fileUrl, fileName,size,content) {
        // 找到对应的图片并更新状态
        _updateImageUploadStatus(imageModels[index], UploadStatus.success, fileUrl: fileUrl,size: size,content:content);
      },
      onSingleError: (index, fileName, error) {
        // 找到对应的图片并更新状态
        _updateImageUploadStatus(imageModels[index], UploadStatus.failed);
        debugPrint("Upload failed: $error");
      },
    );
  }

  // 重试上传图片
  void _retryUploadImage(MessageUploadModel imageModel) {
    if (imageModel.localFilePath == null) return;
    
    // 更新状态为loading
    _updateImageUploadStatus(imageModel, UploadStatus.loading);
    
    // 开始重试上传
    _uploadUtil.uploadSingleFile(
      filePath: imageModel.localFilePath!,
      onSuccess: (fileUrl, fileName,size,content) {
        _updateImageUploadStatus(imageModel, UploadStatus.success, fileUrl: fileUrl,size: size, content: content);
      },
      onError: (error) {
        _updateImageUploadStatus(imageModel, UploadStatus.failed);
        debugPrint("Upload retry failed: $error");
      },
    );
  }

  // 更新图片上传状态
  void _updateImageUploadStatus(MessageUploadModel targetModel, UploadStatus status, {String? fileUrl,int? size,String? content}) {
    // 在selectedImages中找到对应的图片并更新
    for (int i = 0; i < widget.selectedImages.length; i++) {
      MessageUploadModel currentModel = widget.selectedImages[i];
      if (currentModel.localFilePath == targetModel.localFilePath) {
        // 创建更新后的模型
        MessageUploadModel updatedModel = currentModel.copyWith(
          uploadStatus: status,
          file_url: fileUrl ?? currentModel.file_url,
          size: size ?? currentModel.size,
          content: content ?? currentModel.content,
        );
        
        // 通知外部组件更新图片状态
        if (widget.onUpdateImage != null) {
          widget.onUpdateImage!(i, updatedModel);
        }
        break;
      }
    }
  }
}

class _RipplePainter extends CustomPainter {
  final double progress;
  final Color color;
  final double childSize;

  _RipplePainter({
    required this.progress,
    required this.color,
    required this.childSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = size.width / 2;

    // 绘制背景
    final bgPaint = Paint()..color = color;
    canvas.drawCircle(center, childSize / 2, bgPaint);

    // 绘制水波纹
    final ripplePaint = Paint()
      ..color = color.withOpacity(1 - progress)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10.0
      ..strokeCap = StrokeCap.round;

    final rippleRadius = childSize / 2 + (maxRadius - childSize / 2) * progress;
    canvas.drawCircle(center, rippleRadius, ripplePaint);
  }

  @override
  bool shouldRepaint(covariant _RipplePainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.childSize != childSize;
  }
}

class HomeTaskInputLogic extends GetxController {
  var task = ''.obs;
  var _searchType = SearchType.tools.obs;
  Rx<SearchType> get searchType{
    return _searchType;
  }
  var isShowQuestions = false.obs;
  var isOverlayShowing = false;
  var speeching = false.obs;
  var chatting = false.obs;
  var conversationId = "";
  var taskController = new TextEditingController();
  var scrollController = new ScrollController();
  var chatScrollController = new ScrollController();
  var selectedItem = {}.obs;
  var isPressed = false.obs;
  var isRecognizingAni = false.obs;
  var isRecognizeEnd = false.obs;

  bool getGoValid(List<MessageUploadModel> selectImages){
    if(selectImages.isNotEmpty){
      //如果有图片。上传失败或者loading不允许发送
      int index =
      selectImages.indexWhere((e) => e.uploadStatus != UploadStatus.success);
      if(index != -1){
        return false;
      }
    }
    return (speeching.value
        ? task.isNotEmpty && isRecognizeEnd.value
        : task.isNotEmpty);
  }

  void scroll() {
    print("scroll");
    if (scrollController.hasClients) {
      scrollController.jumpTo(scrollController.position.maxScrollExtent);
    }
    if (chatScrollController.hasClients) {
      chatScrollController.jumpTo(chatScrollController.position.maxScrollExtent);
    }
  }

  Future<void> stopChatting(String id) async {
    if (conversationId.isEmpty) {
      showFailToast(S.of(Get.context!).systemBusyPleaseTryAgain);
      return;
    }
    Response res =
        await Get.find<ApiProvider>().post(Api.chat + getGenerate(id), {});
    if (res.statusCode == 200) {
      if (res.body == null) {
        return;
      }
      final Map<String, dynamic> responseBody = res.body;
      if (responseBody['success']) {
        chatting.value = false;
        print('数据推流已断开');
        try {
          Get.find<ChatLogic>().isLoadingAnimation.value = false;
        } catch (e) {}
      }
    } else {
      print("cancel fail");
    }
  }

  String getGenerate(String conversation_id) {
    return "/cancel/${conversation_id}";
  }
}
