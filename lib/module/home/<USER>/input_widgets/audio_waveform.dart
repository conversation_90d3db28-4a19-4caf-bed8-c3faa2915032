import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:mic_stream/mic_stream.dart';

class AudioWaveform extends StatefulWidget {
  const AudioWaveform({super.key});

  @override
  _AudioWaveformState createState() => _AudioWaveformState();
}

class _AudioWaveformState extends State<AudioWaveform>{
  List<double> _waveData = [];
  StreamSubscription<Uint8List>? _audioStreamSubscription;
  DateTime? _lastUpdate;
  static const Duration _minUpdateInterval = Duration(milliseconds: 200);

  @override
  void initState() {
    super.initState();
    _initMicrophone();
  }

  @override
  void dispose() {
    _audioStreamSubscription?.cancel();
    _waveData = [];
    super.dispose();
  }

  Future<void> _initMicrophone() async {
    try {
      final stream = await MicStream.microphone(
        sampleRate: 16000,
        audioFormat: AudioFormat.ENCODING_PCM_16BIT,
      );

      _audioStreamSubscription = stream.listen((audioData) {
        _processAudioData(audioData);
      });
    } catch (e) {
      print('麦克风初始化失败: $e');
    }
  }

  void _processAudioData(Uint8List audioData) {
    // 控制更新频率 - 避免过快更新
    final now = DateTime.now();
    if (_lastUpdate != null && now.difference(_lastUpdate!) < _minUpdateInterval) {
      return;
    }
    _lastUpdate = now;

    // 将PCM数据转换为振幅值
    final Int16List int16Data = Int16List.view(audioData.buffer);
    final List<double> amplitudes = [];

    // 处理音频数据，计算振幅
    for (int i = 0; i < int16Data.length; i += 2) {
      final double amplitude = int16Data[i].abs() / 32768.0;
      amplitudes.add(amplitude);
    }

    // 平均分段采样
    final int sampleCount = 10;
    final List<double> sampledData = List.filled(sampleCount, 0.0);

    if (amplitudes.isNotEmpty) {
      final int segmentSize = amplitudes.length ~/ sampleCount;

      for (int i = 0; i < sampleCount; i++) {
        // 计算每个段的平均值
        final int start = i * segmentSize;
        final int end = (i == sampleCount - 1) ? amplitudes.length : (i + 1) * segmentSize;

        double sum = 0;
        int count = 0;
        for (int j = start; j < end && j < amplitudes.length; j++) {
          sum += amplitudes[j];
          count++;
        }

        if (count > 0) {
          sampledData[i] = sum / count;
        }
      }
    }

    setState(() {
      _waveData = sampledData;
    });
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('声波纹效果'),
      ),
      body: Column(
        children: [
          // 声波纹显示区域
          Container(
            height: 32,
            width: 58,
            color: Colors.black,
            child: CustomPaint(
              painter: WaveformPainter(waveHeights: _waveData),
            ),
          ),
        ],
      ),
    );
  }

  void _startRecording() async {
    await _initMicrophone();
  }

  void _stopRecording() {
    _audioStreamSubscription?.cancel();
    setState(() {
      _waveData = [];
    });
  }
}

class WaveformPainter extends CustomPainter {
  final List<double> waveHeights;
  final Random _random = Random();

  WaveformPainter({required this.waveHeights});

  @override
  void paint(Canvas canvas, Size size) {
    final centerY = size.height / 2;
    final maxHeight = size.height; // 最大高度设为视图高度的80%
    final barWidth = size.width / waveHeights.length;
    final paint = Paint()
      ..strokeWidth = 1
      ..color = Color(0xFF00D4FD)
      ..style = PaintingStyle.fill;

    // 绘制声波纹
    for (int i = 0; i < waveHeights.length; i++) {
      final normalizedHeight = waveHeights[i];
      final height = normalizedHeight * maxHeight;

      // 调整这部分来改变粗细
      final barFillRatio = 0.1 + _random.nextDouble() * 0.3;
      final actualBarWidth = barWidth * barFillRatio;
      final left = i * barWidth + (barWidth - actualBarWidth) / 2;
      final top = centerY - height / 2;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(left, top, actualBarWidth, height),
          const Radius.circular(14),
        ),
        paint,
      );
    }

  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
