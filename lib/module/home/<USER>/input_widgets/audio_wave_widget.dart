import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'dart:async';
import 'dart:typed_data';
import 'package:mic_stream/mic_stream.dart';
import 'package:new_agnes/generated/l10n.dart';
import 'package:new_agnes/widget/ContainerBox.dart';

import '../../../../widget/GradientBorderContainer.dart';

class AudioWaveWidget extends StatefulWidget {
  int type; //0 首页 1 chat
  final VoidCallback onCancel;
  final VoidCallback onDone;

  AudioWaveWidget(
      {super.key, required this.onCancel, required this.onDone, this.type = 0});

  @override
  State<AudioWaveWidget> createState() => _AudioWaveWidgetState();
}

class _AudioWaveWidgetState extends State<AudioWaveWidget> {
  var audioWaveLogic = Get.find<AudioWaveLogic>();
  List<double> _waveData = [];
  StreamSubscription<Uint8List>? _audioStreamSubscription;
  DateTime? _lastUpdate;
  static const Duration _minUpdateInterval = Duration(milliseconds: 180);

  @override
  void initState() {
    print("initState");
    super.initState();
    _startRecording();
  }

  @override
  void dispose() {
    print("dispose");
    _stopRecording();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ContainerBox(
        boxColor: Color(0x7A321138),
        borderColor: Color(0x7A321138),
        radius: widget.type == 1 ? 0 : 12,
        padding: EdgeInsets.only(
            left: widget.type == 1 ? 0.w : 12.w,
            right: widget.type == 1 ? 0.w : 12.w,
            top: 14.w,
            bottom: 14.w),
        child: Obx(() => Container(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      audioWaveLogic.isPressedFinished.value = true;
                      widget.onCancel.call();
                    },
                    child: Image.asset(
                      "assets/icon/icon_recognize_cancel.webp",
                      width: 32.w,
                      height: 32.h,
                    ),
                  ),
                  Expanded(
                    child: !audioWaveLogic.isPressedFinished.value
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                height: 40.h,
                                width: 58.w,
                                color: Colors.transparent,
                                child: CustomPaint(
                                  painter:
                                      WaveformPainter(waveHeights: _waveData),
                                ),
                              ),
                              SizedBox(
                                width: 16.w,
                              ),
                              Text(
                                S.of(context).listening,
                                style: TextStyle(
                                    fontSize: 14.sp, color: Color(0xff00D4FD)),
                              )
                            ],
                          )
                        : Center(
                            child: Text(
                              S.of(context).transcribing,
                              style: TextStyle(
                                  fontSize: 16.sp, color: Color(0xff988B9A)),
                            ),
                          ),
                  ),
                  GestureDetector(
                      onTap: () {
                        widget.onDone.call();
                      },
                      child: !audioWaveLogic.isLoading.value
                          ? Image.asset(
                              "assets/icon/icon_recognize.webp",
                              width: 32.w,
                              height: 32.h,
                            )
                          : Lottie.asset("assets/json/loading.json",
                              width: 38.w, fit: BoxFit.fill))
                ],
              ),
            )));
  }

  void _startRecording() async {
    await _initMicrophone();
  }

  void _stopRecording() {
    _audioStreamSubscription?.cancel();
    _waveData = [];
  }

  Future<void> _initMicrophone() async {
    try {
      final stream = await MicStream.microphone(
        sampleRate: 16000,
        audioFormat: AudioFormat.ENCODING_PCM_16BIT,
      );

      _audioStreamSubscription = stream.listen(
        (audioData) {
          _processAudioData(audioData);
        },
        onError: (Object error) {
          print('Error: $error');
        },
        cancelOnError: true,
      );
    } catch (e) {
      print('麦克风初始化失败: $e');
    }
  }

  void _processAudioData(Uint8List audioData) {
    // 控制更新频率 - 避免过快更新
    final now = DateTime.now();
    if (_lastUpdate != null &&
        now.difference(_lastUpdate!) < _minUpdateInterval) {
      return;
    }
    _lastUpdate = now;

    // 将PCM数据转换为振幅值
    final Int16List int16Data = Int16List.view(audioData.buffer);
    final List<double> amplitudes = [];

    // 处理音频数据，计算振幅
    for (int i = 0; i < int16Data.length; i += 1) {
      // final double amplitude = int16Data[i].abs() / 32767.0;
      final double amplitude = (int16Data[i].abs() / 32767.0).clamp(0.0, 1.0);
      amplitudes.add(amplitude);
    }

    // double smoothingFactor = 0.3; // 平滑因子，值越小越平滑 (0.1-0.5)
    // double previousAmplitude = 0.0;
    //
    // for (int i = 0; i < int16Data.length; i += 1) {
    //   final double rawAmplitude = (int16Data[i].abs() / 32767.0).clamp(0.0, 1.0);
    //   // 应用平滑处理
    //   final double smoothedAmplitude = (smoothingFactor * rawAmplitude) +
    //       ((1 - smoothingFactor) * previousAmplitude);
    //   amplitudes.add(smoothedAmplitude);
    //   previousAmplitude = smoothedAmplitude;
    // }

    // 平均分段采样
    final int sampleCount = 9;
    final List<double> sampledData = List.filled(sampleCount, 0.0);

    if (amplitudes.isNotEmpty) {
      final int segmentSize = amplitudes.length ~/ sampleCount;

      for (int i = 0; i < sampleCount; i++) {
        // 计算每个段的平均值
        final int start = i * segmentSize;
        final int end =
            (i == sampleCount - 1) ? amplitudes.length : (i + 1) * segmentSize;

        double sum = 0;
        int count = 0;
        for (int j = start; j < end && j < amplitudes.length; j++) {
          sum += amplitudes[j];
          count++;
        }

        if (count > 0) {
          sampledData[i] = sum / count;
        }
      }
    }

    setState(() {
      _waveData = sampledData;
    });
  }
}

class WaveformPainter extends CustomPainter {
  final List<double> waveHeights;
  final Random _random = Random();

  WaveformPainter({required this.waveHeights});

  @override
  void paint(Canvas canvas, Size size) {
    final centerY = size.height / 2;
    final maxHeight = size.height * 1.5;
    final barWidth = size.width / waveHeights.length;
    final paint = Paint()
      ..strokeWidth = 2
      ..color = Color(0xFF00D4FD)
      ..style = PaintingStyle.fill;

    // 绘制声波纹
    for (int i = 0; i < waveHeights.length; i++) {
      final normalizedHeight = waveHeights[i];
      // final height = normalizedHeight * maxHeight;
      // 使用平方函数增强视觉效果
      final enhancedHeight = normalizedHeight * normalizedHeight;
      final height = enhancedHeight * maxHeight * 1.5;
      // 调整这部分来改变粗细
      // final barFillRatio = 0.1 + _random.nextDouble() * 0.3;
      var barFillRatio;
      if (i == 4) {
        barFillRatio = 0.3;
      } else {
        barFillRatio = 0.1;
      }
      final actualBarWidth = barWidth * barFillRatio;
      final left = i * barWidth + (barWidth - actualBarWidth) / 2;
      final top = centerY - height / 2;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(left, top, actualBarWidth, height),
          const Radius.circular(14),
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class AudioWaveLogic extends GetxController {
  var isLoading = false.obs;
  var isPressedFinished = true.obs;

  void init(){
    isPressedFinished.value = true;
    isLoading.value = false;
  }
}
