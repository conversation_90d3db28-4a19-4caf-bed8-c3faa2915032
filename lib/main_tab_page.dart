// 主页面，包含底部导航栏和两个页面
import 'dart:async';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_tilt/flutter_tilt.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/group/group_chat/group_chat_page.dart';
import 'package:new_agnes/module/debug/global.dart';
import 'package:new_agnes/module/home/<USER>/input_widgets/audio_wave_widget.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/GlobalFloatingWrapper.dart';

import 'api/Api.dart';
import 'api/ApiProvider.dart';
import 'api/StorageService.dart';
import 'main_tab_logic.dart';
import 'module/chat/group/group_chat/agora_logic.dart';
import 'module/chat/group/group_chat/rtc_chat/rtc_logic.dart';
import 'module/chat/group/group_chat_room/CallFloatingButtonWidget.dart';
import 'module/chat/widget/deep_research_lay.dart';
import 'module/home/<USER>/input_widgets/home_drawer.dart';
import 'module/home/<USER>/logic.dart';
import 'module/chat/group/group_chat/rtc_chat/rtc_utils.dart';
import 'module/home/<USER>/page.dart';
import 'module/home/<USER>/widgets/roles_bottom_navigation_bar.dart';
import '../../../../utils/event_bus.dart';
import 'utils/ble/blueToothManager.dart';
import 'utils/stt/AzureSpeechManager.dart';

class MainTabPage extends StatefulWidget {
  @override
  State<MainTabPage> createState() => _MainTabPageState();
}

class _MainTabPageState extends State<MainTabPage>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  final RolesLogic rolesLogic = Get.isRegistered<RolesLogic>()
      ? Get.find<RolesLogic>()
      : Get.put(RolesLogic());
  final rtcLogic =
      Get.isRegistered<RtcLogic>() ? Get.find<RtcLogic>() : Get.put(RtcLogic());
  late Animation<double> scaleAnimation;
  late AnimationController scaleAnimationController;
  double opacity = 0;
  final HomeDrawerLogic homeDrawerLogic = Get.isRegistered<HomeDrawerLogic>()
      ? Get.find<HomeDrawerLogic>()
      : Get.put(HomeDrawerLogic());
  final MainTabLogic mainTabLogic = Get.isRegistered<MainTabLogic>()
      ? Get.find<MainTabLogic>()
      : Get.put(MainTabLogic());
  final List<Widget> _pages = [
    RolesPage(),
    GroupChatPage(),
  ];
  final logic = Get.put(AgoraLogic());

  var initSuccess = false.obs;

  var mAppKey = "".obs;

  /// 前台服务是否启动
  StreamSubscription? _messageSubscription;

  String? agoraUserId;

  //007eJxTYFhQm2x9lemlf1112ckP1pe+nJPltjOROn6tJjx388xNEosUGMwSk8zN0xItkyyTU0wMzI0SjZJTLS3SUlMMjRMNEg1Mz6++kKEgw8CgfcGYlZGBlYERCEF8fYbE9LzU4vjS4tSieFPjNJPUlMQkXUNTcwtdkyQzY10LAyMzXQPjRKOUNEtzkzSLJADBwCwA
  String? agoraToken;

  @override
  void initState() {
    super.initState();
    try {
      agoraUserId = Get.find<StorageService>().getAgoraUserId();
      agoraToken = Get.find<StorageService>().getAgoraToken();
    } catch (e) {
      agoraUserId = null;
      agoraToken = null;
    }
    CallFloatingButtonWidgetManager().initCallFloatingButtonWidgetManager();
    WidgetsBinding.instance.addObserver(this);
    initScale();
    String agoraAppKey = Get.find<StorageService>().getAgoraInitAppKey();
    if (agoraAppKey.isEmpty) {
      print("debug initSDK getOtherServiceToken");
      rolesLogic.getOtherServiceToken(thirdSDKCallback: (appKey) {
        loadAppKey(appKey);
      }, errorCallback: (err) {
        showFailToast("获取appKey失败${err}");
      });
    } else {
      var data = Get.find<StorageService>().getThirdSDKData();

      if ((data.AZURE_SPEECH_SERVICE_KEY ?? "").isNotEmpty &&
          (data.AZURE_SPEECH_SERVICE_REGION ?? "").isNotEmpty) {
        rolesLogic.initSpeech(data.AZURE_SPEECH_SERVICE_KEY ?? "",
            data.AZURE_SPEECH_SERVICE_REGION ?? "");
      }
      print("debug initSDK appKey from cache");
      loadAppKey(agoraAppKey);
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      homeDrawerLogic.scaffoldKey.currentState?.isDrawerOpen;
      mainTabLogic.getFeatureAccess(callback: () {});
    });
    Get.find<BlueToothManager>().init();
    Get.find<BlueToothManager>().setNotificationCallback((data) async {
      debugPrint("收到蓝牙数据: $data");
      if ((data["type"] ?? "") == "double_click") {
        // 检查是否已经初始化，否则弹窗提醒
        if (AzureSpeechManager.isInitialized.value) {
          // 启动语音识别
          if (AzureSpeechManager.eventCallback == null) {
            print("eventCallback is null");
            var rolesLogic = Get.find<RolesLogic>();
            rolesLogic.setSpeechEventListener();
          }
          await AzureSpeechManager.switchMode(RecognizerMode.normal);
          AzureSpeechManager.startContinuousRecognition(true);
        } else {
          AzureSpeechManager.showUnInitializingDialog();
          return;
        }
        if (rolesLogic.currentType == 1) {
          // 返回到首页
          Get.until((route) => route.isFirst);

          // 关闭侧边栏
          if (homeDrawerLogic.scaffoldKey.currentState?.isDrawerOpen == true) {
            Navigator.of(Get.context!).pop();
          }

          // 不展示底部弹窗
          // logic.showCallAgnesPopup.value = true;
        }
      }
    });
    initEventListener();
  }

  void initEventListener() {
    _messageSubscription = eventBus.on<TabEvent>().listen((value) {
      if (value.type == 1) {
        homeDrawerLogic.scaffoldKey.currentState?.openDrawer();
      }
      if (value.type == 3) {
        setState(() {});
      }
    });
  }

  Future<void> loadAppKey(String appKey) async {
    // print("debug initSDK loadAppKey: ${DateTime.now().millisecondsSinceEpoch}");
    mAppKey.value = appKey;
    print("agora: 初始化的AppKey$appKey");
    bool canInit = appKey.isEmpty || appKey == "#" || appKey.contains("null");
    if (!canInit) {
      try {
        logic.initSDK(appKey).then((val) {
          initSuccess.value = val;
          logic.setAppKey(appKey);
          if (val) {
            if ((agoraUserId ?? "").isNotEmpty &&
                (agoraToken ?? "").isNotEmpty) {
              print("agora: get cache token");
              logic.preOut(agoraUserId!, agoraToken!);
            } else {
              print("agora: get new token");
              Get.find<ApiProvider>().post(Api.getAgoraInfo, {}).then((value) {
                if (value.isOk && value.statusCode == 200) {
                  final Map<String, dynamic> responseBody = value.body;
                  final agoraUserId = responseBody['agora_user_id'];
                  final agoraToken = responseBody['token'];
                  if (responseBody['agora_user_id'] != null &&
                      responseBody['token'] != null &&
                      responseBody['agora_user_id'].toString().isNotEmpty &&
                      responseBody['token'].toString().isNotEmpty) {
                    logic.preOut(agoraUserId, agoraToken);
                  } else {
                    showFailToast(
                        "agora: agora_user_id or token is empty! ${value.statusCode}");
                  }
                }
              });
            }
          } else {
            showFailToast("初始化失败");
            Get.find<StorageService>().removeAppKey();
          }
        });
      } catch (e) {
        showFailToast("初始化失败${e.toString()}");
        Get.find<StorageService>().removeAppKey();
      }
    } else {
      WidgetsBinding.instance.addPostFrameCallback((tim) {
        if (mounted) {
          Get.defaultDialog(
              title: "提示",
              middleText: "未获取到AppKey,是否重新获取?",
              textConfirm: "获取",
              textCancel: "取消",
              onConfirm: () {
                rolesLogic.getOtherServiceToken(thirdSDKCallback: (appKey) {
                  loadAppKey(appKey);
                });
              });
        }
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      // 应用退到后台时启动前台服务
      CallFloatingButtonWidgetManager().isFront = false;
      if (rtcLogic.isJoin) {
        if (CallFloatingButtonWidgetManager().currentRouteName ==
            "/GroupChatRoomPage") {
          eventBus.fire("showFloatButton");
        }
      }
    } else if (state == AppLifecycleState.resumed) {
      CallFloatingButtonWidgetManager().isFront = true;
      if (rtcLogic.isJoin) {
        // 应用回到前台时停止前台服务（可选）
        if (CallFloatingButtonWidgetManager().currentRouteName ==
            "/GroupChatRoomPage") {
          eventBus.fire("hideFloatButton");
        }
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _messageSubscription!.cancel();
    logic.listenerMap.clear();
    RtcUtils.instance.cleanEngine();
    try {
      ChatClient.getInstance.chatManager
          .removeMessageEvent(EventType.group_chat.name);
      ChatClient.getInstance.chatManager
          .removeEventHandler(EventType.group_chat.name);
      ChatClient.getInstance.chatManager.removeEventHandler("1");
    } catch(e) {

    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    CmUtils.bottomBarHeight.value = CmUtils.getBottomBarHeight(Get.context);
    return GlobalFloatingWrapper(
      showDebugFloating: Global.isDebug,
      child: Stack(
        children: [
          Tilt(
            tiltConfig: const TiltConfig(
              disable: true,
              angle: 6.0,
              enableReverse: true,
              enableOutsideAreaMove: false,
              leaveDuration: Duration(milliseconds: 600),
              filterQuality: FilterQuality.high,
            ),
            lightConfig: const LightConfig(disable: true),
            shadowConfig: const ShadowConfig(enableReverse: true),
            childLayout: ChildLayout(inner: [
              ScaleTransition(
                scale: scaleAnimation,
                alignment: Alignment.center,
                filterQuality: FilterQuality.high,
                child: TiltParallax(
                  child: Container(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage("assets/images/bg_home.png"),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
            ]),
            child: const SizedBox(),
          ),

          // Positioned.fill(
          //   child: ScaleTransition(
          //     scale: scaleAnimation,
          //     alignment: Alignment.center,
          //     child: Container(
          //       decoration: BoxDecoration(
          //         image: DecorationImage(
          //           image: AssetImage( "assets/images/bg_home.png"),
          //           fit: BoxFit.cover,
          //         ),
          //       ),
          //     ),
          //   ),
          // ),

          Obx(() {
            return Container(
              color: mainTabLogic.curIndex.value == 0
                  ? Colors.transparent
                  : Color(0x7F0D0D0D),
              width: 1.sw,
              height: 1.sh,
            );
          }),
          Obx(() => Container(
                child: Scaffold(
                  drawerEdgeDragWidth: 200,
                  extendBody: true,
                  onDrawerChanged: (isDrawerOpen) async {
                    if (isDrawerOpen) {
                      await homeDrawerLogic.loadFavouredSearchHistory();
                      await homeDrawerLogic.loadNonFavouredSearchHistory();
                      await homeDrawerLogic.getQuotaRequest();
                    }
                  },
                  key: homeDrawerLogic.scaffoldKey,
                  resizeToAvoidBottomInset: mainTabLogic.curIndex.value == 0,
                  body: Obx(() => IndexedStack(
                        index: mainTabLogic.curIndex.value,
                        children: _pages,
                      )),
                  drawer: Drawer(
                    child: HomeDrawer(context, "home"),
                  ),
                  backgroundColor: Colors.transparent,
                  bottomNavigationBar: Obx(() {
                    return mainTabLogic.isFeatureGroup.value
                        ? RolesBottomNavigationBar(
                            rolesItems: [
                              RolesBarItem(
                                text: "Agnes",
                                selectIconName: "assets/icon/agnes_select.webp",
                                unSelectIconName:
                                    "assets/icon/agnes_unSelect.webp",
                              ),
                              RolesBarItem(
                                text: "CoVibe",
                                selectIconName:
                                    "assets/icon/icon_coVibe_select.webp",
                                unSelectIconName:
                                    "assets/icon/icon_coVibe_unSelect.webp",
                              ),
                            ],
                            curIndex: mainTabLogic.curIndex.value,
                            selectTextStyle:
                                TextStyle(color: Colors.white, fontSize: 10.sp),
                            unSelectTextStyle: TextStyle(
                                color: Color(0xFF988B9A), fontSize: 10.sp),
                            height:
                                RolesBottomNavigationBar.getNavigationBarHeight(
                                        context) +
                                    CmUtils.bottomBarHeight.value,
                            onChangeTab: (index) {
                              print('11=${initSuccess.value}');
                              print('12=${index}');
                              print(
                                  '13=${Get.find<AgoraLogic>().isJoined.value}');
                              mainTabLogic.curIndex.value = index;
                              if (index == 0) {
                                //Get.find<BlueToothManager>().init();
                                Get.find<BlueToothManager>()
                                    .setNotificationCallback((data) async {
                                  debugPrint("home收到蓝牙数据: $data");
                                  if ((data["type"] ?? "") == "double_click") {
                                    // 检查是否已经初始化，否则弹窗提醒
                                    if (AzureSpeechManager
                                        .isInitialized.value) {
                                      // 启动语音识别
                                      if (AzureSpeechManager.eventCallback ==
                                          null) {
                                        print("eventCallback is null");
                                        var rolesLogic = Get.find<RolesLogic>();
                                        rolesLogic.setSpeechEventListener();
                                      }
                                      await AzureSpeechManager.switchMode(
                                          RecognizerMode.normal);
                                      AzureSpeechManager
                                          .startContinuousRecognition(true);
                                    } else {
                                      AzureSpeechManager
                                          .showUnInitializingDialog();
                                      return;
                                    }
                                    if (rolesLogic.currentType == 1) {
                                      // 返回到首页
                                      Get.until((route) => route.isFirst);

                                      // 关闭侧边栏
                                      if (homeDrawerLogic.scaffoldKey
                                              .currentState?.isDrawerOpen ==
                                          true) {
                                        Navigator.of(Get.context!).pop();
                                      }

                                      // 不展示底部弹窗
                                      // logic.showCallAgnesPopup.value = true;
                                    }
                                  }
                                });
                              }
                              if (index == 1) {
                                if (rolesLogic.homeTaskInputLogic
                                    .isRecognizingAni.value) {
                                  WidgetsBinding.instance
                                      .addPostFrameCallback((_) {
                                    var audioWaveLogic =
                                        Get.find<AudioWaveLogic>();
                                    audioWaveLogic.init();
                                    rolesLogic.homeTaskInputLogic
                                        .isRecognizingAni.value = false;
                                    AzureSpeechManager
                                        .stopContinuousRecognition();
                                  });
                                }
                              }
                            },
                          )
                        : SizedBox(
                            height: CmUtils.bottomBarHeight.value,
                          );
                  }),
                ),
              )),
        ],
      ),
    );
  }

  void initScale() {
    scaleAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2400),
      reverseDuration: const Duration(milliseconds: 1000),
    );
    scaleAnimation = Tween(begin: 1.2, end: 2.4).animate(
      CurvedAnimation(
        parent: scaleAnimationController,
        curve: const Cubic(0.16, 1, 0.3, 1),
        reverseCurve: Curves.easeInCirc,
      ),
    );
    scaleAnimationController.addListener(() {
      switch (scaleAnimationController.status) {
        case AnimationStatus.forward:
          if (opacity == 1) return;
          setState(() => opacity = 1);
        case AnimationStatus.reverse:
          if (opacity == 0) return;
          setState(() => opacity = 0);
        case AnimationStatus.dismissed:
        case AnimationStatus.completed:
      }
    });
  }
}
