import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/dialog/fan_kui_dialog.dart';
import 'package:new_agnes/dialog/message_auto_dialog.dart';
import 'package:new_agnes/dialog/message_dialog.dart';
import 'package:new_agnes/dialog/see_markdown_dialog.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:permission_handler/permission_handler.dart';

import '../dialog/download_dialog.dart';
import '../generated/l10n.dart';
import '../module/chat/widget/chat_bottom_tip_widget.dart';
import '../module/mine/permission/permission_page.dart';
import 'cmUtils.dart';

class DialogUtils{

  static Future<void> showDownLoadDialog(id,fileType,content,fileName,type,{pathUrl=''}) async {
    bool isDenied = await CmUtils.requestStoragePermission();
    if(isDenied){
      print("存储权限未授予");
    }else{
      showDialog(
          context: Get.context!,
          barrierDismissible: false,
          builder: (context) {
            return DownloadDialog(id, fileType,
              content, fileName,type,pathUrl:pathUrl ,);
          });
    }
  }

  static void showSeeMarkDownDialog(content,path){
    showDialog(
        context: Get.context!,
        barrierDismissible:true,
        builder: (context) {
          return SeeMarkdownDialog(content,path);
        });
  }

  static void showFanKuiDialog(FeedbackLogic feedbackLogic){
    showDialog(
        context: Get.context!,
        barrierDismissible:true,
        builder: (context) {
          return FanKuiDialog(feedbackLogic);
        });
  }

  static void showRequestFloatingDialog(Function? onConfirm,Function? onCancel){
    showDialog(
        context: Get.context!,
        barrierDismissible:true,
        builder: (context) {
          return MessageAutoDialog(
            title: S.of(context).permissionToExecuteTasksInTheBackgroundIsNotGranted,
                  isTitleCenter: true,
                  data: S.of(context).yourPhoneHasNotAuthorizedAgnesToUseTheFloatingWindowPermissionAgnesWillNotBeAbleToContinuePerformingTasksInTheBackgroundForYou,
                  onLeftName: S.of(context).cancel,
                  onRightName: S.of(context).setNow,
                  height: 350.w,
                  (){
            onCancel?.call();
          }, (){
            onConfirm?.call();
          });
        });
  }

}