// inAppLogUtil.dart
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class inAppLogUtil {
  static final List<LogEntry> _logs = [];
  static final logStream = StreamController<List<LogEntry>>.broadcast();

  static void addLog(String message, {LogLevel level = LogLevel.info}) {
    final entry = LogEntry(
      message: message,
      level: level,
      timestamp: DateTime.now(),
    );
    _logs.add(entry);
    // 保持最新的1000条日志
    if (_logs.length > 1000) {
      _logs.removeRange(0, _logs.length - 1000);
    }
    logStream.sink.add(List.unmodifiable(_logs));
  }

  static List<LogEntry> get logs => List.unmodifiable(_logs);

  // 添加清空日志的方法
  static void clearLogs() {
    _logs.clear();
    logStream.sink.add(List.unmodifiable(_logs)); // 发送清空后的列表
  }

  static void dispose() {
    logStream.close();
  }
}

enum LogLevel { info, warning, error }

class LogEntry {
  final String message;
  final LogLevel level;
  final DateTime timestamp;

  LogEntry({
    required this.message,
    required this.level,
    required this.timestamp,
  });
}

Widget buildInAppLogButton(BuildContext context){
  return Positioned(
    top: 20,
    right: 20,
    child: ElevatedButton(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => LogViewerPage()),
        );
      },
      child: Text('LOG'),
    ),
  );
}

class LogViewerPage extends StatefulWidget {
  @override
  _LogViewerPageState createState() => _LogViewerPageState();
}

class _LogViewerPageState extends State<LogViewerPage> {
  late StreamSubscription<List<LogEntry>> _logSubscription;
  List<LogEntry> _logs = [];
  List<LogEntry> _filteredLogs = [];
  final TextEditingController _searchController = TextEditingController();
  String _searchKeyword = '';

  @override
  void initState() {
    super.initState();
    _logSubscription = inAppLogUtil.logStream.stream.listen((logs) {
      setState(() {
        _logs = logs;
        _filterLogs();
      });
    });

    // 初始化时获取现有日志
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _logs = inAppLogUtil.logs;
        _filterLogs();
      });
    });

    // 监听搜索框变化
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    setState(() {
      _searchKeyword = _searchController.text.trim();
      _filterLogs();
    });
  }

  void _filterLogs() {
    if (_searchKeyword.isEmpty) {
      _filteredLogs = List.from(_logs);
    } else {
      _filteredLogs = _logs.where((log) {
        return log.message.toLowerCase().contains(_searchKeyword.toLowerCase());
      }).toList();
    }
  }

  @override
  void dispose() {
    _logSubscription.cancel();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('应用日志'),
        actions: [
          IconButton(
            icon: Icon(Icons.clear),
            onPressed: () {
              inAppLogUtil.clearLogs(); // 调用 LogManager 的清空方法
            },
          )
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索日志...',
                prefixIcon: Icon(Icons.search),
                suffixIcon: _searchKeyword.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
            ),
          ),
          // 日志列表
          Expanded(
            child: _filteredLogs.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _searchKeyword.isEmpty ? Icons.text_snippet : Icons.search_off,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          _searchKeyword.isEmpty ? '暂无日志' : '未找到匹配的日志',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                        if (_searchKeyword.isNotEmpty)
                          Text(
                            '尝试使用其他关键词搜索',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: _filteredLogs.length,
                    itemBuilder: (context, index) {
                      final log = _filteredLogs[_filteredLogs.length - 1 - index]; // 倒序显示，最新的在上面
                      return ListTile(
                        title: Text(
                          log.message,
                          style: TextStyle(
                            color: _searchKeyword.isNotEmpty
                                ? _highlightMatch(log.message, _searchKeyword)
                                    ? Colors.black
                                    : Colors.grey[600]
                                : Colors.black,
                          ),
                        ),
                        subtitle: Text(
                          '${log.timestamp.toString()} - ${log.level.name}',
                          style: TextStyle(fontSize: 12),
                        ),
                        tileColor: _getLogColor(log.level),
                        isThreeLine: true,
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  bool _highlightMatch(String message, String keyword) {
    return keyword.isEmpty || message.toLowerCase().contains(keyword.toLowerCase());
  }

  Color? _getLogColor(LogLevel level) {
    switch (level) {
      case LogLevel.info:
        return Colors.blue[50];
      case LogLevel.warning:
        return Colors.orange[50];
      case LogLevel.error:
        return Colors.red[50];
      default:
        return null;
    }
  }
}

void logInfo(String message) {
  if (kDebugMode) {
    debugPrint('[INFO] $message');
  }
  inAppLogUtil.addLog(message, level: LogLevel.info);
}

void logWarning(String message) {
  if (kDebugMode) {
    debugPrint('[WARNING] $message');
  }
  inAppLogUtil.addLog(message, level: LogLevel.warning);
}

void logError(String message) {
  if (kDebugMode) {
    debugPrint('[ERROR] $message');
  }
  inAppLogUtil.addLog(message, level: LogLevel.error);
}
