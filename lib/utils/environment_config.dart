import 'package:get/get.dart';
import 'package:new_agnes/api/StorageService.dart';

enum Environment { production, development, gcp, custom }

class EnvironmentConfig extends GetxService {
  static const String _productionBaseUrl = "https://app.agnes-ai.com";
  static const String _developmentBaseUrl = "https://agnes-dev.kiwiar.com";
  static const String _gcpBaseUrl = "https://agnes-test-gcp.kiwiar.com";
  
  Environment _currentEnvironment = Environment.production;
  String? _customBaseUrl;
  final StorageService _storageService = Get.find<StorageService>();
  
  @override
  void onInit() {
    super.onInit();
    _loadEnvironmentFromStorage();
  }
  
  Environment get currentEnvironment => _currentEnvironment;
  
  String get baseUrl {
    switch (_currentEnvironment) {
      case Environment.production:
        return _productionBaseUrl;
      case Environment.development:
        return _developmentBaseUrl;
      case Environment.gcp:
        return _gcpBaseUrl;
      case Environment.custom:
        return _customBaseUrl ?? _productionBaseUrl;
    }
  }
  
  void _loadEnvironmentFromStorage() {
    final savedEnvironment = _storageService.getStorage().read('debug_environment');
    final savedCustomUrl = _storageService.getStorage().read('debug_custom_url');
    
    if (savedEnvironment != null) {
      _currentEnvironment = Environment.values.firstWhere(
        (env) => env.name == savedEnvironment,
        orElse: () => Environment.production,
      );
    } else {
      // 默认根据当前API配置判断环境
      _currentEnvironment = Environment.production;
    }
    
    if (savedCustomUrl != null && savedCustomUrl.isNotEmpty) {
      _customBaseUrl = savedCustomUrl;
    }
  }
  
  void switchEnvironment(Environment environment) {
    _currentEnvironment = environment;
    _storageService.getStorage().write('debug_environment', environment.name);
  }
  
  void setCustomUrl(String url) {
    _customBaseUrl = url;
    _storageService.getStorage().write('debug_custom_url', url);
    switchEnvironment(Environment.custom);
  }
  
  String? get customUrl => _customBaseUrl;
  
  bool get isProduction => _currentEnvironment == Environment.production;
  bool get isDevelopment => _currentEnvironment == Environment.development;
  bool get isGcp => _currentEnvironment == Environment.gcp;
  bool get isCustom => _currentEnvironment == Environment.custom;
  
  String get environmentName {
    switch (_currentEnvironment) {
      case Environment.production:
        return '正式环境';
      case Environment.development:
        return '测试环境';
      case Environment.gcp:
        return 'GCP环境';
      case Environment.custom:
        return '自定义环境';
    }
  }
}