import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:new_agnes/generated/l10n.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:new_agnes/widget/GradientBorderContainer.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../module/home/<USER>/logic.dart';

enum RecognizerMode {
  normal,
  rtc,
}

class AzureSpeechManager {
  static const MethodChannel _methodChannel =
      MethodChannel('azure_speech_recognizer');
  static const MethodChannel _callbackChannel =
      MethodChannel('azure_speech_callback');

  static Function(Map<dynamic, dynamic> event)? eventCallback;
  static bool _isCallbackHandlerSet = false;
  static bool _isFromBle = false;
  static bool _isDisposed = false;

  static get isFromBle => _isFromBle;
  static var isInitialized = false.obs;
  static var isInitializing = false.obs;
  static var isRecognizing = false.obs;
  static var isRecovering = false.obs;
  static var currentMode = RecognizerMode.normal.obs; // 新增：当前模式
  static var listening = 0.obs;

  static late String key;
  static late String reg;

  // 初始化语音识别器（支持模式选择）
  static Future<bool> initializeRecognizer({
    required String subscriptionKey,
    required String region,
    String? language,
    RecognizerMode mode = RecognizerMode.normal, // 新增：模式参数
  }) async {
    if (_isDisposed) {
      print("语音识别器已销毁，无法初始化");
      return false;
    }

    // 如果没有提供语言，则从当前本地化设置获取
    String languageCode = language ?? getCurrentLanguageCode();
    debugPrint("语音识别语言: $languageCode");

    // 默认添加中英文
    List<String> languageCodes = [
      languageCode,
      'zh-CN',
      'en-US',
    ];

    key = subscriptionKey;
    reg = region;

    try {
      isInitializing.value = true;

      // 只设置一次回调处理器
      if (!_isCallbackHandlerSet) {
        _callbackChannel.setMethodCallHandler(_handleCallback);
        _isCallbackHandlerSet = true;
      }

      final result = await _methodChannel.invokeMethod('initializeRecognizer', {
        'subscriptionKey': subscriptionKey,
        'region': region,
        'language': languageCodes,
        'mode': mode.name, // 传递模式参数
      });

      isInitialized.value = result as bool;
      currentMode.value = mode; // 更新当前模式

      if (isInitialized.value) {
        print("语音识别器初始化成功，模式: $mode");
      } else {
        print("语音识别器初始化失败");
      }
      return isInitialized.value;
    } on PlatformException catch (e) {
      print("初始化语音识别器失败: ${e.message}");
      isInitialized.value = false;
      return false;
    } catch (e) {
      print("初始化语音识别器出现未知错误: $e");
      isInitialized.value = false;
      return false;
    } finally {
      isInitializing.value = false;
    }
  }

  // 切换模式
  static Future<bool> switchMode(RecognizerMode mode) async {
    if (_isDisposed) {
      print("语音识别器已销毁，无法切换模式");
      return false;
    }

    if (!isInitialized.value) {
      print("语音识别器未初始化，无法切换模式");
      return false;
    }

    // 如果模式相同，无需切换
    if (currentMode.value == mode) {
      print("当前已是$mode模式，无需切换");
      return true;
    }

    try {
      final result = await _methodChannel.invokeMethod('switchMode', {
        'mode': mode.name,
      });

      bool success = result as bool;
      if (success) {
        currentMode.value = mode;
        print("成功切换到$mode模式");
      } else {
        print("切换到$mode模式失败");
      }

      return success;
    } on PlatformException catch (e) {
      print("切换模式失败: ${e.message}");
      return false;
    } catch (e) {
      print("切换模式出现未知错误: $e");
      return false;
    }
  }

  // 推送音频数据（用于RTC模式）
  static Future<bool> pushAudioData(Uint8List audioData) async {
    if (_isDisposed) {
      print("语音识别器已销毁，无法推送音频数据");
      return false;
    }

    if (!isInitialized.value) {
      print("语音识别器未初始化，无法推送音频数据");
      return false;
    }

    // 只有在RTC模式下才推送音频数据
    if (currentMode.value != RecognizerMode.rtc) {
      print("当前不是RTC模式，无需推送音频数据");
      return false;
    }

    try {
      final result = await _methodChannel.invokeMethod('pushAudioData', {
        'audioData': audioData,
      });

      return result as bool;
    } on PlatformException catch (e) {
      print("推送音频数据失败: ${e.message}");
      return false;
    } catch (e) {
      print("推送音频数据出现未知错误: $e");
      return false;
    }
  }

  // 带重试机制的初始化
  static Future<bool> initializeRecognizerWithRetry({
    required String subscriptionKey,
    required String region,
    String? language,
    RecognizerMode mode = RecognizerMode.normal, // 新增：模式参数
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        print("尝试初始化语音识别器 (第$attempt次/$maxRetries次)");

        bool result = await initializeRecognizer(
          subscriptionKey: subscriptionKey,
          region: region,
          language: language,
          mode: mode, // 传递模式参数
        );

        if (result) {
          print("语音识别器初始化成功");
          return true;
        } else if (attempt < maxRetries) {
          print("初始化失败，${retryDelay.inSeconds}秒后进行第${attempt + 1}次重试");
          await Future.delayed(retryDelay);
        }
      } catch (e) {
        print("第$attempt次初始化尝试出现异常: $e");
        if (attempt < maxRetries) {
          print("${retryDelay.inSeconds}秒后进行第${attempt + 1}次重试");
          await Future.delayed(retryDelay);
        }
      }
    }

    print("语音识别器初始化失败，已达到最大重试次数($maxRetries次)");
    return false;
  }

  static Future<void> reInit(Locale locale) async {
    await initializeRecognizer(
        subscriptionKey: key,
        region: reg,
        language: getCurrentLanguageCode(locale: locale),
        mode: currentMode.value); // 保持当前模式
  }

  // 处理回调
  static Future<void> _handleCallback(MethodCall call) async {
    if (call.method == 'onSpeechEvent') {
      if (call.arguments is Map) {
        Map<dynamic, dynamic> event = Map<dynamic, dynamic>.from(call.arguments);

        // 检查是否是错误或取消事件
        if ((event['reason'] == 'Error' || event['event'] == 'canceled') &&
            event['details'] != null) {
          String errorDetails = event['details'].toString();

          // 检查是否是需要自动恢复的错误
          if (_shouldAutoRecover(errorDetails)) {
            print("检测到可恢复错误，启动自动恢复流程: $errorDetails");
            _triggerAutoRecovery();
          }
        }

        // 通知上层应用事件
        if (eventCallback != null) {
          try {
            eventCallback!(event);
          } catch (e) {
            print("处理语音事件回调时出错: $e");
          }
        }

        // 更新识别状态
        if (event['event'] == 'sessionStarted') {
          isRecognizing.value = true;
        } else if (event['event'] == 'sessionStopped' || event['event'] == 'canceled') {
          isRecognizing.value = false;
        }
      }
    }
  }

  // 判断是否需要自动恢复
  static bool _shouldAutoRecover(String errorDetails) {
    return errorDetails.contains('SPXERR_START_RECOGNIZING_INVALID_STATE_TRANSITION') ||
           errorDetails.contains('Connection was closed') ||
           errorDetails.contains('RuntimeError') ||
           errorDetails.contains('1007');
  }

  // 触发自动恢复流程
  static void _triggerAutoRecovery() {
    // 使用异步任务在后台执行恢复，避免阻塞回调处理
    Future(() async {
      if (isRecovering.value) {
        print("恢复流程已在进行中");
        return;
      }

      isRecovering.value = true;

      try {
        print("开始自动恢复流程");

        // 通知上层应用正在恢复
        if (eventCallback != null) {
          eventCallback!({'event': 'autoRecoveryStarted'});
        }

        // 执行恢复操作
        bool recoverySuccess = await _recoverSpeechRecognizer();

        // 通知恢复结果
        if (eventCallback != null) {
          eventCallback!({
            'event': 'autoRecoveryCompleted',
            'success': recoverySuccess
          });
        }

        if (recoverySuccess) {
          print("自动恢复成功");
        } else {
          print("自动恢复失败");
        }
      } catch (e) {
        print("自动恢复过程中出现异常: $e");
        if (eventCallback != null) {
          eventCallback!({
            'event': 'autoRecoveryFailed',
            'error': e.toString()
          });
        }
      } finally {
        isRecovering.value = false;
      }
    });
  }

  // 实际的恢复操作
  static Future<bool> _recoverSpeechRecognizer() async {
    try {
      print("开始恢复语音识别器");

      // 1. 先尝试停止当前识别（如果正在进行）
      try {
        await _methodChannel.invokeMethod('stopContinuousRecognition');
      } catch (e) {
        print("停止当前识别时出错（可忽略）: $e");
      }

      // 2. 关闭现有识别器
      try {
        await closeRecognizer();
      } catch (e) {
        print("关闭识别器时出错（可忽略）: $e");
      }

      // 3. 等待一段时间确保资源释放
      await Future.delayed(Duration(milliseconds: 1000));

      // 4. 重新初始化（保持原来的模式）
      bool result = await initializeRecognizerWithRetry(
        subscriptionKey: key,
        region: reg,
        mode: currentMode.value, // 保持当前模式
        maxRetries: 3,
      );

      return result;
    } catch (e) {
      print("恢复语音识别器过程中出现错误: $e");
      return false;
    }
  }

  // 设置事件监听器
  static void setSpeechEventListener(int key, Function(Map<dynamic, dynamic> event) callback) {
    print("setSpeechEventListener");
    listening.value = key;
    eventCallback = callback;
  }

  static void removeSpeechEventListener() {
    print("removeSpeechEventListener");
    listening.value = 0;
    eventCallback = null;
  }

  // 开始连续语音识别
  static Future<bool> startContinuousRecognition(bool isFromBle) async {
    if (_isDisposed) {
      print("语音识别器已销毁，无法启动识别");
      return false;
    }

    if (isRecovering.value) {
      print("语音识别器正在恢复中，无法启动识别");
      return false;
    }

    if (!isInitialized.value) {
      print("语音识别器未初始化");
      showUnInitializingDialog();
      return false;
    }

    if (isRecognizing.value) {
      print("语音识别已在进行中");
      return true;
    }

    bool isGranted = await requestMic();
    if (!isGranted) {
      showFailToast(S.of(Get.context!).weNeedAccessToYourMicrophoneForVoiceInteraction);
      return false;
    }

    try {
      var logic = Get.find<RolesLogic>();
      if (logic.currentType == 1
          ? logic.homeTaskInputLogic.chatting.value
          : logic.chatInputLogic.chatting.value) {
        showInfoToast(S.of(Get.context!).pleaseWaitForAgnesToCompleteTheTask);
        return false;
      }
    if(logic.currentType==1){
      logic.homeTaskInputLogic.isRecognizeEnd.value = false;
    }else{
      logic.chatInputLogic.isRecognizeEnd.value = false;
    }
      logic.isRecognizing = true;
      isRecognizing.value = true;

      if (isFromBle) {
        // 耳机触发后，不再处理非耳机的点击事件
        _isFromBle = isFromBle;
      }

      final result = await _methodChannel.invokeMethod('startContinuousRecognition');
      bool success = result as bool;

      if (!success) {
        _resetRecognitionState();
      }

      return success;
    } on PlatformException catch (e) {
      print("启动连续识别失败: ${e.message}");
      _resetRecognitionState();
      return false;
    } catch (e) {
      print("启动连续识别出现未知错误: $e");
      _resetRecognitionState();
      return false;
    }
  }

  // 带恢复机制的开始识别方法
  static Future<bool> startContinuousRecognitionWithRecovery(bool isFromBle) async {
    // 首先尝试正常启动
    bool result = await startContinuousRecognition(isFromBle);

    if (result) {
      return true;
    } else {
      // 启动失败，尝试自动恢复
      print("启动识别失败，尝试自动恢复");
      bool recoverySuccess = await _recoverSpeechRecognizer();

      if (recoverySuccess) {
        // 恢复成功后再次尝试启动
        print("自动恢复成功，再次尝试启动识别");
        return await startContinuousRecognition(isFromBle);
      } else {
        print("自动恢复失败，无法启动识别");
        return false;
      }
    }
  }

  static Future<bool> requestMic() async {
    final micStatus = await Permission.microphone.status;
    if (micStatus.isDenied) {
      var permissionStatus = await Permission.microphone.request();
      return permissionStatus.isGranted;
    } else {
      return true;
    }
  }

  // 停止连续语音识别
  static Future<bool> stopContinuousRecognition() async {
    if (!isRecognizing.value) {
      print("语音识别未在进行中");
      return true;
    }

    try {
      var logic = Get.find<RolesLogic>();
      logic.isRecognizing = false;
      isRecognizing.value = false;
      _isFromBle = false;

      final result = await _methodChannel.invokeMethod('stopContinuousRecognition');
      return result as bool;
    } on PlatformException catch (e) {
      print("停止连续识别失败: ${e.message}");
      _resetRecognitionState();
      return false;
    } catch (e) {
      print("停止连续识别出现未知错误: $e");
      _resetRecognitionState();
      return false;
    }
  }

  // 释放资源
  static Future<bool> closeRecognizer() async {
    try {
      _resetRecognitionState();

      final result = await _methodChannel.invokeMethod('closeRecognizer');
      isInitialized.value = false;
      currentMode.value = RecognizerMode.normal; // 重置模式
      return result as bool;
    } on PlatformException catch (e) {
      print("关闭识别器失败: ${e.message}");
      return false;
    } catch (e) {
      print("关闭识别器出现未知错误: $e");
      return false;
    }
  }

  // 重置识别状态
  static void _resetRecognitionState() {
    isRecognizing.value = false;
    _isFromBle = false;
    try {
      var logic = Get.find<RolesLogic>();
      logic.isRecognizing = false;
    } catch (e) {
      // 忽略错误，因为logic可能不存在
    }
  }

  // 辅助方法：获取当前语言代码
  static String getCurrentLanguageCode({Locale? locale}) {
    try {
      Locale currLocale = locale ?? Localizations.localeOf(Get.context!);
      // 根据您的 ARB 文件映射语言代码到 Azure Speech 服务支持的格式
      switch (currLocale.languageCode) {
        case 'zh':
          return 'zh-CN';
        case 'fil':
          return 'fil-PH';
        case 'id':
          return 'id-ID';
        case 'ms':
          return 'ms-MY';
        case 'th':
          return 'th-TH';
        case 'vi':
          return 'vi-VN';
        default:
          return 'en-US';
      }
    } catch (e) {
      print("获取语言代码时出错: $e");
    }

    // 默认返回英语
    return 'en-US';
  }

  static void showUnInitializingDialog() {
    if (Get.context == null) return;

    showGeneralDialog(
      context: Get.context!,
      barrierDismissible: true,
      barrierLabel: S.of(Get.context!).serviceIsStillStarting,
      // 半透明遮罩
      pageBuilder: (context, animation, secondaryAnimation) {
        return Material(
          color: Color(0xCD201034),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Image.asset(
                      "assets/images/icon_dialog_close.webp",
                      width: 24,
                      height: 24,
                    ),
                  ),
                  SizedBox(height: 24),
                  GradientBorderContainer(
                    borderRadius: BorderRadius.all(Radius.circular(21)),
                    strokeWidth: 2,
                    gradients: [
                      LinearGradient(
                        colors: [
                          Color(0xFFFF3BDF),
                          Color(0x08FF3BDF),
                          Color(0xFF00FFFF),
                          Color(0xFF543C86),
                        ],
                        stops: [0, 0.34, 0.76, 1],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ],
                    child: Container(
                      width: 300,
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(20)),
                        image: DecorationImage(
                          image:
                              AssetImage('assets/images/bg_message.png'),
                          // 背景图路径
                          fit: BoxFit.fill,
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Image.asset(
                                "assets/icon/icon_azure_init_tip.webp",
                                width: 40,
                                height: 40,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 1,
                                child: Text(
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  S.of(Get.context!).serviceIsStillStarting,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 32),
                          Text(
                            S.of(Get.context!).tryOneOfThese,
                            style: const TextStyle(
                              fontSize: 16,
                              height: 1.5,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Image.asset(
                                "assets/icon/icon_green_correct.webp",
                                width: 20,
                                height: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 1,
                                child: Text(
                                  S.of(Get.context!).switchWifimobileDataAndRetry,
                                  maxLines: 2,
                                  softWrap: true,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Image.asset(
                                "assets/icon/icon_green_correct.webp",
                                width: 20,
                                height: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                S.of(Get.context!).reopenTheApp,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutBack,
            ),
            child: child,
          ),
        );
      },
    );
  }

  // 销毁语音识别器
  static Future<void> dispose() async {
    _isDisposed = true;
    eventCallback = null;

    try {
      await stopContinuousRecognition();
      await closeRecognizer();
    } catch (e) {
      print("销毁语音识别器时出错: $e");
    }
  }

  // 公开的恢复方法
  static Future<bool> recoverSpeechRecognizer() async {
    return await _recoverSpeechRecognizer();
  }
}
