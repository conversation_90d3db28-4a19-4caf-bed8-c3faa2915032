import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

void showSuccessToast(String message) {
  Get.rawSnackbar(
    margin: EdgeInsets.all(16),
    backgroundColor: Colors.white,
    borderRadius: 12,
    messageText: Text(
      message,
      style: TextStyle(
        color: Colors.black,
        fontSize: 16,
      ),
    ),
    icon: Icon(
      Icons.check_circle,
      color: Colors.green,
    ),
  );
}

void showCopiedSuccessToast(String message) {
  Get.rawSnackbar(
    margin: EdgeInsets.all(16),
    backgroundColor: Color.fromRGBO(13, 13, 13, 0.8),
    borderRadius: 10,
    messageText: Text(
      message,
      style: TextStyle(
        color: Colors.white,
        fontSize: 16,
      ),
    ),
    icon: Image.asset(
      "assets/groupChat/copied.png",
      width: 20.w,
      height: 20.w,
    ),
  );
}

void showInfoToast(String message) {
  Get.rawSnackbar(
    margin: EdgeInsets.all(16),
    backgroundColor: Colors.white,
    borderRadius: 12,
    messageText: Text(
      message,
      style: TextStyle(
        color: Colors.black,
        fontSize: 16,
      ),
    ),
  );
}

void showFailToast(String message) {
  Get.rawSnackbar(
    margin: EdgeInsets.all(16),
    backgroundColor: Colors.white,
    borderRadius: 12,
    messageText: Text(
      message,
      style: TextStyle(
        color: Colors.black,
        fontSize: 16,
      ),
    ),
    icon: Icon(
      Icons.error,
      color: Colors.red,
    ),
  );
}
