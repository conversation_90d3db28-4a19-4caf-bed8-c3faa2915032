// lib/utils/bluetooth/bluetooth_audio_manager.dart
import 'package:flutter/services.dart';

class BluetoothAudioManager {
  static const MethodChannel _channel = MethodChannel('bluetooth_audio');

  static Future<bool> startBluetoothSco() async {
    try {
      final result = await _channel.invokeMethod('startBluetoothSco');
      return result as bool;
    } on PlatformException catch (e) {
      print("启动蓝牙SCO失败: ${e.message}");
      return false;
    }
  }

  static Future<bool> stopBluetoothSco() async {
    try {
      final result = await _channel.invokeMethod('stopBluetoothSco');
      return result as bool;
    } on PlatformException catch (e) {
      print("停止蓝牙SCO失败: ${e.message}");
      return false;
    }
  }

  static Future<bool> isBluetoothScoOn() async {
    try {
      final result = await _channel.invokeMethod('isBluetoothScoOn');
      return result as bool;
    } on PlatformException catch (e) {
      print("检查蓝牙SCO状态失败: ${e.message}");
      return false;
    }
  }
}
