import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/ble/appCrc16.dart';
import 'package:new_agnes/utils/ble/bluetoothPermissionManager.dart';
import 'package:new_agnes/utils/inAppLogUtil.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../generated/l10n.dart';

class BlueToothManager {
  // 权限状态
  bool _hasPermissions = false;

  NotificationCallback? _notificationCallback;

  // 设置回调函数
  void setNotificationCallback(NotificationCallback callback) {
    _notificationCallback = callback;
  }

  // 保存特征引用
  BluetoothCharacteristic? _writeCharacteristic;
  BluetoothCharacteristic? _notifyCharacteristic;
  BluetoothCharacteristic? _readCharacteristic;

  BluetoothDevice? _connectedDevice;
  final StreamController<BluetoothDevice?> _connectionController =
      StreamController.broadcast();

  void init() {
    _initializeBluetooth();
  }

  /// 初始化蓝牙管理器
  Future<void> _initializeBluetooth() async {
    try {
      FlutterBluePlus.isScanning.listen((isScanning) {
        debugPrint('Scanning: $isScanning===${FlutterBluePlus.lastScanResults.isEmpty}');
        if(!isScanning && FlutterBluePlus.lastScanResults.isEmpty){
          addConnectError(-1);
        }
      });
      // 监听蓝牙适配器状态
      FlutterBluePlus.adapterState.listen((state) async {
        debugPrint('Adapter state changed: $state');

        // 检查蓝牙状态并开始扫描
        if (adapterState == BluetoothAdapterState.on) {
          logInfo("蓝牙已开启");
          if (await BluetoothPermissionManager.checkBluetoothPermissions()) {
            // 开始扫描
            startScanAfterPermissionCheck();
          }
        } else if (adapterState == BluetoothAdapterState.off) {
          logError("蓝牙已关闭，请开启蓝牙");
          resetDeviceState();
        }
      });
    } catch (e) {
      logError("初始化蓝牙管理器出错: $e");
    }
  }

  //蓝牙断开/设备断开 释放资源,修改状态
  void resetDeviceState() async {
    if(_connectedDevice?.isConnected == true){
      await _connectedDevice?.disconnect();
    }
    _connectedDevice = null;
    _connectionController.add(null);
  }

  /// 在权限检查后开始扫描
  Future<void> startScanAfterPermissionCheck() async {
    // 先检查权限
    _hasPermissions =
        await BluetoothPermissionManager.checkBluetoothPermissions();

    // 如果没有权限，尝试请求权限
    if (!_hasPermissions) {
      _hasPermissions =
          await BluetoothPermissionManager.requestBluetoothPermissions();
    }

    if (!_hasPermissions) {
      logError("未获得蓝牙权限，无法使用蓝牙功能");
      return;
    }

    try {
      // 开始扫描
      await startScan();

      // 监听发现的设备
      scanResultsStream.listen((results) {
        debugPrint('扫描结果: $results');
        if (results.isEmpty) {
          logError("设备为空:未发现匹配的蓝牙设备");
          return;
        }

        for (var result in results) {
          // 解析制造商数据
          final parsedData = parseManufacturerData(result.advertisementData.msd[0]);
          // 检查是否为合作设备
          if (isCooperativeDevice(parsedData)) {
            print('蓝牙设备匹配：成功');
            stopScan();
            connectToDeviceByScanResult(result).then((_) {
              _connectedDevice = result.device;
              _connectedDevice?.connectionState.listen((state) {
                if (state == BluetoothConnectionState.disconnected) {
                  logInfo("设备已断开连接");
                  resetDeviceState();
                } else if (state == BluetoothConnectionState.connected) {
                  logInfo("设备已连接");
                  permissionRequest();
                }
              });
              _connectionController.add(result.device);
              logInfo("链接成功： ${result.device.platformName}");
              discoverServices(result.device);
            }).catchError((e) {
              addConnectError(1);
              logError("链接失败： ${result.device.platformName}");
              print(e);
            });
          }
        }

        logError("链接失败：未发现匹配的蓝牙设备");
      },onError: (e){
        logError("监听错误");
      });
    } catch (error) {
      logError("蓝牙扫描出错: $error");
      if(error.toString().contains("Bluetooth is not enabled")){
        showFailToast(S.of(Get.context!).weNeedToUseBluetoothToConnectToYourHeadsetForVoiceInteraction);
      } else if(error.toString().contains("Location services are required for Bluetooth scan")){
        showFailToast("请打开定位");
      }
    }
  }

  Future<void> permissionRequest() async {
    bool opened = await Permission.microphone.isGranted;
    if(!opened){
      PermissionStatus status = await Permission.microphone.request();
      if(!status.isGranted){
        showFailToast(S.of(Get.context!).weNeedAccessToYourMicrophoneForVoiceInteraction);
      }
    }
  }

  // 获取蓝牙适配器状态
  BluetoothAdapterState get adapterState => FlutterBluePlus.adapterStateNow;

  // 获取扫描状态
  bool get isScanning => FlutterBluePlus.isScanningNow;

  // 扫描结果流（包含广播数据）
  Stream<List<ScanResult>> get scanResultsStream => FlutterBluePlus.onScanResults;

  // 连接状态流
  Stream<BluetoothDevice?> get connectionStream => _connectionController.stream;

  // 当前连接的设备
  BluetoothDevice? get connectedDevice => _connectedDevice;

  // 开始扫描蓝牙设备
  Future<void> startScan(
      {Duration timeout = const Duration(seconds: 10)}) async {
    // 检查权限
    if (!_hasPermissions) {
      _hasPermissions =
          await BluetoothPermissionManager.checkBluetoothPermissions();
      if (!_hasPermissions) {
        // ToastUtil.error("未获得蓝牙权限");
        logError("未获得蓝牙权限");
        return;
      }
    }

    if (adapterState != BluetoothAdapterState.on) {
      // ToastUtil.error("蓝牙未开启");
      logError("蓝牙未开启");
      throw Exception('Bluetooth is not enabled');
    }

    if (FlutterBluePlus.isScanningNow) return;
    addConnectError(0);
    try {
      // 开始扫描
      await FlutterBluePlus.startScan(
        timeout: timeout,
        withMsd: [MsdFilter(0x22ee)], // 根据CID先扫描出CID为0x22ee的设备
      );
    } catch (e) {
      logError("蓝牙扫描出错: $e");
      rethrow;
    }
  }

  // 停止扫描
  Future<void> stopScan() async {
    if (!FlutterBluePlus.isScanningNow) return;

    await FlutterBluePlus.stopScan();
  }

  Map<String, int?> parseManufacturerData(List<int> data) {
    if (data.length < 15) {
      print("数据长度不足: ${data.length}");
      return {}; // 数据长度不足
    }

    print("原始数据: $data");
    // print("16进制: ${getNiceHexArray(data)}");
    // logInfo("16进制: ${getNiceHexArray(data)}");

    // 确保有足够的数据
    if (data.length < 6) {
      print("数据不足以包含CID和BID");
      return {};
    }

    // Byte0-1: CID (小端序)
    int cid = data[0] | (data[1] << 8);

    // Byte12-14: BID (小端序)
    int bid = data[12] | (data[13] << 8) | (data[14] << 16);

    // 打印十六进制值用于调试
    String cidHex = '0x${cid.toRadixString(16).padLeft(4, '0').toUpperCase()}';
    String bidHex = '0x${bid.toRadixString(16).padLeft(4, '0').toUpperCase()}';

    print("解析结果: CID=$cidHex, BID=$bidHex");

    return {
      'cid': cid,
      'bid': bid,
    };
  }

  bool isCooperativeDevice(Map<String, int?> parsedData) {
    // 合作设备的 CID 和 BID 标准
    const int cooperativeCID = 0x22EE;
    const int cooperativeBID = 0x010000;

    return parsedData['cid'] == cooperativeCID &&
        parsedData['bid'] == cooperativeBID;
  }

  Future<void> discoverServices(BluetoothDevice device) async {
    List<BluetoothService> services = await device.discoverServices();
    for (var service in services) {
      logInfo("Discovered Service: ${service.uuid}");
      // 查找特定的服务（例如：UUID 为 '0xFF01'）
      if (service.uuid.toString().toUpperCase() == 'FF01') {
        logInfo("Found Target Service: ${service.uuid}");
        // 处理该服务的特征
        handleServiceCharacteristics(service);
      }
    }
  }

  void handleServiceCharacteristics(BluetoothService service) {
    for (var characteristic in service.characteristics) {
      logInfo("Characteristic: ${characteristic.uuid}");

      // 根据特征的 UUID 进行不同的操作
      switch (characteristic.uuid.toString().toUpperCase()) {
        case 'FF16': // Write
          _writeCharacteristic = characteristic;
          break;
        case 'FF17': // Notify
          // setupNotifyCharacteristic(characteristic);
          _notifyCharacteristic = characteristic;
          break;
        case 'FF18': // Read
          _readCharacteristic = characteristic;
          _setupNotifyCharacteristic(characteristic);
          break;
        default:
          print(
              'Unknown Characteristic: ${characteristic.uuid.toString().toUpperCase()}');
      }
    }
  }

  void _setupNotifyCharacteristic(BluetoothCharacteristic characteristic) {
    // 开启通知
    characteristic.setNotifyValue(true).then((_) {
      print('Notify enabled');

      // 监听通知数据
      characteristic.lastValueStream.listen((value) {
        print('Received notification: ${getNiceHexArray(value)}');
        // ToastUtil.success("notify: ${getNiceHexArray(value)}");
        logInfo("notify: ${getNiceHexArray(value)}");
        if (value.length > 2) {
          var response = parseResponse(Uint8List.fromList(value));
          if (response['crcValid'] ?? false) {
            logInfo("crc校验成功");
            // 在收到通知后执行写入操作
            _writeDataAfterNotification(value);
            // 接口回调给页面
            _notificationCallback?.call({
              'type': 'double_click',
              // 'data': value,
            });
            return;
          }
        }
      });
    }).catchError((error) {
      logError('Notify error: $error');
    });
  }

  // 在收到通知后写入数据
  void _writeDataAfterNotification(List<int> receivedData) {
    if (_writeCharacteristic != null) {
      // 构造要写入的数据（根据实际协议调整）
      List<int> dataToWrite = buildCommand(1, Uint8List.fromList([1])); // 示例数据头

      _writeCharacteristic!.write(dataToWrite).then((_) {
        logInfo("响应写入成功: ${getNiceHexArray(dataToWrite)}");
      }).catchError((error) {
        logInfo("响应写入失败: $error");
      });
    }
  }

  // 连接设备（通过ScanResult）
  Future<void> connectToDeviceByScanResult(ScanResult scanResult) async {
    // 检查权限
    if (!_hasPermissions) {
      _hasPermissions =
          await BluetoothPermissionManager.checkBluetoothPermissions();
      if (!_hasPermissions) {
        // ToastUtil.error("未获得蓝牙权限");
        logError("未获得蓝牙权限");
        throw Exception('Bluetooth permissions not granted');
      }
    }

    try {
      // 连接设备
      await scanResult.device.connect().timeout(Duration(seconds: 10)).onError((err,stack){
        addConnectError(1);
      });
    } catch (e) {
      addConnectError(1);
      throw Exception('Bluetooth device connection error: $e');
    }
  }

  // 连接设备（兼容旧方法）
  Future<void> connectToDevice(BluetoothDevice device) async {
    // 检查权限
    if (!_hasPermissions) {
      _hasPermissions =
          await BluetoothPermissionManager.checkBluetoothPermissions();
      if (!_hasPermissions) {
        logError("未获得蓝牙权限");
        throw Exception('Bluetooth permissions not granted');
      }
    }
    try {
      // 连接设备
      await device.connect().timeout(Duration(seconds: 10)).onError((err,stack){
        addConnectError(1);
      });
    } catch (e) {
      addConnectError(1);
      throw Exception('Bluetooth device connection error: $e');
    }
  }
  addConnectError(int error){
    _connectionController.addError(error);
  }

  // 断开连接
  Future<void> disconnect() async {
    if (_connectedDevice != null) {
      await _connectedDevice!.disconnect();
      _connectedDevice = null;
      _connectionController.add(null);
      logInfo("蓝牙设备已断开连接");
    }
  }

  // 释放资源
  void dispose() {
    disconnect();
  }

  String getNiceHexArray(List<int> bytes) {
    return '[${bytes.map((i) => i.toRadixString(16).padLeft(2, '0')).join(', ')}]';
  }
}

typedef NotificationCallback = void Function(Map<String, dynamic> message);
