import 'dart:typed_data';

// 类型别名
typedef uint16_t = int;

uint16_t appCrc16(Uint8List buffer) {
  uint16_t crc = 0xFFFF;

  for (var i = 0; i < buffer.length; i++) {
    crc = ((crc << 8) | (crc >> 8)) & 0xFFFF;
    crc = (crc ^ buffer[i]) & 0xFFFF;
    crc ^= (crc & 0xFF) >> 4;
    crc = crc & 0xFFFF;
    crc ^= (crc << 12) & 0xFFFF;
    crc = crc & 0xFFFF;
    crc ^= ((crc & 0xFF) << 5) & 0xFFFF;
    crc = crc & 0xFFFF;
  }

  print("crc: $crc");
  return crc;
}

// 构建发送指令
Uint8List buildCommand(int cmd, Uint8List data) {
  // 创建缓冲区: FF(1) + cmd(1) + 数据长度(2) + 数据(N) + CRC(2)
  final buffer = BytesBuilder();

  // 添加固定头
  buffer.addByte(0xFF);

  // 添加命令
  buffer.addByte(cmd);

  // 添加数据长度 (小端序)
  // buffer.addByte(data.length & 0xFF);
  // buffer.addByte((data.length >> 8) & 0xFF);
  // 先写死数据长度
  buffer.addByte(0x00);
  buffer.addByte(0x01);

  // 添加数据
  buffer.add(data);

  // 计算CRC (从FF开始到数据结束)
  final crcData = buffer.toBytes();
  final crc = appCrc16(crcData);

  // 添加CRC (小端序)
  buffer.addByte(crc & 0xFF); // 低字节
  buffer.addByte((crc >> 8) & 0xFF); // 高字节

  return buffer.toBytes();
}

// 解析响应
Map<String, dynamic> parseResponse(Uint8List response) {
  if (response.length < 6) {
    throw Exception('响应长度不足');
  }

  // 验证起始字节
  if (response[0] != 0xFF) {
    throw Exception('无效的响应头');
  }

  // 提取命令
  final cmd = response[1];

  // 提取数据长度
  final dataLength = response[2] | (response[3] << 8);

  // 验证数据长度
  if (response.length != 6 + dataLength) {
    throw Exception('数据长度不匹配');
  }

  // 提取数据
  final data = response.sublist(4, 4 + dataLength);

  // 提取CRC
  final receivedCrc =
      response[response.length - 2] | (response[response.length - 1] << 8);

  // 计算CRC (不包括最后2字节)
  final crcData = response.sublist(0, response.length - 2);
  final calculatedCrc = appCrc16(crcData);

  // 提取状态 (最后一个数据字节)
  final status = data.isNotEmpty ? data.last : null;

  return {
    'command': cmd,
    'dataLength': dataLength,
    'data': data,
    'status': status,
    'crcValid': receivedCrc == calculatedCrc,
  };
}
