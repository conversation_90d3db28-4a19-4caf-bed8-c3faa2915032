import 'package:flutter/cupertino.dart';
import 'package:permission_handler/permission_handler.dart';

class BluetoothPermissionManager {
  /// 请求蓝牙相关权限
  static Future<bool> requestBluetoothPermissions() async {
    try {
      // 检查当前权限状态
      var bluetoothConnectStatus = await Permission.bluetoothConnect.status;
      var bluetoothScanStatus = await Permission.bluetoothScan.status;

      // 如果所有权限都已授予，直接返回 true
      if (bluetoothConnectStatus.isGranted &&
          bluetoothScanStatus.isGranted) {
        return true;
      }

      // 请求必要权限
      Map<Permission, PermissionStatus> statuses = await [
        Permission.bluetoothConnect,
        Permission.bluetoothScan,
      ].request();

      // 检查是否所有权限都被授予
      bool allGranted = statuses.values.every((status) => status.isGranted);

      if (allGranted) {
        debugPrint("蓝牙权限获取成功");
        return true;
      } else {
        // 检查是否有权限被永久拒绝
        bool isPermanentlyDenied = statuses.values
            .any((status) => status.isPermanentlyDenied);

        if (isPermanentlyDenied) {
          debugPrint("蓝牙权限被拒绝，请在设置中手动开启");
          // 可以引导用户到应用设置页面
          await openAppSettings();
        } else {
          debugPrint("蓝牙权限获取失败");
        }
        return false;
      }
    } catch (e) {
      debugPrint("请求蓝牙权限时出错: $e");
      return false;
    }
  }

  /// 检查蓝牙权限状态
  static Future<bool> checkBluetoothPermissions() async {
    var bluetoothConnectStatus = await Permission.bluetoothConnect.status;
    var bluetoothScanStatus = await Permission.bluetoothScan.status;

    return bluetoothConnectStatus.isGranted &&
           bluetoothScanStatus.isGranted;
  }

  /// 引导用户到应用设置页面
  static Future<void> openAppSettingsForPermissions() async {
    debugPrint("请在设置中开启蓝牙和位置权限");
    await openAppSettings();
  }
}
