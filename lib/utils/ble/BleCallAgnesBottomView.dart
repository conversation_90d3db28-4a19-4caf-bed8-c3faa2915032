import 'package:flutter/material.dart';
import 'package:new_agnes/widget/GradientBackgroundContainer.dart';

class CallAgnesBottomView extends StatefulWidget {
  const CallAgnesBottomView({super.key});

  @override
  State<StatefulWidget> createState() => _CallAgnesBottomViewState();
}

class _CallAgnesBottomViewState extends State<CallAgnesBottomView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 50,
      left: 0,
      right: 0,
      child: Align(
        alignment: Alignment.center,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32),
            boxShadow: [
              BoxShadow(
                color: Colors.black38,
                blurRadius: 10,
                offset: Offset(0, 2),
              ),
            ],
          ),
          width: 213,
          height: 54,
          child: GradientGlassBackgroundContainer(
            backgroundColor: Colors.transparent,
            borderRadius: BorderRadius.circular(32),
            blurRadius: 3,
            frostedOpacity: 0.05,
            frostedColor: Color(0xFF5E57FE),
            gradients: const [
              GradientConfig.linear(
                colors: [
                  Color(0x807253FA),
                  Color(0x80FF3BDF),
                  Color(0x805E57FE),
                ],
                rect: Rect.fromLTWH(0, -100, 213, 200),
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ],
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset("assets/images/agnes_logan.webp", width: 64, height: 34),
                const Padding(
                  padding: EdgeInsets.only(top: 10),
                  child: Text("Start Speaking",
                      style: TextStyle(
                          fontSize: 16, color: Color(0xFF00F0FF))),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
