import 'package:flutter/material.dart';
import 'package:new_agnes/widget/GradientBackgroundContainer.dart';

Widget buildLoginBg({Widget? child}) {
  return GradientGlassBackgroundContainer(
    backgroundColor: Color(0xFF020D26),
    blurRadius: 100,
    frostedOpacity: 0.01,
    frostedColor: Colors.transparent,
    gradients: [
      GradientConfig.radial(
        colors: [
          Color(0xFFD165E3),
          Color(0xFFB65CC5).withAlpha((255.0 * 0.9).round()),
          Color(0xFF634168).withAlpha((255.0 * 0.59).round()),
          Color(0x00634168),
        ],
        stops: [0, 0.22, 0.67, 1],
        rect: Rect.fromLTWH(0, -147, 393, 313),
      ),
      GradientConfig.radial(
        colors: [
          Color(0xFF2B3F8F),
          Color(0xFF0D1124),
          Color(0x000D1124),
        ],
        stops: [0, 0.9, 1],
        rect: Rect.fromLTWH(182, 641, 306, 306),
      ),
    ],
    child: child,
  );
}

Widget buildHomeBg({Widget? child}) {
  return GradientGlassBackgroundContainer(
    backgroundColor: Color(0xFF1A031E),
    blurRadius: 0,
    frostedOpacity: 0.01,
    frostedColor: Colors.transparent,
    gradients: [
      GradientConfig.radial(
        colors: [
          Color(0xFFD165E3),
          Color(0xE6B65CC5).withAlpha((255.0 * 0.9).round()),
          Color(0x96634168).withAlpha((255.0 * 0.59).round()),
          Color(0x00634168),
        ],
        stops: [0, 0.22, 0.67, 1],
        rect: Rect.fromLTWH(0, -220, 579, 475),
      ),
      GradientConfig.radial(
        colors: [
          Color(0xFFD165E3),
          Color(0xE6B65CC5).withAlpha((255.0 * 0.9).round()),
          Color(0x96634168).withAlpha((255.0 * 0.59).round()),
          Color(0x00634168),
        ],
        stops: [0, 0.22, 0.67, 0.84],
        rect: Rect.fromLTWH(72, 315, 387, 317),
      ),
      GradientConfig.radial(
        colors: [
          Color(0xFF35185D),
          Color(0xFF35185D).withAlpha((255.0 * 0.6).round()),
          Color(0x96634168).withAlpha((255.0 * 0.59).round()),
          Color(0x00634168),
        ],
        stops: [0, 0.22, 0.67, 0.84],
        rect: Rect.fromLTWH(-152, 554, 414, 401),
      ),
    ],
    child: child,
  );
}

Widget buildMineBg({Widget? child}) {
  return GradientGlassBackgroundContainer(
    backgroundColor: Color(0xFF1A031E),
    blurRadius: 0,
    frostedOpacity: 0.01,
    frostedColor: Colors.transparent,
    gradients: [
      GradientConfig.radial(
        colors: [
          Color(0xFFD165E3),
          Color(0xFFB65CC5).withAlpha((255.0 * 0.9).round()),
          // Color(0xFF634168).withAlpha((255.0 * 0.59).round()),
          Color(0xFFFF3BDF).withAlpha((255.0 * 0.25).round()),
          Color(0x00634168),
          // Color(0xFF1A031E),
        ],
        stops: [0, 0, 0, 1],
        rect: Rect.fromLTWH(-200, -102, 500, 852),
      ),

    ],
    child: child,
  );
}
