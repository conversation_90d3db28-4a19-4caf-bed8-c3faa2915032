import 'dart:async';

/// <AUTHOR>
/// @Date 2025/8/18 10:34
///
/// @Description 事件总线
import 'dart:async';
import 'package:flutter/foundation.dart';

/// 事件总线核心类
class EventBus {
  // 单例模式
  static final EventBus _instance = EventBus._internal();
  factory EventBus() => _instance;
  EventBus._internal();

  // 存储事件订阅者的映射
  final Map<Type, List<StreamController>> _controllers = {};

  // 获取事件流（供订阅者使用）
  Stream<T> on<T>() {
    // 为事件类型创建唯一的控制器
    _controllers[T] ??= [];

    // 如果没有对应的控制器，创建一个新的
    if (_controllers[T]!.isEmpty) {
      _controllers[T]!.add(StreamController<T>.broadcast());
    }

    return _controllers[T]!.first.stream as Stream<T>;
  }

  // 发布事件（供发布者使用）
  void fire<T>(T event) {
    // 如果没有订阅者，直接返回
    if (!_controllers.containsKey(T) || _controllers[T]!.isEmpty) {
      if (kDebugMode) {
        print("没有订阅者监听事件: ${T.toString()}");
      }
      return;
    }

    // 向所有订阅者发送事件
    try {
      (_controllers[T]!.first as StreamController<T>).add(event);
    } catch (e) {
      if (kDebugMode) {
        print("发送事件失败: $e");
      }
    }
  }

  // 销毁指定类型的事件控制器
  void destroy<T>() {
    if (_controllers.containsKey(T)) {
      for (var controller in _controllers[T]!) {
        controller.close();
      }
      _controllers.remove(T);
    }
  }

  // 销毁所有事件控制器
  void destroyAll() {
    _controllers.forEach((type, controllers) {
      for (var controller in controllers) {
        controller.close();
      }
    });
    _controllers.clear();
  }
}

// 全局事件总线实例
final EventBus eventBus = EventBus();
