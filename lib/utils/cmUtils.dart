import 'dart:collection';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vibration/vibration.dart';

import '../generated/l10n.dart';
import 'toastUtil.dart';

class CmUtils {
  static String androidAPI = '';
  static String token = '';
  static final RegExp emailRegex = RegExp(r'^[\w-]+(\.[\w-]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*(\.[a-zA-Z]{1,})$');
  static RxDouble bottomBarHeight = 0.0.obs;
  //获取设备信息
  static void getDeviceData() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      androidAPI = '${androidInfo.version.sdkInt}';
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    }
  }

  /**
   * 判断是否是android 13
   */
  static bool isAndroidAPI29() {
    if (Platform.isAndroid) {
      if (int.parse('${androidAPI}') > 32) {
        return true;
      } else {
        return false;
      }
    }
    return false;
  }

  static Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      int sdkInt = androidInfo.version.sdkInt;
      if (sdkInt >= 33) {
        final photosStatus = await Permission.photos.status;
        final videosStatus = await Permission.videos.status;
        final audioStatus = await Permission.audio.status;
        // Android 13+，需要请求媒体权限
        // 如果有任何一个权限未授权，则请求
        if (photosStatus.isDenied ||
            videosStatus.isDenied ||
            audioStatus.isDenied) {
          final statuses = await [
            Permission.photos,
            Permission.videos,
            Permission.audio,
          ].request();

          return statuses.values.any((status) => status.isDenied);
        }
        return false;
      } else {
        final storageStatus = await Permission.storage.status;
        if (storageStatus.isDenied) {
          final finalStatus = await [
            Permission.storage,
          ].request();

          return finalStatus.values.any((status) => status.isDenied);
        }
        return false;
      }
    }
    return true;
  }

  static bool isSuccess(int code) {
    if (code > 199 && code < 300) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * 全局加载进度条
   */
  static showLoading() {
    if (!EasyLoading.isShow) {
      EasyLoading.show(status: 'New_Agnes');
    }
  }

  /**
   * 上传进度显示
   */
  static double pro = 0;

  static showProgressLoading(double progress) {
    double p = double.parse((progress / 100).toStringAsFixed(2));
    if (pro != p) {
      pro = p;
      EasyLoading.showProgress(pro,
          status: '${(pro * 100).toStringAsFixed(2)}%',
          maskType: EasyLoadingMaskType.custom);
      print('进度=${pro}');
    }
  }

  /**
   * 获取bar高度
   */
  static double getBarHeight(context) {
    return MediaQuery.of(context).padding.top;
  }

  /**
   * 获取底部状态栏高度
   */
  static double getBottomBarHeight(context) {
    print('底部导航的高度=${MediaQuery.of(Get.context!).padding.bottom}');
    return MediaQuery.of(Get.context!).padding.bottom;
  }

  /**
   * 判断是否是android
   */
  static bool isAndroid() {
    if (Platform.isIOS) {
      return false;
    } else {
      return true;
    }
    ;
  }

  /**
   * 重复点击
   */
  static var lastPopTime;

  static twoTap(context, onTap, {isToast = false}) {
    if (lastPopTime == null ||
        DateTime.now().difference(lastPopTime) > Duration(milliseconds: 500)) {
      lastPopTime = DateTime.now();
      if (onTap != null) {
        onTap();
      }
      ;
    } else {
      lastPopTime =
          DateTime.now(); //如果不注释这行,则强制用户一定要间隔2s后才能成功点击. 而不是以上一次点击成功的时间开始计算.
      if (isToast) {}
    }
  }

  //复制内容
  static fuZhi(String data) {
    if (data != null && data != '') {
      Clipboard.setData(ClipboardData(text: data));
      showCopiedSuccessToast(S.of(Get.context!).copied);
    }
  }

  //振动
  static zhenDong({time = 40, amplitude = 50}) {
    Vibration.vibrate(duration: time, amplitude: amplitude);
  }

  /**
   * 判断该字段是否是弱密码
   */
  static bool isWeakPassword(String password) {
    if (password?.isNotEmpty != true) return true; //密码为空，弱密码
    if (password.length < 6) return true; //位数不足，弱密码
    if (password.length > 16) return true; //位数过多，弱密码
    Set set = HashSet();
    for (var code in password.codeUnits) {
      if (code >= 48 && code <= 57)
        set.add('数字');
      // else if (code >= 65 && code <= 90)
      //   set.add('大写');
      else if (code >= 65 && code <= 122)
        set.add('小写');
      else
        set.add('特殊');
    }
    return set.length < 3; //种类小于3种，弱密码
  }

  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return emailRegex.hasMatch(email);
  }

  /**
   * 英文首字母大写
   */
  static String firstLetterBig(content) {
    String text = content;
    String capitalizedText = text
        .split(' ')
        .map((word) => word.replaceFirst(word[0], word[0].toUpperCase()))
        .join(' ');
    return capitalizedText;
  }

  /// 外部浏览器加载url
  static void launchExternalBrowser(String url) async {
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
    } else {
      throw Exception('无法打开链接: $url');
    }
  }


  /**
   * 根据 GlobalKey 获取容器高度
   */
  static double getHeightByKey(GlobalKey key) {
    final RenderBox? renderBox = key.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      return renderBox.size.height;
    }
    return 0.0;
  }
}
