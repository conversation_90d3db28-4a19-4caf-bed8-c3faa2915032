class ChatWasteFilterUtils {

  // 所有需要过滤的目标字符串
  static final List<String> _filterTargets = [
    "hando",
    "handoff",
    "ff_to_planner()",
    "ff_to_chat()",
    "ff_to_chat",
    "ff_to_ai",
    "ff_to",
    "ff_to_ai_slides()",
    "_slides()",
    "_slides",
    "ff_to_deep_research()",
    "ff_to_research()",
    "ff_to_planner",
    "ff_to_wide_research()",
    "handoff_to_planner()",
    "handoff_to_ai",
    "handoff_to_ai_slides()",
    "handoff_to_ai_sheet()",
    "handoff_to_deep_research()",
    "handoff_to_chat()",
    "handoff_to_research()",
    "hand_to_wide_research()",
    "handoff_to_chat",
    "handoff_to_pl",
    "hand_off_research",
    "hand_off_research()",
    "handoff_to_research",
    "handoff_to_wide_research()",
    "handoff_to_wide_research",
    "handoff_to_wide",
    "handoff_to_ai_design()",
    "handoff_to_ai_design",
    "ff_to_ai_design()",
    "_design()",
    "_design",
    "ff_to_ai_design",
    "ff_to_wide",
    "_research",
    "_research()",
    "()"
  ];

  // 静态方法：简单的单次检查（向后兼容）
  static bool isFilter(String content) {
    return _filterTargets.any((target) => content.contains(target));
  }

}
