import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

/// 简单的日志工具类
/// 提供统一的日志记录功能，仅支持控制台输出
/// 只在debug模式下打印日志
class LoggerUtils {
  static Logger? _logger;
  static bool _isInitialized = false;

  /// 初始化日志系统
  static void init({Level logLevel = Level.debug}) {
    if (!kDebugMode) return;
    if (_isInitialized) return;

    _logger = Logger(
      filter: ProductionFilter(),
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
    );

    _isInitialized = true;
    _logger?.i('Logger initialized successfully');
  }

  /// 记录调试日志
  static void d(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!kDebugMode) return;
    _ensureInitialized();
    _logger?.d(message, error: error, stackTrace: stackTrace);
  }

  /// 记录信息日志
  static void i(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!kDebugMode) return;
    _ensureInitialized();
    _logger?.i(message, error: error, stackTrace: stackTrace);
  }

  /// 记录警告日志
  static void w(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!kDebugMode) return;
    _ensureInitialized();
    _logger?.w(message, error: error, stackTrace: stackTrace);
  }

  /// 记录错误日志
  static void e(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!kDebugMode) return;
    _ensureInitialized();
    _logger?.e(message, error: error, stackTrace: stackTrace);
  }

  /// 记录致命错误日志
  static void f(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!kDebugMode) return;
    _ensureInitialized();
    _logger?.f(message, error: error, stackTrace: stackTrace);
  }

  /// 确保日志系统已初始化
  static void _ensureInitialized() {
    if (!kDebugMode) return;
    if (!_isInitialized) {
      init();
    }
  }
}

/// 便捷的日志记录方法
class Log {
  /// 调试日志
  static void d(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    LoggerUtils.d(message, error, stackTrace);
  }

  /// 信息日志
  static void i(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    LoggerUtils.i(message, error, stackTrace);
  }

  /// 警告日志
  static void w(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    LoggerUtils.w(message, error, stackTrace);
  }

  /// 错误日志
  static void e(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    LoggerUtils.e(message, error, stackTrace);
  }

  /// 致命错误日志
  static void f(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    LoggerUtils.f(message, error, stackTrace);
  }
}