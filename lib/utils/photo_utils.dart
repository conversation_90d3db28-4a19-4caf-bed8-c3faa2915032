import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';

// import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:permission_handler/permission_handler.dart';

import '../dialog/photo_dialog.dart';
import '../module/mine/permission/permission_page.dart';

class PhotoUtils {
  /**
   * 照片相册 选择
   * height 布局高度 2（230.0）   3（285.0）
   * length 1 拍照 2 相册 3 视频
   * callBack  上传图片后的 路劲返回
   */
  static void goToPhotoDialog(context, height, length, Function callBack,
      {bool isCrop = false, //是否裁剪
      bool isMulti = false, //是否多选
      int maxCount = 9,
      String title = '选择图片'}) {
    showDialog(
        context: context,
        builder: (context) {
          return PhotoDialog(title, height, length, (index) async {
            if (index == 0) {
              //拍照
              paiZhao(isCrop, context, callBack);
            }
            if (index == 1) {
              //相册
              xiangCe(isMulti, isCrop, context, maxCount,callBack);
            }
            if (index == 2) {
              //视频
              shiPin(callBack);
            }
          });
        });
  }

  //拍照
  static Future<void> paiZhao(isCrop, context, callBack) async {
    PermissionStatus status = await Permission.camera.status;
    if (status.isGranted == true) {
      //拍照
      List<File> fileList = [];
      await ImagePicker()
          .pickImage!(source: ImageSource.camera, imageQuality: 100)
          .then((value) {
        File tempFile = File(value!.path);
        if (isCrop) {
          getCropImage(tempFile, (cropedImg) {
            fileList.add(File(cropedImg));
            callBack(fileList);
          }, context);
        } else {
          fileList.add(tempFile);
          callBack(fileList);
        }

      });
    } else {
      await [Permission.camera].request();
      // Get.to(PermissionPage(
      //     '上传图片，需要申请拍照权限，使用户上传图片，裁剪图片，保存图片', [Permission.camera]));
    }
  }

  //相册
  static Future<void> xiangCe(isMulti, isCrop, context, maxCount, callBack) async {
    PermissionStatus status;
    if (CmUtils.isAndroidAPI29()) {
      status = await Permission.photos.status;
    } else {
      status = await Permission.storage.status;
    }
    if (status.isGranted == true || !CmUtils.isAndroidAPI29() || status.isLimited) {
      if (isMulti) {
        List<File> fileList = [];
        //多选图片
        await ImagePicker().pickMultiImage(limit: maxCount).then((List<XFile> value) {
          value.forEach((element) {
            var tempFile = File(element!.path);
            fileList.add(tempFile);
          });
          callBack(fileList);
        });
      } else {
        //单选图片
        await ImagePicker()
            .pickImage(source: ImageSource.gallery, imageQuality: 100)
            .then((value) {
          List<File> fileList = [];
          var tempFile = File(value!.path);
          if (isCrop) {
            getCropImage(tempFile, (cropedImg) {
              fileList.add(File(cropedImg));
              callBack(fileList);
            }, context);
          } else {
            fileList.add(tempFile);
            callBack(fileList);
          }

        });
      }
    } else {
      await [CmUtils.isAndroidAPI29() ? Permission.photos : Permission.storage].request();
      // Get.to(PermissionPage('上传图片，需要申请相册权限，使用户上传图片，裁剪图片，保存图片',
      //     [CmUtils.isAndroidAPI29() ? Permission.photos : Permission.storage]));
    }
  }

  //视频
  static Future<void> shiPin(callBack) async {
    PermissionStatus status;
    if (CmUtils.isAndroidAPI29()) {
      status = await Permission.videos.status;
    } else {
      status = await Permission.storage.status;
    }
    if (status.isGranted == true || !CmUtils.isAndroidAPI29() || status.isLimited) {
      //视频
      await ImagePicker()
          .pickVideo(
        source: ImageSource.gallery,
      )
          .then((value) {
        List<File> fileList = [];
        var tempFile = File(value!.path);
        fileList.add(tempFile);
        callBack(fileList);
      });
    } else {
      await [CmUtils.isAndroidAPI29() ? Permission.videos : Permission.storage].request();
      // Get.to(PermissionPage('上传视频，需要申请相册权限，使用户上传图片，裁剪图片，保存图片',
      //     [CmUtils.isAndroidAPI29() ? Permission.videos : Permission.storage]));
    }
  }

  static Future getCropImage(
      File file, Function onResult, BuildContext context) async {
    // 这里调用第二个剪切组件
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: file.path,
      maxWidth: 1000, //裁剪后的最大图像宽度。
      maxHeight: 1000, //裁剪后的最大图像高度。
      compressQuality: 85, //介于 0 和 100 之间的数字，用于控制图像压缩的质量。注意：此字段在 Web 上被忽略。
      // compressFormat: ImageCompressFormat.jpg,//结果图像的格式，png 或 jpg（默认为 ImageCompressFormat.jpg）。
      // aspectRatio: ,//控制裁剪边界的纵横比。如果设置了此值，则裁剪器将被锁定，并且用户无法更改裁剪边界的纵横比。
    );
    onResult(croppedFile!.path);
  }
}
