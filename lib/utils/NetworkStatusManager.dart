/// <AUTHOR>
/// @Date 2025/9/22 17:03
///
/// @Description 监听网络
import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';

import '../utils/event_bus.dart';

class NetworkStatusManager {
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  final _networkStatusStreamController = StreamController<List<ConnectivityResult>>.broadcast();

  Stream<List<ConnectivityResult>> get onNetworkStatusChanged => _networkStatusStreamController.stream;

  Future<bool> get isConnected async {
    final connectivityResult = await _connectivity.checkConnectivity();
    int len = connectivityResult.where((t) => t == ConnectivityResult.mobile || t == ConnectivityResult.wifi).toList().length;
    return len > 0;
  }

  void init() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((result) {
      _networkStatusStreamController.add(result);
      eventBus.fire(NetworkStatusEvent(result));
    });
  }

  void dispose() {
    _connectivitySubscription?.cancel();
    _networkStatusStreamController.close();
  }
}

// 定义网络状态事件
class NetworkStatusEvent {
  final List<ConnectivityResult> status;

  NetworkStatusEvent(this.status);
}
