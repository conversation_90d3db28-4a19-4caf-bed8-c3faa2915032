import 'dart:io';

import 'package:android_path_provider/android_path_provider.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:new_agnes/utils/toastUtil.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../api/Api.dart';
import '../api/ApiProvider.dart';
import '../generated/l10n.dart';
import '../module/mine/permission/permission_page.dart';
import 'cmUtils.dart';

class FileUtils {
  final Dio _dio = Dio();

  // 下载文件并返回下载的文件路径
  Future<String?> downloadFile(
      {required String url,
      required String fileName,
      Function(double progress)? onProgress,
      Function(String path)? onSavePath,
      query}) async {
    try {
      String savePath = '';
      if (CmUtils.isAndroid()) {
        savePath = await getPublicDownloadPath();
        savePath = '${savePath}/$fileName';
      }
      print('保存的地址=${savePath}');
      // 发起下载请求
      var response = await _dio.download(
        url.contains('http') ? url : '${Api.baseUrl}${url}',
        savePath,
        data: url.contains('http') ? null : query,
        options: url.contains('http')
            ? null
            : Options(
                method: 'POST',
                contentType: Headers.jsonContentType,
                headers: {'Authorization': '${ApiProvider.token}'},
                responseType: ResponseType.bytes,
                receiveTimeout: const Duration(minutes: 10), // 下载超时时间
              ),
        onReceiveProgress: (received, total) {
          if (total != -1 && onProgress != null) {
            double pro = received / total;
            onProgress(pro);
            if (pro >= 1.0) {
              onSavePath!(savePath);
            }
          }
        },
      );
      return savePath;
    } catch (e) {
      print('下载失败: $e');
      // showFailToast(S.of(Get.context!).systemBusy);
      return null;
    }
  }

  // 取消下载
  void cancelDownload() {
    _dio.close(force: true); // 强制关闭所有请求
  }

  // 获取系统下载目录（公共目录）
  Future<String> getPublicDownloadPath() async {
    if (Platform.isAndroid) {
      try {
        // 方法1：使用 downloads_path_provider
        final path = await AndroidPathProvider.downloadsPath;
        return path;
      } catch (e) {
        print('获取下载目录失败: $e');
        return '';
      }
    } else if (Platform.isIOS) {
      // iOS没有真正的公共下载目录
      final dir = await getApplicationDocumentsDirectory();
      return '${dir.path}/Downloads';
    }
    return '';
  }
}
