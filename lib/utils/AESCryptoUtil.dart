import 'dart:convert';
import 'dart:typed_data';
import 'dart:math';

import 'package:encrypt/encrypt.dart';

class AESCryptoUtil {
  static String _key = '';
  static const int KEY_LENGTH = 32; // 256 bits
  static const int IV_LENGTH = 16; // 128 bits

  /// 解密AES-256/CBC加密数据（IV与数据分离）
  ///
  /// 参数:
  /// - encryptedData: 加密数据(base64编码)
  ///
  /// 返回:
  /// - 解密后的明文字符串
  static String decryptAES256CBCSeparate(String encryptedData) {
    try {
      // 解码各部分
      var keyBytes = base64Decode(_key);
      // var keyBytes = base64Decode("41Yo6yKUJifm+C+2NvRdZ2qRO+3UdMq6GiTWoz9sku8=");
      // encryptedData = "tKWExvUa7Os2XbnsQDJPBpoSM57tDrc0aCmTKl+z0CkJe/XitT+jWcNAd65LAuZ8GXzogL5pZ2uIf8ILSVWcnN/jKaYWOykx0cPcnMtJdVALLj2C2pe+GT2eqimZ3ngAciWyBVWd9jlP//zFk34Hs/IwMp0yu/c0ugiya7h2X5lvL0V1E12Jc0xIr3O4yq4x6CmXbtSgwabaKicXdLcDQ++4lHA2fNOEibw/wMmeWxcOxgW+0qh5NSDhd/zaqeZIp4b0xaK/28yG7zhF1gxzPn+zE8qsWnlz8BeyjkzPuQ26tMwUR6YzIBraywVC4XeJ";

      // {
      //   "AZURE_SPEECH_SERVICE_KEY": "5qjNPJgQ5cbzKifG24L4P4UTgWAafeRu42k2jddyehzHwrJcW4bnJQQJ99BHAC3pKaRXJ3w3AAAYACOGbZP2",
      //   "AZURE_SPEECH_SERVICE_REGION": "eastasia",
      //   "AGORA_APP_ID": "3d4824793bcd48fe904a019b30e54c3f"
      // }

      var encryptedDataBytes = base64Decode(encryptedData);
      var ivBytes = encryptedDataBytes.sublist(0, IV_LENGTH);
      var dataBytes = encryptedDataBytes.sublist(IV_LENGTH);

      // 创建加密对象
      var encrypter = Encrypter(AES(Key(keyBytes), mode: AESMode.cbc));

      // 解密数据
      var decrypted = encrypter.decrypt(Encrypted(dataBytes), iv: IV(ivBytes));
      return decrypted;
    } catch (e) {
      throw Exception('解密失败: $e');
    }
  }

  /// AES-256/CBC/PKCS7 加密
  static Encrypted encryptAES256CBCPKCS7(
      String plainText, String key, String iv) {
    final keyBytes = Uint8List.fromList(
        utf8.encode(key.padRight(KEY_LENGTH, '\x00')).sublist(0, KEY_LENGTH));
    final ivBytes = Uint8List.fromList(
        utf8.encode(iv.padRight(IV_LENGTH, '\x00')).sublist(0, IV_LENGTH));

    final encrypter = Encrypter(AES(Key(keyBytes), mode: AESMode.cbc));
    final ivObj = IV(ivBytes);

    // 使用PKCS7填充
    final paddedText = _pkcs7Pad(plainText, 16);

    return encrypter.encrypt(paddedText, iv: ivObj);
  }

  /// AES-256/CBC/PKCS7 解密
  static String decryptAES256CBCPKCS7(
      Encrypted encrypted, String key, String iv) {
    final keyBytes = Uint8List.fromList(
        utf8.encode(key.padRight(KEY_LENGTH, '\x00')).sublist(0, KEY_LENGTH));
    final ivBytes = Uint8List.fromList(
        utf8.encode(iv.padRight(IV_LENGTH, '\x00')).sublist(0, IV_LENGTH));

    final encrypter = Encrypter(AES(Key(keyBytes), mode: AESMode.cbc));
    final ivObj = IV(ivBytes);

    final decrypted = encrypter.decrypt(encrypted, iv: ivObj);
    return _pkcs7Unpad(decrypted);
  }

  /// PKCS7填充
  static String _pkcs7Pad(String text, int blockSize) {
    final bytes = utf8.encode(text);
    final padLength = blockSize - (bytes.length % blockSize);
    final padBytes = List<int>.filled(padLength, padLength);
    final paddedBytes = List<int>.from(bytes)..addAll(padBytes);
    return utf8.decode(paddedBytes);
  }

  /// PKCS7去填充
  static String _pkcs7Unpad(String text) {
    final bytes = utf8.encode(text);
    if (bytes.isEmpty) return text;

    final padLength = bytes.last;
    if (padLength > bytes.length) return text;

    // 验证填充是否正确
    for (int i = bytes.length - padLength; i < bytes.length; i++) {
      if (bytes[i] != padLength) {
        return text; // 填充不正确，返回原字符串
      }
    }

    final unpadBytes = bytes.sublist(0, bytes.length - padLength);
    return utf8.decode(unpadBytes);
  }

  static String getCachedRandomKey() {
    if (_key.isEmpty) {
      _key = generateRandomKey();
    }
    return _key;
  }

  /// 生成随机密钥
  static String generateRandomKey() {
    final random = Random.secure();
    final keyBytes = Uint8List(KEY_LENGTH);
    for (int i = 0; i < KEY_LENGTH; i++) {
      keyBytes[i] = random.nextInt(256);
    }
    return base64Encode(keyBytes);
  }

  /// 生成随机IV
  static String generateRandomIV() {
    final random = Random.secure();
    final ivBytes = Uint8List(IV_LENGTH);
    for (int i = 0; i < IV_LENGTH; i++) {
      ivBytes[i] = random.nextInt(256);
    }
    return base64Encode(ivBytes);
  }
}
