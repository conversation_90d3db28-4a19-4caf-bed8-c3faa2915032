import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';

import '../api/Api.dart';
import '../api/ApiProvider.dart';
import '../api/StorageService.dart';

class FirebaseApi {
  static final FirebaseApi _instance = FirebaseApi._internal();
  factory FirebaseApi() => _instance;
  FirebaseApi._internal() {
    // 私有构造函数
  }

  final _firebaseMessaging = FirebaseMessaging.instance;
  final _localNotifications = FlutterLocalNotificationsPlugin();
  final _androidChannel = const AndroidNotificationChannel(
      'Agnes', 'Agnes Android',  // 这些信息根据自己的app定义
      description: "Agnes",
      importance: Importance.defaultImportance);
  String? fcmToken;

  // 本地消息，处理android的前台消息
  Future initLocalNotifications() async {
    // @drawable/ic_launcher是应用的图标，路径是：android/app/src/main/res/drawable/ic_launcher.png
    const android = AndroidInitializationSettings('@mipmap/ic_launcher');
    const settings = InitializationSettings(android: android);
    await _localNotifications.initialize(settings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          // android 前台消息点击
          final message = RemoteMessage.fromMap(jsonDecode(response.payload!));
          // 处理收到消息
          handleMessage(message);
        });
    final platform = _localNotifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();
    await platform?.createNotificationChannel(_androidChannel);
  }

  // 初始化，获取设备token
  Future<void> initNotifications() async {
    await _firebaseMessaging.requestPermission();
    fcmToken = await _firebaseMessaging.getToken();
    initToServerFCMToken();
    initPushNotifications();
    initLocalNotifications();
  }

  void initToServerFCMToken(){
    if(fcmToken == null){
      return;
    }
    if(fcmToken?.length == 0){
      return;
    }
    bool isRegistered = Get.isRegistered<StorageService>();
    if(!isRegistered){
      return;
    }
    String? accessToken = Get.find<StorageService>().getLoginData().accessToken;
    if(accessToken == null){
      return;
    }
    if(accessToken.length == 0){
      return;
    }
    ApiProvider apiProvider;
    if(Get.isRegistered<ApiProvider>()){
      apiProvider = Get.find<ApiProvider>();
    }else{
      apiProvider = Get.put(ApiProvider());
    }
    apiProvider.get(Api.uploadFCMToken,query: {"firebase_token":fcmToken});
  }

  // 初始化接收消息的各种回调
  Future initPushNotifications() async {
    await _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true, badge: true, sound: true);

    // 打开app时，会执行该回调，获取消息（通常是程序终止时，点击消息打开app的回调）
    _firebaseMessaging.getInitialMessage().then(
          (RemoteMessage? message) {
        if (message == null) return; // 没有消息不执行后操作
        handleMessage(message);
      },
    );

    // 后台程序运行时，点击消息触发
    FirebaseMessaging.onMessageOpenedApp
        .listen((RemoteMessage message) => handleMessage(message));

    // 前台消息，android不会通知，所以需要自定义本地通知（iOS没有前台消息，iOS的前台消息和后台运行时一样的效果）
    FirebaseMessaging.onMessage.listen((message) {
      final notification = message.notification;
      if (notification == null) return;
      if (Platform.isIOS) return;
      _localNotifications.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
              android: AndroidNotificationDetails(
                _androidChannel.id,
                _androidChannel.name,
                channelDescription: _androidChannel.description,
                icon: '@mipmap/ic_launcher',
              )),
          payload: jsonEncode(message.toMap()));
    });

    // 后台处理，后台程序运行时收到消息，不打开app也会执行的回调
    // FirebaseMessaging.onBackgroundMessage(FirebaseApi.handleBackgroundMessage);
  }

  // 处理收到的消息，比如跳转页面之类（这里需要无context跳转，可以参考我写的flutter路由配置篇章介绍）
  void handleMessage(RemoteMessage message) {

  }

// static Future<void> handleBackgroundMessage(RemoteMessage message) async {
//   print('后台消息');
//   // BotToast.showText(text: '后台消息:${message.toString()}');
//   print('title:${message.notification?.title}');
//   print('body:${message.notification?.body}');
//   print('payload:${message.data}');
//   print('message:${message}');
// }
}
