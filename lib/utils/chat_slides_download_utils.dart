import 'dart:io';
import 'package:android_path_provider/android_path_provider.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_agnes/module/chat/page/chat_download_progress_page.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../module/mine/permission/permission_page.dart';
import 'cmUtils.dart';

/// 下载进度枚举
enum DownloadStage {
  /// 准备阶段
  preparing,
  /// 下载文件阶段
  downloading,
  /// 完成阶段
  completed,
  /// 失败阶段
  failed,
  /// 取消阶段
  cancelled,
}

/// 下载进度信息
class DownloadProgress {
  /// 当前阶段
  final DownloadStage stage;
  /// 整体进度 (0.0 - 1.0)
  final double overallProgress;
  /// 当前阶段进度 (0.0 - 1.0)
  final double stageProgress;
  /// 进度描述
  final String message;
  /// 已接收字节数（仅在下载阶段有效）
  final int? received;
  /// 总字节数（仅在下载阶段有效）
  final int? total;

  DownloadProgress({
    required this.stage,
    required this.overallProgress,
    required this.stageProgress,
    required this.message,
    this.received,
    this.total,
  });
}



/// 聊天文件下载工具类 - 高性能版本
/// 
/// 🚀 支持功能：
/// - 直接从URL下载文件
/// - 智能分片并发下载（大文件自动启用，显著提升下载速度）
/// - 实时下载进度和速度显示
/// - 下载任务取消功能
/// - 权限检查和处理
/// - 智能错误处理和重试
/// - 下载性能诊断工具
/// 
/// 🔧 性能优化特性：
/// - 自动检测服务器Range支持
/// - 根据文件大小智能选择分片数量（30M文件使用4个分片）
/// - 连接复用和压缩传输
/// - 优化的Dio配置和超时设置
/// 
/// 📊 使用诊断工具：
/// ```dart
/// // 诊断下载慢的原因
/// final diagnosis = await ChatDownloadFileUtils.diagnosisDownloadPerformance(fileUrl);
/// print('预估30MB下载时间: ${diagnosis['estimated_30mb_time_seconds']}秒');
/// ```
/// 
/// 📥 基本使用：
/// ```dart
/// await ChatDownloadFileUtils.downloadFileWithCallbacks(
///   fileUrl: 'https://example.com/largefile.pdf',
///   fileName: 'document.pdf',
///   onProgress: (progress) => print('${progress.message}'),
///   onSuccess: (filePath) => print('Downloaded: $filePath'),
///   onError: (error) => print('Error: $error'),
/// );
/// ```
class ChatDownloadFileUtils {
  static final Dio _dio = Dio()
    ..options.connectTimeout = Duration(seconds: 30)
    ..options.receiveTimeout = Duration(seconds: 60)
    ..options.sendTimeout = Duration(seconds: 30)
    ..options.headers = {
      'User-Agent': 'Agnes-Flutter-App/1.0',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
    };
  
  // 存储当前活跃的取消令牌
  static final Map<String, CancelToken> _activeCancelTokens = {};
  
  // 为大文件下载优化的Dio实例
  static final Dio _largeDio = Dio()
    ..options.connectTimeout = Duration(seconds: 30)
    ..options.receiveTimeout = Duration(minutes: 10) // 大文件需要更长接收时间
    ..options.sendTimeout = Duration(seconds: 30)
    ..options.headers = {
      'User-Agent': 'Agnes-Flutter-App/1.0',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
    }
    ..interceptors.add(LogInterceptor(requestBody: false, responseBody: false)); // 添加日志但不打印body
  
  /// 取消指定的下载任务
  /// [downloadId] 下载任务的唯一标识符
  static void cancelDownload(String downloadId) {
    final cancelToken = _activeCancelTokens[downloadId];
    if (cancelToken != null && !cancelToken.isCancelled) {
      cancelToken.cancel('Download cancelled by user');
      _activeCancelTokens.remove(downloadId);
    }
  }
  
  /// 取消所有活跃的下载任务
  static void cancelAllDownloads() {
    for (final entry in _activeCancelTokens.entries) {
      if (!entry.value.isCancelled) {
        entry.value.cancel('All downloads cancelled by user');
      }
    }
    _activeCancelTokens.clear();
  }
  
  /// 检查下载是否已被取消
  static bool isDownloadCancelled(String downloadId) {
    final cancelToken = _activeCancelTokens[downloadId];
    return cancelToken?.isCancelled ?? false;
  }
  
  /// 获取文件大小（用于优化下载策略）
  static Future<int?> _getFileSize(String fileUrl, CancelToken? cancelToken) async {
    try {
      final response = await _largeDio.head(
        fileUrl,
        options: Options(
          headers: {'Range': 'bytes=0-0'}, // 只请求第一个字节来获取文件大小
        ),
        cancelToken: cancelToken,
      );
      
      final contentLength = response.headers.value('content-length');
      final contentRange = response.headers.value('content-range');
      
      if (contentRange != null) {
        // 从 content-range 头中解析文件大小 (格式: bytes 0-0/total)
        final match = RegExp(r'bytes \d+-\d+/(\d+)').firstMatch(contentRange);
        if (match != null) {
          return int.tryParse(match.group(1)!);
        }
      }
      
      if (contentLength != null) {
        return int.tryParse(contentLength);
      }
      
      return null;
    } catch (e) {
      print('Failed to get file size: $e');
      return null;
    }
  }
  
  /// 判断是否应该重试下载
  static bool _shouldRetry(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return true;
      case DioExceptionType.badResponse:
        // 5xx服务器错误可以重试
        final statusCode = e.response?.statusCode;
        return statusCode != null && statusCode >= 500;
      default:
        return false;
    }
  }
  
  /// 检查服务器是否支持Range请求（分片下载）
  static Future<bool> _supportsRangeRequests(String fileUrl, CancelToken? cancelToken) async {
    try {
      final response = await _largeDio.head(
        fileUrl,
        options: Options(
          headers: {'Range': 'bytes=0-0'},
        ),
        cancelToken: cancelToken,
      );
      
      return response.statusCode == 206 || 
             response.headers.value('accept-ranges')?.toLowerCase() == 'bytes';
    } catch (e) {
      print('Failed to check range support: $e');
      return false;
    }
  }
  
  /// 根据文件大小智能选择分片数量
  static int _getOptimalChunkCount(int fileSize) {
    // 根据文件大小动态调整分片数量
    if (fileSize < 10 * 1024 * 1024) { // < 10MB
      return 2;
    } else if (fileSize < 50 * 1024 * 1024) { // < 50MB
      return 4;
    } else if (fileSize < 100 * 1024 * 1024) { // < 100MB
      return 6;
    } else { // >= 100MB
      return 8;
    }
  }

  /// 分片并发下载大文件
  static Future<String?> _downloadFileInChunks({
    required String fileUrl,
    required String savePath,
    required int fileSize,
    required Function(int received, int total) onProgress,
    required CancelToken cancelToken,
    int? chunkCount, // 如果不指定，会自动选择最优数量
  }) async {
    final actualChunkCount = chunkCount ?? _getOptimalChunkCount(fileSize);
    final chunkSize = (fileSize / actualChunkCount).ceil();
    final chunks = <Future<List<int>>>[];
    final chunkProgresses = List<int>.filled(actualChunkCount, 0);
    
    print('Starting chunked download: ${actualChunkCount} chunks, ${chunkSize} bytes each');
    
    // 创建分片下载任务
    for (int i = 0; i < actualChunkCount; i++) {
      final start = i * chunkSize;
      final end = (i == actualChunkCount - 1) ? fileSize - 1 : start + chunkSize - 1;
      
      chunks.add(_downloadChunk(
        fileUrl: fileUrl,
        start: start,
        end: end,
        chunkIndex: i,
        cancelToken: cancelToken,
        onProgress: (received) {
          chunkProgresses[i] = received;
          final totalReceived = chunkProgresses.reduce((a, b) => a + b);
          onProgress(totalReceived, fileSize);
        },
      ));
    }
    
    try {
      // 等待所有分片下载完成
      final chunkData = await Future.wait(chunks);
      
      // 检查是否被取消
      if (cancelToken.isCancelled) {
        return null;
      }
      
      // 合并分片数据并写入文件
      final file = File(savePath);
      final sink = file.openWrite();
      
      for (final chunk in chunkData) {
        sink.add(chunk);
      }
      
      await sink.close();
      print('Chunked download completed: $savePath');
      return savePath;
      
    } catch (e) {
      print('Chunked download failed: $e');
      rethrow;
    }
  }
  
  /// 下载单个分片
  static Future<List<int>> _downloadChunk({
    required String fileUrl,
    required int start,
    required int end,
    required int chunkIndex,
    required CancelToken cancelToken,
    required Function(int received) onProgress,
  }) async {
    final startTime = DateTime.now();
    
    final response = await _largeDio.get<List<int>>(
      fileUrl,
      options: Options(
        headers: {
          'Range': 'bytes=$start-$end',
        },
        responseType: ResponseType.bytes,
      ),
      cancelToken: cancelToken,
      onReceiveProgress: (received, total) {
        onProgress(received);
      },
    );
    
    if (response.data == null) {
      throw Exception('Chunk $chunkIndex download failed: no data');
    }
    
    final duration = DateTime.now().difference(startTime);
    final speedMBps = (response.data!.length / (1024 * 1024)) / duration.inSeconds;
    print('Chunk $chunkIndex downloaded: ${response.data!.length} bytes in ${duration.inSeconds}s (${speedMBps.toStringAsFixed(2)}MB/s)');
    
    return response.data!;
  }
  
  /// 下载诊断工具 - 测试网络速度和延迟
  static Future<Map<String, dynamic>> diagnosisDownloadPerformance(String fileUrl) async {
    final results = <String, dynamic>{};
    final cancelToken = CancelToken();
    
    try {
      // 测试1: 连接时间
      final connectStart = DateTime.now();
      final headResponse = await _largeDio.head(fileUrl, cancelToken: cancelToken);
      final connectTime = DateTime.now().difference(connectStart).inMilliseconds;
      results['connect_time_ms'] = connectTime;
      
      // 测试2: 服务器响应时间
      final serverTime = headResponse.headers.value('date');
      results['server_response_time'] = serverTime;
      
      // 测试3: 是否支持Range请求
      final supportsRange = await _supportsRangeRequests(fileUrl, cancelToken);
      results['supports_range'] = supportsRange;
      
      // 测试4: 文件大小
      final fileSize = await _getFileSize(fileUrl, cancelToken);
      results['file_size_bytes'] = fileSize;
      results['file_size_mb'] = fileSize != null ? (fileSize / (1024 * 1024)).toStringAsFixed(2) : null;
      
      // 测试5: 小块下载速度测试 (前1KB)
      final speedTestStart = DateTime.now();
      final speedTestResponse = await _largeDio.get(
        fileUrl,
        options: Options(
          headers: {'Range': 'bytes=0-1023'}, // 下载前1KB
          responseType: ResponseType.bytes,
        ),
        cancelToken: cancelToken,
      );
      final speedTestTime = DateTime.now().difference(speedTestStart).inMilliseconds;
      if (speedTestResponse.data != null) {
        final speedKBps = (speedTestResponse.data!.length / 1024) / (speedTestTime / 1000);
        results['speed_test_kbps'] = speedKBps.toStringAsFixed(2);
        results['estimated_30mb_time_seconds'] = ((30 * 1024) / speedKBps).toStringAsFixed(1);
      }
      
      // 测试6: HTTP头信息
      results['content_encoding'] = headResponse.headers.value('content-encoding');
      results['content_type'] = headResponse.headers.value('content-type');
      results['cache_control'] = headResponse.headers.value('cache-control');
      results['server'] = headResponse.headers.value('server');
      
      print('=== Download Performance Diagnosis ===');
      results.forEach((key, value) {
        print('$key: $value');
      });
      print('=====================================');
      
    } catch (e) {
      results['error'] = e.toString();
      print('Diagnosis failed: $e');
    }
    
    return results;
  }
  
  /// 处理取消逻辑
  static void _handleCancellation(
    String downloadId,
    Function(DownloadProgress progress)? onProgress,
    Function()? onCancelled,
  ) {
    _activeCancelTokens.remove(downloadId);
    onProgress?.call(DownloadProgress(
      stage: DownloadStage.cancelled,
      overallProgress: 0.0,
      stageProgress: 0.0,
      message: 'Download cancelled',
    ));
    onCancelled?.call();
  }
  
  /// 直接下载文件
  /// [fileUrl] 文件下载地址
  /// [fileName] 保存的文件名（可选，如果不提供会从URL中提取）
  /// [headers] 请求头
  /// [onProgress] 整体进度回调
  /// [onSuccess] 下载成功回调，参数为文件路径
  /// [onError] 下载失败回调，参数为错误信息
  /// [onCancelled] 下载取消回调
  /// [timeout] 超时时间（毫秒）
  /// [downloadId] 下载任务的唯一标识符（可选，如果不提供会自动生成）
  static Future<String?> downloadFileWithCallbacks({
    required String fileUrl,
    String? fileName,
    Function(DownloadProgress progress)? onProgress,
    Function(String filePath)? onSuccess,
    Function(String error)? onError,
    Function()? onCancelled,
    int timeout = 120000,
    String? downloadId,
  }) async {
    // 生成或使用提供的下载ID
    final actualDownloadId = downloadId ?? DateTime.now().millisecondsSinceEpoch.toString();
    
    // 创建取消令牌
    final cancelToken = CancelToken();
    _activeCancelTokens[actualDownloadId] = cancelToken;
    
    try {
      // 准备阶段
      onProgress?.call(DownloadProgress(
        stage: DownloadStage.preparing,
        overallProgress: 0.0,
        stageProgress: 0.0,
        message: 'Preparing download...',
      ));
      
      // 检查是否已被取消
      if (cancelToken.isCancelled) {
        _handleCancellation(actualDownloadId, onProgress, onCancelled);
        return null;
      }

      // 获取文件大小用于优化下载
      onProgress?.call(DownloadProgress(
        stage: DownloadStage.preparing,
        overallProgress: 0.1,
        stageProgress: 0.5,
        message: 'Checking file size...',
      ));
      
      final fileSize = await _getFileSize(fileUrl, cancelToken);
      bool useChunkedDownload = false;
      
      if (fileSize != null) {
        final sizeMB = (fileSize / (1024 * 1024)).toStringAsFixed(1);
        print('File size: ${sizeMB}MB');
        
        // 检查是否支持Range请求，以及文件是否足够大以使用分片下载
        if (fileSize > 10 * 1024 * 1024) { // 大于10MB使用分片下载
          onProgress?.call(DownloadProgress(
            stage: DownloadStage.preparing,
            overallProgress: 0.15,
            stageProgress: 0.75,
            message: 'Checking chunked download support...',
          ));
          
          useChunkedDownload = await _supportsRangeRequests(fileUrl, cancelToken);
          print('Chunked download ${useChunkedDownload ? 'supported' : 'not supported'}');
        }
        
        onProgress?.call(DownloadProgress(
          stage: DownloadStage.preparing,
          overallProgress: 0.2,
          stageProgress: 1.0,
          message: 'File size: ${sizeMB}MB${useChunkedDownload ? ' (chunked)' : ''}',
        ));
      }

      // 开始下载文件
      onProgress?.call(DownloadProgress(
        stage: DownloadStage.downloading,
        overallProgress: 0.0,
        stageProgress: 0.0,
        message: useChunkedDownload ? 'Starting chunked download...' : 'Starting file download...',
      ));

      // 用于计算下载速度
      DateTime? lastUpdateTime;
      int? lastReceived;
      
      String? filePath;
      
      // 智能选择下载方式
      if (useChunkedDownload && fileSize != null) {
        // 使用分片并发下载
        final actualFileName = (fileName ?? "") + "_" + _extractFileNameFromUrl(fileUrl);
        final downloadDir = await _getDownloadDirectory();
        String savePath = '';
        
        if (downloadDir != null) {
          final tempFilePath = '${downloadDir.path}/$actualFileName';
          
          if (CmUtils.isAndroid()) {
            savePath = await getPublicDownloadPath();
            savePath = '${savePath}/$actualFileName';
          }
          
          filePath = await _downloadFileInChunks(
            fileUrl: fileUrl,
            savePath: savePath.isEmpty ? tempFilePath : savePath,
            fileSize: fileSize,
            onProgress: (received, total) {
              // 检查是否已被取消
              if (cancelToken.isCancelled) {
                return;
              }
              
              final downloadProgress = total > 0 ? received / total : 0.0;
              final overallProgress = downloadProgress;
              
              // 计算下载速度
              final now = DateTime.now();
              String speedText = '';
              if (lastUpdateTime != null && lastReceived != null) {
                final timeDiff = now.difference(lastUpdateTime!).inMilliseconds;
                if (timeDiff > 500) { // 每500ms更新一次速度
                  final byteDiff = received - lastReceived!;
                  final speedBytesPerSecond = (byteDiff * 1000) / timeDiff;
                  final speedMBPerSecond = speedBytesPerSecond / (1024 * 1024);
                  speedText = ', ${speedMBPerSecond.toStringAsFixed(1)}MB/s';
                  lastUpdateTime = now;
                  lastReceived = received;
                }
              } else {
                lastUpdateTime = now;
                lastReceived = received;
              }
              
              // 格式化已下载和总大小
              final receivedMB = (received / (1024 * 1024)).toStringAsFixed(1);
              final totalMB = (total / (1024 * 1024)).toStringAsFixed(1);
              
              onProgress?.call(DownloadProgress(
                stage: DownloadStage.downloading,
                overallProgress: overallProgress,
                stageProgress: downloadProgress,
                message: 'Chunked downloading... ${receivedMB}MB/${totalMB}MB (${(downloadProgress * 100).toInt()}%)$speedText',
                received: received,
                total: total,
              ));
            },
            cancelToken: cancelToken,
          );
        }
      } else {
        // 使用传统单线程下载
        filePath = await _downloadFileFromUrlWithProgress(
          fileUrl: fileUrl,
          fileName: fileName,
          onProgress: (received, total) {
            // 检查是否已被取消
            if (cancelToken.isCancelled) {
              return;
            }
            
            final downloadProgress = total > 0 ? received / total : 0.0;
            final overallProgress = downloadProgress;
            
            // 计算下载速度
            final now = DateTime.now();
            String speedText = '';
            if (lastUpdateTime != null && lastReceived != null) {
              final timeDiff = now.difference(lastUpdateTime!).inMilliseconds;
              if (timeDiff > 500) { // 每500ms更新一次速度
                final byteDiff = received - lastReceived!;
                final speedBytesPerSecond = (byteDiff * 1000) / timeDiff;
                final speedMBPerSecond = speedBytesPerSecond / (1024 * 1024);
                speedText = ', ${speedMBPerSecond.toStringAsFixed(1)}MB/s';
                lastUpdateTime = now;
                lastReceived = received;
              }
            } else {
              lastUpdateTime = now;
              lastReceived = received;
            }
            
            // 格式化已下载和总大小
            final receivedMB = (received / (1024 * 1024)).toStringAsFixed(1);
            final totalMB = total > 0 ? (total / (1024 * 1024)).toStringAsFixed(1) : '?';
            
            onProgress?.call(DownloadProgress(
              stage: DownloadStage.downloading,
              overallProgress: overallProgress,
              stageProgress: downloadProgress,
              message: 'Downloading... ${receivedMB}MB/${totalMB}MB (${(downloadProgress * 100).toInt()}%)$speedText',
              received: received,
              total: total,
            ));
          },
          timeout: timeout,
          cancelToken: cancelToken,
        );
      }

      // 最终检查是否已被取消
      if (cancelToken.isCancelled) {
        _handleCancellation(actualDownloadId, onProgress, onCancelled);
        return null;
      }

      if (filePath != null) {
        _activeCancelTokens.remove(actualDownloadId);
        onProgress?.call(DownloadProgress(
          stage: DownloadStage.completed,
          overallProgress: 1.0,
          stageProgress: 1.0,
          message: 'Download completed',
        ));
        onSuccess?.call(filePath);
        return filePath;
      } else {
        _activeCancelTokens.remove(actualDownloadId);
        onError?.call('File download failed');
        onProgress?.call(DownloadProgress(
          stage: DownloadStage.failed,
          overallProgress: 0.0,
          stageProgress: 0.0,
          message: 'File download failed',
        ));
      }
    } catch (e) {
      _activeCancelTokens.remove(actualDownloadId);
      
      // 检查是否是取消异常
      if (e is DioException && e.type == DioExceptionType.cancel) {
        _handleCancellation(actualDownloadId, onProgress, onCancelled);
        return null;
      }
      
      // 检查是否是网络相关错误，可以重试
      if (e is DioException && _shouldRetry(e)) {
        print('Network error, but this was the main download attempt. Error: $e');
      }
      
      final errorMessage = 'Download failed: $e';
      onError?.call(errorMessage);
      onProgress?.call(DownloadProgress(
        stage: DownloadStage.failed,
        overallProgress: 0.0,
        stageProgress: 0.0,
        message: errorMessage,
      ));
    }
    return null;
  }


  
  /// 从URL下载文件
  static Future<String?> _downloadFileFromUrlWithProgress({
    required String fileUrl,
    String? fileName,
    Function(int received, int total)? onProgress,
    int timeout = 120000,
    CancelToken? cancelToken,
  }) async {
    try {
      // 检查存储权限
      if (!await _checkStoragePermission()) {
        print('Storage permission denied');
        return null;
      }

      final downloadDir = await _getDownloadDirectory();
      if (downloadDir == null) {
        print('Failed to get download directory');
        return null;
      }
      // 生成文件名
      final actualFileName = (fileName??"") + "_" + _extractFileNameFromUrl(fileUrl);
      // 获取下载目录
      // 请求存储权限(Android)
      String filePath = '${downloadDir.path}/$actualFileName';

      PermissionStatus status;
      if (CmUtils.isAndroidAPI29()) {
        status = await Permission.manageExternalStorage.status;
      } else {
        status = await Permission.storage.status;
      }
      if (status.isGranted == true || !CmUtils.isAndroidAPI29()) {
        String savePath = '';
        if (CmUtils.isAndroid()) {
          savePath = await getPublicDownloadPath();
          savePath = '${savePath}/$actualFileName';
        }
        // 下载文件 - 使用优化的大文件下载实例
        await _largeDio.download(
          fileUrl,
          savePath.isEmpty ? filePath : savePath,
          onReceiveProgress: (received, total) {
            if (total != -1) {
              final progress = (received / total * 100).toInt();
              print('Download progress: $progress%');
              onProgress?.call(received, total);
            }
          },
          options: Options(
            sendTimeout: Duration(seconds: 30),
            receiveTimeout: Duration(seconds: 60),
            headers: {
              'Range': 'bytes=0-', // 支持断点续传
              'Accept': '*/*',
              'Cache-Control': 'no-cache',
            },
            responseType: ResponseType.bytes, // 优化大文件下载
            followRedirects: true,
            maxRedirects: 5,
          ),
          cancelToken: cancelToken,
        );

        print('File download successful: ${savePath.isEmpty ? filePath : savePath}');

      } else {
        // Map<Permission, PermissionStatus> statuses =
        await [CmUtils.isAndroidAPI29()
              ? Permission.manageExternalStorage
              : Permission.storage].request();
        // Get.to(PermissionPage('导出文件，需要申请读写权限，下载文件', [
        //   CmUtils.isAndroidAPI29()
        //       ? Permission.manageExternalStorage
        //       : Permission.storage
        // ]));
        return null;
      }
      return filePath;
    } catch (e) {
      print('File download failed: $e');
      return null;
    }
  }
  
  /// 检查存储权限
  static Future<bool> _checkStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        // Android 13 及以上版本使用新的权限模型
        if (await Permission.storage.isGranted || 
            await Permission.manageExternalStorage.isGranted) {
          return true;
        }
        
        // 请求权限
        final storageStatus = await Permission.storage.request();
        if (storageStatus.isGranted) {
          return true;
        }
        
        // 如果普通存储权限被拒绝，尝试请求管理外部存储权限（Android 11+）
        final manageStatus = await Permission.manageExternalStorage.request();
        return manageStatus.isGranted;
      } else if (Platform.isIOS) {
        // iOS 不需要额外的存储权限
        return true;
      }
      return false;
    } catch (e) {
      print('Check storage permission failed: $e');
      return false;
    }
  }
  
  /// 获取下载目录
  static Future<Directory?> _getDownloadDirectory() async {
    try {
      if (Platform.isAndroid) {
        // Android: 优先使用外部存储的Downloads目录
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          final downloadDir = Directory('${externalDir.path}/Downloads');
          if (!await downloadDir.exists()) {
            await downloadDir.create(recursive: true);
          }
          return downloadDir;
        }
      }
      
      // 如果外部存储不可用，使用应用文档目录
      final appDocDir = await getApplicationDocumentsDirectory();
      final downloadDir = Directory('${appDocDir.path}/Downloads');
      if (!await downloadDir.exists()) {
        await downloadDir.create(recursive: true);
      }
      return downloadDir;
    } catch (e) {
      print('Get download directory failed: $e');
      return null;
    }
  }
  
  /// 从URL中提取文件名
  static String _extractFileNameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      String fileName = uri.pathSegments.last;
      
      // 如果文件名为空或没有扩展名，生成一个默认名称
      if (fileName.isEmpty || !fileName.contains('.')) {
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        fileName = 'download_$timestamp.pdf'; // 默认为PDF格式
      }
      
      return fileName;
    } catch (e) {
      print('Extract file name failed: $e');
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'download_$timestamp.pdf';
    }
  }


  static Future<void> showDownLoadDialog(
      BuildContext context,
      String fileName,
      String fileUrl,
      String format,
      Function(String filePath, String format)? onSuccess,
      Function(String error, String format)? onError) async {
    // 请求存储权限(Android)
    PermissionStatus status;
    if (CmUtils.isAndroidAPI29()) {
      status = await Permission.manageExternalStorage.status;
    } else {
      status = await Permission.storage.status;
    }
    if (status.isGranted == true || !CmUtils.isAndroidAPI29()) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return ChatDownloadProgressWidget(
                fileName: fileName,
                fileUrl: fileUrl,
                format: format,
                onSuccess: onSuccess,
                onError: onError);
          });
    } else {
      await [CmUtils.isAndroidAPI29()
          ? Permission.manageExternalStorage
          : Permission.storage].request();
      // Get.to(PermissionPage('导出文件，需要申请读写权限，下载文件', [
      //   CmUtils.isAndroidAPI29()
      //       ? Permission.manageExternalStorage
      //       : Permission.storage
      // ]));
      return null;
    }
  }

  // 获取系统下载目录（公共目录）
  static Future<String> getPublicDownloadPath() async {
    if (Platform.isAndroid) {
      try {
        // 方法1：使用 downloads_path_provider
        final path =  await AndroidPathProvider.downloadsPath;
        return path;
      } catch (e) {
        print('获取下载目录失败: $e');
        return '';
      }
    } else if (Platform.isIOS) {
      // iOS没有真正的公共下载目录
      final dir = await getApplicationDocumentsDirectory();
      return '${dir.path}/Downloads';
    }
    return '';
  }
}