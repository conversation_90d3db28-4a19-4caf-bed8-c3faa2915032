import 'dart:convert';
import 'dart:io';
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;

import '../api/Api.dart';
import '../api/StorageService.dart';

/// 上传状态枚举
enum UploadStatus {
  loading,
  success,
  failed,
}

/// 上传模式枚举
enum UploadMode {
  concurrent, // 并发上传（默认）
  sequential,  // 顺序上传
}

/// 文件上传模型
class FileUploadModel {
  final String? fileTitle;
  final String? mimeType;
  final String? localFilePath;
  final String? fileUrl;
  final UploadStatus uploadStatus;

  FileUploadModel({
    this.fileTitle,
    this.mimeType,
    this.localFilePath,
    this.fileUrl,
    this.uploadStatus = UploadStatus.loading,
  });

  FileUploadModel copyWith({
    String? fileTitle,
    String? mimeType,
    String? localFilePath,
    String? fileUrl,
    UploadStatus? uploadStatus,
  }) {
    return FileUploadModel(
      fileTitle: fileTitle ?? this.fileTitle,
      mimeType: mimeType ?? this.mimeType,
      localFilePath: localFilePath ?? this.localFilePath,
      fileUrl: fileUrl ?? this.fileUrl,
      uploadStatus: uploadStatus ?? this.uploadStatus,
    );
  }
}

/// Isolate 上传数据模型
class UploadIsolateData {
  final String filePath;
  final String fileName;
  final String mimeType;
  final int index;
  final String uploadUrl;
  final Map<String, String> headers;
  final SendPort responsePort;

  UploadIsolateData({
    required this.filePath,
    required this.fileName,
    required this.mimeType,
    required this.index,
    required this.uploadUrl,
    required this.headers,
    required this.responsePort,
  });
}

/// Isolate 响应数据模型
class UploadIsolateResponse {
  final int index;
  final bool success;
  final String? fileUrl;
  final String? fileName;
  final String? mimeType;
  final String? error;
  final int? size;
  final String? content;

  UploadIsolateResponse({
    required this.index,
    required this.success,
    this.fileUrl,
    this.fileName,
    this.mimeType,
    this.error,
    this.size,
    this.content
  });
}

/// Isolate 入口函数 - 完整的上传流程
void uploadIsolateEntryPoint(UploadIsolateData data) async {
  try {
    // 1. 读取文件
    File file = File(data.filePath);
    if (!await file.exists()) {
      data.responsePort.send(UploadIsolateResponse(
        index: data.index,
        success: false,
        error: 'File not found: ${data.filePath}',
      ));
      return;
    }

    Uint8List fileBytes = await file.readAsBytes();
    
    // 2. 创建 multipart 请求
    var request = http.MultipartRequest('POST', Uri.parse(data.uploadUrl));
    request.headers.addAll(data.headers);
    request.files.add(http.MultipartFile.fromBytes(
      'file',
      fileBytes,
      filename: data.fileName,
      contentType: MediaType.parse(data.mimeType),
    ));

    // 3. 发送请求
    var streamedResponse = await request.send();
    var response = await http.Response.fromStream(streamedResponse);

    // 4. 处理响应
    if (response.statusCode == 200) {
      var responseBody = jsonDecode(response.body);
      data.responsePort.send(UploadIsolateResponse(
        index: data.index,
        success: true,
        fileUrl: responseBody['file_url'],
        fileName: data.fileName,
        mimeType: data.mimeType,
        size: responseBody['size'],
        content: responseBody['content']
      ));
    } else {
      data.responsePort.send(UploadIsolateResponse(
        index: data.index,
        success: false,
        error: 'HTTP ${response.statusCode}: ${response.body}',
      ));
    }
  } catch (e) {
    data.responsePort.send(UploadIsolateResponse(
      index: data.index,
      success: false,
      error: e.toString(),
    ));
  }
}

/// 顺序上传队列项
class SequentialUploadItem {
  final File file;
  final int originalIndex;
  final String fileName;
  final String mimeType;
  int retryCount;

  SequentialUploadItem({
    required this.file,
    required this.originalIndex,
    required this.fileName,
    required this.mimeType,
    this.retryCount = 0,
  });
}

/// 图片上传工具类
class ImageUploadUtil {
  static ImageUploadUtil? _instance;
  static ImageUploadUtil get instance => _instance ??= ImageUploadUtil._();
  
  ImageUploadUtil._();

  // Isolate 管理
  ReceivePort? _receivePort;
  final List<Isolate> _activeIsolates = [];
  
  // 上传回调管理
  final Map<int, Function(UploadIsolateResponse)> _uploadCallbacks = {};
  
  // 顺序上传相关
  bool _isSequentialUploading = false;
  final List<SequentialUploadItem> _uploadQueue = [];
  List<FileUploadModel>? _sequentialResults;
  Function(int index, String fileUrl, String fileName, int? size, String? content)? _onSingleSuccess;
  Function(int index, String fileName, String error)? _onSingleError;
  Function(List<FileUploadModel> results)? _onAllComplete;
  String? _sequentialUploadUrl;
  Map<String, String>? _sequentialHeaders;
  static const int maxRetryCount = 3;

  /// 初始化上传监听
  void _initReceivePort() {
    if (_receivePort == null) {
      _receivePort = ReceivePort();
      _receivePort!.listen(_handleUploadResponse);
    }
  }

  /// 处理 Isolate 上传响应
  void _handleUploadResponse(dynamic message) {
    if (message is UploadIsolateResponse) {
      final callback = _uploadCallbacks[message.index];
      if (callback != null) {
        callback(message);
        _uploadCallbacks.remove(message.index);
      }
    }
  }

  /// 开始顺序上传队列中的下一个文件
  void _processNextSequentialUpload() {
    if (!_isSequentialUploading || _uploadQueue.isEmpty || _sequentialResults == null) {
      return;
    }

    final item = _uploadQueue.removeAt(0);
    final index = item.originalIndex;

    uploadSingleFile(
      filePath: item.file.path,
      uploadUrl: _sequentialUploadUrl,
      headers: _sequentialHeaders,
      customIndex: index,
      onSuccess: (fileUrl, fileName, size, content) {
        // 更新结果
        _sequentialResults![index] = _sequentialResults![index].copyWith(
          fileUrl: fileUrl,
          uploadStatus: UploadStatus.success,
        );
        _onSingleSuccess?.call(index, fileUrl, fileName, size, content);
        
        // 处理下一个文件
        _processNextSequentialUpload();
        
        // 检查是否全部完成
        _checkSequentialUploadComplete();
      },
      onError: (error) {
        // 失败处理：如果重试次数未达到上限，加入队列末尾重试
        if (item.retryCount < maxRetryCount) {
          item.retryCount++;
          _uploadQueue.add(item); // 加入队列末尾
          _processNextSequentialUpload(); // 继续处理下一个
        } else {
          // 达到重试上限，标记为失败
          _sequentialResults![index] = _sequentialResults![index].copyWith(
            uploadStatus: UploadStatus.failed,
          );
          _onSingleError?.call(index, item.fileName, error);
          
          // 处理下一个文件
          _processNextSequentialUpload();
          
          // 检查是否全部完成
          _checkSequentialUploadComplete();
        }
      },
    );
  }

  /// 检查顺序上传是否全部完成
  void _checkSequentialUploadComplete() {
    if (_sequentialResults == null) return;
    
    // 检查是否还有待上传的文件（队列中的文件或loading状态的文件）
    bool hasLoadingFiles = _sequentialResults!.any((file) => file.uploadStatus == UploadStatus.loading);
    
    if (_uploadQueue.isEmpty && !hasLoadingFiles) {
      // 全部完成，调用完成回调
      _onAllComplete?.call(_sequentialResults!);
      
      // 清理状态
      _isSequentialUploading = false;
      _sequentialResults = null;
      _onSingleSuccess = null;
      _onSingleError = null;
      _onAllComplete = null;
      _sequentialUploadUrl = null;
      _sequentialHeaders = null;
    }
  }

  /// 上传单个文件
  /// 
  /// [filePath] 文件路径
  /// [uploadUrl] 上传地址，如果为空则使用默认的群组上传接口
  /// [headers] 请求头，如果为空则使用默认的Authorization头
  /// [customIndex] 自定义索引，如果为空则使用时间戳生成
  /// [onProgress] 上传进度回调 (目前Isolate中暂不支持进度回调)
  /// [onSuccess] 上传成功回调
  /// [onError] 上传失败回调
  Future<void> uploadSingleFile({
    required String filePath,
    String? uploadUrl,
    Map<String, String>? headers,
    int? customIndex,
    Function(String fileUrl, String fileName,int? size,String? content)? onSuccess,
    Function(String error)? onError,
  }) async {
    _initReceivePort();

    // 生成唯一索引 - 优先使用自定义索引，否则使用时间戳
    final index = customIndex ?? DateTime.now().millisecondsSinceEpoch;
    
    // 获取文件信息
    final file = File(filePath);
    if (!await file.exists()) {
      onError?.call('File not found: $filePath');
      return;
    }

    final fileName = path.basename(filePath);
    final mimeType = lookupMimeType(fileName) ?? 'application/octet-stream';

    // 设置默认上传地址和请求头
    final String finalUploadUrl = uploadUrl ?? (Api.baseUrl + Api.groupUploadImage);
    final Map<String, String> finalHeaders = headers ?? {
      "Authorization": "Bearer ${Get.find<StorageService>().getLoginData().accessToken ?? ""}"
    };

    // 设置回调
    _uploadCallbacks[index] = (UploadIsolateResponse response) {
      if (response.success && response.fileUrl != null) {
        onSuccess?.call(response.fileUrl!, response.fileName ?? fileName, response.size,response.content);
      } else {
        onError?.call(response.error ?? 'Upload failed');
      }
    };

    try {
      // 创建上传数据
      final uploadData = UploadIsolateData(
        filePath: filePath,
        fileName: fileName,
        mimeType: mimeType,
        index: index,
        uploadUrl: finalUploadUrl,
        headers: finalHeaders,
        responsePort: _receivePort!.sendPort,
      );

      // 启动 Isolate
      final isolate = await Isolate.spawn(uploadIsolateEntryPoint, uploadData);
      _activeIsolates.add(isolate);
      
    } catch (e) {
      _uploadCallbacks.remove(index);
      onError?.call('Failed to create isolate: $e');
    }
  }

  /// 批量上传文件
  /// 
  /// [files] 文件列表
  /// [uploadUrl] 上传地址，如果为空则使用默认的群组上传接口
  /// [headers] 请求头，如果为空则使用默认的Authorization头
  /// [uploadMode] 上传模式（并发或顺序），默认为并发
  /// [onSingleSuccess] 单个文件上传成功回调
  /// [onSingleError] 单个文件上传失败回调
  /// [onAllComplete] 所有文件上传完成回调（包含成功和失败的文件）
  Future<void> uploadMultipleFiles({
    required List<File> files,
    String? uploadUrl,
    Map<String, String>? headers,
    UploadMode uploadMode = UploadMode.concurrent,
    Function(int index, String fileUrl, String fileName,int? size,String? content)? onSingleSuccess,
    Function(int index, String fileName, String error)? onSingleError,
    Function(List<FileUploadModel> results)? onAllComplete,
  }) async {
    if (files.isEmpty) {
      onAllComplete?.call([]);
      return;
    }

    if (uploadMode == UploadMode.sequential) {
      // 顺序上传模式
      await _uploadSequentially(
        files: files,
        uploadUrl: uploadUrl,
        headers: headers,
        onSingleSuccess: onSingleSuccess,
        onSingleError: onSingleError,
        onAllComplete: onAllComplete,
      );
    } else {
      // 并发上传模式（原有逻辑）
      await _uploadConcurrently(
        files: files,
        uploadUrl: uploadUrl,
        headers: headers,
        onSingleSuccess: onSingleSuccess,
        onSingleError: onSingleError,
        onAllComplete: onAllComplete,
      );
    }
  }

  /// 顺序上传实现
  Future<void> _uploadSequentially({
    required List<File> files,
    String? uploadUrl,
    Map<String, String>? headers,
    Function(int index, String fileUrl, String fileName, int? size, String? content)? onSingleSuccess,
    Function(int index, String fileName, String error)? onSingleError,
    Function(List<FileUploadModel> results)? onAllComplete,
  }) async {
    // 防止重复启动顺序上传
    if (_isSequentialUploading) {
      onSingleError?.call(-1, 'Sequential Upload', 'Another sequential upload is already in progress');
      return;
    }

    _isSequentialUploading = true;
    _sequentialUploadUrl = uploadUrl;
    _sequentialHeaders = headers;
    _onSingleSuccess = onSingleSuccess;
    _onSingleError = onSingleError;
    _onAllComplete = onAllComplete;

    // 初始化结果列表
    _sequentialResults = [];
    _uploadQueue.clear();

    for (int i = 0; i < files.length; i++) {
      final file = files[i];
      final fileName = path.basename(file.path);
      final mimeType = lookupMimeType(fileName) ?? 'application/octet-stream';
      
      // 初始化结果（所有文件初始状态为loading）
      _sequentialResults!.add(FileUploadModel(
        fileTitle: fileName,
        mimeType: mimeType,
        localFilePath: file.path,
        uploadStatus: UploadStatus.loading,
      ));

      // 添加到上传队列
      _uploadQueue.add(SequentialUploadItem(
        file: file,
        originalIndex: i,
        fileName: fileName,
        mimeType: mimeType,
      ));
    }

    // 开始处理第一个文件
    _processNextSequentialUpload();
  }

  /// 并发上传实现（原有逻辑）
  Future<void> _uploadConcurrently({
    required List<File> files,
    String? uploadUrl,
    Map<String, String>? headers,
    Function(int index, String fileUrl, String fileName, int? size, String? content)? onSingleSuccess,
    Function(int index, String fileName, String error)? onSingleError,
    Function(List<FileUploadModel> results)? onAllComplete,
  }) async {
    final List<FileUploadModel> results = [];
    int completedCount = 0;

    for (int i = 0; i < files.length; i++) {
      final file = files[i];
      final fileName = path.basename(file.path);
      final mimeType = lookupMimeType(fileName) ?? 'application/octet-stream';
      
      // 初始化结果
      results.add(FileUploadModel(
        fileTitle: fileName,
        mimeType: mimeType,
        localFilePath: file.path,
        uploadStatus: UploadStatus.loading,
      ));

      // 上传单个文件 - 传递正确的索引
      uploadSingleFile(
        filePath: file.path,
        uploadUrl: uploadUrl,
        headers: headers,
        customIndex: i, // 传递数组索引确保索引对应正确
        onSuccess: (fileUrl, fileName, size, content) {
          results[i] = results[i].copyWith(
            fileUrl: fileUrl,
            uploadStatus: UploadStatus.success,
          );
          onSingleSuccess?.call(i, fileUrl, fileName, size, content);
          
          completedCount++;
          if (completedCount == files.length) {
            onAllComplete?.call(results);
          }
        },
        onError: (error) {
          results[i] = results[i].copyWith(uploadStatus: UploadStatus.failed);
          onSingleError?.call(i, fileName, error);
          
          completedCount++;
          if (completedCount == files.length) {
            onAllComplete?.call(results);
          }
        },
      );
    }
  }

  /// 清理所有 Isolate 资源
  void dispose() {
    // 关闭接收端口
    _receivePort?.close();
    _receivePort = null;
    
    // 清理回调
    _uploadCallbacks.clear();
    
    // 清理顺序上传状态
    _isSequentialUploading = false;
    _uploadQueue.clear();
    _sequentialResults = null;
    _onSingleSuccess = null;
    _onSingleError = null;
    _onAllComplete = null;
    _sequentialUploadUrl = null;
    _sequentialHeaders = null;
    
    // 杀死所有活跃的 Isolate
    for (Isolate isolate in _activeIsolates) {
      isolate.kill(priority: Isolate.immediate);
    }
    _activeIsolates.clear();
  }
}