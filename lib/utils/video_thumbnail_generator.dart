import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// 视频缩略图生成器
class VideoThumbnailGenerator {
  static final Map<String, Uint8List> _thumbnailCache = {};

  /// 从视频URL生成缩略图
  /// [videoUrl] 视频URL
  /// [timeMs] 截取时间点（毫秒），默认为0（第一帧）
  /// [maxWidth] 最大宽度，默认为300
  /// [maxHeight] 最大高度，默认为200
  /// [quality] 质量（0-100），默认为75
  static Future<Uint8List?> generateThumbnail({
    required String videoUrl,
    int timeMs = 0,
    int maxWidth = 300,
    int maxHeight = 200,
    int quality = 75,
  }) async {
    try {
      // 生成缓存键
      final cacheKey = _generateCacheKey(videoUrl, timeMs, maxWidth, maxHeight, quality);
      
      // 检查缓存
      if (_thumbnailCache.containsKey(cacheKey)) {
        return _thumbnailCache[cacheKey];
      }

      // 生成缩略图
      final uint8list = await VideoThumbnail.thumbnailData(
        video: videoUrl,
        imageFormat: ImageFormat.JPEG,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        timeMs: timeMs,
        quality: quality,
        headers: {
          'User-Agent': 'Flutter App',
        },
      );

      // 缓存结果
      if (uint8list != null) {
        _thumbnailCache[cacheKey] = uint8list;
      }

      return uint8list;
    } catch (e) {
      print('生成视频缩略图失败: $e');
      return null;
    }
  }

  /// 从视频URL生成缩略图文件
  /// [videoUrl] 视频URL
  /// [fileName] 自定义文件名，不提供则自动生成
  static Future<String?> generateThumbnailFile({
    required String videoUrl,
    String? fileName,
    int timeMs = 0,
    int maxWidth = 300,
    int maxHeight = 200,
    int quality = 75,
  }) async {
    try {
      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      
      // 生成文件名
      final actualFileName = fileName ?? _generateFileName(videoUrl);
      final thumbnailPath = '${tempDir.path}/$actualFileName.jpg';

      // 检查文件是否已存在
      final file = File(thumbnailPath);
      if (await file.exists()) {
        return thumbnailPath;
      }

      // 生成缩略图文件
      final filePath = await VideoThumbnail.thumbnailFile(
        video: videoUrl,
        thumbnailPath: tempDir.path,
        imageFormat: ImageFormat.JPEG,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        timeMs: timeMs,
        quality: quality,
        headers: {
          'User-Agent': 'Flutter App',
        },
      );

      return filePath;
    } catch (e) {
      print('生成视频缩略图文件失败: $e');
      return null;
    }
  }

  /// 生成缓存键
  static String _generateCacheKey(String videoUrl, int timeMs, int maxWidth, int maxHeight, int quality) {
    final input = '$videoUrl-$timeMs-$maxWidth-$maxHeight-$quality';
    final bytes = utf8.encode(input);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// 生成文件名
  static String _generateFileName(String videoUrl) {
    final bytes = utf8.encode(videoUrl);
    final digest = md5.convert(bytes);
    return 'thumbnail_${digest.toString().substring(0, 8)}';
  }

  /// 清除缓存
  static void clearCache() {
    _thumbnailCache.clear();
  }

  /// 清除特定URL的缓存
  static void clearCacheForUrl(String videoUrl) {
    _thumbnailCache.removeWhere((key, value) => key.contains(videoUrl));
  }

  /// 获取缓存大小
  static int getCacheSize() {
    return _thumbnailCache.length;
  }
}

/// 视频缩略图Widget
class VideoThumbnailWidget extends StatefulWidget {
  final String videoUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final int timeMs;
  final int quality;

  const VideoThumbnailWidget({
    Key? key,
    required this.videoUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.timeMs = 0,
    this.quality = 75,
  }) : super(key: key);

  @override
  State<VideoThumbnailWidget> createState() => _VideoThumbnailWidgetState();
}

class _VideoThumbnailWidgetState extends State<VideoThumbnailWidget> {
  late Future<Uint8List?> _thumbnailFuture;

  @override
  void initState() {
    super.initState();
    _loadThumbnail();
  }

  @override
  void didUpdateWidget(VideoThumbnailWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.videoUrl != widget.videoUrl || 
        oldWidget.timeMs != widget.timeMs ||
        oldWidget.quality != widget.quality) {
      _loadThumbnail();
    }
  }

  void _loadThumbnail() {
    _thumbnailFuture = VideoThumbnailGenerator.generateThumbnail(
      videoUrl: widget.videoUrl,
      timeMs: widget.timeMs,
      maxWidth: (widget.width ?? 300).toInt(),
      maxHeight: (widget.height ?? 200).toInt(),
      quality: widget.quality,
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List?>(
      future: _thumbnailFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return widget.placeholder ?? 
            Container(
              width: widget.width,
              height: widget.height,
              color: Colors.grey[300],
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            );
        }

        if (snapshot.hasError || snapshot.data == null) {
          return widget.errorWidget ?? 
            Container(
              width: widget.width,
              height: widget.height,
              color: Colors.grey[300],
              child: const Center(
                child: Icon(
                  Icons.error_outline,
                  color: Colors.grey,
                ),
              ),
            );
        }

        return Image.memory(
          snapshot.data!,
          width: widget.width,
          height: widget.height,
          fit: widget.fit,
        );
      },
    );
  }
}