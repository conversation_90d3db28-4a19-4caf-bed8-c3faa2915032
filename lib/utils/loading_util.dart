import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class LoadingUtil {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  static OverlayEntry? _overlayEntry;

  /// 在 MaterialApp 里设置 navigatorKey: LoadingUtil.navigatorKey
  static GlobalKey<NavigatorState> get key => navigatorKey;

  static void show() {
    if (_overlayEntry != null) return;
    final context = navigatorKey.currentState?.overlay?.context;
    if (context == null) return;
    _overlayEntry = OverlayEntry(
      builder: (_) => Material(
        color: Colors.transparent,
        child: Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: const Color(0xB30D0D0D), // 0x4D = 0.3透明度
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              Lottie.asset(
                'assets/json/page_loading.json',
                height: 100,
                width: 100,
              ),
            ],
          ),
        ),
      ),
    );
    Overlay.of(context, rootOverlay: true)?.insert(_overlayEntry!);
  }

  static void dismiss() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}