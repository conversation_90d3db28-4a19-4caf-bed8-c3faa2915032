import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:new_agnes/analytics/AnalyticsService.dart';
import 'package:new_agnes/utils/FirebaseApi.dart';
import 'package:new_agnes/utils/NetworkStatusManager.dart';
import 'package:new_agnes/utils/ble/blueToothManager.dart';
import 'package:new_agnes/utils/cmUtils.dart';
import 'package:new_agnes/utils/environment_config.dart';
import 'package:new_agnes/utils/loading_util.dart';
import 'package:new_agnes/widget/CustomAnimation.dart';

import 'api/ApiProvider.dart';
import 'api/StorageService.dart';
import 'generated/l10n.dart';
import 'main_tab_page.dart';
import 'module/account/login/page.dart';
import 'module/chat/ChatFloatingButtonWidget.dart';
import 'module/chat/group/creat_chat/invite_logic.dart';
import 'module/chat/group/group_chat/agora_logic.dart';
import 'module/chat/group/group_chat_room/CallFloatingButtonWidget.dart';
import 'module/home/<USER>/logic.dart';
import 'module/version/config.dart';
import 'module/version/manager.dart';
import 'package:timezone/data/latest.dart' as tz;

import 'utils/event_bus.dart';

// overlay entry point
@pragma("vm:entry-point")
void overlayMain() {
  // String? language = Get.find<StorageService>().getLanguage()??"";
  runApp(ScreenUtilInit(
    designSize: Size(70, 70), // 设计稿尺寸（iPhone 11 或类似）
    minTextAdapt: true,
    splitScreenMode: true,
    builder: (context, child) {
      return MaterialApp(
          debugShowCheckedModeBanner: false,
          home: Material(
              color: Colors.transparent, child: CallFloatingButtonWidget()));
    },
  ));
}

@pragma("vm:entry-point")
void overlayChat() {
  runApp(ScreenUtilInit(
    designSize: Size(70, 70),
    minTextAdapt: true,
    splitScreenMode: true,
    builder: (context, child) {
      return MaterialApp(
          debugShowCheckedModeBanner: false,
          home: Material(
              color: Colors.transparent, child: ChatFloatingButtonWidget()));
    },
  ));
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  tz.initializeTimeZones();
  await Firebase.initializeApp();

  const channel =
      String.fromEnvironment('APP_CHANNEL', defaultValue: 'googleplay');
  await AppUpdateManager().init(
    enableUpdate: UpdateConfig.isUpdateEnabled(channel),
    channel: channel,
  );
  // 初始化 StorageService
  await GetStorage.init();
  Get.put(StorageService());
  Get.put(EnvironmentConfig()); // 注册环境配置服务
  Get.put(ApiProvider());
  // 注册分析服务
  Get.put(AnalyticsManager());
  Get.put(BlueToothManager());
  Get.put(NetworkStatusManager()..init());
  FirebaseApi().initNotifications();
  runApp(ScreenUtilInit(
    designSize: Size(393, 852), // 设计稿尺寸（iPhone 11 或类似）
    minTextAdapt: true,
    splitScreenMode: true,
    builder: (context, child) {
      return MyApp();
    },
  ));
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp>with WidgetsBindingObserver {
  final inviteLogic = Get.put(InviteMemberController());

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    initLoading();
    //获取角色列表数据
    inviteLogic.get_groupChat_roles();

    super.initState();
  }
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        CmUtils.bottomBarHeight.value=CmUtils.getBottomBarHeight(Get.context);
        eventBus.fire(TabEvent(type: 3));
        break;
      case AppLifecycleState.paused:

        break;
      default:
        break;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // TODO: implement dispose
    super.dispose();
  }
  //全局加载进度条
  void initLoading() {
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..indicatorType = EasyLoadingIndicatorType.circle
      ..loadingStyle = EasyLoadingStyle.custom
      ..indicatorSize = 45.0
      ..radius = 10.0
      ..progressColor = Colors.white
      ..backgroundColor = Color(0X4c000000)
      ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..textStyle = TextStyle(
        color: Colors.white,
        fontSize: 10,
      )
      ..userInteractions = false
      ..dismissOnTap = false
      ..boxShadow = []
      ..maskColor = Colors.transparent
      ..lineWidth = 2
      ..customAnimation = CustomAnimation();
  }

  @override
  Widget build(BuildContext context) {
    String? token = Get.find<StorageService>().getLoginData().accessToken ?? '';
    var hasToken = token.isNotEmpty;
    if (hasToken) {
      Get.find<ApiProvider>().setToken('${token}');
    }
    String? language = Get.find<StorageService>().getLanguage() ?? "";

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    // 设置沉浸式状态栏（透明）
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      // 状态栏透明
      systemNavigationBarColor: Colors.transparent,
      // 底部导航栏透明
      statusBarIconBrightness: Brightness.light,
      // 状态栏图标颜色
      statusBarBrightness: Brightness.light,
      // iOS 状态栏亮度模式
      systemNavigationBarIconBrightness: Brightness.light,
      // 底部导航栏图标亮度
      systemNavigationBarDividerColor: Colors.transparent, // 去除导航栏分割线（如有）
    ));

    return GetMaterialApp(
      navigatorKey: LoadingUtil.key,
      localizationsDelegates: const [
        S.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      debugShowCheckedModeBanner: false,
      // showPerformanceOverlay: kDebugMode,
      supportedLocales: S.delegate.supportedLocales,
      enableLog: kDebugMode,
      locale: language.isNotEmpty ? Locale(language) : null,
      builder: EasyLoading.init(),
      // defaultTransition: Transition.rightToLeftWithFade,
      transitionDuration: const Duration(milliseconds: 200),
      // 2. 注册路由观察者
      routingCallback: (route) {
        // CallFloatingButton.updateVisibility(route?.current);
        CallFloatingButtonWidgetManager().currentRouteName = route!.current;
        // LoggerUtils.d("跳转的页面路由--${route?.current}");
      },
      //默认动画效果
      theme: ThemeData(
        useMaterial3: false,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        hoverColor: Colors.transparent,
        canvasColor: Colors.transparent,
        platform: TargetPlatform.iOS,
        //添加这个属性即可
        textTheme: Typography.material2018().black.copyWith(
              bodyMedium: (Typography.material2018().black.bodyMedium ??
                      const TextStyle())
                  .copyWith(
                letterSpacing: -0.382, // 设置全局字间距
              ),
            ),
      ).copyWith(
        scaffoldBackgroundColor: Colors.transparent,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
      ),
      home: hasToken ? MainTabPage() : LoginPage(),
    );
  }
}
