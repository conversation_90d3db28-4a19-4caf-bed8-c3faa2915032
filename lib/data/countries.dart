import 'dart:io' show Platform;
import 'countries.dart';

class Country {
  final String country;
  final String code;
  final String number;

  Country({required this.country, required this.code, required this.number});
}

final List<Map<String, Object>> countriesJson = [
  {"country": "Afghanistan (افغانستان)", "code": "af", "number": "93"},
  {"country": "Albania (Shqipëri)", "code": "al", "number": "355"},
  {"country": "Algeria (الجزائر)", "code": "dz", "number": "213"},
  {"country": "American Samoa", "code": "as", "number": "1"},
  {"country": "Andorra", "code": "ad", "number": "376"},
  {"country": "Angola", "code": "ao", "number": "244"},
  {"country": "Anguilla", "code": "ai", "number": "1"},
  {"country": "Antigua and Barbuda", "code": "ag", "number": "1"},
  {"country": "Argentina", "code": "ar", "number": "54"},
  {"country": "Armenia (Հայաստան)", "code": "am", "number": "374"},
  {"country": "Aruba", "code": "aw", "number": "297"},
  {"country": "Ascension Island", "code": "ac", "number": "247"},
  {"country": "Australia", "code": "au", "number": "61"},
  {"country": "Austria (Österreich)", "code": "at", "number": "43"},
  {"country": "Azerbaijan (Azərbaycan)", "code": "az", "number": "994"},
  {"country": "Bahamas", "code": "bs", "number": "1"},
  {"country": "Bahrain (البحرين)", "code": "bh", "number": "973"},
  {"country": "Bangladesh (বাংলাদেশ)", "code": "bd", "number": "880"},
  {"country": "Barbados", "code": "bb", "number": "1"},
  {"country": "Belarus (Беларусь)", "code": "by", "number": "375"},
  {"country": "Belgium (België)", "code": "be", "number": "32"},
  {"country": "Belize", "code": "bz", "number": "501"},
  {"country": "Benin (Bénin)", "code": "bj", "number": "229"},
  {"country": "Bermuda", "code": "bm", "number": "1"},
  {"country": "Bhutan (འབྲུག)", "code": "bt", "number": "975"},
  {"country": "Bolivia", "code": "bo", "number": "591"},
  {
    "country": "Bosnia and Herzegovina (Босна и Херцеговина)",
    "code": "ba",
    "number": "387"
  },
  {"country": "Botswana", "code": "bw", "number": "267"},
  {"country": "Brazil (Brasil)", "code": "br", "number": "55"},
  {"country": "British Indian Ocean Territory", "code": "io", "number": "246"},
  {"country": "British Virgin Islands", "code": "vg", "number": "1"},
  {"country": "Brunei", "code": "bn", "number": "673"},
  {"country": "Bulgaria (България)", "code": "bg", "number": "359"},
  {"country": "Burkina Faso", "code": "bf", "number": "226"},
  {"country": "Burundi (Uburundi)", "code": "bi", "number": "257"},
  {"country": "Cambodia (កម្ពុជា)", "code": "kh", "number": "855"},
  {"country": "Cameroon (Cameroun)", "code": "cm", "number": "237"},
  {"country": "Canada", "code": "ca", "number": "1"},
  {"country": "Cape Verde (Kabu Verdi)", "code": "cv", "number": "238"},
  {"country": "Caribbean Netherlands", "code": "bq", "number": "599"},
  {"country": "Cayman Islands", "code": "ky", "number": "1"},
  {
    "country": "Central African Republic (République centrafricaine)",
    "code": "cf",
    "number": "236"
  },
  {"country": "Chad (Tchad)", "code": "td", "number": "235"},
  {"country": "Chile", "code": "cl", "number": "56"},
  {"country": "China (中国)", "code": "cn", "number": "86"},
  {"country": "Christmas Island", "code": "cx", "number": "61"},
  {"country": "Cocos (Keeling) Islands", "code": "cc", "number": "61"},
  {"country": "Colombia", "code": "co", "number": "57"},
  {"country": "Comoros (جزر القمر)", "code": "km", "number": "269"},
  {
    "country": "Congo (DRC) (République démocratique du Congo)",
    "code": "cd",
    "number": "243"
  },
  {
    "country": "Congo (Republic) (Congo-Brazzaville)",
    "code": "cg",
    "number": "242"
  },
  {"country": "Cook Islands", "code": "ck", "number": "682"},
  {"country": "Costa Rica", "code": "cr", "number": "506"},
  {"country": "Côte d’Ivoire", "code": "ci", "number": "225"},
  {"country": "Croatia (Hrvatska)", "code": "hr", "number": "385"},
  {"country": "Cuba", "code": "cu", "number": "53"},
  {"country": "Curaçao", "code": "cw", "number": "599"},
  {"country": "Cyprus (Κύπρος)", "code": "cy", "number": "357"},
  {
    "country": "Czech Republic (Česká republika)",
    "code": "cz",
    "number": "420"
  },
  {"country": "Denmark (Danmark)", "code": "dk", "number": "45"},
  {"country": "Djibouti", "code": "dj", "number": "253"},
  {"country": "Dominica", "code": "dm", "number": "1"},
  {
    "country": "Dominican Republic (República Dominicana)",
    "code": "do",
    "number": "1"
  },
  {"country": "Ecuador", "code": "ec", "number": "593"},
  {"country": "Egypt (مصر)", "code": "eg", "number": "20"},
  {"country": "El Salvador", "code": "sv", "number": "503"},
  {
    "country": "Equatorial Guinea (Guinea Ecuatorial)",
    "code": "gq",
    "number": "240"
  },
  {"country": "Eritrea", "code": "er", "number": "291"},
  {"country": "Estonia (Eesti)", "code": "ee", "number": "372"},
  {"country": "Eswatini", "code": "sz", "number": "268"},
  {"country": "Ethiopia", "code": "et", "number": "251"},
  {
    "country": "Falkland Islands (Islas Malvinas)",
    "code": "fk",
    "number": "500"
  },
  {"country": "Faroe Islands (Føroyar)", "code": "fo", "number": "298"},
  {"country": "Fiji", "code": "fj", "number": "679"},
  {"country": "Finland (Suomi)", "code": "fi", "number": "358"},
  {"country": "France", "code": "fr", "number": "33"},
  {
    "country": "French Guiana (Guyane française)",
    "code": "gf",
    "number": "594"
  },
  {
    "country": "French Polynesia (Polynésie française)",
    "code": "pf",
    "number": "689"
  },
  {"country": "Gabon", "code": "ga", "number": "241"},
  {"country": "Gambia", "code": "gm", "number": "220"},
  {"country": "Georgia (საქართველო)", "code": "ge", "number": "995"},
  {"country": "Germany (Deutschland)", "code": "de", "number": "49"},
  {"country": "Ghana (Gaana)", "code": "gh", "number": "233"},
  {"country": "Gibraltar", "code": "gi", "number": "350"},
  {"country": "Greece (Ελλάδα)", "code": "gr", "number": "30"},
  {"country": "Greenland (Kalaallit Nunaat)", "code": "gl", "number": "299"},
  {"country": "Grenada", "code": "gd", "number": "1"},
  {"country": "Guadeloupe", "code": "gp", "number": "590"},
  {"country": "Guam", "code": "gu", "number": "1"},
  {"country": "Guatemala", "code": "gt", "number": "502"},
  {"country": "Guernsey", "code": "gg", "number": "44"},
  {"country": "Guinea (Guinée)", "code": "gn", "number": "224"},
  {"country": "Guinea-Bissau (Guiné Bissau)", "code": "gw", "number": "245"},
  {"country": "Guyana", "code": "gy", "number": "592"},
  {"country": "Haiti", "code": "ht", "number": "509"},
  {"country": "Honduras", "code": "hn", "number": "504"},
  {"country": "Hong Kong (中国香港)", "code": "hk", "number": "852"},
  {"country": "Hungary (Magyarország)", "code": "hu", "number": "36"},
  {"country": "Iceland (Ísland)", "code": "is", "number": "354"},
  {"country": "India (भारत)", "code": "in", "number": "91"},
  {"country": "Indonesia", "code": "id", "number": "62"},
  {"country": "Iran (ایران)", "code": "ir", "number": "98"},
  {"country": "Iraq (العراق)", "code": "iq", "number": "964"},
  {"country": "Ireland", "code": "ie", "number": "353"},
  {"country": "Isle of Man", "code": "im", "number": "44"},
  {"country": "Israel (ישראל)", "code": "il", "number": "972"},
  {"country": "Italy (Italia)", "code": "it", "number": "39"},
  {"country": "Jamaica", "code": "jm", "number": "1"},
  {"country": "Japan (日本)", "code": "jp", "number": "81"},
  {"country": "Jersey", "code": "je", "number": "44"},
  {"country": "Jordan (الأردن)", "code": "jo", "number": "962"},
  {"country": "Kazakhstan (Казахстан)", "code": "kz", "number": "7"},
  {"country": "Kenya", "code": "ke", "number": "254"},
  {"country": "Kiribati", "code": "ki", "number": "686"},
  {"country": "Kosovo", "code": "xk", "number": "383"},
  {"country": "Kuwait (الكويت)", "code": "kw", "number": "965"},
  {"country": "Kyrgyzstan (Кыргызстан)", "code": "kg", "number": "996"},
  {"country": "Laos (ລາວ)", "code": "la", "number": "856"},
  {"country": "Latvia (Latvija)", "code": "lv", "number": "371"},
  {"country": "Lebanon (لبنان)", "code": "lb", "number": "961"},
  {"country": "Lesotho", "code": "ls", "number": "266"},
  {"country": "Liberia", "code": "lr", "number": "231"},
  {"country": "Libya (ليبيا)", "code": "ly", "number": "218"},
  {"country": "Liechtenstein", "code": "li", "number": "423"},
  {"country": "Lithuania (Lietuva)", "code": "lt", "number": "370"},
  {"country": "Luxembourg", "code": "lu", "number": "352"},
  {"country": "Macau (中国澳門)", "code": "mo", "number": "853"},
  {"country": "Madagascar (Madagasikara)", "code": "mg", "number": "261"},
  {"country": "Malawi", "code": "mw", "number": "265"},
  {"country": "Malaysia", "code": "my", "number": "60"},
  {"country": "Maldives", "code": "mv", "number": "960"},
  {"country": "Mali", "code": "ml", "number": "223"},
  {"country": "Malta", "code": "mt", "number": "356"},
  {"country": "Marshall Islands", "code": "mh", "number": "692"},
  {"country": "Martinique", "code": "mq", "number": "596"},
  {"country": "Mauritania (موريتانيا)", "code": "mr", "number": "222"},
  {"country": "Mauritius (Moris)", "code": "mu", "number": "230"},
  {"country": "Mayotte", "code": "yt", "number": "262"},
  {"country": "Mexico (México)", "code": "mx", "number": "52"},
  {"country": "Micronesia", "code": "fm", "number": "691"},
  {"country": "Moldova (Republica Moldova)", "code": "md", "number": "373"},
  {"country": "Monaco", "code": "mc", "number": "377"},
  {"country": "Mongolia (Монгол)", "code": "mn", "number": "976"},
  {"country": "Montenegro (Crna Gora)", "code": "me", "number": "382"},
  {"country": "Montserrat", "code": "ms", "number": "1"},
  {"country": "Morocco (المغرب)", "code": "ma", "number": "212"},
  {"country": "Mozambique (Moçambique)", "code": "mz", "number": "258"},
  {"country": "Myanmar (Burma) (မြန်မာ)", "code": "mm", "number": "95"},
  {"country": "Namibia (Namibië)", "code": "na", "number": "264"},
  {"country": "Nauru", "code": "nr", "number": "674"},
  {"country": "Nepal (नेपाल)", "code": "np", "number": "977"},
  {"country": "Netherlands (Nederland)", "code": "nl", "number": "31"},
  {
    "country": "New Caledonia (Nouvelle-Calédonie)",
    "code": "nc",
    "number": "687"
  },
  {"country": "New Zealand", "code": "nz", "number": "64"},
  {"country": "Nicaragua", "code": "ni", "number": "505"},
  {"country": "Niger (Nijar)", "code": "ne", "number": "227"},
  {"country": "Nigeria", "code": "ng", "number": "234"},
  {"country": "Niue", "code": "nu", "number": "683"},
  {"country": "Norfolk Island", "code": "nf", "number": "672"},
  {"country": "North Korea (조선 민주주의 인민 공화국)", "code": "kp", "number": "850"},
  {
    "country": "North Macedonia (Северна Македонија)",
    "code": "mk",
    "number": "389"
  },
  {"country": "Northern Mariana Islands", "code": "mp", "number": "1"},
  {"country": "Norway (Norge)", "code": "no", "number": "47"},
  {"country": "Oman (عُمان)", "code": "om", "number": "968"},
  {"country": "Pakistan (پاکستان)", "code": "pk", "number": "92"},
  {"country": "Palau", "code": "pw", "number": "680"},
  {"country": "Palestine (فلسطين)", "code": "ps", "number": "970"},
  {"country": "Panama (Panamá)", "code": "pa", "number": "507"},
  {"country": "Papua New Guinea", "code": "pg", "number": "675"},
  {"country": "Paraguay", "code": "py", "number": "595"},
  {"country": "Peru (Perú)", "code": "pe", "number": "51"},
  {"country": "Philippines", "code": "ph", "number": "63"},
  {"country": "Poland (Polska)", "code": "pl", "number": "48"},
  {"country": "Portugal", "code": "pt", "number": "351"},
  {"country": "Puerto Rico", "code": "pr", "number": "1"},
  {"country": "Qatar (قطر)", "code": "qa", "number": "974"},
  {"country": "Réunion (La Réunion)", "code": "re", "number": "262"},
  {"country": "Romania (România)", "code": "ro", "number": "40"},
  {"country": "Russia (Россия)", "code": "ru", "number": "7"},
  {"country": "Rwanda", "code": "rw", "number": "250"},
  {"country": "Saint Barthélemy", "code": "bl", "number": "590"},
  {"country": "Saint Helena", "code": "sh", "number": "290"},
  {"country": "Saint Kitts and Nevis", "code": "kn", "number": "1"},
  {"country": "Saint Lucia", "code": "lc", "number": "1"},
  {
    "country": "Saint Martin (Saint-Martin (partie française))",
    "code": "mf",
    "number": "590"
  },
  {
    "country": "Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)",
    "code": "pm",
    "number": "508"
  },
  {"country": "Saint Vincent and the Grenadines", "code": "vc", "number": "1"},
  {"country": "Samoa", "code": "ws", "number": "685"},
  {"country": "San Marino", "code": "sm", "number": "378"},
  {
    "country": "São Tomé and Príncipe (São Tomé e Príncipe)",
    "code": "st",
    "number": "239"
  },
  {
    "country": "Saudi Arabia (المملكة العربية السعودية)",
    "code": "sa",
    "number": "966"
  },
  {"country": "Senegal (Sénégal)", "code": "sn", "number": "221"},
  {"country": "Serbia (Србија)", "code": "rs", "number": "381"},
  {"country": "Seychelles", "code": "sc", "number": "248"},
  {"country": "Sierra Leone", "code": "sl", "number": "232"},
  {"country": "Singapore", "code": "sg", "number": "65"},
  {"country": "Sint Maarten", "code": "sx", "number": "1"},
  {"country": "Slovakia (Slovensko)", "code": "sk", "number": "421"},
  {"country": "Slovenia (Slovenija)", "code": "si", "number": "386"},
  {"country": "Solomon Islands", "code": "sb", "number": "677"},
  {"country": "Somalia (Soomaaliya)", "code": "so", "number": "252"},
  {"country": "South Africa", "code": "za", "number": "27"},
  {"country": "South Korea (대한민국)", "code": "kr", "number": "82"},
  {"country": "South Sudan (جنوب السودان)", "code": "ss", "number": "211"},
  {"country": "Spain (España)", "code": "es", "number": "34"},
  {"country": "Sri Lanka (ශ්‍රී ලංකාව)", "code": "lk", "number": "94"},
  {"country": "Sudan (السودان)", "code": "sd", "number": "249"},
  {"country": "Suriname", "code": "sr", "number": "597"},
  {"country": "Svalbard and Jan Mayen", "code": "sj", "number": "47"},
  {"country": "Sweden (Sverige)", "code": "se", "number": "46"},
  {"country": "Switzerland (Schweiz)", "code": "ch", "number": "41"},
  {"country": "Syria (سوريا)", "code": "sy", "number": "963"},
  {"country": "Taiwan (中国台灣)", "code": "tw", "number": "886"},
  {"country": "Tajikistan", "code": "tj", "number": "992"},
  {"country": "Tanzania", "code": "tz", "number": "255"},
  {"country": "Thailand (ไทย)", "code": "th", "number": "66"},
  {"country": "Timor-Leste", "code": "tl", "number": "670"},
  {"country": "Togo", "code": "tg", "number": "228"},
  {"country": "Tokelau", "code": "tk", "number": "690"},
  {"country": "Tonga", "code": "to", "number": "676"},
  {"country": "Trinidad and Tobago", "code": "tt", "number": "1"},
  {"country": "Tunisia (تونس)", "code": "tn", "number": "216"},
  {"country": "Turkey (Türkiye)", "code": "tr", "number": "90"},
  {"country": "Turkmenistan", "code": "tm", "number": "993"},
  {"country": "Turks and Caicos Islands", "code": "tc", "number": "1"},
  {"country": "Tuvalu", "code": "tv", "number": "688"},
  {"country": "U.S. Virgin Islands", "code": "vi", "number": "1"},
  {"country": "Uganda", "code": "ug", "number": "256"},
  {"country": "Ukraine (Україна)", "code": "ua", "number": "380"},
  {
    "country": "United Arab Emirates (الإمارات العربية المتحدة)",
    "code": "ae",
    "number": "971"
  },
  {"country": "United Kingdom", "code": "gb", "number": "44"},
  {"country": "United States", "code": "us", "number": "1"},
  {"country": "Uruguay", "code": "uy", "number": "598"},
  {"country": "Uzbekistan (Oʻzbekiston)", "code": "uz", "number": "998"},
  {"country": "Vanuatu", "code": "vu", "number": "678"},
  {
    "country": "Vatican City (Città del Vaticano)",
    "code": "va",
    "number": "39"
  },
  {"country": "Venezuela", "code": "ve", "number": "58"},
  {"country": "Vietnam (Việt Nam)", "code": "vn", "number": "84"},
  {
    "country": "Wallis and Futuna (Wallis-et-Futuna)",
    "code": "wf",
    "number": "681"
  },
  {
    "country": "Western Sahara (الصحراء الغربية)",
    "code": "eh",
    "number": "212"
  },
  {"country": "Yemen (اليمن)", "code": "ye", "number": "967"},
  {"country": "Zambia", "code": "zm", "number": "260"},
  {"country": "Zimbabwe", "code": "zw", "number": "263"},
  {"country": "Åland Islands", "code": "ax", "number": "358"}
];

final List<Country> countryList = countriesJson
    .map((item) => Country(
        country: item["country"].toString(), code: item["code"].toString(), number: item["number"].toString()))
    .toList();

bool isNumeric(String s) => s.isNotEmpty && int.tryParse(s.replaceAll("+", "")) != null;

extension CountryExtensions on List<Country> {
  /// 获取本地国家的区号
  String? getLocalCountryCode() {
    try {
      // 获取系统本地信息
      String locale = Platform.localeName; // 例如: en_US, zh_CN

      // 从本地信息中提取国家代码
      String countryCode = locale.split('_').last.toLowerCase();

      // 在国家列表中查找匹配的国家
      Country? matchedCountry = firstWhere(
        (country) => country.code.toLowerCase() == countryCode,
        orElse: () => Country(country: "", code: "", number: ""),
      );

      // 如果找到匹配的国家，则返回区号
      if (matchedCountry.country.isNotEmpty) {
        return matchedCountry.number;
      }

      return null;
    } catch (e) {
      // 处理可能的异常
      return null;
    }
  }

  /// 根据国家代码获取区号
  String? getCountryCodeByCode(String countryCode) {
    try {
      Country? matchedCountry = firstWhere(
        (country) => country.code.toLowerCase() == countryCode.toLowerCase(),
        orElse: () => Country(country: "", code: "", number: ""),
      );

      if (matchedCountry.country.isNotEmpty) {
        return matchedCountry.number;
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  List<Country> stringSearch(String search) {
    return where(
          (country) => isNumeric(search) || search.startsWith("+")
          ? country.number.contains(search)
          : country.country.toLowerCase().contains(search.toLowerCase()),
    ).toList();
  }
}