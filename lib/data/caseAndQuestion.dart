import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:new_agnes/api/StorageService.dart';
import 'package:new_agnes/module/home/<USER>/logic.dart';
import '../generated/l10n.dart';

class CaseData {
  final String content;
  final String url;
  final String title;
  final int id;
  final String image;

  CaseData(
      {required this.content,
      required this.url,
      required this.title,
      required this.id,
      required this.image});
}

enum SearchType {
  tools,
  search,
  research,
  deepResearch,
  aiSlides,
  aiDesign,;

  String name(BuildContext context) {
    switch (this) {
      case search:
        return S.of(context).search;
      case deepResearch:
        return S.of(context).deepResearch;
      case aiSlides:
        return S.of(context).aiSlides;
      case SearchType.research:
        return S.of(context).research;
      case SearchType.aiDesign:
        return S.of(context).aiDesign;
      case tools:
        return '';
    }
  }

  String get param {
    switch (this) {
      case search:
        return '1';
      case deepResearch:
        return '2';
      case aiSlides:
        return '3';
      case tools:
        return '0';
      case research:
        return '6';
      case SearchType.aiDesign:
        return '4';
    }
  }

  String get icon {
    switch (this) {
      case search:
        return 'assets/icon/icon_search.svg';
      case deepResearch:
        return 'assets/icon/icon_deep_research.svg';
      case aiSlides:
        return 'assets/icon/icon_ai_slides.svg';
      case tools:
        return 'assets/icon/icon_tools.svg';
      case research:
        return 'assets/icon/icon_tools.svg';
      case aiDesign:
        return 'assets/icon/icon_design.svg';
    }
  }
}

final List<String> questions = [
  "Studies in Singapore History",
  "NUS Application Guide",
  "2025 Music Trends",
  "AI Trends In SEA",
  "Weather today how?",
  "MRT crowded now?",
  "Last MRT what time?",
  "Where to top-up EZ-Link?",
  "CPF how to check?",
  "Where got cheap groceries?",
  "ERP how much now?",
  "Parking free or not?",
  "Best chicken rice where?",
  "Where to makan ah?",
  "Got halal food nearby?",
  "Any buffet promo now?",
  "Where to buy durian?",
  "Best bubble tea where?",
  "Nice cafe recommend?",
  "Hawker centre best ah?",
  "Any bar happy hour?",
  "Taxi fare to airport?",
  "How to go Orchard?",
  "Bus route to MBS?",
  "MRT breakdown or not?",
  "Where to rent bike?",
  "How to pay LTA fine?",
  "Where got part-time job?",
  "PR how to apply?",
  "SkillsFuture can use where?",
  "MOM latest rules what?",
  "Which tuition centre good?",
  "How to start business?",
];

final List<Map<String, Object>> localCaseData = [
  {
    "content": "Singapore B2B GenAI Client Prospects (Pre-Series B 2025)",
    "url": "https://app.agnes-ai.com/share?conversationid=1751611190576513",
    "title": "新加坡AI公司",
    "id": 1751611190576513,
    "image": "assets/images/case1.png"
  },
  {
    "content":
        "Detailed report on dephosphorization and denitrification in steel rolling",
    "url": "https://app.agnes-ai.com/share?conversationid=****************",
    "title": "轧钢除磷脱氮技术",
    "id": ****************,
    "image": "assets/images/case2.png"
  },
  {
    "content": "Development Trends of Elevator Guide-Rail Steel in China",
    "url": "https://app.agnes-ai.com/share?conversationid=****************",
    "title": "电梯钢发展历史",
    "id": ****************,
    "image": "assets/images/case3.png"
  },
  {
    "content":
        "Analysis of 2024 Annual Assets of Chinese Joint-Stock Commercial Banks",
    "url": "https://app.agnes-ai.com/share?conversationid=****************",
    "title": "中国股份制银行报告",
    "id": ****************,
    "image": "assets/images/case4.png"
  },
  {
    "content":
        "Investment Value Analysis of China's Non-Listed Defense Inertial Navigation Firms",
    "url": "https://app.agnes-ai.com/share?conversationid=****************",
    "title": "军工企业调研",
    "id": ****************,
    "image": "assets/images/case5.png"
  },
  {
    "content": "Adidas Sales Analysis on Shopee and Lazada",
    "url": "https://app.agnes-ai.com/share?conversationid=****************",
    "title": "阿迪达斯销售对比",
    "id": ****************,
    "image": "assets/images/case6.png"
  },
  {
    "content": "2025 Singapore Infectious Disease Prevalence Assessment",
    "url": "https://app.agnes-ai.com/share?conversationid=****************",
    "title": "新加坡疾病调研",
    "id": ****************,
    "image": "assets/images/case7.png"
  },
  {
    "content": "Asia-Pacific AI Program Review",
    "url": "https://app.agnes-ai.com/share?conversationid=1751619146384935",
    "title": "亚太AI专业调研",
    "id": 1751619146384935,
    "image": "assets/images/case8.png"
  },
  {
    "content": "Force Interaction Lesson Plan Design",
    "url": "http://app.agnes-ai.com/share?conversationid=1751619200730942",
    "title": "力的相互作用",
    "id": 1751619200730942,
    "image": "assets/images/case9.png"
  },
  {
    "content":
        "Asia-Pacific STEM University Rankings and Recommendations with Student Experience",
    "url": "https://app.agnes-ai.com/share?conversationid=1751620321689419",
    "title": "亚洲大学专业排行",
    "id": 1751620321689419,
    "image": "assets/images/case10.png"
  },
  {
    "content": "Plan Family Trip to Shanghai China",
    "url": "https://app.agnes-ai.com/share?conversationid=1751620331637425",
    "title": "上海旅行",
    "id": 1751620331637425,
    "image": "assets/images/case11.png"
  },
  {
    "content": "Compare EV vs Gas Car for Family Buyer",
    "url": "https://app.agnes-ai.com/share?conversationid=1752215417555360",
    "title": "买电车还是油车",
    "id": 1752215417555360,
    "image": "assets/images/case12.png"
  },
  {
    "content": "Find Apartment Near SGH Work",
    "url": "https://app.agnes-ai.com/share?conversationid=1751621304543289",
    "title": "租房",
    "id": 1751621304543289,
    "image": "assets/images/case13.png"
  },
  {
    "content": "Renewable Resources Report for Europe",
    "url": "https://app.agnes-ai.com/share?conversationid=1751621539642133",
    "title": "欧洲可再生资源调研",
    "id": 1751621539642133,
    "image": "assets/images/case14.png"
  },
  {
    "content": "Mideast Startup Report Prep",
    "url": "https://app.agnes-ai.com/share?conversationid=1751621687232178",
    "title": "中东科技公司",
    "id": 1751621687232178,
    "image": "assets/images/case15.png"
  },
  {
    "content": "Global Coffee Launch Plan for Southeast Asia Cities",
    "url": "https://app.agnes-ai.com/share?conversationid=1751621874879677",
    "title": "咖啡调研",
    "id": 1751621874879677,
    "image": "assets/images/case16.png"
  },
  {
    "content": "Buy vs Rent Home Decision for Shangri-La Employee",
    "url": "https://app.agnes-ai.com/share?conversationid=1752219869843240",
    "title": "租房还是买房",
    "id": 1752219869843240,
    "image": "assets/images/case17.png"
  },
  {
    "content": "Analyzing Health Impacts of Vegetarian Diets",
    "url": "https://app.agnes-ai.com/share?conversationid=1752215417555360",
    "title": "素食主义",
    "id": 1752215417555360,
    "image": "assets/images/case18.png"
  }
];

// 缓存热门问题
List<String> hotQuestions = [];

void setHotQuestions(List<String> questions) {
  int nowTimes = DateTime.now().millisecondsSinceEpoch;
  Get.find<StorageService>().setHotQuestionTimes(nowTimes);
  hotQuestions = questions;
  debugPrint("setHotQuestions");
}

List<String> getQuestions() {
  var shouldRefresh = shouldRefreshHotQuestions();
  if (shouldRefresh) {
    debugPrint("getHotQuestions");
    Get.find<RolesLogic>().getHotNews();
  }
  final List<String> shuffled =
      List.from(hotQuestions.isEmpty ? questions : hotQuestions)
        ..shuffle(Random());
  return shuffled.take(3).toList();
}

bool shouldRefreshHotQuestions() {
  int nowTimes = DateTime.now().millisecondsSinceEpoch;
  var lastTimes = Get.find<StorageService>().getHotQuestionTimes();
  if (lastTimes == 0) return true;
  int differenceInMinutes = (nowTimes - lastTimes) ~/ (1000 * 60);
  debugPrint("differenceInMinutes:${differenceInMinutes}");
  return differenceInMinutes >= 60;
}

List<Map<String, Object>> getCaseData() {
  final List<Map<String, Object>> shuffled = List.from(localCaseData)
    ..shuffle(Random());
  return shuffled.take(6).toList();
}

final List<CaseData> caseDataList = getCaseData()
    .map((item) => CaseData(
        content: item["content"].toString(),
        url: item["url"].toString(),
        title: item["title"].toString(),
        id: item["id"] as int,
        image: item["image"].toString()))
    .toList();
