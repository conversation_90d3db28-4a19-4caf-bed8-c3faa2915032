{"api": {"api_key": "5bb29c55148eb192bd68e2b00ff3f8a3", "base_url": "https://api.pgyer.com/apiv2"}, "build": {"build_type": "android", "build_install_type": 1, "build_password": "", "build_description": "<PERSON>lut<PERSON> App", "build_update_description": "新版本更新-测试脚本", "build_install_date": 2, "build_install_start_date": "", "build_install_end_date": "", "build_channel_shortcut": "", "oversea": ""}, "flutter": {"build_command": "flutter build apk --release --flavor=googleplay", "apk_path": "build/app/outputs/flutter-apk/app-googleplay-release.apk", "clean_before_build": true, "get_dependencies": true}, "upload": {"max_retries": 3, "retry_delay": 5, "check_interval": 3, "max_check_attempts": 60}}