## 0.5.0
- Update gradle version
- Fix `NullPointerException` in OverlayService


## 0.4.5
- Added instructions for android 14 compatibility

## 0.4.4
- Fix overlay close crash  
- Add `startPosition`: start overlay in default position  
- Add `moveOverlay`: Update the overlay position in the screen  
- Add `getOverlayPosition`: Get the current overlay position

## 0.4.3
- Fix overlay height bug

## 0.4.2
- Fix touch freeze

## 0.4.1
- Remove secure flag  
- Detach view from engine after closing  
- Fix Example to show (Sending data between Main & overlay)

## 0.3.3
- Fix bugs related to android 12+  
- Some code optimizations  
- Fix overlay popup on top of status bar  
- Fix overlay closing

## 0.3.2
- Add the position gravity feature

## 0.3.1
- Fix the overlay permission on Android versions <= 6  
- Add the possibility to resize overlay while it's in action

## 0.2.9
- Fix closing overlay  
- Possibility to check if the overlay is active or not

## 0.2.8
- Change overlay flags names

## 0.2.7
- Fix overlay issue to target all SDK versions  
- Add `overlayTitle` and `overlayContent` arguments  
- Fix typo

## 0.2.2
- Add custom notification content text  
- Improve the `flagNotFocusable`  
- Update example

## 0.2.1
- Add flag update on runtime

## 0.0.2
- Fix keyboard not showing on TextFields

## 0.0.1
- Initial release
