name: markdown_widget
description: A new markdown package. It supports TOC function, code highlighting, and it supports all platforms
version: 2.3.2+8
homepage: https://github.com/asjqkkkk/markdown_widget

topics:
  - markdown
  - flutter
  - widgets

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  #markdown data parser
  markdown: ^7.2.1
  #highlight code
  highlight: ^0.7.0
  flutter_highlight: ^0.7.0


  # to click link
  url_launcher: ^6.3.1
  # to get the index number on scroll for listview
  visibility_detector: ^0.4.0+2
  # make listview available to jump to index
  scroll_to_index: ^3.0.1


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.1
  mockito: ^5.3.2
  build_runner: ^2.3.3
  path: ^1.8.2
  lints: ^2.1.1
flutter:
  assets:
    - assets/images/


screenshots:
  - description: 'Screenshot 1'
    path: screenshots/screenshot.png