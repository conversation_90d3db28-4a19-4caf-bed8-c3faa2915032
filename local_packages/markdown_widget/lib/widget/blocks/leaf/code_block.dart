import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_highlight/themes/a11y-dark.dart';
import 'package:flutter_highlight/themes/a11y-light.dart';
import 'package:highlight/highlight.dart' as hi;
import 'package:markdown_widget/markdown_widget.dart';
import 'package:markdown/markdown.dart' as m;

///Tag: [MarkdownTag.pre]
///
///An indented code block is composed of one or more indented chunks separated by blank lines
///A code fence is a sequence of at least three consecutive backtick characters (`) or tildes (~)
class CodeBlockNode extends ElementNode {
  CodeBlockNode(this.element, this.preConfig, this.visitor);

  String get content => element.textContent;
  final PreConfig preConfig;
  final m.Element element;
  final WidgetVisitor visitor;

  @override
  InlineSpan build() {
    String? language = preConfig.language;
    try {
      final languageValue =
          (element.children?.first as m.Element).attributes['class']!;
      language = languageValue.split('-').last;
    } catch (e) {
      language = null;
      debugPrint('get language error:$e');
    }
    final splitContents = content
        .trim()
        .split(visitor.splitRegExp ?? WidgetVisitor.defaultSplitRegExp);
    if (splitContents.last.isEmpty) splitContents.removeLast();
    final codeBuilder = preConfig.builder;
    if (codeBuilder != null) {
      return WidgetSpan(child: codeBuilder.call(content, language ?? ''));
    }
    final widget = Container(
      margin: preConfig.margin,
      child: _GradientBorderContainer.single(
        strokeWidth: 1,
        borderRadius: BorderRadius.circular(10),
        gradient: const LinearGradient(
          colors: [
            Color(0xFF7253FA),
            Color(0xFFFF3BDF),
            Color(0xFF5E57FE),
          ],
          stops: [0.0, 0.32, 1.0],
          begin: Alignment.bottomLeft,
          end: Alignment.topRight,
          tileMode: TileMode.decal,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Color(0x6042768F),
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10)),
                image: DecorationImage(
                  image: AssetImage(
                    "assets/images/ic_code_bg.png",
                    package: "markdown_widget",
                  ),
                  fit: BoxFit.cover,
                ),
              ),
              child: Row(
                children: [
                  Text(
                    (language ?? "").toUpperCase(),
                    style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFFFF3BDF),
                        fontWeight: FontWeight.w500),
                  ),
                  Spacer(),
                  Image.asset(
                    "assets/images/ic_code_copy.png",
                    package: "markdown_widget",
                    width: 20,
                    height: 20,
                  ),
                  SizedBox(
                    width: 8,
                  ),
                  GestureDetector(
                    onTap: () async{
                      preConfig.copyCallback?.call(content);
                    },
                    child: Text(
                      "Copy",
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: Colors.white),
                    ),
                  )
                ],
              ),
            ),
            Container(
              padding: preConfig.padding,
              width: double.infinity,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: List.generate(splitContents.length, (index) {
                    final currentContent = splitContents[index];
                    return ProxyRichText(
                      TextSpan(
                        text: "${index + 1}  $currentContent",
                        style: preConfig.codeStyle
                        // children: highLightSpans(
                        //   "${index + 1}  $currentContent",
                        //   language: language ?? preConfig.language,
                        //   theme: preConfig.theme,
                        //   textStyle: style,
                        //   styleNotMatched: preConfig.styleNotMatched,
                        // ),
                      ),
                      richTextBuilder: visitor.richTextBuilder,
                    );
                  }),
                ),
              ),
            ),
          ],
        ),
      ),
    );
    return WidgetSpan(
        child:
            preConfig.wrapper?.call(widget, content, language ?? '') ?? widget);
  }

  @override
  TextStyle get style => preConfig.textStyle.merge(parentStyle);
}

///transform code to highlight code
List<InlineSpan> highLightSpans(
  String input, {
  String? language,
  bool autoDetectionLanguage = false,
  Map<String, TextStyle> theme = const {},
  TextStyle? textStyle,
  TextStyle? styleNotMatched,
  int tabSize = 8,
}) {
  return convertHiNodes(
      hi.highlight
          .parse(input.trimRight(),
              language: autoDetectionLanguage ? null : language,
              autoDetection: autoDetectionLanguage)
          .nodes!,
      theme,
      textStyle,
      styleNotMatched);
}

List<TextSpan> convertHiNodes(
  List<hi.Node> nodes,
  Map<String, TextStyle> theme,
  TextStyle? style,
  TextStyle? styleNotMatched,
) {
  List<TextSpan> spans = [];
  var currentSpans = spans;
  List<List<TextSpan>> stack = [];

  void traverse(hi.Node node, TextStyle? parentStyle) {
    final nodeStyle = parentStyle ?? theme[node.className ?? ''];
    final finallyStyle = (nodeStyle ?? styleNotMatched)?.merge(style);
    if (node.value != null) {
      currentSpans.add(node.className == null
          ? TextSpan(text: node.value, style: finallyStyle)
          : TextSpan(text: node.value, style: finallyStyle));
    } else if (node.children != null) {
      List<TextSpan> tmp = [];
      currentSpans.add(TextSpan(children: tmp, style: finallyStyle));
      stack.add(currentSpans);
      currentSpans = tmp;

      for (var n in node.children!) {
        traverse(n, nodeStyle);
        if (n == node.children!.last) {
          currentSpans = stack.isEmpty ? spans : stack.removeLast();
        }
      }
    }
  }

  for (var node in nodes) {
    traverse(node, null);
  }
  return spans;
}

///config class for pre
class PreConfig implements LeafConfig {
  final EdgeInsetsGeometry padding;
  final Decoration decoration;
  final EdgeInsetsGeometry margin;
  final TextStyle textStyle;

  /// the [styleNotMatched] is used to set a default TextStyle for code that does not match any theme.
  final TextStyle? styleNotMatched;
  final CodeWrapper? wrapper;
  final CodeBuilder? builder;

  ///see package:flutter_highlight/themes/
  final Map<String, TextStyle> theme;
  final String language;
  final ValueChanged<String>? copyCallback;
  final TextStyle codeStyle;

  const PreConfig({
    this.padding = const EdgeInsets.all(16.0),
    this.decoration = const BoxDecoration(
      color: Color(0xffeff1f3),
      borderRadius: BorderRadius.all(Radius.circular(8.0)),
    ),
    this.margin = const EdgeInsets.symmetric(vertical: 8.0),
    this.textStyle = const TextStyle(fontSize: 16),
    this.styleNotMatched,
    this.theme = a11yLightTheme,
    this.language = 'dart',
    this.wrapper,
    this.builder,
    this.copyCallback,
    this.codeStyle = const TextStyle(
        fontSize: 16, color: Colors.white, fontWeight: FontWeight.w400),
  }) : assert(builder == null || wrapper == null);

  static PreConfig get darkConfig => const PreConfig(
        decoration: BoxDecoration(
          color: Color(0xff555555),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        theme: a11yDarkTheme,
      );

  ///copy by other params
  PreConfig copy({
    EdgeInsetsGeometry? padding,
    Decoration? decoration,
    EdgeInsetsGeometry? margin,
    TextStyle? textStyle,
    TextStyle? styleNotMatched,
    CodeWrapper? wrapper,
    Map<String, TextStyle>? theme,
    String? language,
  }) {
    return PreConfig(
      padding: padding ?? this.padding,
      decoration: decoration ?? this.decoration,
      margin: margin ?? this.margin,
      textStyle: textStyle ?? this.textStyle,
      styleNotMatched: styleNotMatched ?? this.styleNotMatched,
      wrapper: wrapper ?? this.wrapper,
      theme: theme ?? this.theme,
      language: language ?? this.language,
    );
  }

  @nonVirtual
  @override
  String get tag => MarkdownTag.pre.name;
}

typedef CodeWrapper = Widget Function(
  Widget child,
  String code,
  String language,
);

typedef CodeBuilder = Widget Function(String code, String language);

class _GradientBorderContainer extends StatefulWidget {
  final double strokeWidth;
  final List<Gradient> gradients;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final Widget? child;

  const _GradientBorderContainer({
    Key? key,
    required this.strokeWidth,
    required this.gradients,
    this.width,
    this.height,
    this.borderRadius = BorderRadius.zero,
    this.child,
  }) : super(key: key);

  // 为了向后兼容，提供一个命名构造函数来接收单个gradient
  _GradientBorderContainer.single({
    Key? key,
    required double strokeWidth,
    required Gradient gradient,
    double? width,
    double? height,
    BorderRadius borderRadius = BorderRadius.zero,
    Widget? child,
  }) : this(
          key: key,
          strokeWidth: strokeWidth,
          gradients: [gradient],
          width: width,
          height: height,
          borderRadius: borderRadius,
          child: child,
        );

  @override
  State<_GradientBorderContainer> createState() =>
      _GradientBorderContainerState();
}

class _GradientBorderContainerState extends State<_GradientBorderContainer> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SizedBox(
          width: widget.width,
          height: widget.height,
          child: CustomPaint(
            painter: _GradientBorderPainter(
              strokeWidth: widget.strokeWidth,
              gradients: widget.gradients,
              borderRadius: widget.borderRadius,
            ),
            child: Padding(
              padding: EdgeInsets.zero,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

class _GradientBorderPainter extends CustomPainter {
  final double strokeWidth;
  final List<Gradient> gradients;
  final BorderRadius borderRadius;

  _GradientBorderPainter({
    required this.strokeWidth,
    required this.gradients,
    this.borderRadius = BorderRadius.zero,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (gradients.isEmpty) return;

    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // 如果只有一个渐变，使用原始方法
    if (gradients.length == 1) {
      final rrect = RRect.fromRectAndCorners(
        rect.inflate(strokeWidth / 2),
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );

      final paint = Paint()
        ..shader = gradients[0].createShader(rect)
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke;

      canvas.drawRRect(rrect, paint);
    } else {
      // 多个渐变的情况 - 创建融合效果
      final outerRect = rect.inflate(strokeWidth / 2);
      final innerRect = rect.deflate(strokeWidth / 2);

      final outerRRect = RRect.fromRectAndCorners(
        outerRect,
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );

      final innerRRect = RRect.fromRectAndCorners(
        innerRect,
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );

      // 创建边框路径
      final borderPath = Path()
        ..addRRect(outerRRect)
        ..addRRect(innerRRect)
        ..fillType = PathFillType.evenOdd;

      canvas.save();
      canvas.clipPath(borderPath);

      // 使用多个图层混合实现渐变融合效果
      final paint = Paint();

      // 计算每个渐变的角度偏移
      final angleStep = 360.0 / gradients.length;

      for (int i = 0; i < gradients.length; i++) {
        // 创建一个覆盖整个边框区域的矩形
        final gradientRect = Rect.fromLTWH(
          -strokeWidth / 2,
          -strokeWidth / 2,
          size.width + strokeWidth,
          size.height + strokeWidth,
        );

        paint
          ..shader = gradients[i].createShader(gradientRect)
          ..style = PaintingStyle.fill
          ..blendMode = BlendMode.srcOver;

        // 对于多个渐变，使用部分透明度实现融合
        if (gradients.length > 1) {
          paint.color = Colors.white.withOpacity(1.0 / gradients.length);
        }

        canvas.drawRect(gradientRect, paint);
      }

      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is! _GradientBorderPainter) return false;
    return strokeWidth != oldDelegate.strokeWidth ||
        gradients != oldDelegate.gradients ||
        borderRadius != oldDelegate.borderRadius;
  }
}
