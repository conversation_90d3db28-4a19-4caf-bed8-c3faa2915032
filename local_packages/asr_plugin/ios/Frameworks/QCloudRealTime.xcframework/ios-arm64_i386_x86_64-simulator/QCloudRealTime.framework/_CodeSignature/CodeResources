<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/QCloudAudioDataSource.h</key>
		<data>
		YEhxG6dlVGIs0SeEBCun6xSDO5o=
		</data>
		<key>Headers/QCloudAudioRecorderDataSource.h</key>
		<data>
		PhVtAf0iA+4zHOjCIq4BZvCp8NM=
		</data>
		<key>Headers/QCloudCommonParams.h</key>
		<data>
		2tIDEEBS3rW6kK7cQIJbV4P8eWU=
		</data>
		<key>Headers/QCloudConfig.h</key>
		<data>
		JwzDwphpfR627lnC6SYcZ4Ko/cM=
		</data>
		<key>Headers/QCloudRealTimeRecognizer.h</key>
		<data>
		IC0fRlNgZ9sDKHcQvedAwGJe5So=
		</data>
		<key>Headers/QCloudRealTimeResult.h</key>
		<data>
		D082CoZUc/7O07H/S/6B0wvgc84=
		</data>
		<key>Headers/sdk_version.h</key>
		<data>
		1twH/U/CDyoaOi1HLNlD/oj9WIk=
		</data>
		<key>Info.plist</key>
		<data>
		duBKk0BrmEyb7Y7t+Zo/XjVD9WE=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/QCloudAudioDataSource.h</key>
		<dict>
			<key>hash</key>
			<data>
			YEhxG6dlVGIs0SeEBCun6xSDO5o=
			</data>
			<key>hash2</key>
			<data>
			F0tAF+1hAr+SB42WkpusPPwUznOPjVW0U9XIRDnqJGA=
			</data>
		</dict>
		<key>Headers/QCloudAudioRecorderDataSource.h</key>
		<dict>
			<key>hash</key>
			<data>
			PhVtAf0iA+4zHOjCIq4BZvCp8NM=
			</data>
			<key>hash2</key>
			<data>
			NMo9VeUheGEL3grX75fB8fEArA+s+/5a7RHBJZHNbnE=
			</data>
		</dict>
		<key>Headers/QCloudCommonParams.h</key>
		<dict>
			<key>hash</key>
			<data>
			2tIDEEBS3rW6kK7cQIJbV4P8eWU=
			</data>
			<key>hash2</key>
			<data>
			Xaz5P9oss2qS4NAwmmRUhNSgAvpxWjA1KDWMfZvhlP4=
			</data>
		</dict>
		<key>Headers/QCloudConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			JwzDwphpfR627lnC6SYcZ4Ko/cM=
			</data>
			<key>hash2</key>
			<data>
			qTF44GMQX0pzbHTXlf99Cyf7VmWleeX2ykA0uzBdL5c=
			</data>
		</dict>
		<key>Headers/QCloudRealTimeRecognizer.h</key>
		<dict>
			<key>hash</key>
			<data>
			IC0fRlNgZ9sDKHcQvedAwGJe5So=
			</data>
			<key>hash2</key>
			<data>
			nB9O8dfd1xbByb5JiEEAMBRrlTFSgz+yXH2v/mWfZhc=
			</data>
		</dict>
		<key>Headers/QCloudRealTimeResult.h</key>
		<dict>
			<key>hash</key>
			<data>
			D082CoZUc/7O07H/S/6B0wvgc84=
			</data>
			<key>hash2</key>
			<data>
			4rsQ/XnV0doKytr8lF1jU3HRdhdEEz/3rAggjcI+ynw=
			</data>
		</dict>
		<key>Headers/sdk_version.h</key>
		<dict>
			<key>hash</key>
			<data>
			1twH/U/CDyoaOi1HLNlD/oj9WIk=
			</data>
			<key>hash2</key>
			<data>
			OhQpukxTd4OXlacXC4cUWpOuVN9dAxpq7ifYlO+idXQ=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
