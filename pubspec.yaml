name: new_agnes
description: "A new agnes project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.1.244+2001244
#version: 0.0.1+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  #  http: ^1.4.0
  get: ^4.7.2
  get_storage: ^2.1.0
  flutter_screenutil: ^5.0.0+1
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  share_plus: ^11.0.0
  google_api_availability: ^5.0.1
  flutter_staggered_grid_view: ^0.6.2  # 瀑布流布局
  pull_to_refresh: ^2.0.0  # 下拉刷新和上拉加载
  flutter_svg: ^2.2.0
  cached_network_image: ^3.2.0 #网络图片
  permission_handler: 11.3.1 #权限
  image_cropper: ^9.1.0 #图片裁剪
  image_picker: 1.1.2 #图片裁剪
  device_info_plus: ^11.3.0 #设备信息
  path: ^1.8.0
  mime: ^1.0.4
  http_parser: ^4.0.2
  dio: ^5.8.0+1
  image: ^4.1.0
  flutter_easyloading: ^3.0.5 #loading
  markdown_widget:
    path: ./local_packages/markdown_widget
  google_sign_in: ^6.0.0
  lottie: ^3.3.1
#  sign_in_with_apple: ^7.0.1
  flutter_inappwebview: ^6.1.5 #web
  android_path_provider: ^0.3.1
  logger: any
  flutter_blue_plus: ^1.35.5
  url_launcher: ^6.1.11
  flutter_staggered_animations: ^1.1.1
  # singular 渠道统计归因包
  singular_flutter_sdk: ^1.8.0
  in_app_update: 4.2.3
  package_info_plus: ^8.3.0
#  pdfx: ^2.9.2 #pdf渲染
  syncfusion_flutter_pdfviewer: 29.1.38
  open_file: ^3.5.10
  flutter_tilt: ^3.3.1 #视差
  encrypt: ^5.0.3
  flutter_spinkit: ^5.2.2
  vibration: ^3.1.3 #振动
  agora_chat_sdk: ^1.3.3
  agora_rtc_engine: ^6.5.2
  flutter_slidable: ^3.0.0
  connectivity_plus: ^6.1.4
  lpinyin: ^2.0.2
  popover: ^0.3.1
  contacts_service_plus: ^0.0.2  # 通讯录访问
  flutter_overlay_window:
    path: ./local_packages/flutter_overlay_window  # 悬浮窗
  asr_plugin:
    path: ./local_packages/asr_plugin  # 腾讯语音识别
  protobuf: ^3.1.0
  mic_stream: ^0.7.2
  firebase_core: ^4.1.0
  firebase_analytics: ^12.0.1
  video_thumbnail: ^0.5.6  # 视频缩略图生成
  better_player_plus: ^1.0.8
  image_gallery_saver_plus: ^4.0.1
  timezone: ^0.10.1
  flutter_libphonenumber: ^2.5.1
  scrollview_observer: ^1.26.2
  marquee: 2.3.0
  blurrycontainer: ^2.1.0


dependency_overrides:
  intl: ^0.20.2
  http: ^1.0.0
  collection: ^1.19.1
dev_dependencies:
  intl_utils: ^2.8.8
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
#  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/images/
    - assets/icon/
    - assets/json/
    - assets/groupChat/

  generate: true

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  #  fonts:
  #    - family: PressStart2P
  #      fonts:
  #        - asset: assets/font/PressStart2P-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
flutter_intl:
  enabled: true
