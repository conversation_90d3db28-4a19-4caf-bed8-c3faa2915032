package com.sobrr.agnes;

import android.content.Context;
import android.media.AudioManager;
import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

public class BluetoothAudioPlugin implements FlutterPlugin, MethodCallHandler {
    private MethodChannel channel;
    private Context context;
    private AudioManager audioManager;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "bluetooth_audio");
        channel.setMethodCallHandler(this);
        this.context = flutterPluginBinding.getApplicationContext();
        this.audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        switch (call.method) {
            case "startBluetoothSco":
                startBluetoothSco(result);
                break;
            case "stopBluetoothSco":
                stopBluetoothSco(result);
                break;
            case "isBluetoothScoOn":
                isBluetoothScoOn(result);
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    private void startBluetoothSco(Result result) {
        try {
            audioManager.startBluetoothSco();
            audioManager.setBluetoothScoOn(true);
            result.success(true);
        } catch (Exception e) {
            result.error("START_SCO_ERROR", e.getMessage(), null);
        }
    }

    private void stopBluetoothSco(Result result) {
        try {
            audioManager.stopBluetoothSco();
            audioManager.setBluetoothScoOn(false);
            result.success(true);
        } catch (Exception e) {
            result.error("STOP_SCO_ERROR", e.getMessage(), null);
        }
    }

    private void isBluetoothScoOn(Result result) {
        try {
            boolean isOn = audioManager.isBluetoothScoOn();
            result.success(isOn);
        } catch (Exception e) {
            result.error("CHECK_SCO_ERROR", e.getMessage(), null);
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
    }
}
