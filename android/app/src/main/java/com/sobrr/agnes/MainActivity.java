package com.sobrr.agnes;

import android.os.Bundle;
import androidx.annotation.NonNull;

import io.flutter.embedding.engine.renderer.FlutterRenderer;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;

public class MainActivity extends FlutterActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        System.setProperty("io.flutter.embedding.android.EnableVulkan", "false");
        System.setProperty("io.flutter.embedding.android.EnableImpeller", "false");
        super.onCreate(savedInstanceState);
    }

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        // 使用 FlutterEngine 获取 FlutterPluginBinding 来注册插件
        flutterEngine.getPlugins().add(new BluetoothAudioPlugin());
        // 注册Azure语音插件
        flutterEngine.getPlugins().add(new AzureSpeechPlugin());
    }
}
