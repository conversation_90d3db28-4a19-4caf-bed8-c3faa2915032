package com.sobrr.agnes;

import android.content.Context;
import android.media.AudioManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

import com.microsoft.cognitiveservices.speech.*;
import com.microsoft.cognitiveservices.speech.audio.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AzureSpeechPlugin implements FlutterPlugin, MethodChannel.MethodCallHandler {
    private static final String TAG = "AzureSpeechPlugin";

    private MethodChannel methodChannel;
    private MethodChannel callbackChannel;
    private Context context;

    private SpeechRecognizer speechRecognizer;
    private SpeechConfig speechConfig;

    // 音频配置相关
    private PushAudioInputStream pushAudioInputStream;
    private AudioConfig audioConfig;
    private String currentMode = "normal"; // 记录当前模式

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        this.context = flutterPluginBinding.getApplicationContext();

        // 创建方法通道
        methodChannel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "azure_speech_recognizer");
        methodChannel.setMethodCallHandler(this);

        // 创建回调通道
        callbackChannel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "azure_speech_callback");
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        switch (call.method) {
            case "initializeRecognizer":
                initializeRecognizer(call, result);
                break;
            case "startContinuousRecognition":
                startContinuousRecognition(result);
                break;
            case "stopContinuousRecognition":
                stopContinuousRecognition(result);
                break;
            case "closeRecognizer":
                closeRecognizer(result);
                break;
            case "switchMode": // 新增：切换模式方法
                switchMode(call, result);
                break;
            case "pushAudioData":
                pushAudioData(call, result);
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    private void initializeRecognizer(MethodCall call, MethodChannel.Result result) {
        try {
            String subscriptionKey = call.argument("subscriptionKey");
            String region = call.argument("region");
            List<String> language = call.argument("language");
            String mode = call.argument("mode"); // 识别模式 ("normal" 或 "rtc")

            if (subscriptionKey == null || region == null) {
                result.error("INVALID_ARGUMENTS", "Subscription key and region are required", null);
                return;
            }

            // 如果已经初始化过，先清理旧资源
            cleanupRecognizerResources();

            // 创建语音配置
            speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);

            if (language == null || language.isEmpty()) {
                Log.i(TAG, "使用默认语言: zh-CN,en-US");
                language = new ArrayList<>();
                language.add("zh-CN");
                language.add("en-US");
            }

            // 配置多语言
            AutoDetectSourceLanguageConfig autoDetectSourceLanguageConfig = AutoDetectSourceLanguageConfig.fromLanguages(language);

            // 根据模式选择音频输入方式
            setupAudioConfig(mode);

            // 创建语音识别器
            speechRecognizer = new SpeechRecognizer(speechConfig, autoDetectSourceLanguageConfig, audioConfig);
            currentMode = mode;

            // 设置事件监听器
            setupEventListeners();

            result.success(true);
            Log.i(TAG, "语音识别器初始化成功，模式: " + mode);
        } catch (Exception e) {
            Log.e(TAG, "初始化语音识别器失败", e);
            result.error("INIT_ERROR", e.getMessage(), null);
        }
    }

    // 新增：设置音频配置
    private void setupAudioConfig(String mode) throws Exception {
        if ("rtc".equals(mode)) {
            // RTC模式：使用推送音频流
            AudioStreamFormat audioFormat = AudioStreamFormat.getWaveFormatPCM(16000, (short) 16, (short) 2);
            pushAudioInputStream = AudioInputStream.createPushStream(audioFormat);
            audioConfig = AudioConfig.fromStreamInput(pushAudioInputStream);
            Log.i(TAG, "RTC模式：使用推送音频流");
        } else {
            // 普通模式：使用默认麦克风输入
            audioConfig = AudioConfig.fromDefaultMicrophoneInput();
            Log.i(TAG, "普通模式：使用默认麦克风输入");
        }
    }

    // 新增：切换模式
    private void switchMode(MethodCall call, MethodChannel.Result result) {
        try {
            String mode = call.argument("mode");
            if (mode == null) {
                result.error("INVALID_ARGUMENTS", "Mode is required", null);
                return;
            }

            // 如果模式相同，无需切换
            if (mode.equals(currentMode)) {
                result.success(true);
                return;
            }

            // 检查是否已初始化
            if (speechConfig == null || speechRecognizer == null) {
                result.error("NOT_INITIALIZED", "Recognizer not initialized", null);
                return;
            }

            // 停止当前识别（如果正在进行）
            if (speechRecognizer != null) {
                speechRecognizer.stopContinuousRecognitionAsync().get();
            }

            // 保存当前配置
            AutoDetectSourceLanguageConfig currentLanguageConfig = getCurrentLanguageConfig();

            // 清理音频相关资源
            cleanupAudioResources();

            // 重新设置音频配置
            setupAudioConfig(mode);

            // 重新创建语音识别器
            speechRecognizer = new SpeechRecognizer(speechConfig, currentLanguageConfig, audioConfig);
            currentMode = mode;

            // 重新设置事件监听器
            setupEventListeners();

            result.success(true);
            Log.i(TAG, "模式切换成功: " + mode);
        } catch (Exception e) {
            Log.e(TAG, "切换模式失败", e);
            result.error("SWITCH_MODE_ERROR", e.getMessage(), null);
        }
    }

    // 辅助方法：获取当前语言配置
    private AutoDetectSourceLanguageConfig getCurrentLanguageConfig() {
        // 这里简化处理，实际应用中可能需要保存语言配置
        List<String> languages = new ArrayList<>();
        languages.add("zh-CN");
        languages.add("en-US");
        return AutoDetectSourceLanguageConfig.fromLanguages(languages);
    }

    private void setupEventListeners() {
        if (speechRecognizer == null) return;

        // 正在识别中的事件（中间结果）
        speechRecognizer.recognizing.addEventListener((o, speechRecognitionResultEventArgs) -> {
            final String text = speechRecognitionResultEventArgs.getResult().getText();
            Log.i(TAG, "正在识别中: " + text);

            // 使用 MethodChannel 发送回调
            if (callbackChannel != null) {
                Map<String, Object> event = new HashMap<>();
                event.put("event", "recognizing");
                event.put("text", text);

                // 在主线程执行
                new Handler(Looper.getMainLooper()).post(() -> {
                    callbackChannel.invokeMethod("onSpeechEvent", event);
                });
            }
        });

        // 识别完成的事件（最终结果）
        speechRecognizer.recognized.addEventListener((o, speechRecognitionResultEventArgs) -> {
            final String text = speechRecognitionResultEventArgs.getResult().getText();
            final ResultReason reason = speechRecognitionResultEventArgs.getResult().getReason();
            Log.i(TAG, "识别完成: " + text + ", Reason: " + reason);

            // 使用 MethodChannel 发送回调
            if (callbackChannel != null) {
                Map<String, Object> event = new HashMap<>();
                event.put("event", "recognized");
                event.put("text", text);
                event.put("reason", reason.toString());

                // 在主线程执行
                new Handler(Looper.getMainLooper()).post(() -> {
                    callbackChannel.invokeMethod("onSpeechEvent", event);
                });
            }
        });

        // 会话开始事件
        speechRecognizer.sessionStarted.addEventListener((o, sessionEventArgs) -> {
            Log.i(TAG, "会话开始");
            if (callbackChannel != null) {
                Map<String, Object> event = new HashMap<>();
                event.put("event", "sessionStarted");
                new Handler(Looper.getMainLooper()).post(() -> {
                    callbackChannel.invokeMethod("onSpeechEvent", event);
                });
            }
        });

        // 会话结束事件
        speechRecognizer.sessionStopped.addEventListener((o, sessionEventArgs) -> {
            Log.i(TAG, "会话结束");
            if (callbackChannel != null) {
                Map<String, Object> event = new HashMap<>();
                event.put("event", "sessionStopped");
                new Handler(Looper.getMainLooper()).post(() -> {
                    callbackChannel.invokeMethod("onSpeechEvent", event);
                });
            }
        });

        // 识别取消事件
        speechRecognizer.canceled.addEventListener((o, canceledEventArgs) -> {
            String cancellationDetails = CancellationDetails.fromResult(canceledEventArgs.getResult()).toString();
            Log.i(TAG, "识别被取消: " + cancellationDetails);

            if (callbackChannel != null) {
                Map<String, Object> event = new HashMap<>();
                event.put("event", "canceled");
                event.put("reason", canceledEventArgs.getReason().toString());
                event.put("details", cancellationDetails);
                new Handler(Looper.getMainLooper()).post(() -> {
                    callbackChannel.invokeMethod("onSpeechEvent", event);
                });
            }
        });
    }

    private void startContinuousRecognition(MethodChannel.Result result) {
        try {
            if (speechRecognizer != null) {
                speechRecognizer.startContinuousRecognitionAsync();
                result.success(true);
                Log.i(TAG, "开始连续识别");
            } else {
                result.error("NOT_INITIALIZED", "语音识别器未初始化", null);
            }
        } catch (Exception e) {
            Log.e(TAG, "启动连续识别失败", e);
            result.error("START_ERROR", e.getMessage(), null);
        }
    }

    private void stopContinuousRecognition(MethodChannel.Result result) {
        try {
            if (speechRecognizer != null) {
                speechRecognizer.stopContinuousRecognitionAsync();
                result.success(true);
                Log.i(TAG, "停止连续识别");
            } else {
                result.error("NOT_INITIALIZED", "语音识别器未初始化", null);
            }
        } catch (Exception e) {
            Log.e(TAG, "停止连续识别失败", e);
            result.error("STOP_ERROR", e.getMessage(), null);
        }
    }

    private void closeRecognizer(MethodChannel.Result result) {
        try {
            cleanupRecognizerResources();
            result.success(true);
            Log.i(TAG, "语音识别器已关闭");
        } catch (Exception e) {
            Log.e(TAG, "关闭语音识别器失败", e);
            result.error("CLOSE_ERROR", e.getMessage(), null);
        }
    }

    // 新增：清理识别器资源
    private void cleanupRecognizerResources() {
        // 关闭语音识别器
        if (speechRecognizer != null) {
            try {
                speechRecognizer.close();
            } catch (Exception e) {
                Log.e(TAG, "关闭语音识别器失败", e);
            }
            speechRecognizer = null;
        }

        // 清理音频资源
        cleanupAudioResources();

        // 清理配置
        speechConfig = null;
        currentMode = "normal";
    }

    // 新增：清理音频资源
    private void cleanupAudioResources() {
        // 关闭推送音频流
        if (pushAudioInputStream != null) {
            try {
                pushAudioInputStream.close();
            } catch (Exception e) {
                Log.e(TAG, "关闭音频输入流失败", e);
            }
            pushAudioInputStream = null;
        }

        // 关闭音频配置
        if (audioConfig != null) {
            try {
                audioConfig.close();
            } catch (Exception e) {
                Log.e(TAG, "关闭音频配置失败", e);
            }
            audioConfig = null;
        }
    }

    private void pushAudioData(MethodCall call, MethodChannel.Result result) {
        try {
            byte[] audioData = call.argument("audioData");
//            Log.e(TAG, "推送音频数据长度：" + audioData.length);
            if (pushAudioInputStream != null && audioData != null) {
                pushAudioInputStream.write(audioData);
                result.success(true);
            } else {
                result.error("INVALID_STATE", "Audio stream not initialized or audio data is null", null);
            }
        } catch (Exception e) {
            Log.e(TAG, "推送音频数据失败", e);
            result.error("PUSH_AUDIO_ERROR", e.getMessage(), null);
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        // 清理所有资源
        cleanupRecognizerResources();

        if (methodChannel != null) {
            methodChannel.setMethodCallHandler(null);
            methodChannel = null;
        }

        if (callbackChannel != null) {
            callbackChannel.setMethodCallHandler(null);
            callbackChannel = null;
        }
    }
}
