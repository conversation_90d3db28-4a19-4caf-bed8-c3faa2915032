plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

afterEvaluate {
    println "Flutter minSdkVersion: ${flutter.minSdkVersion}"
    println "Flutter targetSdkVersion: ${flutter.targetSdkVersion}"
}

android {
    namespace = "com.sobrr.agnes"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.sobrr.agnes"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 26
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    signingConfigs {
        release {
            storeFile file('../kiwi_agnes.jks')
            storePassword 'android'
            keyAlias 'kiwi'
            keyPassword 'android'
        }
        debug {
            storeFile file('../kiwi_agnes.jks')
            storePassword 'android'
            keyAlias 'kiwi'
            keyPassword 'android'
        }
    }

    sourceSets {
        googleplay {
            manifest.srcFile 'src/googleplay/AndroidManifest.xml'
        }
        other {
            manifest.srcFile 'src/other/AndroidManifest.xml'
        }
    }

    flavorDimensions "flavor-type"

    productFlavors {
        googleplay {
            dimension "flavor-type"
            applicationId "com.sobrr.agnes"
            manifestPlaceholders = [appChannel: "googleplay"]
        }
        other {
            dimension "flavor-type"
            applicationId "com.sobrr.agnes"
            manifestPlaceholders = [appChannel: "other"]
        }
    }

    buildTypes {
        release {
            // 添加编译选项以提高线程安全性
            ndk {
                debugSymbolLevel 'FULL'
            }
            // ① 启用混淆
            minifyEnabled true
            // ② 启用资源压缩（可选）
            shrinkResources true
            signingConfig signingConfigs.release
        }
        debug {
            signingConfig signingConfigs.debug
            minifyEnabled false // 可根据需要启用混淆
        }
    }
}

dependencies {
    implementation "androidx.core:core-splashscreen:1.0.1"
    implementation 'com.google.android.gms:play-services-auth:21.4.0'
    implementation "com.google.android.play:app-update:2.1.0"
    implementation "com.google.android.play:app-update-ktx:2.1.0"
    // Speech SDK
    implementation 'com.microsoft.cognitiveservices.speech:client-sdk:1.45.0'
}

flutter {
    source = "../.."
}
