# Keep BlockHound integration classes
-dontwarn reactor.blockhound.integration.BlockHoundIntegration
-dontwarn reactor.core.scheduler.ReactorBlockHoundIntegration
#-keep class reactor.blockhound.integration.BlockHoundIntegration { *; }
#-keep class reactor.core.scheduler.ReactorBlockHoundIntegration { *; }
# 保留 Conscrypt
-keep class org.conscrypt.** { *; }
-dontwarn org.conscrypt.**

# 保留 OpenJSSE
-keep class org.openjsse.** { *; }
-dontwarn org.openjsse.**
# 或者更简单的忽略警告
-dontwarn reactor.blockhound.**
-dontwarn reactor.core.scheduler.**

# Preserve Singular SDK classes
-keep class com.singular.sdk.** { *; }

# Preserve Android Install Referrer library
-keep public class com.android.installreferrer.** { *; }

# Uncomment the following line if you are using the Singular 'revenue' function with Google Play Billing Library
#-keep public class com.android.billingclient.** { *; }

# Agora Chat SDK - 忽略中国厂商推送SDK的缺失类
-dontwarn com.heytap.msp.push.**
-dontwarn com.meizu.cloud.pushsdk.**
-dontwarn com.vivo.push.**
-dontwarn com.xiaomi.mipush.sdk.**

# 环信推送相关类
-dontwarn com.hyphenate.push.platform.**
-keep class com.hyphenate.push.platform.** { *; }

# Agora Chat SDK 核心类保护
-keep class com.hyphenate.** { *; }
-keep class io.agora.chat.** { *; }

# Jackson 库相关
-dontwarn java.beans.**
-dontwarn com.fasterxml.jackson.databind.ext.Java7SupportImpl

# 保护 Jackson 核心类
-keep class com.fasterxml.jackson.** { *; }
-keepclassmembers class * {
    @com.fasterxml.jackson.annotation.* <fields>;
    @com.fasterxml.jackson.annotation.* <methods>;
}
