#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agnes Flutter App 自动编译打包上传工具
跨平台支持：Windows、macOS、Linux
一个脚本解决所有平台的上传需求
"""

import os
import sys
import json
import time
import requests
import subprocess
import argparse
import platform
from datetime import datetime
from typing import Dict, Optional, Tuple

# Windows控制台UTF-8支持
if platform.system().lower() == 'windows':
    try:
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    except:
        pass

class FlutterAppUploader:
    def __init__(self, config_file: str = "upload_config.json"):
        """初始化上传器"""
        self.config_file = config_file
        self.config = self.load_config()
        self.session = requests.Session()
        self.is_windows = platform.system().lower() == 'windows'
        
    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if not config.get('api', {}).get('api_key'):
                raise ValueError("API Key 未配置，请在配置文件中设置 api.api_key")
            
            return config
        except FileNotFoundError:
            print(f"❌ 配置文件 {self.config_file} 不存在，请先运行 --setup")
            sys.exit(1)
        except json.JSONDecodeError:
            print(f"❌ 配置文件 {self.config_file} 格式错误")
            sys.exit(1)
        except ValueError as e:
            print(f"❌ 配置错误: {e}")
            sys.exit(1)

    def run_command(self, command: str, cwd: str = None) -> Tuple[bool, str]:
        """执行命令"""
        print(f"🔧 执行命令: {command}")
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd or os.getcwd(),
                capture_output=True,
                text=True,
                check=True,
                encoding='utf-8' if self.is_windows else None
            )
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令执行失败: {e}")
            print(f"❌ 错误输出: {e.stderr}")
            return False, e.stderr

    def check_environment(self) -> bool:
        """检查环境"""
        print("🔍 检查环境...")
        
        # 检查Python
        print(f"✅ Python: {sys.version.split()[0]}")
        
        # 检查Flutter
        success, output = self.run_command("flutter --version")
        if not success:
            print("❌ Flutter 未安装或不在PATH中")
            return False
        
        # 检查requests模块
        try:
            import requests
            print(f"✅ requests 模块已安装")
        except ImportError:
            print("⚠️  正在安装 requests 模块...")
            success, _ = self.run_command(f"{sys.executable} -m pip install requests")
            if not success:
                print("❌ 无法安装requests模块")
                return False
        
        return True

    def setup_config(self):
        """初始化配置文件"""
        print("🛠️  初始化配置文件...")
        
        if os.path.exists(self.config_file):
            reply = input(f"配置文件已存在，是否覆盖? (y/N): ")
            if reply.lower() != 'y':
                print("取消初始化")
                return
        
        config = {
            "api": {
                "api_key": "YOUR_PGYER_API_KEY",
                "base_url": "https://api.pgyer.com/apiv2"
            },
            "build": {
                "build_type": "android",
                "build_install_type": 1,
                "build_password": "",
                "build_description": "Agnes Flutter App",
                "build_update_description": "新版本更新",
                "build_install_date": 2,
                "build_install_start_date": "",
                "build_install_end_date": "",
                "build_channel_shortcut": "",
                "oversea": ""
            },
            "flutter": {
                "build_command": "flutter build apk --release --flavor=googleplay",
                "apk_path": "build/app/outputs/flutter-apk/app-googleplay-release.apk",
                "clean_before_build": True,
                "get_dependencies": True
            },
            "upload": {
                "max_retries": 3,
                "retry_delay": 5,
                "check_interval": 3,
                "max_check_attempts": 60
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置文件已创建: {self.config_file}")
        print("⚠️  请编辑配置文件，设置您的蒲公英API Key")
        print("🔗 获取API Key: https://www.pgyer.com/account/api")

    def clean_and_prepare(self) -> bool:
        """清理并准备构建环境"""
        print("🧹 准备构建环境...")
        
        if self.config['flutter'].get('clean_before_build', True):
            success, _ = self.run_command("flutter clean")
            if not success:
                return False
        
        if self.config['flutter'].get('get_dependencies', True):
            success, _ = self.run_command("flutter pub get")
            if not success:
                return False
        
        return True

    def build_flutter_app(self) -> bool:
        """编译Flutter应用"""
        print("📱 开始编译Flutter应用...")
        
        build_command = self.config['flutter']['build_command']
        success, output = self.run_command(build_command)
        
        if success:
            print("✅ Flutter应用编译成功")
            return True
        else:
            print("❌ Flutter应用编译失败")
            return False

    def check_apk_exists(self) -> bool:
        """检查APK文件是否存在"""
        apk_path = self.config['flutter']['apk_path']
        
        if self.is_windows:
            apk_path = apk_path.replace('/', os.sep)
        
        if os.path.exists(apk_path):
            file_size = os.path.getsize(apk_path)
            print(f"✅ APK文件存在: {apk_path} ({file_size / (1024*1024):.2f} MB)")
            return True
        else:
            print(f"❌ APK文件不存在: {apk_path}")
            return False

    def get_upload_token(self) -> Optional[Dict]:
        """获取上传token"""
        print("🔑 获取上传token...")
        
        url = f"{self.config['api']['base_url']}/app/getCOSToken"
        
        data = {
            '_api_key': self.config['api']['api_key'],
            'buildType': self.config['build']['build_type']
        }
        
        # 添加可选参数
        build_config = self.config['build']
        optional_params = {
            'oversea': 'oversea',
            'build_install_type': 'buildInstallType',
            'build_password': 'buildPassword',
            'build_description': 'buildDescription',
            'build_update_description': 'buildUpdateDescription',
            'build_install_date': 'buildInstallDate',
            'build_install_start_date': 'buildInstallStartDate',
            'build_install_end_date': 'buildInstallEndDate',
            'build_channel_shortcut': 'buildChannelShortcut'
        }
        
        for config_key, api_key in optional_params.items():
            value = build_config.get(config_key)
            if value:
                # 如果是build_update_description，自动生成当前时间
                if config_key == 'build_update_description':
                    current_time = datetime.now().strftime("%Y-%m%d-%H:%M")
                    data[api_key] = current_time
                    print(f"📅 自动设置更新描述: {current_time}")
                else:
                    data[api_key] = value

        try:
            response = self.session.post(url, data=data)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('code') == 0:
                print("✅ 获取上传token成功")
                return result['data']
            else:
                print(f"❌ 获取上传token失败: {result.get('message', '未知错误')}")
                return None
                
        except requests.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return None

    def upload_file(self, token_data: Dict) -> bool:
        """上传文件到蒲公英"""
        print("📤 上传APK文件...")
        
        apk_path = self.config['flutter']['apk_path']
        if self.is_windows:
            apk_path = apk_path.replace('/', os.sep)
        
        data = {
            'key': token_data['key'],
            'signature': token_data['params']['signature'],
            'x-cos-security-token': token_data['params']['x-cos-security-token'],
            'x-cos-meta-file-name': os.path.basename(apk_path)
        }
        
        try:
            with open(apk_path, 'rb') as f:
                files = {'file': f}
                
                response = self.session.post(
                    token_data['endpoint'],
                    data=data,
                    files=files,
                    timeout=300
                )
                
                if response.status_code == 204:
                    print("✅ APK文件上传成功")
                    return True
                else:
                    print(f"❌ APK文件上传失败: HTTP {response.status_code}")
                    return False
                    
        except requests.RequestException as e:
            print(f"❌ 上传请求失败: {e}")
            return False
        except FileNotFoundError:
            print(f"❌ APK文件不存在: {apk_path}")
            return False

    def check_build_status(self, build_key: str) -> Optional[Dict]:
        """检查构建状态"""
        print("⏳ 检查应用发布状态...")
        
        url = f"{self.config['api']['base_url']}/app/buildInfo"
        params = {
            '_api_key': self.config['api']['api_key'],
            'buildKey': build_key
        }
        
        max_attempts = self.config['upload'].get('max_check_attempts', 60)
        check_interval = self.config['upload'].get('check_interval', 3)
        
        for attempt in range(max_attempts):
            try:
                response = self.session.get(url, params=params)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get('code') == 0:
                    print("✅ 应用发布成功!")
                    return result['data']
                elif result.get('code') == 1247:
                    print(f"⏳ 应用正在发布中... (第{attempt + 1}次检查)")
                    time.sleep(check_interval)
                    continue
                elif result.get('code') == 1216:
                    print(f"❌ 应用发布失败: {result.get('message', '未知错误')}")
                    return None
                else:
                    print(f"❌ 检查状态失败: {result.get('message', '未知错误')}")
                    return None
                    
            except requests.RequestException as e:
                print(f"❌ 检查状态请求失败: {e}")
                time.sleep(check_interval)
                continue
        
        print("❌ 检查状态超时")
        return None

    def print_build_info(self, build_info: Dict):
        """打印构建信息"""
        print("\n🎉 发布成功! 应用信息:")
        print(f"📱 应用名称: {build_info.get('buildName', 'N/A')}")
        print(f"📦 版本号: {build_info.get('buildVersion', 'N/A')}")
        print(f"🔢 版本编号: {build_info.get('buildVersionNo', 'N/A')}")
        
        # 处理文件大小，可能是字符串或数字
        file_size = build_info.get('buildFileSize', 0)
        try:
            file_size_mb = float(file_size) / (1024*1024)
            print(f"📏 文件大小: {file_size_mb:.2f} MB")
        except (ValueError, TypeError):
            print(f"📏 文件大小: {file_size}")
        
        print(f"🔗 下载链接: https://www.pgyer.com/{build_info.get('buildShortcutUrl', 'N/A')}")
        print(f"📱 二维码: {build_info.get('buildQRCodeURL', 'N/A')}")

    def upload(self, skip_build: bool = False) -> bool:
        """执行完整的上传流程"""
        print("🚀 开始自动编译打包上传流程...")
        
        if not skip_build:
            if not self.clean_and_prepare():
                return False
            
            if not self.build_flutter_app():
                return False
        
        if not self.check_apk_exists():
            return False
        
        token_data = self.get_upload_token()
        if not token_data:
            return False
        
        if not self.upload_file(token_data):
            return False
        
        build_info = self.check_build_status(token_data['key'])
        if build_info:
            self.print_build_info(build_info)
            return True
        else:
            return False

def main():
    parser = argparse.ArgumentParser(description='Agnes Flutter App 自动编译打包上传工具')
    parser.add_argument('--config', default='upload_config.json', help='配置文件路径')
    parser.add_argument('--skip-build', action='store_true', help='跳过编译步骤，仅上传APK')
    parser.add_argument('--setup', action='store_true', help='初始化配置文件')
    parser.add_argument('--check', action='store_true', help='检查环境和依赖')
    
    args = parser.parse_args()
    
    # 显示平台信息
    system_name = {
        'windows': 'Windows',
        'darwin': 'macOS', 
        'linux': 'Linux'
    }.get(platform.system().lower(), platform.system())
    
    print(f"🖥️  运行平台: {system_name}")
    
    if args.setup:
        uploader = FlutterAppUploader.__new__(FlutterAppUploader)
        uploader.config_file = args.config
        uploader.setup_config()
        return
    
    if args.check:
        uploader = FlutterAppUploader.__new__(FlutterAppUploader)
        uploader.is_windows = platform.system().lower() == 'windows'
        if uploader.check_environment():
            print("✅ 环境检查通过")
        else:
            print("❌ 环境检查失败")
            sys.exit(1)
        return
    
    try:
        uploader = FlutterAppUploader(args.config)
        success = uploader.upload(skip_build=args.skip_build)
        if not success:
            sys.exit(1)
        print("\n🎉 所有操作完成!")
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        sys.exit(130)

if __name__ == '__main__':
    main()