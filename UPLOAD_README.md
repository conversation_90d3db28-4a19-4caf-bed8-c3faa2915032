# Agnes Flutter App 自动编译打包上传工具

这是一个用于 Agnes Flutter 应用的自动化编译打包上传工具，支持一键完成从 Flutter 编译到蒲公英平台上传的完整流程。

## 功能特性

- 🚀 **一键自动化**: 支持完整的编译、打包、上传流程
- 🌍 **跨平台支持**: Windows、macOS、Linux 统一脚本
- ⚙️ **灵活配置**: 通过配置文件管理所有参数
- 📱 **多格式支持**: 支持 Android APK 上传（可扩展支持 iOS）
- 🔄 **智能重试**: 自动重试机制，提高上传成功率
- 📊 **详细日志**: 完整的操作日志和状态反馈
- 🛡️ **错误处理**: 完善的错误处理和用户提示
- ⏰ **自动时间戳**: 自动生成上传时间作为更新说明

## 文件说明

- `upload` - 主脚本，跨平台 Python 脚本（Linux/macOS 可直接执行）
- `upload.bat` - Windows 批处理入口，调用主脚本
- `upload_config.json` - 配置文件，包含所有可配置参数

## 快速开始

### 1. 环境要求

- **Python 3.6+**: 用于运行上传脚本
- **Flutter SDK**: 用于编译 Flutter 应用
- **requests 库**: Python HTTP 库（脚本会自动安装）

### 2. 初始化配置

```bash
# Linux/macOS
./upload --setup

# Windows
upload.bat --setup
# 或直接使用
python upload --setup
```

这会创建 `upload_config.json` 配置文件，你需要编辑它来设置你的参数。

### 3. 配置 API Key

编辑 `upload_config.json` 文件，将 `YOUR_PGYER_API_KEY` 替换为你的蒲公英 API Key：

```json
{
  "api": {
    "api_key": "your_actual_api_key_here",
    "base_url": "https://api.pgyer.com/apiv2"
  }
}
```

**获取 API Key**: 登录 [蒲公英后台](https://www.pgyer.com/account/api) 获取你的 API Key。

### 4. 执行上传

```bash
# Linux/macOS
./upload                    # 完整流程：清理 -> 编译 -> 上传
./upload --skip-build       # 仅上传已编译的 APK
./upload --config my.json   # 使用自定义配置文件

# Windows
upload.bat                  # 完整流程
upload.bat --skip-build     # 仅上传APK
upload.bat --config my.json # 自定义配置

# 或任意平台直接使用Python
python upload --help
```

## 详细配置说明

### API 配置 (`api`)

```json
{
  "api": {
    "api_key": "your_pgyer_api_key",     // 蒲公英 API Key（必填）
    "base_url": "https://api.pgyer.com/apiv2"  // API 基础地址
  }
}
```

### 构建配置 (`build`)

```json
{
  "build": {
    "build_type": "android",              // 应用类型: android/ios
    "build_install_type": 1,              // 安装方式: 1=公开, 2=密码, 3=邀请
    "build_password": "",                 // 安装密码（可选）
    "build_description": "Agnes Flutter App",  // 应用介绍
    "build_update_description": "新版本更新",   // 更新说明（自动生成当前时间）
    "build_install_date": 2,              // 有效期: 1=设置时间, 2=长期有效
    "build_install_start_date": "",       // 有效期开始时间（格式: 2024-01-01）
    "build_install_end_date": "",         // 有效期结束时间（格式: 2024-12-31）
    "build_channel_shortcut": "",         // 渠道短链接（可选）
    "oversea": ""                         // 海外加速: 1=海外, 2=国内, 空=自动
  }
}
```

**⏰ 自动时间戳功能**: 当配置了 `build_update_description` 参数时，脚本会自动将其值替换为当前上传时间，格式为 `YYYY-MMDD-HH:MM`（如：`2025-0825-14:30`）。这样可以确保每次上传都有明确的时间标记。

### Flutter 配置 (`flutter`)

```json
{
  "flutter": {
    "build_command": "flutter build apk --release",  // 编译命令
    "apk_path": "build/app/outputs/flutter-apk/app-release.apk",  // APK 路径
    "clean_before_build": true,           // 编译前是否清理
    "get_dependencies": true              // 编译前是否获取依赖
  }
}
```

### 上传配置 (`upload`)

```json
{
  "upload": {
    "max_retries": 3,          // 最大重试次数
    "retry_delay": 5,          // 重试延迟（秒）
    "check_interval": 3,       // 状态检查间隔（秒）
    "max_check_attempts": 60   // 最大检查次数
  }
}
```

## 使用示例

### 基本使用

```bash
# 检查环境和依赖
./upload --check        # Linux/macOS
upload.bat --check      # Windows

# 执行完整的编译上传流程
./upload               # Linux/macOS
upload.bat             # Windows

# 查看帮助
./upload --help        # Linux/macOS
upload.bat --help      # Windows
```

### 高级使用

```bash
# 跳过编译，仅上传现有 APK
./upload --skip-build              # Linux/macOS
upload.bat --skip-build            # Windows

# 使用自定义配置文件
./upload --config prod.json        # Linux/macOS
upload.bat --config prod.json      # Windows

# 直接使用 Python（任意平台）
python upload --config my_config.json
python3 upload --skip-build
```

### 自定义编译命令

你可以在配置文件中修改 `build_command` 来自定义编译参数：

```json
{
  "flutter": {
    "build_command": "flutter build apk --release --target-platform android-arm64",
    "apk_path": "build/app/outputs/flutter-apk/app-release.apk"
  }
}
```

## 输出示例

成功上传后，脚本会显示详细的应用信息：

```
📅 自动设置更新描述: 2025-0825-14:30
🎉 发布成功! 应用信息:
📱 应用名称: Agnes Flutter App
📦 版本号: 1.0.0
🔢 版本编号: 1
📏 文件大小: 25.30 MB
🔗 下载链接: https://www.pgyer.com/abcd1234
📱 二维码: https://qr.pgyer.com/abcd1234
📝 应用介绍: Agnes Flutter App
🆕 更新说明: 2025-0825-14:30
📅 上传时间: 2024-01-01 12:00:00
```

## 故障排除

### 常见问题

1. **API Key 错误**
   ```
   ❌ 获取上传token失败: Invalid API Key
   ```
   - 检查 `upload_config.json` 中的 `api_key` 是否正确
   - 确认 API Key 来自蒲公英官方后台

2. **Flutter 编译失败**
   ```
   ❌ Flutter应用编译失败
   ```
   - 检查 Flutter 环境是否正常：`flutter doctor`
   - 手动执行编译命令测试：`flutter build apk --release`
   - 检查项目依赖是否完整：`flutter pub get`

3. **APK 文件不存在**
   ```
   ❌ APK文件不存在: build/app/outputs/flutter-apk/app-release.apk
   ```
   - 确认编译成功完成
   - 检查配置文件中的 `apk_path` 是否正确
   - 手动检查 APK 文件是否存在

4. **网络上传失败**
   ```
   ❌ APK文件上传失败: HTTP 403
   ```
   - 检查网络连接
   - 尝试配置 `oversea` 参数（1=海外，2=国内）
   - 检查文件大小是否超过限制

### 调试模式

如果遇到问题，你可以：

1. **检查环境**:
   ```bash
   ./upload --check        # Linux/macOS
   upload.bat --check      # Windows
   ```

2. **分步执行**:
   ```bash
   # 先编译
   flutter build apk --release
   
   # 再上传
   ./upload --skip-build        # Linux/macOS
   upload.bat --skip-build      # Windows
   ```

3. **查看详细日志**: 脚本会输出详细的执行日志，包括每一步的状态和错误信息。

## 安全注意事项

1. **API Key 保护**: 不要将包含真实 API Key 的配置文件提交到代码仓库
2. **配置文件**: 建议将 `upload_config.json` 添加到 `.gitignore`
3. **权限控制**: 确保只有授权用户能够执行上传脚本

## 扩展和自定义

### 添加新的编译目标

修改配置文件中的 `build_command` 和 `apk_path`：

```json
{
  "flutter": {
    "build_command": "flutter build appbundle --release",
    "apk_path": "build/app/outputs/bundle/release/app-release.aab"
  }
}
```

### 支持多环境配置

创建不同的配置文件：

- `upload_config_dev.json` - 开发环境
- `upload_config_prod.json` - 生产环境

使用时指定配置文件：

```bash
./upload --config upload_config_prod.json        # Linux/macOS
upload.bat --config upload_config_prod.json      # Windows
```

## 更新日志

- **v2.1.0** (2025-08-25)
  - ⏰ 新增自动时间戳功能
  - 📅 `build_update_description` 自动生成当前上传时间（格式：YYYY-MMDD-HH:MM）
  - 🔄 增强上传日志显示，明确显示时间戳设置

- **v2.0.0** (2024-01-01)
  - 🌍 完整跨平台支持（Windows、macOS、Linux）
  - 📦 简化为单一脚本架构
  - 🚀 自动环境检测和依赖安装
  - 🛠️ 统一的命令行接口

- **v1.0.0** (2024-01-01)
  - 初始版本发布
  - 支持 Android APK 自动编译上传
  - 完整的配置文件支持

## 许可证

本工具基于项目许可证开源，仅供 Agnes Flutter 项目内部使用。

## 支持

如果遇到问题或需要帮助，请：

1. 查看本文档的故障排除部分
2. 检查蒲公英官方文档：https://www.pgyer.com/doc/view/api
3. 联系项目维护人员